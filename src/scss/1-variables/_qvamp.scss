// Theme colors
$white: #fff;
$gray-100: #F4F4F4;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: #212529;
$gray-q1: #8e9aa7;
$gray-q2: #363d44;
$black: #000;
$gold: #FFD700;
$black-600: #898989;
$black-700: #262A33;

$primary: #a30657;
$secondary: #3A3A3A;
$tertiary: #f8e8ee;
$success: #0F683E;
$info: $gray-400;
$warning: #b76e79;
$danger: #8a0014;
$green-bg-card: #E3FFC7;

$primary-light: #FECDE0;

$light: $gray-600;
$dark: #750441;
$rose-gold: #b76e79;
$primary-bg: #CC076D;

$poptavka-bg: #0245CA;
$kalkulace-bg: #0E4030;
$event-bg: $primary;
$harmonogram-bg: #674626;
$aktivity-bg: #EAC435;

$qvamp-gradient: radial-gradient(circle at 0 0,
        #099ef1 0%,
        #6863f8 18.82%,
        #d84ffa 32.6%,
        #f058c5 52.83%,
        #ff4f90 68.03%,
        #ff6558 87.66%,
        #ff891f 100%
);

$qvamp-gradient-light: linear-gradient(
                45deg, // úhel
                #099ef1 0%,
                #7612fa 52%,
                #d84ffa 90%
);

// Text s gradientem
.text-gradient {
  -webkit-text-fill-color: transparent;
  background: $qvamp-gradient;
  -webkit-background-clip: text;
  background-clip: text;
  display: inline-block;
}



.poptavka-bg {
  background-color: $poptavka-bg;
}

.border-poptavka{
  border-color: $poptavka-bg!important;
}

.kalkulace-bg {
  background-color: $kalkulace-bg;
}

.border-kalkulace{
  border-color: $kalkulace-bg;
}

.event-bg {
  background-color: $event-bg;
}
.harmonogram-bg {
  background-color: $harmonogram-bg;
}
.aktivity-bg {
  background-color: $aktivity-bg;
}
.text-poptavka{
  color:$poptavka-bg;
}

.bg-secondary-grad{
  background: rgb(0,0,0);
  background: radial-gradient(circle, rgba(0,0,0,1) 0%, rgba(58,58,58,1) 78%);
}

.bg-white-30{
  background-color: #FFFFFF30;
}

.bg-gray-200{
  background-color: $gray-200;
}

//Texty
p, h1, h2, h3, h4, .lead {
  color: $secondary;
}

/* Nadpisy */
h1 {
  font-size: 3.5rem;
  line-height: 4rem;
  font-weight: bold;
}

h2 {
  font-size: 2.875rem;
  line-height: 3.375rem;
  font-weight: 600;
}

h3 {
  font-size: 2.375rem;
  line-height: 2.875rem;
  font-weight: 600;
}

h4 {
  font-size: 1.875rem;
  line-height: 2.375rem;
  font-weight: 600;
}

.lead {
  font-size: 1.5rem;
  line-height: 2rem;
}

.p {
  font-size: 1rem;
  line-height: 1.5rem;
}

small {
  color: $gray-600;
}

.text-secondary {
  color: $secondary;
}

.text-strong{
  font-weight: 700;
}

.text-gray {
  color: $gray-600;
}

.text-gray-light {
  color: $gray-500;
}

//Button
$primary-hover: #6f043b;

// Sidebar brand
$bg-body-tertiary: #F8F9FA;

// Splash
$splash-bg: rgb(73 61 61 / 0.5);

// Wrapper
$wrapper-bg: #F6F6F6;
$grad-color: #33001b;

// Body
$body-bg: #F6F6F6;

//color card
$color-card: #fae6ef;

//report-minus-bacground
$bg-minus: #fae6ef;
$bg-plus: #e8fde1;

// Event- zbývající počet dní
$d-1000: $info;
$d-30: $warning;
$d-14: $danger;
$d-3: red;
$po-akci: $success;

// Table
$table-active-bg: #fae6f0;

$input-border-radius: 16px;
$input-border-radius-sm: 16px;
$input-border-radius-lg: 16px;

$btn-border-radius: 16px;
$btn-border-radius-sm: 16px;
$btn-border-radius-lg: 16px;

$pagination-border-radius: 16px;
$pagination-border-radius-sm: 16px;
$pagination-border-radius-lg: 16px;
//font

// Radiusy
$border-radius-sm: 16px;
$border-radius: 16px;
$border-radius-lg: 16px;

.radius-icon{
  border-radius: .5rem;
}

.icon-shadow{
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1) !important;
}

p, h1, h2, h3, h4, h5 {
  color: $black-700 !important;
}

.z-9{
  z-index: 9999!important;
}

.cursor-text{
  cursor: text!important;
}

//buttons
.btn-primary {
  padding: 7px 11px;
  border-radius: $border-radius;
}

.btn-outline-light:hover{
  background-color: $gray-100!important;
}

//profile picture
.profile-pic-bg {
  align-items: center;
  background-color: #ffe3fc;
  border: 0.5px solid;
  border-radius: 50%;
  display: flex;
  height: 20px;
  justify-content: center;
  width: 20px;
  font-size: 8px;
  font-weight: bold;
}

//formuláře hash
.form-borde-shaddow {
  border: $primary 1px solid;
  border-radius: $border-radius;
  box-shadow: 0 24px 64px #26214a1a;
}

.form-borde-shaddow-info {
  border: $info 1px solid;
  border-radius: $border-radius;
}

//zabarvení záhlaví tabulky
.primary-color-row {
  background-color: $secondary;
  color: $white !important;
  font-weight: 500;
  border-radius: $border-radius;
  margin-top: 1rem !important;
  margin-right: 1rem !important;
}

.background-body-bg-color {
  background-color: $body-bg;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

.toolbar-section {
  margin-bottom: 1rem;
  padding: 0.6rem 0rem !important;
}

.toolbar-section h4 {
  margin-bottom: 0;
  font-size: 18px;
  font-weight: 500;
}

#panely_parent .list-group {
  border-radius: $border-radius;
}

.modal-body .list-group {
  border-radius: $border-radius !important;
}

.list-group {
  border-radius: $border-radius !important;
}

.rohy-2-2 {
  border-radius: $border-radius $border-radius 0 0;
}

.list-group-item-secondary {
  background-color: $white !important;

  &.active {
    background-color: $secondary!important;
  }
}

//Modal-design
.modal-content {
  border-radius: $border-radius !important;
}

.modal-title {
  font-weight: 400;
  font-size: 1rem;
}

.modal-header {

}

.form-control {
  border-color: #e6e8ed !important;
}
.input-right-sharp {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}
.rounded-custom {
  border-top-right-radius: 15px;
  border-bottom-right-radius: 15px;
}

/* Zvětšení přepínače pomocí transformace */
.form-switch-lg input[type="checkbox"] {
  transform: scale(1.5); /* Zvětší přepínač na 150% původní velikosti */
  cursor: pointer;
}
/* Možná úprava labelu, pokud chcete větší text */
.form-switch-lg .form-check-label {
  font-size: 1.25rem;
  margin-left: 1rem;
}

.form-control.is-invalid, .was-validated .form-control:invalid {
  border-color: $danger !important;
}

.form-control-inline {
  width: auto !important;
  margin-left: 3px;
}

.left_label {
  font-size: 1.2rem;
}

.price {
  line-height: 1.6rem
}

/*app/front/api/modul/nabidka/detail/ApiDetailNabidkaPage.latte*/
/*Hash - nabídka-pk*/
.proposal-card {
  border-radius: $border-radius;
}

/*Tabulka - hash nabídka-pk*/
.table-proposal th {
  background: $secondary !important; /*Primary color*/
  color: $white;
}

.nadpis-sedy {
  background: $secondary; /*Primary color*/
  color: $white;
  text-align: center;
  font-weight: 500;
  font-size: 1.4rem;
  border-radius: $border-radius;
}

.cena {
  color: $primary;
  font-size: 2rem;
}

.color-card {
  background-color: $color-card;
  border-radius: $border-radius;
}

.event-status {
  border: solid 1px $d-30;
  border-radius: $border-radius;
  background-color: $d-30; //variabilní barva podle zbývajících dnů do akce
  box-shadow: 0 24px 64px #26214a1a;
}

//Comment
.bg-chat {
  background-color: $body-bg;
}

//Úkoly - event
//Comment
.bg-ukol {
  background-color: $gray-200 !important;
}

//rating zákazníka
.rating {
  display: inline-block;
  position: relative;
}

.star {
  font-size: 1.5rem;
}

.rating label {
  position: absolute;
  cursor: pointer;
}

.rating label:last-child {
  position: static;
}

.rating label:nth-child(1) {
  z-index: 5;
}

.rating label:nth-child(2) {
  z-index: 4;
}

.rating label:nth-child(3) {
  z-index: 3;
}

.rating label:nth-child(4) {
  z-index: 2;
}

.rating label:nth-child(5) {
  z-index: 1;
}

.rating label input {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
}

.rating label .star {
  float: left;
  color: transparent;
}

.rating label:last-child .star {

  text-shadow: 0 0 1px $gray-600;
}

.rating:not(:hover) label input:checked ~ .star,
.rating:hover label:hover input ~ .star {
  color: $gold;
}

.rating label input:focus:not(:checked) ~ .star:last-child {
  color: $gold;
}


/*Úpravy nabulek číselníků*/
#CenoveSkupinyTable_length, #CenoveSkupinyTable_info, #SeznamMistnostiTable_length, #SeznamMistnostiTable_info, #TypyAkciTable_length, #TypyAkciTable_info, #dashboard-poptavky #SeznamLeaduTable .dyntable-search, #dashboard-poptavky #SeznamLeaduTable_info, #dashboard-poptavky #SeznamLeaduTable_length {
  display: none;
}

/*Nápovědy*/
.napoveda-modal {
  border: 1px solid $secondary;
  border-radius: $border-radius;
}

//Přepínač
.notActive {
  background-color: transparent !important;
  color: $primary !important;
}

//Časová osa - platební scénáře
.ikona {
  color: $white;
}

.ikona-start-konec {
  font-size: 2rem;
}

@media only screen and (max-width: 700px) {
  .timeline-platba-scenar {
    border: solid 1px $white !important;
  }
}

.timeline-platba-scenar-polozka {
  font-size: 0.8rem;
  padding-left: 0px !important;
  color: $white !important;
}

//Dashboard
#dashboard-poptavky thead {
  display: none !important;
}

.alert-message a {
  color: white;
}

.napoveda-ikona {
  font-size: 2rem;
  color: $info;
}

//Ohraničený input - form
.input-ohraniceni {
  border: 1px $gray-300 solid;
  border-radius: $border-radius;
}

//Vyhledávání položek v přehledu položek v modalu úpravy balíku
#SeznamPolozekBalikuTable .dyntable-search, #SeznamPolozekBalikuTable_length, #SeznamPolozekBalikuTable_filter {
  display: none;
}

//Uprava vzhledu karet
.card {
  border-radius: $border-radius !important;
}

label.btn-check:hover {
  color: $gray-700 !important;;

}

label:not(.btn) {
  margin-bottom: 0.2rem;
  text-transform: uppercase;
  font-size: 0.65rem !important;

   &:not(.is-invalid){
      color: $gray-700 !important;
   }
}

.form-floating label {
  color: $gray-900 !important;
  padding: 1rem 1.2rem;
}

textarea::placeholder {
  color: $gray-200;
  font-size: 0.6rem;
}

.js-datepicker .datetimepicker-input, .js-timepicker .datetimepicker-input {
  border-radius: 12px 0px 0px 12px !important;
}

.js-datepicker .input-group-text, .js-timepicker .input-group-text {
  border-radius: 0px 12px 12px 0px !important;
}

.input-levy-round {
  border-radius: 12px 0px 0px 12px !important;
}

.input-pravy-round {
  border-radius: 0px 12px 12px 0px !important;
}

.bg-gray-100 {
  background-color: $gray-100 !important;
}

//Reporty
.report-bef {
  padding: 0.2rem 0.3rem 0.2rem 0.3rem;
  border-radius: 5px;
}

.report-bef-plus {
  background-color: $bg-plus;
  color: $success;
}

.report-bef-minus {
  background-color: $bg-minus;
  color: $danger;
}

//Block line
.blok-line-2 {
  height: auto !important;
}

//zkratky-menu
.zkratky-menu {
  background-color: $primary !important;
  border-radius: $border-radius !important;
  min-width: 10px !important;
}

.zkratky-menu a {
  color: $white;
  border-radius: $border-radius;
}

.zkratky-menu a:hover {
  color: $gray-400;
}

.zkratky-menu .dropdown-item:hover {
  background-color: $primary;
  border-radius: $border-radius;
}

//Aktivity - dashboard
.aktivity-dashboard td {
  font-size: 0.9rem;
}

.text-muted {
  font-size: 0.8rem !important;
}

/*Tabulky - úpravy*/
.table {
  color: $gray-q2 !important;
}

//Tlačítka
.pridat {
  border-radius: 999px;
  background-color: $primary;
  padding: 0.5rem;
  color: $white;
  font-size: 0.8rem;
}

/* The customcheck */
.customcheck {
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.customcheck input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

/*Sidebar - úpravy*/
.bg-body-tertiary {
  background-color: $bg-body-tertiary;
}

@media (max-width: 991.97px) {
  .navbar {
    margin: 1rem 0.5rem 0 0.5rem;
    border-radius:$border-radius;
  }
  #pills-tab{
    background-color: $body-bg!important;
  }
}

@media (min-width: 992px) {
  .content {
    padding: 0 1rem !important;
  }
}

.navbar .nav-item .dropdown-menu {
  border-radius: 12px !important;
}
.notifikace:first-child{
  border-radius:12px 12px 0 0 !important;
}

.overflow-x-auto {
  overflow-x: auto;
}

.radius-card {
  border-radius: $border-radius !important;
}

.w-10 {
  width: 10%;
}

.w-20 {
  width: 20%;
}

.w-30 {
  width: 30%;
}

.w-40 {
  width: 40%;
}

.w-60 {
  width: 60%;
}

.w-70 {
  width: 70%;
}

.w-80 {
  width: 80%;
}

.w-90 {
  width: 90%;
}

.border-radius {
  border-radius: $border-radius;
}

.sticky-menu {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
}

.portal-logo {
  max-height: 100px;
  width: auto;
  object-fit: contain;
}

.check-list-primary li span svg {
  color: $primary;
  margin-right: 5px;
}

.sw-theme-arrows .toolbar > .btn {
  background-color: $primary !important;
  border: none !important;
}

.bg-primary-card-light {
  background-color: $primary-light !important;
}

.green-bg-card {
  background-color: $green-bg-card !important;
}

.bg-glass{
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
}

.radius-card-catalog {
  border-radius: $border-radius 0 0 $border-radius;
}

@media screen and (max-width: 767px) {
  .radius-card-catalog {
    border-radius: $border-radius $border-radius 0 0;
  }

}

.hover-card:hover {
  box-shadow: 0 0 11px rgba(33, 33, 33, .2);
}

.min-width-5r-70 {
  width: min(5rem, 70%) !important;
}

@media screen and (min-width: 767px) {
  .client-min-width-5r-70 {
    width: min(5rem, 70%) !important;
  }
}

//Kalendář
.fc .fc-button-primary {
  background-color: transparent !important;
  color: $secondary!important;
}

.fc .fc-button-active {
  background-color: $secondary !important;
  color: $white!important;
}

.fc .fc-button {
  border-radius: $border-radius !important;
  font-size: 0.9em !important;
  margin: 0 2px !important;
}

.fc .fc-button-primary:hover {
  box-shadow: rgba(17, 17, 26, 0.1) 0px 4px 16px, rgba(17, 17, 26, 0.1) 0px 8px 24px, rgba(17, 17, 26, 0.1) 0px 16px 56px;
}

.fc .fc-scrollgrid-liquid, .fc-list, .fc-scroller{
  border-radius:$border-radius;
}

.fc thead th{
  border-radius:$border-radius $border-radius 0 0;
}

.fc .fc-col-header-cell a, .fc-list-day-text, .fc-list-day-side-text{
  color:$secondary;
}

.fc-theme-standard .fc-list-day-cushion{
  background-color: $gray-100!important;
  padding-top: .8rem!important;
  padding-bottom: .8rem!important;
}

.fc .fc-daygrid-day-number{
  color:$gray-600;
}

.fc-daygrid-event-harness {
  cursor: pointer;
  color: $gray-q2 !important;
}

.fc-daygrid-more-link{
  font-size: 0.7rem;
  margin-left: 0.4rem;
}

.fc-daygrid-day-number:hover{
  text-underline: none!important;
  background-color: $primary;
  border-radius: 12px;
  color: $white;
}

.fc-header-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top:1rem;
}

@media screen and (max-width: 1150px) {
  .fc-header-toolbar{
    flex-direction: column-reverse;
  }
}

.fc-toolbar-chunk {
  display: inline;
  justify-content: center;
  align-items: center;
}


.fc-daygrid-event-harness a{
  font-weight: 700;
  border-radius: $border-radius;
  padding-left: .2rem;
}

.fc-list-event:hover{
  cursor: pointer;
}


/*Kalendář - responzivita*/
@media (max-width: 991.97px) {

  .fc-toolbar-title {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .fc-toolbar-chunk{
    text-align: center;
  }
}

//Shadow
.shaddow{
  box-shadow: rgba(17, 17, 26, 0.1) 0px 4px 16px, rgba(17, 17, 26, 0.1) 0px 8px 24px, rgba(17, 17, 26, 0.1) 0px 16px 56px!important;
}

//Hover třídy
.shaddow-hover:hover, .shaddow-hover.active-shaddow{
  box-shadow: rgba(17, 17, 26, 0.1) 0px 4px 16px, rgba(17, 17, 26, 0.1) 0px 8px 24px, rgba(17, 17, 26, 0.1) 0px 16px 56px;
  transition: box-shadow 0.2s ease-in-out;
}

.hover-icon-arrow:hover{
  color: $primary;
}

//Karty
.border-light-gray{
  border-color: $gray-300!important;
}

.tox-tinymce{
  border-radius: $border-radius;
}

.shaddow-light{
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}

.tox-tinymce{
  border-radius: $border-radius!important;
  padding-left: 0!important;
  padding-right: 0!important;
}

//Telefon - předvolba
.js-telephone-input-row .select2-container--bootstrap4 .select2-selection{
  border-radius: $border-radius 0 0 $border-radius!important;
}

.select2-container--open .select2-dropdown--above, .select2-container--open .select2-dropdown--below{
  border-radius: $border-radius!important;
  border-top:1px;
  border-bottom: 1px;
}

.select2-container--bootstrap4.select2-container--open.select2-container--above .select2-selection{
  border-top-color: none;
}
.select2-container--bootstrap4.select2-container--open.select2-container--below .select2-selection{
  border-top-color: none;
}
.select2-container--bootstrap4 .select2-selection{
  border-color: $gray-300!important;
}

.select2 .selection .select2-selection--multiple{
  border-radius: $border-radius!important;
}


//Data Tables
/* Styl pro tabulku */
table.dataTable {
  width: 100%;
  border-collapse: collapse;
  background-color: #ffffff;
  overflow: hidden;

  thead {
    background-color: $white;
    text-align: left;
    th {
      font-weight: 600;
      color: $gray-700;
      text-transform: uppercase;
      position: sticky;
      top: 0;
      background-color: $white;
      z-index: 10;
    }
  }

  tbody {
    background-color: $white;

    tr {
      border-bottom: 1px solid $gray-100;

      &:nth-child(even) {
        background-color: $white;
      }

      &:hover {
        background-color: $gray-100!important;
      }

      td {
        padding: 8px 10px;
        color: $gray-700;
      }
    }
  }

  /* Stylování pro paginaci */
  .dataTables_wrapper {
    display: flex;
    flex-direction: column;

    .dataTables_paginate {
      display: flex;
      justify-content: flex-end;
      padding: 10px 0;

      .paginate_button {
        background-color: #ffffff;
        border: 1px solid $gray-300;
        color: $gray-700;
        padding: 5px 10px;
        margin: 0 2px;
        border-radius: $border-radius;
        cursor: pointer;

        &.current {
          background-color: $primary;
          border: 1px solid $primary;
          color: #ffffff;
        }

        &:hover {
          background-color: $secondary;
          border: 1px solid $primary;
          color: $primary;
        }
      }
    }

    .dataTables_filter {
      float: right;
      margin-bottom: 10px;
    }

    .dataTables_length {
      float: left;
      margin-bottom: 10px;
    }
  }
}

/* Styl pro řazení ikon */
table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:after {
  font-family: 'Bootstrap Icons';
  padding-left: 10px;
  opacity: 0.5;
}

table.dataTable thead .sorting:after {
  content: "\f15d"; /* Icon for sorting */
}

table.dataTable thead .sorting_asc:after {
  content: "\f15e"; /* Icon for ascending sort */
}

table.dataTable thead .sorting_desc:after {
  content: "\f15f"; /* Icon for descending sort */
}

thead tr th{
  font-size: 0.75rem;
  color: $gray-600;
}

.glass-efect {
  background-color: rgba(255, 255, 255, 0.6); /* Poloprůhledné bílé pozadí */
  backdrop-filter: blur(10px); /* Efekt rozmazání skla */
  -webkit-backdrop-filter: blur(10px); /* Efekt rozmazání pro Safari */
}

.position-20-10{
  position: absolute;
  bottom: 20%;
  left: 10%;
}

.position-55-10{
  position: absolute;
  bottom: 60%;
  right: 10%;
}

@media only screen and (max-width: 992px) {
  .position-20-10{
    bottom: 20%;
    left: 5%;
  }
  .position-55-10{
    bottom: 20%;
    right: 5%;
  }
}


/* Kontejner pro "tabulku" s horizontálním scrollováním na menších zařízeních */
.grid-table {
  display: grid;
  grid-auto-rows: auto;
  width: 100%;
  overflow-x: auto;
}

/* Hlavička – pevně definované sloupce */
.grid-header {
  display: grid;
  border-bottom: 1px solid #dee2e6;
  font-weight: bold;
  padding: 8px 12px;

  &.app{
    grid-template-columns: 40px 60px auto 180px 100px 180px 70px 120px;
  }

  &.portal{
    grid-template-columns: 60px auto 100px 200px 250px 70px;

    @media (min-width: 1000px) and (max-width: 1200px) {
      grid-template-columns: 60px 200px 100px 200px 250px 70px;
    }
  }
}

/* Řádky – stejná definice sloupců jako v hlavičce */
.grid-row {
  display: grid;
  padding: 8px 12px;
  border-bottom: 1px solid #dee2e6;
  align-items: center;

  &.app {
    grid-template-columns: 40px 60px auto 180px 100px 180px 70px 120px;
  }

  &.portal {
    grid-template-columns: 60px auto 100px 200px 250px 70px;

    @media (min-width: 1000px) and (max-width: 1200px) {
      grid-template-columns: 60px 200px 100px 200px 250px 70px;
    }
  }
}

/* Hlavička – položky modal */
.grid-header-polozky-modal {
  display: grid;
  grid-template-columns: 350px 160px 200px 160px 100px;
  border-bottom: 1px solid #dee2e6;
  font-weight: bold;
  padding: 8px 12px;
  column-gap: 10px;
}

/* Řádky – stejná definice sloupců jako v hlavičce - položky modal */
.grid-row-polozky-modal {
  display: grid;
  grid-template-columns: 350px 160px 200px 160px 100px;
  padding: 8px 12px;
  border-bottom: 1px solid #dee2e6;
  align-items: center;
  column-gap: 10px;
}

/* Společný styl pro buňky */
.grid-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Specifické styly pro obrázek */
.cell-image img {
  height: 50px;
  width: 50px;
  object-fit: cover;
  border-radius: 4px;
}

/* Přizpůsobení textu v buňkách, pokud je třeba */
.cell-name p {
  margin-bottom: 4px;
}

/* Zajištění, že na menších obrazovkách se objeví horizontální scroll */
@media (max-width: 768px) {
  .grid-table {
    /* Udržuje strukturu, ale umožňuje posouvání */
    grid-template-columns: minmax(1120px, 100%);
  }
}

/* Collapse element přes celý grid řádek */
.collapse-full {
  grid-column: 1 / -1;
}

/*Nové styly 2025*/
.label-card{
  margin-bottom: 0.2rem;
  padding: .2rem .3rem;
  text-transform: uppercase;
  font-size: 0.65rem!important;
  background-color: $secondary;
  color: $white!important;

  &.lb-center{
    border-radius: 0 0 8px 8px;
  }

  &.lb-left{
    border-radius: 8px 0 8px 0;
  }

  &.lb-right{
    border-radius: 0 8px 0 8px;
  }
}

.img-lb-left{
  border-radius: 8px 0 8px 0;
}

.img-lb-right{
  border-radius: 0 8px 0 8px;
}

.img-lb-top{
  border-radius: 8px 8px 0 0;
}

.gallery-item {
  position: relative;
  overflow: hidden;
}

.gallery-item img {
  width: 100%;
  max-height: 430px;
  object-fit: contain;
  object-position: center;
  display: block;
}

//Booking portal

.suggestion-item {
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  display: block;
  transition: background-color 0.2s ease-in-out, transform 0.2s ease-in-out;
}
.suggestion-item:hover {
  background-color: #f8f9fa;
  transform: translateX(3px);
}
/* Upravený vzhled nápovědné karty */
.suggestion-card {
  border: none;
  border-radius: 0.5rem;
  z-index: 2147483647; // nad vším
}
.suggestion-card h6 {
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
  margin-bottom: 0.5rem;
}

@media (min-width: 1024px) {
  .homeHeading {
    height: 184px!important;
  }
}

.homeHeadingRight {
  background-image: url('https://qvamp.cz/wp-content/uploads/2025/01/situace-event-qvamp.webp');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}
@media (max-width: 995px){
  .btnHledat{
    width: 100%;
  }
}

//Blog
.img-nahled-blog-hlavni{
  border-radius: $border-radius $border-radius 0 0;
  height: 350px;
  object-fit: cover;
}
.img-nahled-blog-hlavni-sec{
  border-radius: $border-radius $border-radius 0 0;
  height: 160px!important;
  object-fit: cover;
}

.img-nahled-blog{
  border-radius: $border-radius $border-radius 0 0;
  height: 200px!important;
  object-fit: cover;
}

// Průvodce nastavením portálu
/* Styl pro svislou navigaci kroků */
.nav-steps .nav-link {
  display: flex;
  align-items: flex-start; /* Zarovná text v horní části vedle kruhu */
  gap: 0.75rem; /* Mezery mezi kruhem a textem */
  text-align: left;
  position: relative;
  border-radius: 0.5rem;
  padding: 1rem;
}
/* Kruh se zobrazením čísla kroku */
.step-indicator {
  flex-shrink: 0;
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background-color: $gray-200;  /* Výchozí šedá barva */
  color: $secondary;            /* Tmavší šedá barva pro text */
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-top: 2px;           /* Opticky vycentrované vzhledem k textu */
}
/* Aktivní krok – kruh se přebarví na primární barvu */
.nav-link.active .step-indicator {
  background-color: $primary;
  color: $white;
}

.nav-link-outline {
  border-radius: $border-radius;
  background-color: transparent;
  color: $secondary!important;
  padding: 1rem;
  transition: background-color 0.3s, color 0.3s, border-color 0.3s;
}
.nav-link-outline:hover,
.nav-link-outline.active {
  border: 1px solid $primary!important;
  background-color: $primary-light!important;
  color: $secondary!important;
}
/* Titulek a krátký popis */
.step-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}
.step-subtitle {
  font-size: 0.675rem;
  color: $gray-600!important;
  margin: 0;
}
/* Doplňkový údaj (např. čas) zarovnaný doprava */
.step-time {
  margin-left: auto;
  font-size: 0.75rem;
  color: $gray-500;
  align-self: center;
}
/* (Volitelné) Svislá linka mezi kroky */
.nav-steps .nav-link:not(:last-child)::after {
  content: "";
  position: absolute;
  left: 17px;  /* Zarovnání s kruhem (.step-indicator) */
  top: 100%;
  width: 2px;
  height: 16px; /* Nastavte dle potřeby */
  background-color: $gray-100;
  z-index: -1;
}


//PORTÁL
.accordionProfil .accordion-button:not(.collapsed) {
  background-color: transparent;
  color: #272626;
}

// Border s gradientem
.border-gradient {
  border: 3px solid transparent;
  border-image: $qvamp-gradient 1;
}

.bg-qvamp-gradient{
  background: $qvamp-gradient!important;
}

.bg-qvamp-gradient-light{
  background: $qvamp-gradient-light!important;
}

// Tlačítko s gradientem
.btn-gradient {
  background: $qvamp-gradient-light;
  color: $white!important;
  border: $tertiary;

  &:hover {
    opacity: 0.9;
  }
}

// Shadow s gradientem (přes pseudo-element)
.shadow-gradient {
  position: relative;
  background: #fff!important;
  border-radius: 16px;

  &::before {
    content: "";
    position: absolute;
    inset: -8px;                   // přesah okolo
    border-radius: inherit;
    /* 1. vrstva = bílé jádro, 2. vrstva = gradientový okraj */
    background:
            linear-gradient(#fff, #fff) padding-box,
            $qvamp-gradient border-box;
    border: 8px solid transparent; // „šířka prstence“
    filter: blur(18px);            // měkký vzhled
    opacity: .6;
    z-index: 0;
  }

  > * { position: relative; z-index: 1; }
}

@media (max-width: 991px) {
  .shadow-gradient::before {
    content: none !important;
  }
}

.number-wrapper {
  position: relative;
  display: inline-block;
}
.highlight-circle {
  position: absolute;
  top: 0;
  left: 0;
  width: 150%;
  height: 150%;
}


/*Blog*/
.clanek .h2{
  margin-top: 1rem;
}

/*Portal*/
#whatInput::placeholder, #whereInput::placeholder {
  color: $gray-500;
}

.service-card {
  height: 300px;

  @media (min-width: 991px) {
    height: 100px;
  }
}
