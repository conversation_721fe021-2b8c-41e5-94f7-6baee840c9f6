export class ApiClient
{

   private static csrfToken: string | null = null;
   private static csrfPromise: Promise<void> | null = null;

   private static getCookie(name: string): string | null {
      const match = document.cookie.match('(^|;)\\s*' + name + '\\s*=\\s*([^;]+)');
      return match ? decodeURIComponent(match[2]) : null;
   }

   /**
    * Ensures CSRF token is loaded (from cookie or via endpoint) exactly once.
    */
   private static ensureCsrfToken(): Promise<void> {
      if (ApiClient.csrfToken) {
         return Promise.resolve();
      }
      if (ApiClient.csrfPromise) {
         return ApiClient.csrfPromise;
      }

      // Try cookie first
      const cookieToken = ApiClient.getCookie('XSRF-TOKEN');
      if (cookieToken) {
         ApiClient.csrfToken = cookieToken;
         return Promise.resolve();
      }

      // Fetch from server
      ApiClient.csrfPromise = fetch(`/~csrf-token-generator`, {
         credentials: 'include',
         headers: { 'Accept': 'application/json' }
      })
         .then(res => {
            if (!res.ok) throw new Error('Failed to load CSRF token');
            return res.json();
         })
         .then(data => {
            ApiClient.csrfToken = data.csrf;
         })
         .catch(err => {
            ApiClient.csrfPromise = null;
            throw err;
         });

      return ApiClient.csrfPromise;
   }

   /**
    * Unified static fetch method.
    * - GET if no body
    * - POST if body provided
    * - Accepts query params via `params` option
    * - Automatically injects CSRF token
    */
   public static async fetch<T = any>(
      path: string,
      options?: {
         body?: any;
         params?: Record<string, string | number>;
         headers?: Record<string, string>;
         method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
      }
   ): Promise<T> {
      // 1) ensure CSRF token is ready
      await ApiClient.ensureCsrfToken();

      // 2) build URL + query string
      let url = path;
      if (options?.params) {
         const qs = new URLSearchParams();
         for (const [key, value] of Object.entries(options.params)) {
            qs.append(key, String(value));
         }
         url += `?${qs}`;
      }

      // 3) assemble headers
      const headers = new Headers(options?.headers);
      headers.set('X-CSRF-TOKEN', ApiClient.csrfToken!);

      // 4) decide method & body
      const init: RequestInit = {
         credentials: 'include',
         headers,
         method: options?.method
            ?? (options?.body != null ? 'POST' : 'GET')
      };

      if (options?.body != null) {
         if (options.body instanceof FormData){
            init.body = options.body;
         } else {
            headers.set('Content-Type', 'application/json');
            init.body = JSON.stringify(options.body);
         }
      }

      const res = await fetch(url, init);

      if (res.status === 403) {
         ApiClient.csrfToken = null;
         ApiClient.csrfPromise = null;
         return ApiClient.fetch(path, options);
      }

      if (!res.ok) {
         const errorText = await res.text();
         throw new Error(`API Error ${res.status}: ${errorText}`);
      }

      // 7) parse response
      const contentType = res.headers.get('Content-Type') || '';
      if (contentType.includes('application/json')) {

         return res.json().then(response => {
            const messages = response['messages'];
            delete response['messages'];

            if(messages === undefined || messages.length === 0)
               return response;

            messages.forEach(function(message: FlashMessage, index: number) {
               toastr[message['type']](message['message'], message['title'], JSON.parse(message['settingJson']));
            });

            return response;
         });
      }
      // @ts-ignore
      return res.text();
   }
}

type FlashMessage = {
   type: 'success'|'error'|'warning'|'info',
   message: string,
   title: string,
   settingJson: string
}