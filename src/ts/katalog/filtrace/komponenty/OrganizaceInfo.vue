<template>
  <a :href="organizace.portal_odkaz" target="_blank" class="card border border-light-gray radius-card shaddow-hover px-0 mb-3">
    <div class="row">
      <div class="col-md-3 col-12 d-flex">
        <div class="w-100" >
          <img
              :src="`/${organizace.profile_photo}`"
              class="w-100 img-fit radius-card-catalog"
              :alt="organizace.nazev || 'foto organizace'"
          />
        </div>
      </div>
      <div class="col-12 col-md-9">
        <div class="row">
          <div class="col-md-8 p-3">
            <div class="row">
              <div class="text-decoration-none">
                <h3 class="text-primary d-block text-truncate">{{ organizace.nazev }}</h3>
              </div>
            </div>
            <p class="text-black-50">
              <i class="bi bi-geo me-1 text-primary"></i>
              {{ organizace.mesto }}, {{ organizace.stat }}
            </p>

            <div class="row mt-4">
              <VenueInfo
                  v-if="isVenueType"
                  :data="{
                   celkovaKapacita: venueData.celkovaKapacita,
                   celkovaPlocha: venueData.celkovaPlocha,
                   nejvetsiMistnost: venueData.nejvetsiMistnost
              }"
              />
              <div v-else class="service-info">
                <p class="text-muted">
                  <i class="bi bi-gear me-1"></i>
                  Poskytovatel služeb
                </p>
              </div>
            </div>
          </div>

          <div class="align-items-start col-md d-flex justify-content-around mt-3 mb-3">
            <div class="btn-group">
              <div class="rounded-start border border-primary align-content-center"
                   data-bs-toggle="tooltip"
                   data-bs-placement="top"
                   data-bs-custom-class="custom-tooltip"
                   :data-bs-title="hodnoceniTooltip">
                <span class="lead text-primary mx-2">
                  <i class="bi bi-star me-1"></i>{{ hodnoceni }}
                </span>
              </div>
              <button
                  class="btn btn-outline-primary"
                  data-bs-toggle="tooltip"
                  data-bs-placement="top"
                  data-bs-custom-class="custom-tooltip"
                  title="Zadat poptávku"
                  @click="handlePoptavkaRedirect">
                <i class="bi bi-calendar4-range"></i>
              </button>
              <button
                  :class="favouriteButtonClass"
                  :data-katalog-favourite-place="organizace.id"
                  data-bs-toggle="tooltip"
                  data-bs-placement="top"
                  data-bs-custom-class="custom-tooltip"
                  :title="favouriteTooltipTitle"
                  @click="handleFavouriteClick">
                <i class="bi bi-heart me-1"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a>
</template>

<script setup lang="ts">
import {computed, ref, onMounted, onUnmounted, nextTick, watch} from 'vue';
import VenueInfo from "./VenueInfo.vue";
import {OrganizaceItem} from "../store/PlacesLoadStore";
import { useI18n } from 'vue-i18n';
import { Tooltip } from 'bootstrap';
const { t } = useI18n();

const props = defineProps<{
  organizace: OrganizaceItem;
}>();

const isFavourite = ref<boolean>(props.organizace.isFavourite || false);
const favouriteButtonRef = ref<HTMLButtonElement | null>(null);

const isVenueType = computed(() :boolean => {
  return 'celkovaKapacita' in props.organizace;
});

const venueData = computed(() :OrganizaceItem => {
  return props.organizace as OrganizaceItem;
});

const hodnoceni = computed(() :number => {
  return props.organizace.hodnoceni;
});

const hodnoceniTooltip = computed(() => {
  const rating = props.organizace.hodnoceni;
  return rating
      ? t('message.Hodnoceni_tooltip_s_hodnotou', { rating })
      : t('message.Hodnoceni_tooltip_bez_hodnoty');
});

const favouriteButtonClass = computed(() => {
  return isFavourite.value
      ? 'btn-primary btn w-auto shaddow-hover'
      : 'btn-outline-primary btn w-auto shaddow-hover';
});

const favouriteTooltipTitle = computed(() => {
  return isFavourite.value
      ? t('message.Odebrat_z_oblibenych', 'Odebrat z oblíbených')
      : t('message.Pridat_do_oblibenych', 'Přidat do oblíbených');
});

const handlePoptavkaRedirect = () => {
  if (props.organizace.poptavka_odkaz) {
    window.open(props.organizace.poptavka_odkaz, '_blank');
  }
};

const handleFavouriteClick = (event: Event) => {
  isFavourite.value = !isFavourite.value;
};

const updateTooltip = () => {
  const button = document.querySelector(`[data-katalog-favourite-place="${props.organizace.id}"]`);
  if (button) {
    const existingTooltip = Tooltip.getInstance(button as Element);
    if (existingTooltip) {
      existingTooltip.dispose();
    }

    button.setAttribute('title', favouriteTooltipTitle.value);

    new Tooltip(button as Element, {
      placement: 'top',
      customClass: 'custom-tooltip'
    });
  }
};

watch(isFavourite, async () => {
  await nextTick();
  updateTooltip();
});

// Změnu ukládá listener v modulu FavouritePlacesModule
const handleFavouriteUpdated = (event: CustomEvent) => {
  const { organizaceId, isFavourite: newFavouriteStatus } = event.detail;

  if (organizaceId === props.organizace.id) {
    isFavourite.value = newFavouriteStatus;
  }
};

onMounted(() => {
  document.addEventListener('favouriteUpdated', handleFavouriteUpdated as EventListener);
});

onUnmounted(() => {
  document.removeEventListener('favouriteUpdated', handleFavouriteUpdated as EventListener);
});
</script>

<style scoped>
.radius-card {
  border-radius: 0.375rem;
}

.radius-card-catalog {
  border-radius: 0.5rem;
}

.shaddow-hover:hover {
  box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 0.15);
  transition: box-shadow 0.3s ease;
}

.img-fit {
  object-fit: cover;
  height: 100%;
}

.service-info {
  padding: 1rem 0;
}

.service-info p {
  margin: 0;
  font-size: 0.9rem;
}
</style>