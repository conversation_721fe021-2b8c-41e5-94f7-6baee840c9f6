import {BaseModule} from "system/modules/BaseModule";
import {ApiClient} from "system/api/ApiClient";

export default class GoogleOauthModule
   extends BaseModule
{

   onReady() {
      const buttons = document.body.querySelectorAll('button[data-google-login]');

      if (buttons.length === 0)
         return;

      buttons.forEach((btn) => {
         new GoogleAuthButton(btn as HTMLButtonElement);
      });
   }

}

class GoogleAuthButton
{

   private button;

   constructor(button :HTMLButtonElement) {
      this.button = button;

      this.button.addEventListener('click', this.onClickAction.bind(this));
   }

   async onClickAction(event: Event) {
      this.button.disabled = true;

      event.preventDefault();
      event.stopPropagation();
      const response = await ApiClient.fetch<{ urlLink: string }>('/~oauth-links/google');

      location.href = response.urlLink;
   }
}