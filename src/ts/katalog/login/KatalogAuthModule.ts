import {BaseModule} from "system/modules/BaseModule";
import {ApiClient} from "system/api/ApiClient";

export class KatalogAuthModule extends BaseModule {
   private checkEmailUrl: string = '/auth-email';
   private submitPostUrl: string = '/auth-submit';

   private formContainer!: HTMLDivElement;
   private recaptchaKey!: string;

   onReady() {
      const formContainer = document.querySelector<HTMLDivElement>('div[data-katalog-auth]');
      if (!formContainer) return;

      this.formContainer = formContainer;
      this.recaptchaKey = this.formContainer.dataset.katalogAuth ?? '6LdrnucoAAAAAJD7mt5K6G8hhO3c66hwgz1hPQgb';

      const emailForm = formContainer.querySelector<HTMLFormElement>('form#checkEmailAuthForm');
      if (!emailForm) {
         console.error('Check email form not found');
         return;
      }

      emailForm.addEventListener('submit', async (e: SubmitEvent) => {
         e.preventDefault();

         const submitter = (e.submitter instanceof HTMLButtonElement) ? e.submitter : null;
         if (submitter) submitter.disabled = true;

         const formData = new FormData(emailForm);

         grecaptcha.enterprise.ready(async () => {
            try {
               const recaptchaResponse = await grecaptcha.enterprise.execute(this.recaptchaKey, {action: 'EMAIL_CHECK'});
               formData.set('token', recaptchaResponse);

               const resp = await ApiClient.fetch<{
                  success: boolean,
                  nextForm: string | undefined,
               }>(this.checkEmailUrl, {
                  method: 'POST',
                  body: formData
               });

               if (submitter) submitter.disabled = false;

               if (!resp.success || !resp.nextForm) return;

               emailForm.remove();
               await this.handleNextForm(resp.nextForm);
            } catch (err) {
               if (submitter) submitter.disabled = false;
               console.error(err);
            }
         });
      });
   }

   /**
    * Vykreslí HTML ze serveru do this.formContainer a spustí všechny <script> v pořadí.
    * Vrátí první <form> uvnitř kontejneru, aby se na něj dal navěsit submit handler.
    */
   private async renderServerForm(html: string): Promise<HTMLFormElement | null> {
      const doc = new DOMParser().parseFromString(html, 'text/html');

      // 1) Oddělit skripty a připravit fragment s markupem
      const sourceScripts = Array.from(doc.querySelectorAll('script'));
      sourceScripts.forEach(s => s.parentElement?.removeChild(s));

      const fragment = document.createDocumentFragment();
      for (const node of Array.from(doc.body.childNodes)) {
         fragment.appendChild(document.importNode(node, true));
      }

      // 2) Mount (nahradíme obsah kontejneru)
      this.formContainer.replaceChildren(fragment);

      // 3) Spustit skripty (jQuery validate apod.)
      await this.runScriptsSequentially(sourceScripts);

      // 4) Vrátit první <form> v kontejneru
      return this.formContainer.querySelector('form');
   }

   private async handleNextForm(nextFormHtml: string): Promise<void> {
      const form = await this.renderServerForm(nextFormHtml);
      if (!form) {
         console.warn('No <form> found in nextForm payload.');
         return;
      }
      this.bindSubmit(form);
   }

   private bindSubmit(form: HTMLFormElement): void {
      form.addEventListener('submit', (e: SubmitEvent) => {
         if (e.defaultPrevented) return;

         const targetForm = e.target as HTMLFormElement;

         // Pokud je k dispozici jQuery Validation, validuj přes ni
         const $ = (window as any).jQuery as JQueryStatic | undefined;
         if ($) {
            const $form = $(targetForm);
            const hasValidator = !!$form.data('validator');
            //@ts-ignore
            if (hasValidator && !$form.valid()) {
               // jQuery Validation ukáže chyby, my končíme
               return;
            }
         } else {
            // fallback na HTML5 constraint validation, když jQuery Validation není
            if (!targetForm.checkValidity()) {
               targetForm.reportValidity?.();
               e.preventDefault();
               return;
            }
         }

         e.preventDefault();

         const submitter = e.submitter as HTMLButtonElement | null;

         const formData = new FormData(targetForm);
         if (submitter && submitter.name) {
            formData.set(submitter.name, '');
            submitter.disabled = true;
         }

         grecaptcha.enterprise.ready(async () => {
            try {
               const recaptchaResponse = await grecaptcha.enterprise.execute(this.recaptchaKey, {action: 'AUTH_SUBMIT'});
               formData.set('token', recaptchaResponse);

               const resp = await ApiClient.fetch<{
                  success: boolean,
                  nextForm: string | undefined,
                  redirectUrl: string | undefined,
               }>(this.submitPostUrl, {
                  method: 'POST',
                  body: formData
               });

               if (submitter) submitter.disabled = false;

               if (!resp.success) return;

               if (resp.redirectUrl) {
                  window.location.replace(resp.redirectUrl);
                  return;
               }

               if (resp.nextForm) {
                  // čistě vyčistit a vykreslit další krok
                  await this.handleNextForm(resp.nextForm);
               }
            } catch (err) {
               if (submitter) submitter.disabled = false;
               console.error(err);
            }
         });
      });
   }

   /**
    * Načte a spustí skripty sekvenčně ve stejném pořadí, v jakém přišly.
    * - respektuje CSP nonce (přebere z první <script[nonce]> na stránce)
    * - deduplikuje externí skripty podle URL
    * - podporuje type="module" (čeká na onload)
    */
   private async runScriptsSequentially(sourceScripts: HTMLScriptElement[]): Promise<void> {
      const pageNonce = document.querySelector('script[nonce]')?.getAttribute('nonce') ?? '';

      const loadedSrc = new Set<string>(
         Array.from(document.scripts)
            .map(s => s.src)
            .filter(Boolean)
      );

      for (const source of sourceScripts) {
         const s = document.createElement('script');

         // Přenést relevantní atributy
         if (source.type) s.type = source.type;
         if (source.getAttribute('nomodule') !== null) s.setAttribute('nomodule', '');
         if (source.crossOrigin) s.crossOrigin = source.crossOrigin;
         if (source.referrerPolicy) s.referrerPolicy = source.referrerPolicy;
         if (source.integrity) s.integrity = source.integrity;
         if (pageNonce) s.setAttribute('nonce', pageNonce);

         if (source.src) {
            if (loadedSrc.has(source.src)) continue; // už je načtený
            s.src = source.src;

            // Důležité: dynamické skripty jsou defaultně async; držíme pořadí ručním awaitem
            (s as any).async = false;

            await new Promise<void>((resolve, reject) => {
               s.onload = () => {
                  s.remove();
                  resolve();
               };
               s.onerror = () => {
                  s.remove();
                  reject(new Error(`Failed to load ${source.src}`));
               };
               document.head.appendChild(s);
            });
            loadedSrc.add(source.src);
         } else {
            // Inline skript – vykoná se okamžitě při připojení
            s.text = source.textContent ?? '';
            document.head.appendChild(s);
            s.remove();
         }
      }
   }
}