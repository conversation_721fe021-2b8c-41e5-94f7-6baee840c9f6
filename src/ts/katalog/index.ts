import {handleError} from "system/error/AppError";
import Dime from "system/dime/Dime";
import SystemModulesDispatcher from "system/modules/SystemModulesDispatcher";
import LanguageSelectModule from "system/modules/types/LanguageSelectModule";
import SplashLoader from "system/modules/types/SplashLoader";
import TelefonInputModule from "system/modules/types/TelefonInputModule";
import GoogleOauthModule from "katalog/login/GoogleOauthModule";
import FavouriteModule from "katalog/oblibene/FavouritePlacesModule";
import Searchbar from "./filtrace/Searchbar";
import {KatalogAuthModule} from "katalog/login/KatalogAuthModule";

try {
   Dime.register();
   const dispatcher = new SystemModulesDispatcher();

   dispatcher
      .registerModule(new SplashLoader())
      .registerModule(new LanguageSelectModule())
      .registerModule(new TelefonInputModule())
      .registerModule(new LanguageSelectModule())
      .registerModule(new GoogleOauthModule())
      .registerModule(new FavouriteModule())
      .registerModule(new Searchbar())
      .registerModule(new KatalogAuthModule());

   dispatcher.dispatch();
} catch (e: unknown) {
   throw handleError(e);
}