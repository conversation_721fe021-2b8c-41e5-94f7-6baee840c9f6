import {BaseModule} from "system/modules/BaseModule";
import {ApiClient} from "system/api/ApiClient";
import {Modal} from "bootstrap";

type ApiResponse = {
   success: boolean;
   showModal: false|string;
   status: 'add'|'remove';
};

export default class FavouritePlacesModule extends BaseModule {
   private readonly apiEndpoint: string = "/~katalog-oblibene";

   async prepare(): Promise<void> {
      document.body.addEventListener('click', async (event: MouseEvent) => {
         if (
            !(event.target instanceof HTMLElement)
         )
            return;

         const button = event.target instanceof HTMLButtonElement
            ? event.target
            : event.target.closest<HTMLButtonElement>('button[data-katalog-favourite-place]');

         if (
            !button
            || !button.dataset.katalogFavouritePlace
         )
            return;

         event.preventDefault();
         event.stopPropagation();
         button.disabled = true;

         const response = await ApiClient.fetch<ApiResponse>(this.apiEndpoint, {
            body: {
               organizaceID: Number(button.dataset.katalogFavouritePlace),
            }
         });

         button.disabled = false;

         if (!response.success)
            return;

         if (response.showModal !== false){
            const modalElement = document.body.querySelector(`div#${response.showModal}`);

            if (!modalElement)
               return;

            Modal.getOrCreateInstance(modalElement).show();
            return;
         }

         if (response.status === 'add')
            button.classList.replace('btn-outline-primary', 'btn-primary');
         else
            button.classList.replace('btn-primary', 'btn-outline-primary');


         document.dispatchEvent(new CustomEvent('favouriteUpdated', {
            detail: {
               organizaceId: Number(button.dataset.katalogFavouritePlace),
               isFavourite: response.status === 'add'
            }
         }));
      });
   }
}