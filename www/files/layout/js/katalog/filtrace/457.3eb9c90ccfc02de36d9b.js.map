{"version": 3, "file": "457.3eb9c90ccfc02de36d9b.js", "mappings": "yGA2CA,UA3CW,CACPA,QAAS,CACLC,MAAO,gBACPC,eAAgB,oBAChBC,UAAW,YACXC,UAAW,YACXC,WAAY,WACZC,SAAU,UACVC,eAAgB,+DAChBC,UAAW,QACXC,QAAS,UACTC,mBAAoB,6BACpBC,mBAAoB,4BACpBC,sBAAuB,4BACvBC,kCAAmC,+CACnCC,WAAY,cACZC,SAAU,SACVC,YAAa,gBACbC,WAAY,eACZC,WAAY,aACZC,UAAW,gBACXC,OAAQ,SACRC,aAAc,iBACdC,uBAAwB,iCACxBC,yBAA0B,oCAC1BC,8BAA+B,oCAC/BC,mBAAoB,0BACpBC,WAAY,YACZC,6BAA8B,oBAC9BC,8BAA+B,uBAC/BC,MAAO,OACPC,eAAgB,qBAChBC,iBAAkB,sBAClBC,eAAgB,yBAChBC,2BAA4B,0CAC5BC,yBAA0B,mCAC1BC,QAAS,WACTC,qBAAsB,sBACtBC,qBAAsB,oBACtBC,eAAgB,kB", "sources": ["webpack://qvamp/./src/i18n/locales/pl.ts"], "sourcesContent": ["const pl = {\n    message: {\n        hello: '<PERSON><PERSON><PERSON> świecie',\n        <PERSON><PERSON>_kapacity: 'Wed<PERSON>ug pojemności',\n        Od_A_do_Z: 'Od A do Z',\n        Od_Z_do_A: 'Od Z do A',\n        <PERSON><PERSON><PERSON><PERSON>: '<PERSON><PERSON><PERSON><PERSON>',\n        <PERSON><PERSON>ry: '<PERSON><PERSON><PERSON>',\n        <PERSON><PERSON><PERSON><PERSON>_count: '<PERSON><PERSON> wyników | Znaleziono {n} wynik | Znaleziono {n} wyników',\n        <PERSON><PERSON><PERSON><PERSON>: 'O<PERSON><PERSON>',\n        <PERSON><PERSON><PERSON><PERSON>: 'W<PERSON>brane',\n        Plocha_prostoru_m2: 'Powierzchnia miejsca (m²):',\n        Pocet_prostor_salu: 'Liczba pomieszczeń (sal):',\n        <PERSON><PERSON><PERSON><PERSON>_prostor_osob: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> miejsca (osób):',\n        <PERSON><PERSON><PERSON><PERSON>_nejvetsiho_prostoru_osob: 'Pojemność największego pomieszczenia (osób):',\n        <PERSON><PERSON><PERSON><PERSON>: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        Vyhledat: '<PERSON><PERSON><PERSON>',\n        <PERSON><PERSON>_podle: 'Sortuj według',\n        V<PERSON><PERSON>_tagy: '<PERSON><PERSON><PERSON><PERSON> tagi',\n        <PERSON><PERSON>_luzeb: 'Typy usług',\n        <PERSON><PERSON>_<PERSON><PERSON><PERSON>: 'Ty<PERSON> wydarzeń',\n        Sluzby: 'Usługi',\n        Nacist_dalsi: 'Załaduj więcej',\n        Nacitam_dalsi_vysledky: 'Ładowanie kolejnych wyników...',\n        Zobrazeno_n_z_n_vysledku: 'Wyświetlono {n} z {total} wyników',\n        Chyba_pri_nacitani_formuulare: 'Błąd podczas ładowania formularza',\n        Nacitani_formulare: 'Ładowanie formularza...',\n        Vyhledavam: 'Wyszukuję',\n        Hodnoceni_tooltip_s_hodnotou: 'Ocena: {rating}/5',\n        Hodnoceni_tooltip_bez_hodnoty: 'Jeszcze nie oceniono',\n        Chyba: 'Błąd',\n        Nic_nenalezeno: 'Nic nie znaleziono',\n        Celkova_kapacita: 'Całkowita pojemność',\n        Celkova_plocha: 'Całkowita powierzchnia',\n        Plocha_nejvetsiho_prostoru: 'Powierzchnia największego pomieszczenia',\n        Tento_udaj_nam_neni_znam: 'Ta informacja nie jest nam znana',\n        Nezname: 'Nieznane',\n        Pridat_do_oblibenych: 'Dodaj do ulubionych',\n        Odebrat_z_oblibenych: 'Usuń z ulubionych',\n        Zadat_poptavku: 'Złóż zapytanie',\n    }\n};\n\nexport default pl"], "names": ["message", "hello", "Podle_kapacity", "Od_<PERSON>_<PERSON>_<PERSON>", "Od_<PERSON>_do_A", "<PERSON><PERSON><PERSON><PERSON>", "Prostory", "V<PERSON><PERSON><PERSON>_count", "<PERSON><PERSON><PERSON><PERSON>", "Z<PERSON>leno", "Plocha_prostoru_m2", "Pocet_prostor_salu", "<PERSON><PERSON><PERSON><PERSON>_prostor_osob", "<PERSON><PERSON><PERSON><PERSON>_ne<PERSON><PERSON><PERSON><PERSON>_prostoru_osob", "V<PERSON><PERSON>ti", "Vyhledat", "<PERSON><PERSON>_podle", "Vyber_tagy", "Typy_luzeb", "<PERSON><PERSON>_akci", "Sluzby", "Na<PERSON>_da<PERSON>i", "<PERSON><PERSON><PERSON>_dalsi_vys<PERSON>ky", "Zobrazeno_n_z_n_vysledku", "Chyba_pri_nacitani_formuulare", "Nacitani_formulare", "Vyhledavam", "Hodnoceni_tooltip_s_hodnotou", "Hodnoceni_tooltip_bez_hodnoty", "Chyba", "<PERSON><PERSON>_ne<PERSON><PERSON>o", "Ce<PERSON>ova_kapacita", "Celkova_plocha", "<PERSON><PERSON><PERSON>_nej<PERSON><PERSON><PERSON>_prostoru", "Tento_udaj_nam_neni_znam", "Nezname", "Pridat_do_oblibenych", "Ode<PERSON><PERSON>_<PERSON>_oblibenych", "Zadat_poptavku"], "sourceRoot": ""}