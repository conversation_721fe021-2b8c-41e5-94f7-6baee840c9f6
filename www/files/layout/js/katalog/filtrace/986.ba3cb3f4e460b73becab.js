"use strict";(self.webpackChunkqvamp=self.webpackChunkqvamp||[]).push([[986],{1986:function(e,a,t){t.r(a),a.default={message:{hello:"<PERSON><PERSON> világ",Podle_kapacity:"<PERSON><PERSON><PERSON><PERSON><PERSON> szerint",Od_A_do_Z:"A-tól Z-ig",Od_Z_do_A:"Z-től A-ig",Dodavatele:"Szolgáltatók",Pro<PERSON>ry:"<PERSON><PERSON><PERSON><PERSON><PERSON>",V<PERSON><PERSON>ky_count:"Ninc<PERSON> eredmé<PERSON> | {n} eredmény találva | {n} ered<PERSON>ny találva",Hodnoceni:"Értékelés",Zvoleno:"Kiválasztva",Plocha_prostoru_m2:"Helyszín területe (m²):",<PERSON>cet_prostor_salu:"Helyiségek száma (termek):",<PERSON><PERSON><PERSON>ta_prostor_osob:"<PERSON><PERSON><PERSON><PERSON> ka<PERSON> (fő):",<PERSON><PERSON><PERSON><PERSON>_nejvetsiho_prostoru_osob:"Legnagyobb helyiség kapacit<PERSON>a (fő):",Vlastnosti:"Tulajdonságok",Vyhledat:"Keresés",Radit_podle:"Rendezés",Vyber_tagy:"Címkék választása",Typy_luzeb:"Szolgáltatástípusok",Typy_akci:"Eseménytípusok",Sluzby:"Szolgáltatások",Nacist_dalsi:"Több betöltése",Nacitam_dalsi_vysledky:"További eredmények betöltése...",Zobrazeno_n_z_n_vysledku:"{n} / {total} eredmény megjelenítve",Chyba_pri_nacitani_formuulare:"Hiba az űrlap betöltésekor",Nacitani_formulare:"Űrlap betöltése...",Vyhledavam:"Keresek",Hodnoceni_tooltip_s_hodnotou:"Értékelés: {rating}/5",Hodnoceni_tooltip_bez_hodnoty:"Még nem értékelt",Chyba:"Hiba",Nic_nenalezeno:"Nincs találat",Celkova_kapacita:"Összkapacitás",Celkova_plocha:"Összterület",Plocha_nejvetsiho_prostoru:"Legnagyobb terem területe",Tento_udaj_nam_neni_znam:"Ez az adat nem áll rendelkezésünkre",Nezname:"Ismeretlen",Pridat_do_oblibenych:"Kedvencekhez adás",Odebrat_z_oblibenych:"Eltávolítás a kedvencek közül",Zadat_poptavku:"Árajánlat kérése"}}}}]);
//# sourceMappingURL=986.ba3cb3f4e460b73becab.js.map