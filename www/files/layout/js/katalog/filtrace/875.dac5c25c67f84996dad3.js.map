{"version": 3, "file": "875.dac5c25c67f84996dad3.js", "mappings": "yGA2CA,UA3CW,CACPA,QAAS,CACLC,MAAO,YACPC,eAAgB,iBAChBC,UAAW,YACXC,UAAW,YACXC,WAAY,cACZC,SAAU,YACVC,eAAgB,mEAChBC,UAAW,aACXC,QAAS,UACTC,mBAAoB,yBACpBC,mBAAoB,0BACpBC,sBAAuB,8BACvBC,kCAAmC,yCACnCC,WAAY,aACZC,SAAU,WACVC,YAAa,cACbC,WAAY,cACZC,WAAY,eACZC,UAAW,aACXC,OAAQ,SACRC,aAAc,iBACdC,uBAAwB,+BACxBC,yBAA0B,sCAC1BC,8BAA+B,+BAC/BC,mBAAoB,yBACpBC,WAAY,aACZC,6BAA8B,yBAC9BC,8BAA+B,qBAC/BC,MAAO,QACPC,eAAgB,gBAChBC,iBAAkB,mBAClBC,eAAgB,iBAChBC,2BAA4B,8BAC5BC,yBAA0B,8BAC1BC,QAAS,UACTC,qBAAsB,uBACtBC,qBAAsB,uBACtBC,eAAgB,e", "sources": ["webpack://qvamp/./src/i18n/locales/sk.ts"], "sourcesContent": ["const sk = {\n    message: {\n        hello: '<PERSON><PERSON><PERSON> svet',\n        <PERSON><PERSON>_kapacity: 'Podľa kapacity',\n        Od_A_do_Z: 'Od A po Z',\n        Od_Z_do_A: 'Od Z po A',\n        <PERSON><PERSON><PERSON><PERSON>: 'Do<PERSON><PERSON><PERSON><PERSON>',\n        <PERSON><PERSON>ry: '<PERSON><PERSON>',\n        <PERSON><PERSON><PERSON><PERSON>_count: '<PERSON><PERSON><PERSON><PERSON> výsle<PERSON>ky | Nájdené {n} výsledky | Nájdených {n} výsledkov',\n        <PERSON><PERSON><PERSON><PERSON>: 'Hodn<PERSON><PERSON>',\n        <PERSON><PERSON><PERSON><PERSON>: 'Z<PERSON><PERSON><PERSON>',\n        <PERSON><PERSON>ha_prostoru_m2: 'Plocha priestoru (m²):',\n        Po<PERSON>t_prostor_salu: '<PERSON><PERSON><PERSON> priestorov (sál):',\n        <PERSON><PERSON><PERSON><PERSON>_prostor_osob: '<PERSON><PERSON><PERSON><PERSON> priestorov (osôb):',\n        <PERSON><PERSON><PERSON><PERSON>_nejvetsiho_prostoru_osob: 'Kapacita najväčšieho priestoru (osôb):',\n        <PERSON><PERSON>nosti: 'Vlastnos<PERSON>',\n        <PERSON><PERSON><PERSON><PERSON><PERSON>: 'Vy<PERSON>ľadať',\n        <PERSON><PERSON>_podle: '<PERSON><PERSON><PERSON> pod<PERSON>a',\n        V<PERSON>ber_tagy: 'V<PERSON><PERSON><PERSON> tagy',\n        <PERSON><PERSON>_luze<PERSON>: '<PERSON><PERSON> služie<PERSON>',\n        <PERSON><PERSON>_<PERSON>k<PERSON>: 'Typy akci<PERSON>',\n        <PERSON><PERSON>zby: 'Služby',\n        Nacist_dalsi: 'Načítať ďalšie',\n        Nacitam_dalsi_vysledky: 'Načítavam ďalšie výsledky...',\n        Zobrazeno_n_z_n_vysledku: 'Zobrazených {n} z {total} výsledkov',\n        Chyba_pri_nacitani_formuulare: 'Chyba pri načítaní formulára',\n        Nacitani_formulare: 'Načítanie formulára...',\n        Vyhledavam: 'Vyhľadávam',\n        Hodnoceni_tooltip_s_hodnotou: 'Hodnotenie: {rating}/5',\n        Hodnoceni_tooltip_bez_hodnoty: 'Zatiaľ nehodnotené',\n        Chyba: 'Chyba',\n        Nic_nenalezeno: 'Nič nenájdené',\n        Celkova_kapacita: 'Celková kapacita',\n        Celkova_plocha: 'Celková plocha',\n        Plocha_nejvetsiho_prostoru: 'Plocha najväčšej miestnosti',\n        Tento_udaj_nam_neni_znam: 'Tento údaj nám nie je známy',\n        Nezname: 'Neznáme',\n        Pridat_do_oblibenych: 'Pridať do obľúbených',\n        Odebrat_z_oblibenych: 'Odobrať z obľúbených',\n        Zadat_poptavku: 'Zadať dopyt',\n    }\n};\n\nexport default sk"], "names": ["message", "hello", "Podle_kapacity", "Od_<PERSON>_<PERSON>_<PERSON>", "Od_<PERSON>_do_A", "<PERSON><PERSON><PERSON><PERSON>", "Prostory", "V<PERSON><PERSON><PERSON>_count", "<PERSON><PERSON><PERSON><PERSON>", "Z<PERSON>leno", "Plocha_prostoru_m2", "Pocet_prostor_salu", "<PERSON><PERSON><PERSON><PERSON>_prostor_osob", "<PERSON><PERSON><PERSON><PERSON>_ne<PERSON><PERSON><PERSON><PERSON>_prostoru_osob", "V<PERSON><PERSON>ti", "Vyhledat", "<PERSON><PERSON>_podle", "Vyber_tagy", "Typy_luzeb", "<PERSON><PERSON>_akci", "Sluzby", "Na<PERSON>_da<PERSON>i", "<PERSON><PERSON><PERSON>_dalsi_vys<PERSON>ky", "Zobrazeno_n_z_n_vysledku", "Chyba_pri_nacitani_formuulare", "Nacitani_formulare", "Vyhledavam", "Hodnoceni_tooltip_s_hodnotou", "Hodnoceni_tooltip_bez_hodnoty", "Chyba", "<PERSON><PERSON>_ne<PERSON><PERSON>o", "Ce<PERSON>ova_kapacita", "Celkova_plocha", "<PERSON><PERSON><PERSON>_nej<PERSON><PERSON><PERSON>_prostoru", "Tento_udaj_nam_neni_znam", "Nezname", "Pridat_do_oblibenych", "Ode<PERSON><PERSON>_<PERSON>_oblibenych", "Zadat_poptavku"], "sourceRoot": ""}