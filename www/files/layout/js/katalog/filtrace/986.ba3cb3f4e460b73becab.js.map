{"version": 3, "file": "986.ba3cb3f4e460b73becab.js", "mappings": "0GA2CA,UA3CW,CACPA,QAAS,CACLC,MAAO,cACPC,eAAgB,oBAChBC,UAAW,aACXC,UAAW,aACXC,WAAY,eACZC,SAAU,aACVC,eAAgB,+DAChBC,UAAW,YACXC,QAAS,cACTC,mBAAoB,0BACpBC,mBAAoB,6BACpBC,sBAAuB,4BACvBC,kCAAmC,uCACnCC,WAAY,gBACZC,SAAU,UACVC,YAAa,WACbC,WAAY,oBACZC,WAAY,sBACZC,UAAW,iBACXC,OAAQ,iBACRC,aAAc,iBACdC,uBAAwB,kCACxBC,yBAA0B,sCAC1BC,8BAA+B,6BAC/BC,mBAAoB,qBACpBC,WAAY,UACZC,6BAA8B,wBAC9BC,8BAA+B,mBAC/BC,MAAO,OACPC,eAAgB,gBAChBC,iBAAkB,gBAClBC,eAAgB,cAChBC,2BAA4B,4BAC5BC,yBAA0B,sCAC1BC,QAAS,aACTC,qBAAsB,oBACtBC,qBAAsB,gCACtBC,eAAgB,oB", "sources": ["webpack://qvamp/./src/i18n/locales/hu.ts"], "sourcesContent": ["const hu = {\n    message: {\n        hello: '<PERSON><PERSON> v<PERSON>',\n        <PERSON><PERSON>_kapacity: '<PERSON><PERSON><PERSON><PERSON><PERSON> szerint',\n        Od_A_do_Z: 'A-tól Z-ig',\n        Od_Z_do_A: 'Z-től A-ig',\n        <PERSON><PERSON><PERSON><PERSON>: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tó<PERSON>',\n        Prostory: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        <PERSON><PERSON><PERSON><PERSON>_count: '<PERSON><PERSON><PERSON> ered<PERSON> | {n} ered<PERSON>ny találva | {n} eredmény találva',\n        <PERSON><PERSON><PERSON><PERSON>: '<PERSON>rtékel<PERSON>',\n        <PERSON><PERSON><PERSON><PERSON>: 'Kiv<PERSON>lasztva',\n        Plocha_prostoru_m2: '<PERSON><PERSON><PERSON><PERSON> területe (m²):',\n        Pocet_prostor_salu: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sz<PERSON> (termek):',\n        <PERSON><PERSON><PERSON><PERSON>_prostor_osob: '<PERSON><PERSON><PERSON><PERSON> kapacit<PERSON>a (fő):',\n        <PERSON><PERSON><PERSON><PERSON>_nejvetsiho_prostoru_osob: 'Legnagyobb helyiség kapacit<PERSON>a (fő):',\n        <PERSON><PERSON><PERSON>ti: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        <PERSON><PERSON><PERSON><PERSON><PERSON>: '<PERSON><PERSON><PERSON>',\n        <PERSON><PERSON>_pod<PERSON>: '<PERSON><PERSON><PERSON>',\n        <PERSON><PERSON>ber_tagy: 'Címkék választása',\n        <PERSON><PERSON>_luzeb: '<PERSON><PERSON>lgáltatástípusok',\n        Typy_akci: 'Eseménytípusok',\n        Sluzby: 'Szolgáltatások',\n        Nacist_dalsi: 'Több betöltése',\n        Nacitam_dalsi_vysledky: 'További eredmények betöltése...',\n        Zobrazeno_n_z_n_vysledku: '{n} / {total} eredmény megjelenítve',\n        Chyba_pri_nacitani_formuulare: 'Hiba az űrlap betöltésekor',\n        Nacitani_formulare: 'Űrlap betöltése...',\n        Vyhledavam: 'Keresek',\n        Hodnoceni_tooltip_s_hodnotou: 'Értékelés: {rating}/5',\n        Hodnoceni_tooltip_bez_hodnoty: 'Még nem értékelt',\n        Chyba: 'Hiba',\n        Nic_nenalezeno: 'Nincs találat',\n        Celkova_kapacita: 'Összkapacitás',\n        Celkova_plocha: 'Összterület',\n        Plocha_nejvetsiho_prostoru: 'Legnagyobb terem területe',\n        Tento_udaj_nam_neni_znam: 'Ez az adat nem áll rendelkezésünkre',\n        Nezname: 'Ismeretlen',\n        Pridat_do_oblibenych: 'Kedvencekhez adás',\n        Odebrat_z_oblibenych: 'Eltávolítás a kedvencek közül',\n        Zadat_poptavku: 'Árajánlat kérése',\n    }\n};\n\nexport default hu"], "names": ["message", "hello", "Podle_kapacity", "Od_<PERSON>_<PERSON>_<PERSON>", "Od_<PERSON>_do_A", "<PERSON><PERSON><PERSON><PERSON>", "Prostory", "V<PERSON><PERSON><PERSON>_count", "<PERSON><PERSON><PERSON><PERSON>", "Z<PERSON>leno", "Plocha_prostoru_m2", "Pocet_prostor_salu", "<PERSON><PERSON><PERSON><PERSON>_prostor_osob", "<PERSON><PERSON><PERSON><PERSON>_ne<PERSON><PERSON><PERSON><PERSON>_prostoru_osob", "V<PERSON><PERSON>ti", "Vyhledat", "<PERSON><PERSON>_podle", "Vyber_tagy", "Typy_luzeb", "<PERSON><PERSON>_akci", "Sluzby", "Na<PERSON>_da<PERSON>i", "<PERSON><PERSON><PERSON>_dalsi_vys<PERSON>ky", "Zobrazeno_n_z_n_vysledku", "Chyba_pri_nacitani_formuulare", "Nacitani_formulare", "Vyhledavam", "Hodnoceni_tooltip_s_hodnotou", "Hodnoceni_tooltip_bez_hodnoty", "Chyba", "<PERSON><PERSON>_ne<PERSON><PERSON>o", "Ce<PERSON>ova_kapacita", "Celkova_plocha", "<PERSON><PERSON><PERSON>_nej<PERSON><PERSON><PERSON>_prostoru", "Tento_udaj_nam_neni_znam", "Nezname", "Pridat_do_oblibenych", "Ode<PERSON><PERSON>_<PERSON>_oblibenych", "Zadat_poptavku"], "sourceRoot": ""}