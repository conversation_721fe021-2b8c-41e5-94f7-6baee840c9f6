@charset "UTF-8";
@import url("https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;500;600;700;800&display=swap");
@import "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.min.css";
a {
    color: #a30657;
    text-decoration: none;
}

p {
    color: #272626;
}

html, body {
    overflow-x: hidden;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    background-color: #f6f6f6;
}

.body {
    font-family: "Manrope", sans-serif;
    color: #272626;
}

.bg-body {
    background-color: #f6f6f6 !important;
}

.bg-secondary {
    background-color: #272626 !important;
}

.w-10{
    width: 10%;
}

.w-15{
    width: 15%;
}

.w-40{
    width: 40%!important;
}

.text-bold-700{
    font-weight: 700;
}

.bg-primary-grad {
    background: rgb(163, 6, 87);
    background: linear-gradient(117deg, rgb(163, 6, 87) 19%, rgba(212, 51, 134, 0.9430365896) 70%);
}

.nav-link:hover {
    cursor: pointer;
    color: #a30657;
}

.nav-pills .nav-link.active {
    border-radius: 16px;
}

.nav-pills .nav-link:hover {
    background-color: #F4F4F4;
    border-radius: 16px;
}

.navbar{
    box-shadow: none;
}

.navbar-brand img {
    width: 7rem !important;
}

.text-primary {
    color: #a30657 !important;
}

.text-black-500 {
    color: #adb5bd;
}

.text-light-gray {
    color: #ced4da;
}

.btn {
    border-radius: 16px;
}

.btn-outline-primary:active, .btn-outline-primary:hover, .btn-outline-primary:focus {
    color: #fff !important;
    background-color: #a30657 !important;
    border-color: #a30657 !important;
}

.btn-check:checked + .btn {
    color: #fff !important;
    background-color: #a30657 !important;
    border-color: #a30657;
}

.btn.btn-outline-primary:hover {
    box-shadow: 0 15px 35px rgba(255, 49, 88, 0.3019607843);
}

.btn .btn-outline-primary .active {
    background-color: #primary !important;
}

.btn-primary {
    background-color: #a30657;
    border-color: #a30657;
}

.btn-outline-primary {
    border-color: #a30657;
    color: #a30657;
}

.btn-primary:hover {
    color: #fff;
    background-color: #a30657;
    border-color: #a30657;
}

.nav-pills .nav-link {
    border-radius: 16px;
    border-color: #a30657;
    color: #a30657;
}

.nav-pills .nav-link.active, .nav-pills .nav-link:hover {
    color: #fff;
    background-color: #a30657;
    border-color: #a30657;
}

a:hover {
    color: #a30657;
}

.shaddow-hover:hover {
    box-shadow: rgba(17, 17, 26, 0.1) 0px 4px 16px, rgba(17, 17, 26, 0.1) 0px 8px 24px, rgba(17, 17, 26, 0.1) 0px 16px 56px;
}

.hover-icon-arrow:hover {
    color: #a30657;
}

.btn-link {
    color: #a30657;
    text-decoration: none;
}

.btn-link:hover, .btn-link.active {
    background-color: #e9ecef;
    color: #a30657;
}

.border-light-gray {
    border-color: #dee2e6 !important;
}

.radius-card {
    border-radius: 16px;
}

.card-shaddow {
    box-shadow: rgba(0, 0, 0, 0.12) 0px 1px 3px, rgba(0, 0, 0, 0.24) 0px 1px 2px;
}

.text-zvyrazneni {
    background: linear-gradient(90deg, rgba(255, 187, 51, 0) 0, rgba(255, 187, 51, 0.7490196078) 5%, rgba(255, 187, 51, 0.2509803922) 95%, rgba(255, 187, 51, 0) 100%);
    border-radius: 0.1rem;
    box-decoration-break: clone;
    -webkit-box-decoration-break: clone;
    display: inline;
    margin-left: -0.2em;
    margin-right: -0.2em;
    padding-left: 0.2em;
    padding-right: 0.2em;
}

.card {
    border-radius: 16px;
}

.img-grey {
    filter: grayscale(1);
    opacity: 0.7;
}

.img-grey:hover {
    filter: none;
}

.img-fit{
    object-fit: cover;
}

.bg-cover {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.bg-center {
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover; /* nebo 'contain' podle potřeby */
}

.border-primary {
    border-color: #a30657 !important;
}

.banner {
    min-height: 500px;
    background-size: cover;
    background-position: center;
}

@media (max-width: 767px) {
    .banner {
        min-height: 400px;
    }
}
.banner-content {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.banner:hover .banner-content {
    opacity: 1;
}

.horizontal-scroll {
    display: flex;
    overflow-x: auto;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    padding: 1rem 0;
}

.horizontal-scroll .card {
    flex: 0 0 auto;
    width: 300px;
    margin-right: 1rem;
}

.horizontal-scroll::-webkit-scrollbar {
    height: 2px;
}

.horizontal-scroll::-webkit-scrollbar-thumb {
    background-color: #a30657;
    border-radius: 16px;
}

.w-4r {
    width: 4rem;
}

.h-4r {
    height: 4rem;
}

.termsfeed-com---palette-light.termsfeed-com---nb {
    border-radius: 16px;
    border: 1px solid #a30657;
}

.termsfeed-com---palette-light .cc-nb-okagree {
    background-color: #a30657 !important;
    border-radius: 16px;
}

.termsfeed-com---palette-light .cc-nb-reject {
    background-color: transparent !important;
    color: #272626 !important;
    border: 1px solid #a30657;
    border-radius: 16px;
}

.termsfeed-com---palette-light .cc-nb-changep {
    background-color: transparent !important;
    color: #272626 !important;
    border: 1px solid #a30657;
    border-radius: 16px;
}

.cc-pc-container {
    border-radius: 16px;
}

.termsfeed-com---palette-light .cc-cp-foot-save {
    background-color: #a30657 !important;
    border-radius: 16px;
}

.form-range::-webkit-slider-thumb {
    background-color: #a30657;
}

.nav-tabs .nav-item .nav-link, .tabs-navbar .nav-link {
    color: #a30657;
}

.nav-tabs .nav-link.active {
    background-color: transparent;
}

/*# sourceMappingURL=styles.css.map */

/*noUi Slider*/
.noUi-connect{
    background: #a30657!important;
}

.noUi-horizontal .noUi-handle{
    width: 28px!important;
    height: 28px;
    border-radius: 50px;
}

/*Galerie*/
.gallery {
    display: flex;
    flex-wrap: wrap;
}

/* drobné doladění vzhledu */
.gallery .card { border-radius: .75rem; transition: transform .12s ease, box-shadow .12s ease; }
.gallery .card:hover { transform: translateY(-2px); box-shadow: 0 .5rem 1rem rgba(0,0,0,.10); }

/* volitelné: jemné zaoblení náhledu */
.gallery .ratio img { border-radius: .75rem; }

/* pokud nepoužíváš BS 5.2 utilities pro object-fit, přidej: */
.object-fit-cover { object-fit: cover; object-position: center; }

.gallery a {
    flex: 1 1 calc(33.333% - 10px);
}

.img-thumbnail {
    width: 100%;
    height: auto;
    display: block;
}

/*Offcanvas Hodnocení*/
#offcanvasReview{
    width: 50%;
}

@media (max-width: 767px) {
    #offcanvasReview{
        width: 100%;
    }
}


/* Mobilní panel spodní */
 .mobile-bottom-nav {
     position: fixed;
     bottom: 0; left: 0; right: 0;
     background: #fff;
     border-top: 1px solid rgba(0,0,0,.08);
     padding-bottom: max(env(safe-area-inset-bottom), 8px);
     box-shadow: 0 -6px 24px rgba(0,0,0,.06);
     z-index: 999;
 }
.mobile-bottom-nav .nav-link {
    padding: 10px 0 4px;
    min-height: 56px;
    font-size: 12px;
    line-height: 1.1;
    color: #6b7280;
}
.mobile-bottom-nav .nav-link .bi {
    font-size: 20px;
    display: block;
    margin-bottom: 2px;
}
.mobile-bottom-nav .nav-link.active,
.mobile-bottom-nav .nav-link:focus,
.mobile-bottom-nav .nav-link:hover {
    color: #a30657; /* text-dark-ish */
}
.mobile-bottom-nav .nav-link.active .nav-dot { opacity: .9; }
/* Skrytí na >=md, panel je primárně mobilní */
@media (min-width: 768px) {
    .mobile-bottom-nav { display: none; }
}
body { padding-bottom: 80px; } /* prostor pro fixní nav */


/* FAQ */
.accordion-button:not(.collapsed) {
    background-color: #f6f6f6!important;
}

.accordion-collapse{
    background-color: white;
}

.h-portal-home{
    font-size: 2rem;
}

@media (min-width: 768px) {
    .h-portal-home{
    font-size: 3rem;
    }
}


/* Qvamp akcent – sladěno s tlačítkem „Najít“ */
:root {
    --qvamp-accent: #b1005a; /* uprav dle brandu */
    --qvamp-accent-20: rgba(177, 0, 90, .20);
}

.vp-section {
    /* jemná separace od hero (nahoře) */
    margin-top: 0.5rem;
}

/* karta benefitu */
.vp-card {
    background: #fff;
    padding: 1.5rem 1.25rem;
    border: 1px solid rgba(0,0,0,.06);
    box-shadow:
            0 6px 18px rgba(0,0,0,.06),
            0 1px 0 rgba(0,0,0,.02);
    transition: transform .2s ease, box-shadow .2s ease;
}

.vp-card:hover {
    transform: translateY(-2px);
    box-shadow:
            0 10px 26px rgba(0,0,0,.08),
            0 1px 0 rgba(0,0,0,.02);
}

/* kruhová ikonka s jemným „glow“ do růžova */
.vp-icon-wrap {
    width: 56px;
    height: 56px;
    margin-inline: auto;
    margin-bottom: 1rem;
    border-radius: 999px;
    display: grid;
    place-items: center;
    color: var(--qvamp-accent);
    background: radial-gradient(closest-side, rgba(177,0,90,.10), rgba(177,0,90,0));
    border: 1px solid rgba(0,0,0,.06);
}

.vp-icon-wrap i {
    font-size: 1.4rem;
    line-height: 1;
}

/* typografie sladěná s hero */
.vp-title {
    font-weight: 700;
    font-size: 1.1rem;
    line-height: 1.25;
    margin-bottom: .25rem;
    color: #2b2b2b;
}

.vp-text {
    margin: 0;
    color: #6b6b6b;
    font-size: .95rem;
}

/* responsivní mezery a zarovnání */
@media (max-width: 767.98px) {
    .vp-card { padding: 1.25rem 1rem; }
    .vp-title { font-size: 1.05rem; }
    .vp-text  { font-size: .95rem; }
}

