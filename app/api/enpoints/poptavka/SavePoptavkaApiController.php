<?php namespace app\api\enpoints\poptavka;

use app\front\controllers\mista\NeulozenaPoptavkaController;
use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceNastaveni;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\system\controller\Controller;
use app\system\controller\exceptions\page404\Exception404;
use app\system\Environment;
use app\system\helpers\Files;
use app\system\model\api\mista\MistaApiKeyRow;
use app\system\model\api\mista\MistaApiKeys;
use app\system\model\leady\lead\NovaPoptavkaEvent;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\organizace\personal\Personal;
use app\system\model\organizace\personal\prirazeni\event\ChangePersonalPrirazeniEvent;
use app\system\model\organizace\subscription\MistoSubscription;
use app\system\Redirect;
use app\system\router\NewRouter;
use app\system\router\Route;
use app\system\traits\JsonAjax;
use Exception;
use Tracy\Debugger;
use Tracy\ILogger;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 07.03.2023 */
#[Route('^/api/lead/post(?:/*)(?<pubKey>\w*)$')]
class SavePoptavkaApiController
{

   use Controller;
   use JsonAjax;

   private const string LOG_DIR = 'api';

   const ERR_INVALID_INPUT = 412;

   public function call() :void {
      $post_key = NewRouter::get()->getRoute()->getParametrs()['pubKey'];
      $referer = $_SERVER['HTTP_REFERER'] ?? null;

      if(
         !isset($_POST['id_mista'])
         || !($this->api_key = MistaApiKeys::getByPublicKey($post_key, $_POST['id_mista']))
         || $this->api_key->id_mista !== (int)$_POST['id_mista']
         || !($organizace = Organizace::getMisto($this->api_key->id_mista))
      ){
         if($referer)
            throw new Exception404();

         $this->sendAjaxResponse([
            'status' => 404,
            'message' => 'Not Found',
         ]);
      }

      $this->organizace = $organizace;

      try{
         $this->post = $_POST;
         $this->process();

         if($_SERVER['HTTP_REFERER'] ?? null)
            Redirect::toLocation($_SERVER['HTTP_REFERER']);

         $this->sendAjaxResponse([
            'status' => 200,
            'message' => 'succesfull',
         ]);
      }catch(Exception $e){
         if($e->getCode() !== self::ERR_INVALID_INPUT){
            Debugger::log($e, ILogger::ERROR);
         }else{
            Files::checkDir($logDir = Files::prepareRootDirPath(Files::LOG_DIR . self::LOG_DIR));
            file_put_contents(
               sprintf('%s/%s', $logDir, 'invalid-data.log'),
               sprintf(
                  '[%s] | Msg: %s | Data: %s | IP: %s',
                  date('Y-m-d H:i:s'),
                  $e->getMessage(),
                  json_encode($this->post, JSON_UNESCAPED_UNICODE),
                  Environment::getIpAddress()
               ) . PHP_EOL,
               FILE_APPEND
            );
         }

         if($_SERVER['HTTP_REFERER'] ?? null)
            Redirect::to(sprintf('%s?%s',
               NeulozenaPoptavkaController::getUrl(),
               http_build_query([
                  'back' => urlencode(gzdeflate($_SERVER['HTTP_REFERER'])),
                  'message' => urlencode(gzdeflate($e->getMessage())),
               ])
            ));

         $this->sendAjaxResponse([
            'status' => 400,
            'message' => $e->getMessage(),
         ], true);
      }
   }

   private function validateData() :void {
      if(!trim($this->post['jmeno']?? ''))
         throw new Exception('Invalid first name', self::ERR_INVALID_INPUT);

      if(!trim($this->post['prijmeni']?? ''))
         throw new Exception('Invalid last name', self::ERR_INVALID_INPUT);

      if(!trim($this->post['email']?? ''))
         throw new Exception('Invalid email', self::ERR_INVALID_INPUT);

      if(!trim($this->post['telefon']?? ''))
         throw new Exception('Invalid phone', self::ERR_INVALID_INPUT);

//      @TODO parsování hodnot které nejsou povinné
   }

   private function process() :void {
      $this->validateData();

      $poptavka = (new NovaPoptavkaEvent())
         ->setOrganizaceRow($this->organizace)
         ->preparePoptavka(array_merge($this->post, ['id_jazyk_zakaznik' => OrganizaceJazyky::getPrimary($this->organizace->id_organizace)]))
         ->setZdrojPoptavky($this->post['event_source'] ?: 0)
         ->setNotifikaceZakaznik()
         ->call();

      if(
         MistoSubscription::get($this->api_key->id_mista)?->isValid()
         && isset(OrganizaceNastaveni::getByOrganizace($this->organizace->id_organizace)->id_prirazovany_personal)
         && ($personal = Personal::get(OrganizaceNastaveni::getByOrganizace($this->organizace->id_organizace)->id_prirazovany_personal))
      ) {
         (new ChangePersonalPrirazeniEvent(
            $poptavka->getPoptavka(),
            [$personal->id_personal => $personal->id_personal])
         )->call();
      }
   }

   protected ?MistaApiKeyRow $api_key;
   protected OrganizaceRow $organizace;
   private array $post;
}