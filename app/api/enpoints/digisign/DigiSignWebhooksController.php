<?php namespace app\api\enpoints\digisign;

use app\system\controller\Controller;
use app\system\Environment;
use app\system\helpers\Files;
use app\system\router\NewRouter;
use app\system\router\Route;
use app\system\webhooks\digisign\DigiWebhook;
use Error;
use Exception;
use <PERSON>\Debugger;
use <PERSON>\ILogger;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.05.2023 */
#[Route('^/api/digisign(?:/*)(?<event>\w*)$')]
class DigiSignWebhooksController
{

   use Controller;

   public function call() :void {
      if(!($eventName = NewRouter::get()->getRoute()->getParametrs()['event']))
         die(404);

      $header = $_SERVER['HTTP_SIGNATURE'] ?? '';
      $payload = file_get_contents('php://input');

      try{
         if (preg_match('/t=(?<t>\d+),s=(?<s>\w+)/', $header, $matches) !== 1)
            throw new Exception('Unable to parse signature header', self::CODE_UNAUTHORIZED);

         $ts = (int)($matches['t'] ?? 0);
         $signature = $matches['s'] ?? '';

         if ($ts < (time() - 300))
            throw new Exception('Request is older than 5 minutes', self::CODE_UNAUTHORIZED);

//      Find webhook -> getSecret dostanu např.: envelopeCompleted
         $envClass = DigiWebhook::getHook($eventName);

         if(!($secret = $envClass->getSecret()))
            throw new Exception('Webhook missing signature', self::CODE_UNAUTHORIZED);

         $expected = hash_hmac('sha256', $ts . '.' . $payload, $secret);
         if (hash_equals($expected, $signature) === false)
            throw new Exception('Invalid signature', self::CODE_UNAUTHORIZED);

         $payload = json_decode($payload);

         $envClass->setPayload($payload);
         $envClass->call();
      }catch(Exception|Error $e){
         if($e->getCode() === self::CODE_UNAUTHORIZED){
            header('HTTP/1.0 403 Forbidden');
            file_put_contents(
               $this->getFilePath(self::ERROR_LOG),
               sprintf(
                  '[%s] | %s | Unauthorized: %s | IP: %s | header: %s',
                  date('Y-m-d H:i:s'),
                  $eventName,
                  $e->getMessage(),
                  Environment::getIpAddress(),
                  $header
               ) . PHP_EOL,
               FILE_APPEND
            );
            return;
         }

         header('HTTP/1.1 500 Internal Server Error');
         Debugger::log($e, ILogger::ERROR);
         file_put_contents(
            $this->getFilePath(self::ERROR_LOG),
            sprintf(
               '[%s] | %s | Error: %s | IP: %s | header: %s',
               date('Y-m-d H:i:s'),
               $eventName,
               $e->getMessage(),
               Environment::getIpAddress(),
               $header,
            ) . PHP_EOL,
            FILE_APPEND
         );

         return;
      }

      file_put_contents(
         $this->getFilePath(self::LAST_LOG),
         sprintf(
            '[%s] | %s | IP: %s',
            date('Y-m-d H:i:s'),
            $eventName,
            Environment::getIpAddress(),
         ) . PHP_EOL,
         FILE_APPEND
      );
   }

   protected function getFilePath(string $fileName) :string {
      Files::checkDir($logDir = Files::prepareRootDirPath(Files::LOG_DIR . self::LOG_DIR));

      return sprintf('%s/%s', $logDir, $fileName);
   }

   const CODE_UNAUTHORIZED = 999;
   const LOG_DIR = '/webhook';
   const ERROR_LOG = 'webhook-error.log';
   const LAST_LOG = 'webhook-last.log';
}