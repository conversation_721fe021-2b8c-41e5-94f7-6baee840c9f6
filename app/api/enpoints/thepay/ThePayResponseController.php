<?php namespace app\api\enpoints\thepay;

use app\front\controllers\EnvironmentController;
use app\system\controller\Controller;
use app\system\Environment;
use app\system\helpers\Files;
use app\system\model\organizace\subscription\event\CheckPaymentEvent;
use app\system\model\organizace\subscription\platby\MistoSubscriptionPlatby;
use app\system\Redirect;
use app\system\router\Route;
use app\system\SystemVersion;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 10.03.2023 */
#[Route('^/api/thepay$')]
class ThePayResponseController
{

   use Controller;

   const STATE_RETURN = 'return';
   const STATE_NOTIFY = 'notify';
   const LOG_DIR = '/thepay';

   public function call() :void {
//      @TODO allow only few ip address

      $uid = $_GET['payment_uid']?? null;

      if(!$uid || !($platba = MistoSubscriptionPlatby::getForUid($uid)))
         die('UID Missing');

      Files::checkDir($logDir = Files::prepareRootDirPath(Files::LOG_DIR . self::LOG_DIR));
      $parts = explode('-', $platba->uid_subscription);

      if(count($parts) === 3){
         file_put_contents(
            sprintf('%s/%s', $logDir, 'thepay-access.log'),
            sprintf(
               '[%s] | UID: %s | Child subscription payment | IP: %s',
               date('Y-m-d H:i:s'),
               $uid,
               Environment::getIpAddress()
            ) . PHP_EOL,
            FILE_APPEND
         );
         die('Child payment');
      }

      $success = CheckPaymentEvent::checkUid($uid);

      file_put_contents(
         sprintf('%s/%s', $logDir, 'thepay-access.log'),
         sprintf(
            '[%s] | UID: %s | Status: %s | IP: %s',
            date('Y-m-d H:i:s'),
            $uid,
            $success ? 'paid' : 'pending',
            Environment::getIpAddress()
         ) . PHP_EOL,
         FILE_APPEND
      );

      if(($_GET['state']?? '') === self::STATE_RETURN)
         Redirect::toLocation(Redirect::check(EnvironmentController::getUrl(EnvironmentController::ACTION_RELOAD_SESSION), SystemVersion::APP));

      die('OK');
   }
}