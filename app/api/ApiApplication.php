<?php namespace app\api;

use app\api\enpoints\digisign\DigiSignWebhooksController;
use app\api\enpoints\poptavka\SavePoptavkaApiController;
use app\api\enpoints\thepay\ThePayResponseController;
use app\System;
use app\system\Application;
use app\system\application\environment\BlankApplicationEnvironment;
use app\system\application\IEnvironment;
use app\system\router;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 07.03.2023 */
class ApiApplication extends Application
{

   public function prepareRoutes(router\RoutesContainer $container) :void {
      $container->addControllers(
         SavePoptavkaApiController::class,
         ThePayResponseController::class,
         DigiSignWebhooksController::class,
      );
   }

   protected function prepareApplicationEnvironment(System $system) :IEnvironment {
      return BlankApplicationEnvironment::init($system);
   }
}