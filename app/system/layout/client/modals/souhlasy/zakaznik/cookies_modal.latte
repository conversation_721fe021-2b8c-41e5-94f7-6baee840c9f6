{varType app\front\organizace\model\organizace\OrganizaceRow $organizace}

<div class="container">
   <form method="post">

      <div class="row">
         <h4>{_'Nastavení ochrany soukromí'}</h4>
      </div>

      {include '../cookie/cookie_table.latte'}


      {include 'souhlasy_table.latte', organizace => $organizace}

      <div class="row mt-4">
         <input type="hidden" value="{$organizace->id_organizace}" name="id_organizace">
         <div class="col-auto">
            <button type="submit" name="btnUlozitSouhlasy" class="form-control btn btn-primary js-souhlasy-all"><i class="bi bi-check me-1"></i>{_'Přijmout vše'}</button>
         </div>
         <div class="col-auto">
            <button type="submit" name="btnUlozitSouhlasy" class="form-control btn btn-outline-secondary js-souhlasy">{_'Přijmout vybrané'}</button>
         </div>
      </div>
   </form>
</div>

<script>
   $(function() {
      const body = $('body');

      body.on('click', 'button.js-souhlasy-all', function(e) {
         if(e.isDefaultPrevented())
            return;

         e.preventDefault();
         const form = $(this).closest('form');

         body.find('input.js-consent-check').each(function(i, el) {
            $(el).prop('checked', true);
         });


         if(!form.valid())
            return;

         form[0].requestSubmit(this);

         form.find('button[type=submit]').each(function(i, el) {
            $(el).prop('disabled', true);
         });
      })
   })
</script>