<?php namespace app\system\layout\client\modals\souhlasy\cookie;

use app\katalog\cookies\KatalogCookiesController;
use app\system\component\Templater;
use app\system\cookies\CookieSouhlasy;
use app\system\modul\modal\Modal;
use app\system\modul\modal\styles\ModalPositionEnum;
use app\system\modul\modal\styles\ModalScaleEnum;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 13.12.2023 */
class CookieNastaveniModal extends Modal
{

   public function getTitleName() :string {
      return 'Cookies';
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'cookies' => KatalogCookiesController::getUrl('cz'),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUlozitSouhlasyCookie', function($post) {
         CookieSouhlasy::saveTypy(array_keys($post['cookie'] ?? []));
      });
   }

   protected bool $canClose = false;
   protected bool $showOnLoad = true;

   protected ModalScaleEnum $scale = ModalScaleEnum::DEFAULT;
   protected ModalPositionEnum $position = ModalPositionEnum::VERTICAL;
}