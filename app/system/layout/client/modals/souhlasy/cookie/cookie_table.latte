{varType string $cookies}

<div class="row mb-2 mx-0">
   <div class="col">
      <div class="row mb-2">
         <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" role="switch"
                   id="systemCookie" n:attr="checked: true, disabled: true">

            <label class="form-check-label text-secondary" for="systemCookie">
               {_'Cookies nezbytné pro fungování systému'}
            </label>
         </div>
      </div>

      {foreach app\system\cookies\CookieTypy::cases() as $typ}
         <div n:if="$typ->mustAccept()" class="row mb-2">
            <div class="row">
            <div class="col-auto form-check form-switch">
               <input class="form-check-input js-consent-check" type="checkbox" role="switch" name="cookie[{$typ->value}]"
                      id="statisticCookie" n:attr="checked: false">

               <label class="form-check-label text-secondary" for="statisticCookie">
                  {$typ->getTranslatedTitle()}
               </label>
            </div>
            <div class="col">
               <a data-bs-toggle="collapse" href="#collapseAnalyticCokieInfo" role="button" aria-expanded="false" aria-controls="collapseAnalyticCokieInfo">
                  <i class="bi bi-info-square"></i>
               </a>
            </div>
            </div>
               <div class="row">
                  <div class="collapse card p-2" id="collapseAnalyticCokieInfo">
                     <small>
                        {_'Tyto cookies nám napoví, které části webu jsou pro vás nejpřínosnější. Díky tomu dokážeme web zlepšovat tak, aby se vám používal co nejlépe.'}
                        <a href="{$cookies}" target="_blank">{_'Více o cookies'}</a>
                     </small>
                  </div>
               </div>
         </div>
      {/foreach}

   </div>
</div>