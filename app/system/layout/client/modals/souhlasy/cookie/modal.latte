<div class="container">
   <form method="post">
      <div class="row">
         <h4>{_'Nastavení ochrany soukrom<PERSON>'}</h4>
      </div>

      {include 'cookie_table.latte'}

      <div class="row mt-4 gap-5">
         <div class="col-auto">
            <button type="submit" name="btnUlozitSouhlasyCookie" class="form-control btn btn-primary js-souhlasy-all"><i class="bi bi-check-circle me-1"></i>{_'Přijmout vše'}</button>
         </div>
         <div class="col-auto align-content-end">
            <button type="submit" name="btnUlozitSouhlasyCookie" class="btn btn-sm btn-link text-secondary js-souhlasy">{_'Přijmout vybrané'}</button>
         </div>
      </div>
   </form>
</div>

<script>
   $(function() {
      const body = $('body');

      body.on('click', 'button.js-souhlasy-all', function(e) {
         if(e.isDefaultPrevented())
            return;

         e.preventDefault();
         const form = $(this).closest('form');

         body.find('input.js-consent-check').each(function(i, el) {
            $(el).prop('checked', true);
         });


         if(!form.valid())
            return;

         form[0].requestSubmit(this);

         form.find('button[type=submit]').each(function(i, el) {
            $(el).prop('disabled', true);
         });
      })
   })
</script>