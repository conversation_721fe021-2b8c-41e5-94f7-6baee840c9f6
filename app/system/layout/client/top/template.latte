{varType app\system\model\translator\Jazyky[] $jazyky}
{varType app\system\model\translator\Jazyky $selectedJazyk}
{varType ?string $organizationName}
{varType ?app\system\users\zakaznici\LoggedZakaznik $zakaznik}
{varType string $homeHref}

<nav class="navbar navbar-expand navbar-theme">
   <div class="container-fluid p-3">
      <a class="navbar-brand" href="{$homeHref}">
         {if $organizationName}
            <img src="/files/system/img/icons/qvamp_icon_default.svg" alt="qvamp logo" width="auto" height="30" class="d-inline-block">
            <span class="border-start ms-1 ps-2">{$organizationName}</span>
         {else}
            <img src="/files/system/img/icons/qvamp_logo_default.svg" alt="qvamp logo" width="auto" height="30" class="d-inline-block">
         {/if}
      </a>
   </div>
   <div class="navbar-collapse collapse" n:if="$zakaznik">
      <ul class="navbar-nav ms-auto">
         {include uzivatel}
      </ul>
   </div>

   <div class="navbar-collapse collapse" n:if="!$zakaznik">
      <ul class="navbar-nav ms-auto">
         <li class="nav-item ms-lg-2">
            <select class="js-select-language" style="width: 150px">
               {foreach $jazyky as $jazyk}
                  <option value="{$jazyk->value}" {if $jazyk == $selectedJazyk}selected{/if} data-flag-icon="{$jazyk->getIcon()}">{$jazyk->getTitle()}{if $jazyk->isNotVisible()} - DEV{/if}</option>
               {/foreach}
            </select>
         </li>
      </ul>

   </div>
</nav>

{define uzivatel}
   <li class="nav-item dropdown ms-lg-2">
      <a class="nav-link dropdown-toggle position-relative" href="#" id="userDropdown" data-bs-toggle="dropdown">
         <div class="nav-flex border-corner">
            {$zakaznik->full_name}
         </div>
      </a>
      <div class="dropdown-menu dropdown-menu-end js-dropdown-keep" aria-labelledby="userDropdown">
         <div class="px-2">
            <select class="js-select-language">
               {foreach $jazyky as $jazyk}
                  <option value="{$jazyk->value}" {if $jazyk == $selectedJazyk}selected{/if} data-flag-icon="{$jazyk->getIcon()}">{$jazyk->getTitle()}</option>
               {/foreach}
            </select>
         </div>

         {if $zakaznik->isRegistered}
            <div class="dropdown-divider"></div>
            <a class="dropdown-item" href="#"><i class="align-middle me-1 bi bi-person-gear"></i> {_'Nastavení profilu'}</a>
         {/if}

         <div class="dropdown-divider"></div>
         <a class="dropdown-item" href="/handle-logout"><i class="align-middle me-1 bi bi-box-arrow-right"></i> {_'Odhlásit se'}</a>
      </div>
   </li>
{/define}