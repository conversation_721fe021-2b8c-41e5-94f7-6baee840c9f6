<?php namespace app\system\layout\sidemenu;

use app\admin\controllers\digisign\DigisignNastaveniController;
use app\admin\controllers\misto\DetailMistaAdminController;
use app\admin\controllers\nastaveni\AdminNastaveniController;
use app\admin\controllers\PrekladyController;
use app\admin\controllers\sablonyTexty\PrehledSablonTextuController;
use app\admin\controllers\subscription\NastaveniSubscriptionController;
use app\admin\controllers\UzivateleController;
use app\admin\home\AdminHomeController;
use app\admin\statistika\AdminStatistikaController;
use app\front\controllers\aktivity\PrehledAktivitController;
use app\front\controllers\eventy\DetailEventuController;
use app\front\controllers\eventy\PrehledEventuController;
use app\front\controllers\home\HomepageController;
use app\front\controllers\mista\PrehledOrganizaceMistaController;
use app\front\controllers\nabidky\DetailNabidkyController;
use app\front\controllers\nabidky\PrehledNabidekController;
use app\front\controllers\navody\PrehledNavoduController;
use app\front\controllers\organizace\DetailMailSablonyController;
use app\front\controllers\organizace\DetailMistaController;
use app\front\controllers\organizace\PrehledMistController;
use app\front\controllers\platby\scenare\DetailPlatebnihoScenareController;
use app\front\controllers\platby\scenare\PrehledPlatebnichScenaruController;
use app\front\controllers\poptavky\DetailPoptavkyController;
use app\front\controllers\poptavky\PrehledPoptavekController;
use app\front\controllers\reporty\ReportyPageController;
use app\front\controllers\zakaznici\PrehledZakaznikuController;
use app\system\application\AdminApplicationEnvironment;
use app\system\application\FrontApplicationEnvironment;
use app\system\component\Component;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\front\controllers\kalendar\KalendarPageController;

/** Created by Kryštof Czyź. Date: 15.04.2022 */
class SidemenuComponent extends Component
{

   function setData() :array {
      $this->prepareItems();

      return [
         'items' => $this->menuItems,
         'bottomItems' => $this->bottomItems,
         'homepageLink' => ($isAdmin = $this->environment instanceof AdminApplicationEnvironment)
            ? AdminHomeController::getUrl()
            : '/',
         'isAdmin' => $isAdmin,
      ];
   }

   public function addItem(Item $item) :static {
      $this->menuItems[] = $item;
      return $this;
   }

   public function addBottomItem(Item $item) :static {
      $this->bottomItems[] = $item;
      return $this;
   }

   public function setEnvirotnment(FrontApplicationEnvironment|AdminApplicationEnvironment $environment) :static {
      $this->environment = $environment;
      return $this;
   }

   private function prepareItems() {
      $this->environment instanceof AdminApplicationEnvironment
         ?  $this->subscribeAdminItems()
         :  $this->subscribeFrontItems();
   }

   private function subscribeFrontItems() {
      $this->personalRigts = $this->environment->personal?->getAccessRights() ?: [];
      $this->isAdmin = $this->environment->isMajitel || isset($this->personalRigts[PersonalAccessRightsEnum::ADMIN->value]);

      $this->addItem(
         Item::init('Nástěnka')
            ->setIcon('bi bi-house')
            ->setUrl(HomepageController::getUrl())
            ->setListenControllers([
               HomepageController::class,
            ])
      );

      if($this->environment->licenceTyp->hasEventCRM()){
         $this->appendAktivity();
         $this->appendKalendar();
      }

      $this->appendZakaznici();
      $this->appendPoptavky();

      if($this->environment->licenceTyp->hasEventCRM()){
         $this->appendKalkulace();
         $this->appendEventy();
      }

      $this->appendNastaveni();

//      if($this->isAdmin || in_array(PersonalAccessRightsEnum::NASTROJE, $this->personalRigts))
//         $this->addItem(
//            Item::init('Nástroje')
//               ->setIcon('bi bi-calculator')
//               ->setUrl(NastrojePageController::getUrl())
//               ->setListenControllers([
//                  NastrojePageController::class,
//               ])
//         );

      if($this->environment->licenceTyp->hasPrehledy() && $this->isAdmin || in_array(PersonalAccessRightsEnum::REPORTY, $this->personalRigts))
         $this->addItem(
            Item::init('Přehledy')
               ->setIcon('bi bi-clipboard-data')
               ->setUrl(ReportyPageController::getUrl())
               ->setListenControllers([
                  ReportyPageController::class,
               ])
         );

      $this->addBottomItem(
         Item::init('Nápověda')
            ->setIcon('bi bi-question-square')
            ->setUrl(PrehledNavoduController::getUrl())
            ->setListenControllers([
               PrehledNavoduController::class,
            ])
      );
   }

   private function subscribeAdminItems() {
      $this
         ->addItem(Item::init('Přehled uživatelů')
            ->setIcon('bi bi-person-add')
            ->setUrl(UzivateleController::getUrl())
            ->setListenControllers([
               UzivateleController::class,
            ]))
         ->addItem(Item::init('Přehled míst')
            ->setIcon('bi bi-pin-map')
            ->setUrl(\app\admin\controllers\misto\PrehledMistController::getUrl())
            ->setListenControllers([
               \app\admin\controllers\misto\PrehledMistController::class,
               DetailMistaAdminController::class,
            ]))
         ->addItem(Item::init('Překlady')
            ->setIcon('bi bi-translate')
            ->setUrl(PrekladyController::getUrl())
            ->setListenControllers([
               PrekladyController::class,
            ]))
         ->addItem(Item::init('Šablony textů')
            ->setIcon('bi bi-layout-text-window')
            ->setUrl(PrehledSablonTextuController::getUrl())
            ->setListenControllers([
               PrehledSablonTextuController::class
            ]))
         ->addItem(Item::init('Předplatné')
            ->setIcon('bi bi-credit-card-2-back')
            ->setUrl(NastaveniSubscriptionController::getUrl())
            ->setListenControllers([
               NastaveniSubscriptionController::class
            ]))
         ->addItem(Item::init('DigiSign')
            ->setIcon('bi bi-check2-square')
            ->setUrl(DigisignNastaveniController::getUrl())
            ->setListenControllers([
               DigisignNastaveniController::class
            ]))
         ->addItem(Item::init('Nastavení')
            ->setIcon('bi bi-check2-square')
            ->setUrl(AdminNastaveniController::getUrl())
            ->setListenControllers([
               AdminNastaveniController::class
            ]))
         ->addItem(
            Item::init('Statistika')
               ->setIcon('bi bi-check2-square')
               ->setUrl(AdminStatistikaController::getUrl())
               ->setListenControllers([
                  AdminStatistikaController::class,
               ])
         );
   }

   private function appendZakaznici() {
      if(!$this->isAdmin && !in_array(PersonalAccessRightsEnum::ZAKAZNICI, $this->personalRigts))
         return;

      $this->addItem(
         Item::init('Zákazníci')
            ->setIcon('bi bi-person-vcard')
            ->setUrl(PrehledZakaznikuController::getUrl())
            ->setListenControllers([
               PrehledZakaznikuController::class,
            ])
      );

      if($this->environment->versionType->isVendor())
         $this->addItem(
            Item::init('Místa')
               ->setIcon('bi bi-pin-map')
               ->setUrl(PrehledOrganizaceMistaController::getUrl())
               ->setListenControllers([
                  PrehledOrganizaceMistaController::class,
               ])
         );
   }

   private function appendKalendar() :void {
      $this->addItem(
         Item::init('Kalendář')
            ->setIcon('bi bi-calendar3')
            ->setUrl(KalendarPageController::getUrl())
            ->setListenControllers([
               KalendarPageController::class,
            ])
      );
   }

   private function appendPoptavky() {
      if(!$this->isAdmin && !in_array(PersonalAccessRightsEnum::POPTAVKY, $this->personalRigts))
         return;

      $this->addItem(
         Item::init('Poptávky')
            ->setIcon('bi bi-file-earmark-arrow-up')
            ->setUrl(PrehledPoptavekController::getUrl())
            ->setListenControllers([
               PrehledPoptavekController::class,
               DetailPoptavkyController::class
            ])
      );
   }

   private function appendKalkulace() {
      if(!$this->isAdmin && !in_array(PersonalAccessRightsEnum::KALKULACE, $this->personalRigts))
         return;

      $this->addItem(
//         Mezera na konci je chválně kvůlu odlišení překladu
         Item::init('Kalkulace ')
            ->setIcon('bi bi-receipt')
            ->setUrl(PrehledNabidekController::getUrl())
            ->setListenControllers([
               PrehledNabidekController::class,
               DetailNabidkyController::class,
            ])
      );
   }

   private function appendEventy() {
      if(!$this->isAdmin && !in_array(PersonalAccessRightsEnum::EVENTY, $this->personalRigts))
         return;

      $this->addItem(
         Item::init('Eventy')
            ->setIcon('bi bi-calendar2-event')
            ->setUrl(PrehledEventuController::getUrl())
            ->setListenControllers([
               PrehledEventuController::class,
               DetailEventuController::class,
            ])
      );
   }

   private function appendNastaveni() {
      if(!$this->isAdmin)
         return;

      if($this->environment->versionType->isVendor()){
         $this->addBottomItem(
            Item::init('Nastavení')
               ->setIcon('bi bi-gear')
               ->setUrl(DetailMistaController::getUrl($this->environment->id_organizace))
               ->setListenControllers([
                  DetailMistaController::class,
                  DetailMailSablonyController::class,
                  DetailPlatebnihoScenareController::class,
                  PrehledPlatebnichScenaruController::class,
               ])
         );
         return;
      }

      $this->addBottomItem(
         Item::init('Nastavení')
            ->setIcon('bi bi-gear')
            ->setUrl(DetailMistaController::getUrl($this->environment->id_organizace))
            ->setListenControllersCallback(function(bool $detail) :array {
               if($detail)
                  return [
                     DetailMistaController::class,
                     DetailMailSablonyController::class,
                     DetailPlatebnihoScenareController::class,
                     PrehledPlatebnichScenaruController::class,
                  ];

               return [
                  DetailMistaController::class,
                  PrehledMistController::class,
                  DetailMailSablonyController::class,
                  DetailPlatebnihoScenareController::class,
                  PrehledPlatebnichScenaruController::class,
               ];
            })
      );
   }

   private function appendAktivity() :void {
      $this->addItem(
         Item::init('Aktivity')
            ->setIcon('bi bi-calendar2-event')
            ->setUrl(PrehledAktivitController::getUrl())
            ->setListenControllers([
               PrehledAktivitController::class,
            ])
      );
   }

   /** @var Item[]  */
   protected array $menuItems = [];

   /** @var Item[]  */
   protected array $bottomItems = [];
   protected FrontApplicationEnvironment|AdminApplicationEnvironment $environment;

   /** @var PersonalAccessRightsEnum[] */
   protected array $personalRigts = [];
   protected bool $isAdmin;
}