<?php namespace app\system\modul\modal;

use app\system\traits\JsonAjax;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 18.01.2022 */
abstract class AjaxModal extends Modal
{

   use JsonAjax;

   final public static function decodeClass(string $classHash) :string {
      return gzinflate(urldecode($classHash));
   }

   final public static function encodeClass(?string $className = null) :string {
      if(!$className)
         $className = static::class;

      return u<PERSON>ncode(gzdeflate($className));
   }

   final public function sendContent() {
      $this->sendAjaxResponse([
         'content' => $this->getContent()
      ]);
   }

   final public static function getShowAttributes(?string $slug = null) :array {
      $modal = static::init();

      $attr = $modal->btnToggleAttributes() + [
         'data-class' => self::encodeClass(),
      ];

//      @TODO připravit na i array, pokud array tak se hodí do json a encoduje je, teoreticky by <PERSON><PERSON> stejně jako v panelu addParametr a getParametr
      if($slug)
         $attr['data-slug'] = $slug;

      return $attr;
   }

   final public static function getShowAttributesMultipleSugs(array $slug = null) :array {
      $modal = static::init();

      $attr = $modal->btnToggleAttributes() + [
            'data-class' => self::encodeClass(),
         ];

      if (!is_null($slug)) {
         $attr['data-slug'] = is_array($slug) ? json_encode($slug) : $slug;
      }
      return $attr;
   }

   public function prepareAjaxData() :void { }

   final protected function getContent() :string {
      $this->prepareAjaxData();
      return parent::getContent();
   }

   protected bool $ajaxLoad = true;
}