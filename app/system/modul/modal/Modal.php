<?php namespace app\system\modul\modal;

use app\System;
use app\system\component\Templater;
use app\system\model\translator\TextVariablesFactory;
use app\system\modul\modal\styles\ModalPositionEnum;
use app\system\modul\modal\styles\ModalScaleEnum;
use app\system\traits\PostListener;
use Exception;
use Nette\Utils\Html;
use ReflectionClass;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 17.01.2022 */
abstract class Modal
{

   const DEFAULT_FILE = 'modal';

   use PostListener;

   protected static array $modalCache = [];
   public static function init() :static {
      return self::$modalCache[static::class]??= new static();
   }

   public static function getShowButton(string $label) :string {
      $modal = static::init();
      return (string)Html::el('button', [
         'class' => 'btn btn-primary col-sm-3 col-6',
      ] + $modal->btnToggleAttributes())->setHtml($label);
   }

   abstract public function getTitleName() :TextVariablesFactory|string|null;

   public function getTranslatedTitle() :TextVariablesFactory|string {
      if(!$this->getTitleName())
         return '';

      return $this->getTitleName() instanceof TextVariablesFactory
         ? $this->getTitleName()
         : System::getTranslator()->layoutTranslate($this->getTitleName());
   }

   final public function render() :string {
      return Templater::prepare(__DIR__ . '/_layout.latte', [
         'content' => $this->ajaxLoad? '': $this->getContent(),
         'idModal' => $this->getIdModal(),
         'title' => $this->getTranslatedTitle(),
         'canDelete' => $this->canDelete,
         'ajaxLoad' => $this->ajaxLoad,
         'canClose' => $this->canClose,
         'showOnLoad' => $this->showOnLoad,
         'scrollable' => $this->scrollable,
         'scale' => $this->scale,
         'position' => $this->position,
      ], false)->render();
   }

   /** @return string[] vrací html atributy potřebné pro zobrazení modalu */
   public function btnToggleAttributes() {
      return [
         'data-bs-toggle' => 'modal',
         'data-bs-target' => '#' . $this->getIdModal(),
      ];
   }

   final public function getIdModal() :string {
      if($this->getStaticID())
         return $this->getStaticID();

      return 'mod' . crc32(static::class) . $this->uniId;
   }

   final public function isAjaxLoad() :bool {
      return $this->ajaxLoad;
   }

   final public function wantTopMenuLink() :bool {
      return $this->topMenuLink;
   }

   final protected function getTemplateFile($className) :string {
      $refl = new ReflectionClass($className);

      if(is_file($file = dirname($refl->getFileName()) . '/' . $this->getFileName() . '.latte'))
         $templateFile = $file;
      else
         throw new Exception('Template modalu nebyl nalezen');

      return $templateFile;
   }

   final public function disableOutsideClose() :static {
      $this->canClose = false;
      return $this;
   }

   final public function setUniqueId(string $value) :static {
      $this->uniId = $value;
      return $this;
   }

   protected function getContent() :string {
      $template = $this->getTemplateFile(static::class);
      $templater = Templater::prepare($template);
      $this->prepareModal($templater);
      return $templater->render();
   }

   protected function getFileName() :string {
      return $this->templateName;
   }

//   @TODO přejmenovat na prepareStaticModal()
   public function prepareModal(Templater $templater) { }

   protected function getStaticID() :?string { return null; }

   protected string $templateName = self::DEFAULT_FILE;
   protected string $uniId = '';

   protected bool $canDelete = false;
   protected bool $ajaxLoad = false;
   protected bool $showOnLoad = false;
   protected bool $topMenuLink = false;
   protected bool $canClose = true;
   protected bool $scrollable = true;

   protected ModalScaleEnum $scale = ModalScaleEnum::EXTRA_LARGE;
   protected ModalPositionEnum $position = ModalPositionEnum::DEFAULT;
}