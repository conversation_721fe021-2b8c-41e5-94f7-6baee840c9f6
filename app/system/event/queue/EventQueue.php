<?php namespace app\system\event\queue;

use app\system\event\Event;
use app\system\event\handler\EventHandler;
use app\system\event\queue\error\EventQueueError;
use app\system\helpers\Files;
use dibi;
use Dibi\DateTime;
use <PERSON><PERSON>\Fluent;
use Error;
use Exception;
use <PERSON>\Debugger;
use <PERSON>\ILogger;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.10.2023 */
class EventQueue
{

   const TABLE = 'system_event_queue';
   const ALIAS = 'seq';

   public static bool $development = false;

   public static function addToStack(Event|IEventQueue $event) :void {
      $json = json_encode(
         $event instanceof IEventQueue ? $event->getQueueParams() : [],
         JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES
      );
      $hash = md5($json);

      if(!!($row = self::getForParamHash($event::class, $hash)))
         return;

      $event->setAddedToStack();
      dibi::insert(self::TABLE, [
         'class' => $event::class,
         'params' => $json,
         'params_hash' => $hash,
         'status' => QueueStatus::CREATED->value,
      ])->execute();
   }

   public static function callStack(int $eventLimit = 20) :void {
      if(empty($unfinished = self::getUnfinished($eventLimit)))
         return;

      $start = microtime(true);
      $startTime = new DateTime();

      $counter = 0;
      $finished = [];

      $ids = array_column(self::getUnfinished($eventLimit), 'id');
      $affected = self::setStatus($ids, QueueStatus::IN_PROCESS);

      foreach($unfinished as $queuedEvent){
         $counter++;
         $queuedEvent->setStarted();
         $eventStarted = microtime(true);

         try {
            /** @var Event $event */
            $event = new $queuedEvent->class();

            if($event instanceof IEventQueue)
               $event->onBeforeQueuedCall(json_decode($queuedEvent->params, true));

            if($event->getSuccess())
               EventHandler::call($event);

            $finished[] = $queuedEvent->setDone((round(microtime(true) - $eventStarted, 4) * 100), $startTime)->id;
         } catch(Exception|Error $e) {
            Debugger::log($e, ILogger::ERROR);

            if(self::$development)
               throw $e;

            $queuedEvent->setError(
               bcmul(round(microtime(true) - $eventStarted, 4), '100'),
               json_encode([
                  'message' => $e->getMessage(),
                  'file' => $e->getFile(),
                  'line' => $e->getLine(),
                  'trace' => $e->getTrace(),
               ], JSON_UNESCAPED_UNICODE),
            );
         }
      }

      if($counter > 0)
         file_put_contents(self::getTodayStackLog(), sprintf(
               '[%s] Time: %dms | Finished: %d from %d | Errors: %d | Stack: [%s]',
               date('Y-m-d H:i:s'),
               round(microtime(true) - $start, 4) * 100,
               $cFinished = count($finished),
               $counter,
               $counter - $cFinished,
               implode(',', $finished)
            ) . PHP_EOL, FILE_APPEND);
   }

   public static function getTodayStackLog() :string {
      static $log;

      if(isset($log))
         return $log;

      Files::checkDir($dir = sprintf('%s/%s/%s', Files::prepareRootDirPath(Files::LOG_DIR . '/events'), date('Y'), date('m')));

      return $log = sprintf(
         '%s/%s',
         $dir,
         sprintf('finished_%s.log', date('Y_m_d')),
      );
   }

   /** @return EventQueueRow[]|Fluent */
   public static function find() :Fluent|array {
      return dibi::select('%n.*', self::ALIAS)
         ->from(self::TABLE, self::ALIAS)
         ->setupResult('setRowClass', EventQueueRow::class);
   }

   /** @param QueueStatus[] $allowed_statuses */
   public static function getForParamHash(string $class, string $hash, array $allowed_statuses = []) :?EventQueueRow {
      $q = self::find()
         ->where('%n.class = %s', self::ALIAS, $class)
         ->where('%n.params_hash = %s', self::ALIAS, $hash);

      if(!empty($allowed_statuses))
         $q->where('%n.status IN %in', self::ALIAS, QueueStatus::getValues($allowed_statuses));

      return $q->fetch();
   }

   /** @return EventQueueRow[] */
   public static function getUnfinished(?int $eventLimit = null) :array {
      return self::find()
         ->where('%n.status = %i', self::ALIAS, QueueStatus::CREATED->value)
         ->fetchAll(limit: $eventLimit);
   }

   /** @return EventQueueRow[] */
   public static function getAll(?int $eventLimit = null) :array {
      return self::find()
         ->fetchAll(limit: $eventLimit);
   }

   public static function save(EventQueueRow $row) :EventQueueRow {
      if($row->id?? false){
         dibi::update(self::TABLE, $row->toUpdateArray())
            ->where('id = %i', $row->id)
            ->execute();
      } else{
         dibi::insert(self::TABLE, $row->toUpdateArray())
            ->execute();
         $row->id = dibi::getInsertId();
      }
      return $row;
   }

   public static function setStatus(array $ids, QueueStatus $status) :int {
      return dibi::update(self::TABLE, [
         'status' => $status,
      ])->where('id IN %in', $ids)
         ->execute()
         ->getRowCount();
   }

   public static function delete(EventQueueRow $row) :void {
      EventQueueError::delete($row->id);
      dibi::delete(self::TABLE)
         ->where('id = %i', $row->id)
         ->execute();
   }
}