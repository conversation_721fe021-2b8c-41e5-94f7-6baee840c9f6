<?php namespace app\system\cookies;

use app\system\Cookies;
use dibi;
use Dibi\DateTime;
use <PERSON><PERSON>\Fluent;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 13.12.2023 */
class CookieSouhlasy
{

   const TABLE = 'cookie_souhlasy';

   /** @return CookieSouhlasRow[]|Fluent */
   public static function find() :?Fluent {
      return dibi::select('cs.*')
         ->from(self::TABLE, 'cs')
         ->setupResult('setRowClass', CookieSouhlasRow::class);
   }

   public static function get(int $id) :?CookieSouhlasRow {
      return self::find()->where('cs.id = %i', $id)->fetch();
   }

   /** @param CookieSouhlasRow[] $rows */
   public static function saveMultiple(array $rows) :void {
      if($rows)
         dibi::query('INSERT INTO %n %ex', self::TABLE, $rows);
   }

   public static function hasFilledSettings() :bool {
      return !empty($_COOKIE[self::COOKIES_ACCEPTED]);
   }

   public static function getTypyAccepted() :array {
      return json_decode($_COOKIE[self::COOKIES_ACCEPTED] ?? '[]', true);
   }

   public static function hasTypyAccepted(CookieTypy ...$typy) :bool {
      return !array_diff(CookieTypy::getValues($typy), self::getTypyAccepted());
   }

   public static function saveTypy(array $typy) :void {
      $_COOKIE[self::COOKIES_ACCEPTED] = json_encode($typy);
      Cookies::set(
         self::COOKIES_ACCEPTED,
         $_COOKIE[self::COOKIES_ACCEPTED],
         self::getExpireAt()->getTimestamp() - time(),
         sameSite: 'None'
      );

      dibi::query('DELETE FROM %n WHERE id_session = %s', self::TABLE, session_id());

      $rows = array();

      foreach($typy as $typ){
         $row = CookieSouhlasRow::getFor(CookieTypy::from($typ));
         $row->expire_at = self::getExpireAt();
         $rows[] = $row;
      }

      if(!$rows){
         $row = CookieSouhlasRow::getFor(CookieTypy::ZADNE);
         $row->expire_at = self::getExpireAt();
         $rows[] = $row;
      }

      CookieSouhlasy::saveMultiple($rows);
   }

   public static function delete(CookieSouhlasRow $row) :?int {
      return dibi::delete(self::TABLE)->where('id = %i', $row->id)->execute();
   }

   public static function deleteDeprecated() {
      return dibi::delete(self::TABLE)->where('platnost_do < NOW()')->execute();
   }

   protected static function getExpireAt() :DateTime {
      static $platnost;
      return $platnost ??= (new DateTime())->add(new \DateInterval('P3M'));
   }

   protected const COOKIES_ACCEPTED = 'qvamp-client-cookies';
}