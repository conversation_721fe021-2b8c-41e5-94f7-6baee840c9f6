<?php namespace app\system\helpers\telefon\input;

use app\System;
use app\system\component\Component;
use app\system\model\staty\telefon\StatPredvolbaRow;
use app\system\model\staty\telefon\StatTelefonPatterns;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 10.01.2024 */
class TelefonInputComponent extends Component
{


   public static function getInput(string $inputName, ?string $prefix = null, ?string $telefon = null, bool $required = true, bool $hasLabel = true, ?string $placeholder = null) :Html {
      $component = self::getComponent()
         ->setInputName($inputName, $required)
         ->setTelefonValue($prefix, $telefon)
         ->setHasLabel($hasLabel)
         ->setInitOnReady(true)
         ->setPlaceholder($placeholder);

      return new Html($component->prepareTemplater(cache: false)->render());
   }

   function setData() :array {
      $this->defaultStat = StatTelefonPatterns::getForStat(System::get()->getApplication()->environment->getStat()->id);

      $this->data = [
         'predvolby' => StatTelefonPatterns::getAll(),
         'inputName' => $this->inputName,
         'required' => $this->required,
         'hasLabel' => $this->hasLabel,
         'placeholder' => $this->placeholder,
         'initOnReady' => $this->initOnReady,
      ];

      $this->data['prefixInputName'] = 'prefix' . ucfirst($this->inputName);
      $this->appendTelefon();

      return $this->data;
   }

   public function setInputName(string $inputName, bool $required = true) :static {
      $this->inputName = $inputName;
      $this->required = $required;
      return $this;
   }

   public function setTelefonValue(?string $prefix, ?string $telefon) :static {
      $this->telefon = $telefon ? new TelefonValue($prefix, $telefon) : null;
      return $this;
   }

   public function setTelefonClass(TelefonValue $telefon) :static {
      $this->telefon = $telefon;
      return $this;
   }

   public function setHasLabel(bool $hasLabel) :TelefonInputComponent {
      $this->hasLabel = $hasLabel;
      return $this;
   }

   public function setPlaceholder(?string $placeholder) :TelefonInputComponent {
      $this->placeholder = $placeholder;
      return $this;
   }

   public function setInitOnReady(bool $initOnReady) :static {
      $this->initOnReady = $initOnReady;
      return $this;
   }

   private function appendTelefon() :void {
      $this->data['defaultStat'] = $this->defaultStat;
      $this->data['telefon'] = $this->telefon ?? null;
   }

   private bool $initOnReady = true;
   private string $inputName = 'telefon';
   private ?string $placeholder = null;
   private StatPredvolbaRow $defaultStat;
   private ?TelefonValue $telefon;
   private bool $required;
   private bool $hasLabel = true;
}