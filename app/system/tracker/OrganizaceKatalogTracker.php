<?php namespace app\system\tracker;

use app\System;
use app\system\tracker\data\OrganizaceKatalogTrackerDailyRow;
use app\system\tracker\data\OrganizaceKatalogTrackerModel;
use app\system\tracker\secret\TrackerSecretStore;
use dibi;
use Dibi\DateTime;
use Symfony\Component\HttpFoundation\ServerBag;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 27.08.2025 */
class OrganizaceKatalogTracker
{

   public static function trackPage(int $organizaceID, TrackerPageType $pageType) :void {
      (new self($organizaceID, $pageType))->track();
   }

   public static function cronRollupAndRotate() :void {
      self::getSecretStore()->rotateSecret();

      dibi::delete(OrganizaceKatalogTrackerModel::TABLE_DAILY_UNIQUE)
         ->where('day < %d', (new \DateTimeImmutable('now'))->modify('-8 days')->format('Y-m-d'))
         ->execute();
   }

   private static function getSecretStore() :TrackerSecretStore {
      static $store;
      return $store ??= new TrackerSecretStore();
   }

   public function __construct(
      private readonly int             $organizaceID,
      private readonly TrackerPageType $pageType,
      private readonly bool            $enableDailyUniques = true,
   ) {
      $this->server = System::get()->getRequest()->server;
      $this->appSecret = self::getSecretStore()->getSecret();
   }

   public function track() :void {
      if(System::get()->detection->isBot) return;

      $this->incrementDailyVisit();

      if($this->enableDailyUniques){
         $this->incrementDailyUnique();
      }
   }

   protected function incrementDailyVisit() :void {
      $dailyRow = new OrganizaceKatalogTrackerDailyRow();
      $dailyRow->id_organizace = $this->organizaceID;
      $dailyRow->page_key = $this->pageType->value;
      $dailyRow->day = new DateTime();
      $dailyRow->views = 1;

      dibi::insert(OrganizaceKatalogTrackerModel::TABLE_DAILY, $dailyRow->toArray())
         ->on('DUPLICATE KEY UPDATE views = views + 1')
         ->execute();
   }

   protected function incrementDailyUnique() :void {
      $day = gmdate('Y-m-d');
      $hash = $this->dailyHash();

      dibi::query(
         'INSERT IGNORE INTO %n %v',
         OrganizaceKatalogTrackerModel::TABLE_DAILY_UNIQUE,
         [
            'day%d' => $day,
            'id_organizace%i' => $this->organizaceID,
            'page_key%s' => $this->pageType->value,
            'hash%bin' => $hash,
         ]
      );

      if(dibi::getAffectedRows() === 0)
         return;

      dibi::update(OrganizaceKatalogTrackerModel::TABLE_DAILY, [
         'unique%sql' => '`unique` + 1'
      ])->where('id_organizace = %i', $this->organizaceID)
         ->where('page_key = %s', $this->pageType->value)
         ->where('day = CURRENT_DATE()')
         ->execute();
   }

   private function dailyHash() :string {
      $coarseIP = $this->getCoarseIP(
         $this->getClientIp()
      );

      $uaMajor = $this->getUaMajor();
      $day = gmdate('Y-m-d');

      return hash_hmac(
         'sha256',
         $day . '|' . $coarseIP . '|' . $uaMajor . '|' . $this->organizaceID . '|' . $this->pageType->value,
         $this->getDailySalt($day),
         true
      );
   }

   private function getClientIp() :?string {
      if($xff = $this->server->getString('HTTP_X_FORWARDED_FOR')){
         $parts = array_map('trim', explode(',', $xff));
         $ip = end($parts);
         if($ip && filter_var($ip, FILTER_VALIDATE_IP)) return $ip;
      }

      $ip = $this->server->get('REMOTE_ADDR');
      return (filter_var($ip, FILTER_VALIDATE_IP))? $ip: null;
   }

   private function getCoarseIP(?string $clientIP) :string {
      if($clientIP) return '';

      if(filter_var($clientIP, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)){
         $packed = @inet_pton($clientIP);
         if($packed === false) return '';
         $hex = bin2hex($packed);  // 32 hex
         return substr($hex, 0, 12); // ~/48
      }

      if(filter_var($clientIP, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)){
         $parts = explode('.', $clientIP);
         $parts[3] = '0';
         return implode('.', $parts); // /24
      }
      return '';
   }

   private function getUaMajor() :string {
      if(
         !($ua = $this->server->getString('HTTP_USER_AGENT'))
         || !preg_match('~([A-Za-z]+)[/ ](\d{1,3})~', $ua, $m)
      ){
         return 'UA/0';
      }

      return $m[1] . '/' . $m[2];
   }

   private function getDailySalt(string $day) :string {
      return hash_hmac('sha256', $day, $this->appSecret, true);
   }

   private ServerBag $server;
   private string $appSecret;
}