<?php namespace app\system\tracker\data;

use Di<PERSON>\Fluent;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 28.08.2025 */
class OrganizaceKatalogTrackerModel
{

   const string TABLE_DAILY = 'organizace_katalog_tracker_daily';
   const string TABLE_DAILY_UNIQUE = 'organizace_katalog_tracker_daily_unique';

   /** @return Fluent|OrganizaceKatalogTrackerDailyRow[] */
   public static function find() :Fluent|array {
      return \dibi::select('oktd.*')
         ->from(self::TABLE_DAILY, 'oktd')
         ->setupResult('setRowClass', OrganizaceKatalogTrackerDailyRow::class);
   }
}