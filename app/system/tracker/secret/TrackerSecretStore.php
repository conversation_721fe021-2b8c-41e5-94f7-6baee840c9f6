<?php namespace app\system\tracker\secret;

use app\system\helpers\Files;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 28.08.2025 */
class TrackerSecretStore
{

   private const string SECRET_FILE = 'temp/.tracker-secret';

   public function __construct(
      private readonly int $keyBytes = 32
   ) {
      $this->path = Files::prepareRootDirPath(self::SECRET_FILE);
   }

   public function getSecret(bool $asBinary = true) :string {
      $data = $this->readOrCreate();
      return $asBinary? base64_decode($data['key'], true): $data['key'];
   }

   public function rotateSecret() :string {
      $now = new \DateTimeImmutable('now');
      $today = $now->format('Y-m-d');

      $data = $this->readFile();
      if($data === null || ($data['date'] ?? '') !== $today){
         $data = [
            'date' => $today,
            'key' => base64_encode(random_bytes($this->keyBytes)),
         ];
         $this->atomicWrite($data);
      }

      return base64_decode($data['key'], true);
   }

   private function readOrCreate() :array {
      $data = $this->readFile();
      if($data) return $data;

      $data = [
         'date' => (new \DateTimeImmutable('now'))->format('Y-m-d'),
         'key' => base64_encode(random_bytes($this->keyBytes)),
      ];
      $this->atomicWrite($data);
      return $data;
   }

   private function readFile() :?array {
      if(!is_file($this->path)) return null;

      $fp = @fopen($this->path, 'rb');
      if(!$fp) return null;

      try{
         @flock($fp, LOCK_SH);
         $json = stream_get_contents($fp);
         @flock($fp, LOCK_UN);
      } finally {
         fclose($fp);
      }

      $arr = @json_decode((string)$json, true);
      if(!is_array($arr) || !isset($arr['date'], $arr['key'])) return null;

      $raw = base64_decode((string)$arr['key'], true);
      if($raw === false || strlen($raw) !== $this->keyBytes) return null;

      return $arr;
   }

   private function atomicWrite(array $data) :void {
      $dir = dirname($this->path);
      if(!is_dir($dir)){
         if(!@mkdir($dir, 0700, true) && !is_dir($dir)){
            throw new \RuntimeException("Cannot create dir: $dir");
         }
      }

      $tmp = $this->path . '.tmp.' . bin2hex(random_bytes(4));
      $json = json_encode($data, JSON_UNESCAPED_SLASHES);

      file_put_contents($tmp, $json, LOCK_EX);
      @chmod($tmp, 0600);

      if(!@rename($tmp, $this->path)){
         @unlink($tmp);
         throw new \RuntimeException("Atomic rename failed to {$this->path}");
      }
   }

   private string $path;
}