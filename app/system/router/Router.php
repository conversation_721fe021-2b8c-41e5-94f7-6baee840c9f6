<?php namespace app\system\router;

use app\system\application\IEnvironment;
use app\system\model\translator\Jazyky;
use app\system\Redirect;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 16.12.2023 */
class Router
{

   public readonly Jazyky $jazyk;

   public function __construct(IEnvironment $environment) {
      $this->jazyk = $environment->getJazyk();
   }

   public function getRouteContainer() :RoutesContainer {
      return $this->container ??= new RoutesContainer();
   }

   public function getRoute() :?Route {
      return $this->matchedRoute ?? null;
   }

   public function getControllerName() :string {
      $controllerClass = $this->getRoute()->getAction()->getDeclaringClass();
      return $controllerClass->getName();
   }

   public function matchRoute(string $realPath) :?Route {
      try{
         foreach($this->getRouteContainer()->getRoutes() as $route){
            if($route->isMatchingPattern($realPath, $this->jazyk))
               return $this->matchedRoute = $route;
         }
      }catch(DifferentLanguageRouteException $routeException){
         $route = $routeException->getRoute();
         Redirect::to($route->getUrl($this->jazyk)->setAssocParameters($route->getParametrs()));
      }

      return null;
   }

   public function getUrl(string $controllerClass, int|string ...$parameters) :RouteUrlFactory {
      $route = $this->getOrCreateRoute($controllerClass);

      if($route === null)
         throw new \Exception('Nenalezená routa u controlleru ' . $controllerClass);

      return $route->getUrl($this->jazyk, ...$parameters);
   }

   /** @return RouteUrlFactory[] */
   public function getAllUrlMutations(string $controllerClass, int|string ...$parameters) :array {
      $route = $this->getOrCreateRoute($controllerClass);

      return $route->getAllUrls(...$parameters);
   }

   private function getOrCreateRoute(string $controllerClass) :?Route {
      if($route = $this->getRouteContainer()->getRoute($controllerClass))
         return $route;

      $this->getRouteContainer()->addControllers($controllerClass);

      return $this->getRouteContainer()->getRoute($controllerClass);
   }

   private Route $matchedRoute;
   private RoutesContainer $container;
}