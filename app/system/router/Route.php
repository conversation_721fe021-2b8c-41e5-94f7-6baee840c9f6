<?php namespace app\system\router;

use app\system\helpers\ControllerReflectionMethod;
use app\system\model\translator\Jazyky;
use Attribute;
use Exception;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 05.06.2022 */
#[Attribute]
class Route
{

   /**
    * @param string|array $patterns
    * @throws Exception
    */
   public function __construct(string|array $patterns) {
      if(is_array($patterns) && !isset($patterns[Jazyky::EN->getISO6391()]))
         throw new Exception(sprintf('U lokalizované routy je povinný jazyk %s', Jazyky::EN->getTitle()));

      $this->pattern = $patterns;
   }

   public function setAction(ControllerReflectionMethod $controllerAction) :static {
      $this->action = $controllerAction;
      return $this;
   }

   public function getAction() :ControllerReflectionMethod {
      return $this->action;
   }

   public function getRoutePattern() :string|array {
      return $this->pattern;
   }

   public function setParametr(string $key, int|string $value) :static {
      $this->parametrs[$key] = $value;
      return $this;
   }

   public function getParametrs() :array {
      return $this->parametrs;
   }

   public function getLabel() :string {
      return sprintf('%s::%s()',
         $this->action->getDeclaringClass()->getShortName(),
         $this->action->getName(),
      );
   }

   public function getUrl(?Jazyky $jazyk = null, int|string ...$parameters) :RouteUrlFactory {
      return (new RouteUrlFactory($this))
         ->setJazyk($jazyk)
         ->setParameters(...$parameters);
   }

   /**
    * @param int|string ...$parameters
    * @return array
    */
   public function getAllUrls(int|string ...$parameters) :array {
      if(is_string($this->pattern)){
         return [
            (new RouteUrlFactory($this))
               ->setParameters(...$parameters)
               ->__toString(),
         ];
      }

      $return = [];

      foreach($this->pattern as $langIso => $pattern){
         $jazyk = Jazyky::tryFromISO($langIso);
         $return[$jazyk->getISO6391()] = (new RouteUrlFactory($this))
            ->setJazyk($jazyk)
            ->setParameters(...$parameters)
            ->__toString();
      }
       return $return;
   }

   public function getLocalized(Jazyky $jazyk) :?string {
      return $this->pattern[$jazyk->getISO6391()] ?? null;
   }

   public function getLocalizedWithFallback(Jazyky $jazyk) :string {
      return $this->getLocalized($jazyk) ?: $this->pattern[Jazyky::EN->getISO6391()];
   }

   /** @throws DifferentLanguageRouteException */
   public function isMatchingPattern(string $realPath, Jazyky $jazyk) :bool {
      if(!$this->isLocalized())
         return $this->checkPattern($this->pattern, $realPath);

      if($this->checkPattern($this->getLocalizedWithFallback($jazyk), $realPath))
         return true;

      foreach($this->pattern as $lang => $pattern){
         if($lang === $jazyk->getISO6391())
            continue;

         if($this->checkPattern($pattern, $realPath))
            throw (new DifferentLanguageRouteException())
               ->addRoute($this)
               ->langFromUrl($lang)
               ->langFromSystem($jazyk->getISO6391());
      }

      return false;
   }

   public function isLocalized() :bool {
      return is_array($this->pattern);
   }

   private function checkPattern(string $pattern, string $realPath) :bool {
      if(preg_match($this->formatPattern($pattern), $realPath, $matches)){
         foreach($matches as $key => $value)
            if(is_string($key))
               $this->setParametr($key, $value);

         return true;
      }

      return false;
   }

   private function formatPattern(string $pattern) :string {
      return sprintf('!%s!', $pattern);
   }

   protected string|array $pattern;
   protected array $parametrs = [];
   protected ControllerReflectionMethod $action;
}