<?php namespace app\system\mailer\templates\properties\groups;

use app\system\mailer\templates\properties\BasePropertiesGroup;
use app\system\mailer\templates\properties\EmailProperty;
use app\system\mailer\templates\properties\EmailPropertyGroup;
use app\system\model\event\BaseEventRow;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 17.09.2024 */
#[EmailPropertyGroup(self::PREFIX, 'Event')]
class EventProperties extends BasePropertiesGroup
{

   private const string PREFIX = 'EVENT';

   #[EmailProperty('Název')]
   public readonly string $name;

   #[EmailProperty('Datum')]
   public readonly string $date;

   #[EmailProperty('Datum začátek')]
   public readonly string $date_start;

   #[EmailProperty('Datum konec')]
   public readonly string $date_end;

   #[EmailProperty('Typ eventu')]
   public readonly string $type;

   public function __construct(
      private readonly BaseEventRow $event
   ) {
      $this->name = $this->event->nazev ?: $this->event->getDefaultNazev();
      $this->date = $this->event->getDatumString();
      $this->date_start = $this->event->date_start->format('j.n.Y H:i');
      $this->date_end = $this->event->date_end->format('j.n.Y H:i');
      $this->type = $this->event->getTypEventu()?->nazev_eventu ?: '';
   }
}