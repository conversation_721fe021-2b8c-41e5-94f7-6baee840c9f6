<?php namespace app\system\mailer\templates\properties\groups;

use app\system\mailer\templates\properties\BasePropertiesGroup;
use app\system\mailer\templates\properties\EmailProperty;
use app\system\mailer\templates\properties\EmailPropertyGroup;
use app\system\model\katalog\zakaznici\verify\KatalogExistZakaznikCodeRow;
use app\system\model\organizace\zakaznici\keys\OrganizaceZakaznikKeysRow;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 16.09.2024 */
#[EmailPropertyGroup(self::PREFIX, 'Klientský portál')]
class ClientCodeProperties extends BasePropertiesGroup
{

   private const string PREFIX = 'CODE';

   #[EmailProperty('Kód pro přihlášení')]
   public readonly string $verification;

   public function __construct(
      private readonly OrganizaceZakaznikKeysRow|KatalogExistZakaznikCodeRow $session
   ) {
      $this->verification = $this->session->code;
   }
}