<?php namespace app\system\mailer\templates;

use app\front\organizace\model\organizace\email\CreatedKatalogOrganizaceEmail;
use app\front\organizace\model\organizace\email\CreatedOrganizaceEmail;
use app\front\uzivatel\email\UzivatelEmailZmenaEmail;
use app\front\uzivatel\email\UzivatelZmenaHeslaEmail;
use app\system\chat\email\NovaZpravaChatEmail;
use app\system\model\event\email\NovyEventEmail;
use app\system\model\formular\email\FormularVyplneniEmail;
use app\system\model\katalog\zakaznici\email\RegistraceKatalogZakaznikEmail;
use app\system\model\leady\lead\email\NovaPoptavkaEmail;
use app\system\model\leady\lead\email\ZamitnutaPoptavkaEmail;
use app\system\model\nabidka\email\OdeslanaNabidkaEmail;
use app\system\model\nabidka\email\PlatnostNabidkyEmail;
use app\system\model\nabidka\email\ZmenyNabidkyEmail;
use app\system\model\nabidka\smlouva\email\DigisignMessageEmail;
use app\system\model\organizace\personal\email\InvitePersonalEmail;
use app\system\model\organizace\subscription\email\BuySubscriptionEmail;
use app\system\model\organizace\subscription\email\FakturaSubscriptionEmail;
use app\system\model\organizace\zakaznici\email\VerifikaceExistZakaznikEmail;
use app\system\model\organizace\zakaznici\email\VerifikacePrihlaseniZakaznikaEmail;
use app\system\notifikace\NotifikaceEmail;

/** Created by Kryštof Czyź. Date: 02.09.2024 */
enum Templates :string
{

   case MISTO_NOVE = 'misto-nove';
   case SUBSCRIPTION_ZAKOUPENI = 'subscription-zakoupeni';
   case NOTIFIKACE = 'notifikace';
   case ZMENA_NABIDKA = 'zmena-nabidka';
   case ZAMITNUTA_POPTAVKA = 'zamitnuta-poptavka';
   case USER_INVITE = 'user-invite';
   case CUSTOMER_INVITE = 'customer-invite';
   case FORMULAR_K_VYPLNENI = 'formular-k-vyplneni';
   case ZMENA_HESLA = 'zmena-hesla';
   case ZMENA_EMAIL = 'zmena-email';
   case RECIEVED_LEAD = 'recieved-lead';
   case SENDED_OFFER = 'sended-offer';
   case DIGISIGN_EMAIL = 'digisign-email';
   case PLATNOST_NABIDKY_UPOZORNENI = 'platnost-nabidky-upozorneni';
   case NEW_EVENT = 'new-event';
   case NEW_CHAT_MESSAGE = 'new-chat-message';
   case ORGANIZACE_KATALOG_NOVE = 'organizace-katalog-nove';
   case SUBSCRIPTION_FAKTURA = 'subscription-faktura';
   case VER_EXIST_ZAKAZNIK = 'ver-exist-zakaznik';
   case REGISTRACE_ZAKAZNIK = 'katalog-registrace-zakaznik';

   public function getClass() :string {
      return match ($this) {
         self::MISTO_NOVE => CreatedOrganizaceEmail::class,
         self::SUBSCRIPTION_ZAKOUPENI => BuySubscriptionEmail::class,
         self::NOTIFIKACE => NotifikaceEmail::class,
         self::ZMENA_NABIDKA => ZmenyNabidkyEmail::class,
         self::ZAMITNUTA_POPTAVKA => ZamitnutaPoptavkaEmail::class,
         self::USER_INVITE => InvitePersonalEmail::class,
         self::CUSTOMER_INVITE => VerifikacePrihlaseniZakaznikaEmail::class,
         self::FORMULAR_K_VYPLNENI => FormularVyplneniEmail::class,
         self::ZMENA_HESLA => UzivatelZmenaHeslaEmail::class,
         self::ZMENA_EMAIL => UzivatelEmailZmenaEmail::class,
         self::RECIEVED_LEAD => NovaPoptavkaEmail::class,
         self::SENDED_OFFER => OdeslanaNabidkaEmail::class,
         self::DIGISIGN_EMAIL => DigisignMessageEmail::class,
         self::PLATNOST_NABIDKY_UPOZORNENI => PlatnostNabidkyEmail::class,
         self::NEW_EVENT => NovyEventEmail::class,
         self::NEW_CHAT_MESSAGE => NovaZpravaChatEmail::class,
         self::ORGANIZACE_KATALOG_NOVE => CreatedKatalogOrganizaceEmail::class,
         self::SUBSCRIPTION_FAKTURA => FakturaSubscriptionEmail::class,
         self::VER_EXIST_ZAKAZNIK => VerifikaceExistZakaznikEmail::class,
         self::REGISTRACE_ZAKAZNIK => RegistraceKatalogZakaznikEmail::class,
      };
   }

   public function getInstance() :BaseEmail {
      $className = $this->getClass();

      return new $className();
   }

   /**
    * @param string[] $mailCodes
    * @return string[]
    */
   public static function getRest(array $mailCodes) :array {
      $rest = [];

      foreach(self::cases() as $template){
         if(!in_array($template->value, $mailCodes))
            $rest[] = $template->value;
      }

      return $rest;
   }

   /** @param static[] $enums */
   public static function getValues(array $enums) :array {
      $r = [];

      foreach($enums as $item)
         $r[] = $item->value;

      return $r;
   }
}
