<?php namespace app\system\migration\version;

use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\system\Environment;
use app\system\mailer\templates\create\MailTemplateCreator;
use app\system\mailer\templates\Templates;
use app\system\migration\Migrations;
use app\system\model\organizace\fakturace\FakturacniAdresa;
use app\system\model\organizace\fakturace\FakturacniAdresaRow;
use app\system\model\organizace\kontakt\OrganizaceKontaktRow;
use app\system\model\organizace\meny\Meny;
use app\system\model\organizace\meny\OrganizaceMenaAdder;
use app\system\model\organizace\nastaveni\ChybejiciNastaveni;
use app\system\model\organizace\nastaveni\ChybejiciNastaveniTyp;
use app\system\model\organizace\personal\Personal;
use app\system\model\organizace\subscription\event\SubscriptionActivateEvent;
use app\system\model\organizace\subscription\MistoSubscription;
use app\system\model\organizace\subscription\OrganizaceLicenceTyp;
use app\system\model\uzivatele\session\data\OrganizationLicenceData;
use app\system\model\uzivatele\session\data\UserData;
use app\system\model\uzivatele\Uzivatel;
use dibi;

/** Created by Kryštof Czyź. Date: 09.02.2025 */
class MigrationKatalog extends Migrations
{

   public function prepare() :void {
      $this->resolver->add(250908.134605, function() {
         MailTemplateCreator::create()
            ->setCode(Templates::REGISTRACE_ZAKAZNIK)
            ->setTitle('Registrace nového zákazníka')
            ->save();
      });

      $this->resolver->add(250903.181626, function() {
         dibi::query('CREATE TABLE `katalog_zakaznik_telefon` (
           `id` int(11) NOT NULL AUTO_INCREMENT,
           `id_zakaznik` int(11) NOT NULL,
           `prefix` varchar(5) DEFAULT NULL,
           `telefon` varchar(30) NOT NULL,
           `is_primarni` tinyint(1) NOT NULL DEFAULT 1,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           `updated` timestamp NOT NULL DEFAULT current_timestamp(),
           PRIMARY KEY (`id`),
           KEY `id_zakaznik` (`id_zakaznik`),
           CONSTRAINT `katalog_zakaznik_telefon_ibfk_1` FOREIGN KEY (`id_zakaznik`) REFERENCES `katalog_zakaznik` (`id_zakaznik`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250903.174405, function() {
         MailTemplateCreator::create()
            ->setCode(Templates::VER_EXIST_ZAKAZNIK)
            ->setTitle('Ověření emailu')
            ->save();
      });

      $this->resolver->add(250903.163131, function() {
         dibi::query('CREATE TABLE `katalog_exist_zakaznik_code` (
           `id` int NOT NULL AUTO_INCREMENT,
           `id_zakaznik` int NOT NULL,
           `code` varchar(6) COLLATE utf8mb4_general_ci NOT NULL,
           `expired` date NOT NULL,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           PRIMARY KEY (`id`),
           KEY `id_zakaznik` (`id_zakaznik`),
           CONSTRAINT `katalog_exist_zakaznik_code_ibfk_1` FOREIGN KEY (`id_zakaznik`) REFERENCES `zakaznici` (`id_zakaznik`)
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250828.140306, function() {
         dibi::query('CREATE TABLE `organizace_katalog_tracker_daily` (
           `id_organizace` int NOT NULL,
           `page_key` varchar(64) COLLATE utf8mb4_general_ci NOT NULL,
           `day` date NOT NULL,
           `views` int NOT NULL DEFAULT 0,
           `unique` int NOT NULL DEFAULT 0,
           PRIMARY KEY (`id_organizace`,`page_key`,`day`),
           KEY `day_idx` (`day`) USING BTREE,
           KEY `org_page_idx` (`id_organizace`,`page_key`) USING BTREE,
           CONSTRAINT `organizace_katalog_tracker_daily_ibfk_1` FOREIGN KEY (`id_organizace`) REFERENCES `organizace` (`id_organizace`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');

         dibi::query('CREATE TABLE `organizace_katalog_tracker_daily_unique` (
           `id_organizace` int NOT NULL,
           `page_key` varchar(64) COLLATE utf8mb4_general_ci NOT NULL,
           `day` date NOT NULL,
           `hash` binary(32) NOT NULL,
           PRIMARY KEY (`id_organizace`,`page_key`,`day`,`hash`)
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250828.112005, function() {
         if(!Environment::isProduction())
            return;

         $org = Organizace::getMisto(91);

         $sub = MistoSubscription::create(
            $org,
            OrganizaceLicenceTyp::from($org->licence_typ)->getSubscriptionPolozka($org->getTypOrganizace()),
         );

         SubscriptionActivateEvent::renew($sub, notification: false);

         ChybejiciNastaveni::delete(
            $org->id_organizace,
            ChybejiciNastaveniTyp::ZAKLADNI_KATALOG->value
         );

         OrganizationLicenceData::get(91)?->delete();
      });

      $this->resolver->add(250825.171025, function() {
         if(!Environment::isProduction())
            return;

         Organizace::deleteUncompleted(89);
         OrganizationLicenceData::get(89)?->delete();

         UserData::get(169)?->delete();
      });

      $this->resolver->add(250825.164225, function() {
         /** @var OrganizaceRow[] $organizace */
         $organizace = Organizace::find()
            ->where('m.licence_typ = 2')
            ->fetchAll();

         foreach ($organizace as $organizaceRow) {
            if(Personal::getByUzivatelMisto($organizaceRow->id_uzivatel, $organizaceRow->id_organizace))
               continue;

            Personal::createFromUzivatel(Uzivatel::get($organizaceRow->id_uzivatel), $organizaceRow->id_organizace);
            UserData::get($organizaceRow->id_uzivatel)?->delete();
         }
      });

      $this->resolver->add(250823.182827, function() {
         dibi::query('ALTER TABLE `leady` ADD COLUMN `id_system_event_type` int NULL DEFAULT NULL AFTER `id_event_type`;');
         dibi::query('ALTER TABLE `leady` ADD FOREIGN KEY (`id_system_event_type`) REFERENCES `system_katalog_akce_typ` (`id`) ON DELETE SET DEFAULT;');
         dibi::query('ALTER TABLE `leady` CHANGE `id_event_type` `id_event_type` int NULL;');

         dibi::query('ALTER TABLE `dodavatele_poptavky` ADD COLUMN `id_system_event_type` int NULL DEFAULT NULL AFTER `id_event_type`;');
         dibi::query('ALTER TABLE `dodavatele_poptavky` ADD FOREIGN KEY (`id_system_event_type`) REFERENCES `system_katalog_akce_typ` (`id`) ON DELETE SET DEFAULT;');
         dibi::query('ALTER TABLE `dodavatele_poptavky` CHANGE `id_event_type` `id_event_type` int NULL;');
      });

      $this->resolver->add(250821.151445, function() {
         if(!Environment::isProduction())
            return;

         $org = Organizace::getMisto(85);

         $sub = MistoSubscription::create(
            $org,
            OrganizaceLicenceTyp::from($org->licence_typ)->getSubscriptionPolozka($org->getTypOrganizace()),
         );

         SubscriptionActivateEvent::renew($sub, notification: false);

         ChybejiciNastaveni::delete(
            $org->id_organizace,
            ChybejiciNastaveniTyp::ZAKLADNI_KATALOG->value
         );

         OrganizationLicenceData::get(85)?->delete();
      });

      $this->resolver->add(250821.150631, function() {
         if(!Environment::isProduction())
            return;

//         Zapomenutý refresh z minulé migrace
         OrganizationLicenceData::get(83)?->delete();
         $mRow = new OrganizaceKontaktRow();
         $mRow->id_mista = 85;
         $mRow->ulice = 'Milady Horákové 19 , Seifertova 65';
         $mRow->mesto = 'Praha 3';
         $mRow->psc = '13000';
         $mRow->stat = 44;
         $mRow->save();

         $fakturacni = new FakturacniAdresaRow();
         $fakturacni->id_organizace = 85;
         $fakturacni->jmeno = 'H&Hotels s.r.o.';
         $fakturacni->ulice = 'Václavské náměstí 7';
         $fakturacni->mesto = 'Praha 1';
         $fakturacni->psc = '11000';
         $fakturacni->id_stat = 44;
         $fakturacni->ic = '45809534';
         $fakturacni->dic = 'CZ45809534';
         FakturacniAdresa::save($fakturacni);

         (new OrganizaceMenaAdder(85, Meny::CZK))->add();
      });

      $this->resolver->add(250820.195116, function() {
         if(!Environment::isProduction())
            return;

         $org = Organizace::getMisto(83);

         $sub = MistoSubscription::create(
            $org,
            OrganizaceLicenceTyp::from($org->licence_typ)->getSubscriptionPolozka($org->getTypOrganizace()),
         );

         SubscriptionActivateEvent::renew($sub, notification: false);

         ChybejiciNastaveni::delete(
            $org->id_organizace,
            ChybejiciNastaveniTyp::ZAKLADNI_KATALOG->value
         );

         OrganizationLicenceData::get(83)?->delete();
      });

      $this->resolver->add(250819.183245, function() {
         MailTemplateCreator::create()
            ->setCode(Templates::SUBSCRIPTION_FAKTURA)
            ->setTitle('Faktura za předplatné')
            ->save();
      });

      $this->resolver->add(250819.150106, function() {
         dibi::query('ALTER TABLE `misto_subscription`
            ADD COLUMN `payment_period` varchar(2) NOT NULL DEFAULT "1M" AFTER `uid`;');
      });

      $this->resolver->add(250818.132052, function() {
         MailTemplateCreator::create()
            ->setCode(Templates::ORGANIZACE_KATALOG_NOVE)
            ->setTitle('Nová organizace v katalogu')
            ->save();
      });

      $this->resolver->add(250813.105315, function () {
         dibi::query('DROP TABLE IF EXISTS `katalog_zakaznik_oblibene_organizace`');
         dibi::query('CREATE TABLE `katalog_zakaznik_oblibene_organizace` (
           `id_organizace` int(11) NOT NULL,
           `id_uzivatel` int(11) NOT NULL,
           `created` datetime NOT NULL DEFAULT current_timestamp(),
           PRIMARY KEY (`id_organizace`,`id_uzivatel`),
           KEY `id_uzivatel` (`id_uzivatel`),
           CONSTRAINT `katalog_zakaznik_oblibene_organizace_ibfk_1` FOREIGN KEY (`id_organizace`) REFERENCES `organizace` (`id_organizace`) ON DELETE CASCADE,
           CONSTRAINT `katalog_zakaznik_oblibene_organizace_ibfk_2` FOREIGN KEY (`id_uzivatel`) REFERENCES `katalog_zakaznik` (`id_zakaznik`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250809.101107, function () {
         dibi::query('ALTER TABLE `organizace_katalog_vlastnosti` DROP FOREIGN KEY `system_vlastnosti_organizace_ibfk_1`;');
         dibi::query('ALTER TABLE `organizace_katalog_vlastnosti` ADD FOREIGN KEY (`id_vlastnost`) REFERENCES `system_vlastnosti` (`id`) ON DELETE CASCADE;');

         dibi::query('ALTER TABLE `organizace_katalog_vlastnosti_mistnosti`  DROP FOREIGN KEY `organizace_katalog_vlastnosti_mistnosti_ibfk_2`;');
         dibi::query('ALTER TABLE `organizace_katalog_vlastnosti_mistnosti` ADD FOREIGN KEY (`id_vlastnost`) REFERENCES `system_vlastnosti` (`id`) ON DELETE CASCADE;');
      });

      $this->resolver->add(250710.115815, function () {
         dibi::query('CREATE TABLE `katalog_recenze_related_entity` (
           `id` int NOT NULL AUTO_INCREMENT,
           `id_recenze` int NOT NULL,
           `id_entity` int NOT NULL,
           `entity_version` int NOT NULL,
           `entity_type` varchar(10) NOT NULL,
           PRIMARY KEY (`id`),
           UNIQUE KEY `UNIQUE` (`id_recenze`,`id_entity`,`entity_version`,`entity_type`) USING BTREE,
           CONSTRAINT `katalog_recenze_related_entity_ibfk_1` FOREIGN KEY (`id_recenze`) REFERENCES `katalog_recenze` (`id`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250710.112016, function () {
         dibi::query('CREATE TABLE `katalog_recenze_odpovedi` (
           `id_otazka` int(11) NOT NULL,
           `id_recenze` int(11) NOT NULL,
           `body` int(11) NOT NULL,
           PRIMARY KEY (`id_otazka`,`id_recenze`),
           KEY `id_recenze` (`id_recenze`),
           CONSTRAINT `katalog_recenze_odpovedi_ibfk_3` FOREIGN KEY (`id_recenze`) REFERENCES `katalog_recenze` (`id`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250710.112015, function () {
         dibi::query('CREATE TABLE `katalog_recenze` (
           `id` int(11) NOT NULL AUTO_INCREMENT,
           `id_zakaznik` int(11) NOT NULL,
           `id_organizace` int(11) NOT NULL,
           `popisek_pozitivni` text DEFAULT NULL,
           `popisek_negativni` text DEFAULT NULL,
           `pocet_bodu` int(11) NOT NULL DEFAULT 0,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           `is_completed` int(11) NOT NULL DEFAULT 0,
           PRIMARY KEY (`id`),
           KEY `id_zakaznik` (`id_zakaznik`),
           KEY `id_organizace` (`id_organizace`),
           CONSTRAINT `katalog_recenze_ibfk_1` FOREIGN KEY (`id_zakaznik`) REFERENCES `organizace_zakaznici` (`id`) ON DELETE CASCADE,
           CONSTRAINT `katalog_recenze_ibfk_2` FOREIGN KEY (`id_organizace`) REFERENCES `organizace` (`id_organizace`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250509.013006, function () {
         dibi::query('CREATE TABLE `organizace_katalog_highlighty` (
           `id` int(11) NOT NULL AUTO_INCREMENT,
           `id_organizace` int(11) NOT NULL,
           `icon` varchar(25) NOT NULL,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           `updated` timestamp NOT NULL DEFAULT current_timestamp(),
           `id_jazyk` int(3) NOT NULL,
           `title` char(60) NOT NULL,
           `text` varchar(120) NOT NULL,
           PRIMARY KEY (`id`),
           KEY `id_organizace` (`id_organizace`),
           CONSTRAINT `organizace_katalog_highlighty_ibfk_1` FOREIGN KEY (`id_organizace`) REFERENCES `organizace` (`id_organizace`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250509.013004, function () {
         dibi::query('CREATE TABLE `organizace_katalog_otazky_odpoved` (
           `id_organizace_otazka` int(11) NOT NULL AUTO_INCREMENT,        
           `id_jazyk` int(11) NOT NULL,
           `odpoved` text NOT NULL,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           `updated` timestamp NOT NULL DEFAULT current_timestamp(),
           PRIMARY KEY (`id_organizace_otazka`,`id_jazyk`),
           CONSTRAINT `organizace_katalog_otazky_odpoved_ibfk_1` FOREIGN KEY (`id_organizace_otazka`) REFERENCES `organizace_katalog_otazky` (`id`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250509.013002, function () {
         dibi::query('CREATE TABLE `organizace_katalog_otazky` (
           `id` int(11) NOT NULL AUTO_INCREMENT,
            PRIMARY KEY (`id`),
           `id_organizace` int(11) NOT NULL,
           `id_otazka` int(11) NOT NULL,
           `aktivni` tinyint(1) NOT NULL DEFAULT 1,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
            KEY `id_organizace` (`id_organizace`),
            KEY `id_otazka` (`id_otazka`),
           CONSTRAINT `organizace_katalog_otazky_ibfk_1` FOREIGN KEY (`id_organizace`) REFERENCES `organizace` (`id_organizace`) ON DELETE CASCADE,
           CONSTRAINT `organizace_katalog_otazky_ibfk_2` FOREIGN KEY (`id_otazka`) REFERENCES `system_katalog_otazky` (`id`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250509.013001, function () {
         dibi::query('CREATE TABLE `system_katalog_otazky_preklady` (
           `id_otazka` int(11) NOT NULL,
           `id_jazyk` int(11) NOT NULL,
           `nazev` varchar(200) NOT NULL,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           `updated` timestamp NOT NULL DEFAULT current_timestamp(),
           PRIMARY KEY (`id_otazka`,`id_jazyk`),
           CONSTRAINT `system_katalog_otazky_preklady_ibfk_1` FOREIGN KEY (`id_otazka`) REFERENCES `system_katalog_otazky` (`id`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250509.013000, function () {
         dibi::query('CREATE TABLE `system_katalog_otazky` (
           `id` int(11) NOT NULL AUTO_INCREMENT,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           PRIMARY KEY (`id`)
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250508.110626, function () {
         dibi::query('CREATE TABLE `system_clanky_fotky` (
           `id` int(11) NOT NULL AUTO_INCREMENT,
           `id_clanek` int(11) NOT NULL,
           `src` varchar(255) NOT NULL,
           `poradi` int(11) NOT NULL,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           `updated` timestamp NOT NULL DEFAULT current_timestamp(),
           PRIMARY KEY (`id`),
           KEY `id_clanek` (`id_clanek`),
           CONSTRAINT `system_clanky_fotky_ibfk_1` FOREIGN KEY (`id_clanek`) REFERENCES `system_clanky` (`id`) ON DELETE CASCADE
         ) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250508.110620, function () {
         dibi::query('CREATE TABLE `system_clanky` (
           `id` int(11) NOT NULL AUTO_INCREMENT,
           `id_jazyk` tinyint(4) NOT NULL,
           `nazev` varchar(255) NOT NULL,
           `text` text,
           `meta_title` char(70) NULL DEFAULT NULL,
           `meta_description` char(170) NULL DEFAULT NULL,
           `meta_tag` char(255) NULL DEFAULT NULL,
           `html_content` text,
           `priority` int(11) NULL DEFAULT NULL,
           `published` timestamp NULL DEFAULT NULL,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           `updated` timestamp NOT NULL DEFAULT current_timestamp(),
           PRIMARY KEY (`id`)
         ) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250427.144620, function () {
         dibi::query('DROP TABLE IF EXISTS `katalog_zakaznik`');
         dibi::query('CREATE TABLE `katalog_zakaznik` (
           `id_zakaznik` int(11) NOT NULL,
           `full_name` varchar(160) NOT NULL,
           `id_jazyk` tinyint(3) NOT NULL DEFAULT 1,
           `password` varchar(140) DEFAULT NULL,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           `updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP,
           PRIMARY KEY (`id_zakaznik`)
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250427.144611, function () {
         dibi::query('CREATE TABLE `system_mesto_nazev` (
           `id_mesto` int(11) NOT NULL,
           `id_jazyk` int(11) NOT NULL,
           `nazev` varchar(100) NOT NULL,
           `nazev_url` varchar(100) NOT NULL,
           `created` datetime NOT NULL DEFAULT current_timestamp(),
           `updated` datetime NOT NULL DEFAULT current_timestamp(),
           PRIMARY KEY (`id_mesto`,`id_jazyk`),
           CONSTRAINT `system_mesto_nazev_ibfk_1` FOREIGN KEY (`id_mesto`) REFERENCES `system_mesta` (`id`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250427.144609, function () {
         dibi::query('CREATE TABLE `system_mesta` (
           `id` int(11) NOT NULL AUTO_INCREMENT,
           `id_region` int(11) NOT NULL DEFAULT 1,
           `created` datetime NOT NULL DEFAULT current_timestamp(),
           PRIMARY KEY (`id`)
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250425.160005, function () {
         dibi::query('CREATE TABLE `organizace_sluzba_fotky` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `id_sluzba` int(11) NOT NULL,
            `src` varchar(255) NOT NULL,
            `poradi` int(11) NOT NULL,
            `created` datetime DEFAULT current_timestamp(),
            `updated` datetime DEFAULT current_timestamp(),
            PRIMARY KEY (`id`),
            KEY `id_sluzba` (`id_sluzba`),
            CONSTRAINT `organizace_sluzba_fotky_ibfk_1` FOREIGN KEY (`id_sluzba`) REFERENCES `organizace_sluzby` (`id`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250425.160004, function () {
         dibi::query('CREATE TABLE `organizace_sluzba_akce_typ` (
           `id_akce` int(11) NOT NULL,
           `id_sluzba` int(11) NOT NULL,
           `created` datetime DEFAULT current_timestamp(),
           `updated` datetime DEFAULT current_timestamp(),
           PRIMARY KEY (`id_akce`,`id_sluzba`),
           KEY `id_sluzba` (`id_sluzba`),
           CONSTRAINT `organizace_sluzba_akce_typ_ibfk_1` FOREIGN KEY (`id_akce`) REFERENCES `system_katalog_akce_typ` (`id`) ON DELETE CASCADE,
           CONSTRAINT `organizace_sluzba_akce_typ_ibfk_2` FOREIGN KEY (`id_sluzba`) REFERENCES `organizace_sluzby` (`id`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250425.160003, function () {
         dibi::query('CREATE TABLE `organizace_sluzba_nazev` (
           `id_sluzba` int(11) NOT NULL,
           `id_jazyk` int(11) NOT NULL,
           `nazev` varchar(30) NOT NULL,
           `popis` text DEFAULT NULL,
           `created` datetime DEFAULT current_timestamp(),
           `updated` datetime DEFAULT current_timestamp(),
           PRIMARY KEY (`id_sluzba`,`id_jazyk`),
           CONSTRAINT `organizace_sluzba_nazev_ibfk_1` FOREIGN KEY (`id_sluzba`) REFERENCES `organizace_sluzby` (`id`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250425.160002, function () {
         dibi::query('CREATE TABLE `organizace_sluzby` (
           `id` int(11) NOT NULL AUTO_INCREMENT,
           `id_organizace` int(11) NOT NULL,
           `id_sluzba` int(11) NOT NULL,
           `created` datetime DEFAULT current_timestamp(),
           `updated` datetime DEFAULT current_timestamp(),
           PRIMARY KEY (`id`),
           KEY `id_sluzba` (`id_sluzba`),
           KEY `id_organizace` (`id_organizace`),
           CONSTRAINT `organizace_sluzby_ibfk_1` FOREIGN KEY (`id_sluzba`) REFERENCES `system_sluzby` (`id`) ON DELETE CASCADE,
           CONSTRAINT `organizace_sluzby_ibfk_2` FOREIGN KEY (`id_organizace`) REFERENCES `organizace` (`id_organizace`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250425.160001, function () {
         dibi::query('CREATE TABLE `system_sluzby_nazev` (
           `id_sluzba` int(11) NOT NULL,
           `id_jazyk` int(11) NOT NULL,
           `nazev` varchar(30) NOT NULL,
           `created` datetime DEFAULT current_timestamp(),
           `updated` datetime DEFAULT current_timestamp(),
           PRIMARY KEY (`id_sluzba`,`id_jazyk`),
           CONSTRAINT `system_sluzby_nazev_ibfk_1` FOREIGN KEY (`id_sluzba`) REFERENCES `system_sluzby` (`id`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250425.160000, function () {
         dibi::query('CREATE TABLE `system_sluzby` (
           `id` int(11) NOT NULL AUTO_INCREMENT,
           `version` tinyint(4) NOT NULL,
           `icon` varchar(25) NOT NULL,
           `show_search` tinyint(4) NOT NULL DEFAULT 0,
           `created` datetime DEFAULT current_timestamp(),
           `updated` datetime DEFAULT current_timestamp(),
           PRIMARY KEY (`id`)
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250325.204609, function () {
         dibi::query('CREATE TABLE `organizace_katalog_vlastnosti_mistnosti` (
           `id_prostor` int(11) NOT NULL,
           `id_vlastnost` int(11) NOT NULL,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           PRIMARY KEY (`id_prostor`,`id_vlastnost`),
           KEY `id_prostor` (`id_prostor`),
           KEY `id_vlastnost` (`id_vlastnost`),
           CONSTRAINT `organizace_katalog_vlastnosti_mistnosti_ibfk_1` FOREIGN KEY (`id_prostor`) REFERENCES `mista_mistnosti` (`id_mistnost`) ON DELETE CASCADE,
           CONSTRAINT `organizace_katalog_vlastnosti_mistnosti_ibfk_2` FOREIGN KEY (`id_vlastnost`) REFERENCES `system_vlastnosti_nazev` (`id_vlastnost`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250325.204544, function () {
         dibi::query('CREATE TABLE `organizace_katalog_vlastnosti` (
           `id_vlastnost` int(11) NOT NULL,
           `id_organizace` int(11) NOT NULL,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           PRIMARY KEY (`id_vlastnost`,`id_organizace`),
           KEY `id_organizace` (`id_organizace`),
           CONSTRAINT `system_vlastnosti_organizace_ibfk_1` FOREIGN KEY (`id_organizace`) REFERENCES `organizace` (`id_organizace`) ON DELETE CASCADE,
           CONSTRAINT `system_vlastnosti_organizace_ibfk_2` FOREIGN KEY (`id_vlastnost`) REFERENCES `system_vlastnosti_nazev` (`id_vlastnost`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250325.204541, function () {
         dibi::query('CREATE TABLE `system_vlastnosti_nazev` (
           `id_jazyk` tinyint(3) NOT NULL,
           `id_vlastnost` int(11) NOT NULL,
           `nazev` varchar(100) NOT NULL,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
           PRIMARY KEY (`id_jazyk`,`id_vlastnost`),
           KEY `id_vlastnost` (`id_vlastnost`),
           CONSTRAINT `system_vlastnosti_nazev_ibfk_2` FOREIGN KEY (`id_vlastnost`) REFERENCES `system_vlastnosti` (`id`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250325.204530, function () {
         dibi::query('CREATE TABLE `system_vlastnosti` (
           `id` int(11) NOT NULL AUTO_INCREMENT,
           `icon` varchar(25) NOT NULL,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
           PRIMARY KEY (`id`)
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250312.174808, function() {
         dibi::query('CREATE TABLE `organizace_katalog_typy_akci` (
            `id_organizace` int(5) NOT NULL,
            `id_typ` int(5) NOT NULL,
            `created` timestamp NOT NULL DEFAULT current_timestamp(),
            PRIMARY KEY (`id_organizace`,`id_typ`),
            KEY `id_typ` (`id_typ`),
            CONSTRAINT `organizace_katalog_typy_akci_ibfk_1` FOREIGN KEY (`id_organizace`) REFERENCES `organizace` (`id_organizace`) ON DELETE CASCADE,
            CONSTRAINT `organizace_katalog_typy_akci_ibfk_2` FOREIGN KEY (`id_typ`) REFERENCES `system_katalog_akce_typ` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci');
      });

      $this->resolver->add(250312.174802, function() {
         dibi::query('CREATE TABLE `system_katalog_akce_typ_nazev` (
            `id_jazyk` tinyint(3) NOT NULL,
            `id_typ` int(5) NOT NULL,
            `nazev` varchar(25) NOT NULL,
            `created` timestamp NOT NULL DEFAULT current_timestamp(),
            PRIMARY KEY (`id_jazyk`,`id_typ`),
            KEY `id_typ` (`id_typ`),
            CONSTRAINT `system_katalog_akce_typ_nazev_ibfk_1` FOREIGN KEY (`id_typ`) REFERENCES `system_katalog_akce_typ` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci');
      });

      $this->resolver->add(250312.174757, function() {
         dibi::query('CREATE TABLE `system_katalog_akce_typ` (
            `id` int(5) NOT NULL AUTO_INCREMENT,
            `created` timestamp NOT NULL DEFAULT current_timestamp(),
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250213.235121, function () {
         dibi::query('CREATE TABLE `organizace_katalog_tagy` (
           `id_tag` int(3) NOT NULL ,
           `id_organizace` int(11) NOT NULL,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           PRIMARY KEY (`id_tag`,`id_organizace`),
           KEY `id_organizace` (`id_organizace`),
           CONSTRAINT `system_tagy_organizace_ibfk_1` FOREIGN KEY (`id_tag`) REFERENCES `system_tagy` (`id`) ON DELETE CASCADE,
           CONSTRAINT `system_tagy_organizace_ibfk_2` FOREIGN KEY (`id_organizace`) REFERENCES `organizace` (`id_organizace`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250213.235114, function() {
         dibi::query('CREATE TABLE `system_tagy_nazvy` (
           `id_jazyk` int(3) NOT NULL,
           `id_tag` int(3) NOT NULL,
           `nazev` varchar(100) DEFAULT NULL,
           PRIMARY KEY (`id_jazyk`,`id_tag`),
           KEY `id_tag` (`id_tag`),
           CONSTRAINT `system_tagy_nazvy_ibfk_2` FOREIGN KEY (`id_tag`) REFERENCES `system_tagy` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250213.235107, function() {
         dibi::query('CREATE TABLE `system_tagy` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
             PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250209.194829, function() {
         dibi::query('ALTER TABLE `organizace`
            ADD COLUMN `licence_typ` tinyint(2) NOT NULL DEFAULT 1 AFTER `typ`;');
      });
   }
}