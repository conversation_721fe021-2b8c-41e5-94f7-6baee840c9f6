<?php namespace app\system\migration\version;

use app\front\organizace\model\organizace\Organizace;
use app\system\application\ApplicationEntityType;
use app\system\application\ApplicationVersion;
use app\system\Environment;
use app\system\migration\Migrations;
use app\system\model\event\hoste\EventHosteModel;
use app\system\model\event\hoste\EventHosteRow;
use app\system\model\event\VendorEventy;
use app\system\model\event\VenueEventy;
use app\system\model\event\predchozi\EventPredchoziEntita;
use app\system\model\event\predchozi\EventPredchoziEntitaRow;
use app\system\model\mista\OrganizaceMista;
use app\system\model\nabidka\hoste\KalkulaceHosteModel;
use app\system\model\nabidka\hoste\KalkulaceHosteRow;
use app\system\model\nabidka\mista\NabidkyDodavateAdresy;
use app\system\model\nabidka\mista\NabidkyDodavateleAdresaRow;
use app\system\model\nabidka\smlouva\event\UpdateObalkaQueuedEvent;
use app\system\model\nabidka\VendorNabidky;
use app\system\model\nabidka\VenueNabidky;
use app\system\model\organizace\fakturace\FakturacniAdresa;
use app\system\model\organizace\fakturace\FakturacniAdresaRow;
use app\system\model\organizace\kontakt\OrganizaceKontaktRow;
use app\system\model\organizace\meny\Meny;
use app\system\model\organizace\meny\OrganizaceMenaAdder;
use app\system\model\organizace\personal\Personal;
use app\system\model\polozky\kategorie\PolozkyKategorie;
use app\system\model\polozky\Polozky;
use app\system\model\poptavky\dodavatele\PoptavkyDodavateleAdresy;
use app\system\model\poptavky\VendorPoptavky;
use app\system\model\staty\Staty;
use app\system\model\uzivatele\session\data\UserData;
use app\system\model\uzivatele\session\data\UserOrganizationData;
use dibi;

/** Created by Kryštof Czyź. Date: 16.01.2025 */
class Migration25 extends Migrations
{

   public function prepare() :void {
      $this->resolver->add(250911.200644, function() {
         dibi::query('CREATE TABLE `organizace_kalkulace_introduction_sablony` (
           `id` int(11) NOT NULL AUTO_INCREMENT,
           `id_organizace` int(11) NOT NULL,
           `id_jazyk` int(11) NOT NULL,
           `title` varchar(80) NOT NULL,
           `text` text DEFAULT NULL,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
           PRIMARY KEY (`id`),
           CONSTRAINT `organizace_kalkulace_introduction_sablony_ibfk_1` FOREIGN KEY (`id_organizace`) REFERENCES `organizace` (`id_organizace`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250909.145726, function() {
         dibi::query('ALTER TABLE `organizace_sluzba_nazev`
            CHANGE `nazev` `nazev` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL;');
      });

      $this->resolver->add(250829.134752, function() {
         dibi::query('ALTER TABLE `organizace_kalendare`
            ADD COLUMN `anonymni` tinyint(1) NOT NULL DEFAULT 0 AFTER `aktivity`;');
      });

      $this->resolver->add(250823.175932, function() {
         dibi::query('ALTER TABLE `eventy` ADD COLUMN `priznak` int NULL DEFAULT NULL;');
         dibi::query('ALTER TABLE `eventy` ADD FOREIGN KEY (`priznak`) REFERENCES `organizace_priznaky` (`id`) ON DELETE SET NULL');

         dibi::query('ALTER TABLE `dodavatele_eventy` ADD COLUMN `priznak` int NULL DEFAULT NULL;');
         dibi::query('ALTER TABLE `dodavatele_eventy` ADD FOREIGN KEY (`priznak`) REFERENCES `organizace_priznaky` (`id`) ON DELETE SET NULL');
      });

      $this->resolver->add(250821.135316, function() {
         dibi::query('DROP TABLE IF EXISTS `system_organizace_poznamka`;');
         dibi::query('CREATE TABLE `system_organizace_poznamka` (
              `id_organizace` int(11) NOT NULL,
              `poznamka` text DEFAULT NULL,
              `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id_organizace`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250805.194534, function() {
         if(Environment::isProduction())
            (new UpdateObalkaQueuedEvent())
               ->setObalkaID(111)
               ->call();
      });

       $this->resolver->add(250724.223438, function() {
          dibi::query('CREATE TABLE `organizace_kalendar_personal` (
              `id_kalendar` int NOT NULL,
              `id_personal` int NOT NULL,
              KEY `id_kalendar` (`id_kalendar`),
              KEY `id_personal` (`id_personal`),
              CONSTRAINT `organizace_kalendar_personal_ibfk_1` FOREIGN KEY (`id_kalendar`) REFERENCES `organizace_kalendare` (`id`) ON DELETE CASCADE,
              CONSTRAINT `organizace_kalendar_personal_ibfk_2` FOREIGN KEY (`id_personal`) REFERENCES `organizace_personal` (`id_personal`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
       });

      $this->resolver->add(250724.223432, function() {
         dibi::query('CREATE TABLE `organizace_kalendare` (
           `id` int(11) NOT NULL AUTO_INCREMENT,
           `id_organizace` int(11) NOT NULL,
           `nazev` varchar(50) NOT NULL,
           `eventy` tinyint(1) NOT NULL,
           `smeny` tinyint(1) NOT NULL,
           `aktivity` tinyint(1) NOT NULL,
           `unique_token` varchar(255) NOT NULL,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
           PRIMARY KEY (`id`),
           KEY `id_organizace` (`id_organizace`),
           CONSTRAINT `organizace_kalendare_ibfk_1` FOREIGN KEY (`id_organizace`) REFERENCES `organizace` (`id_organizace`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250724.203709, function() {
         dibi::query('ALTER TABLE `dodavatele_nabidka`
            ADD COLUMN `client_first_visit` timestamp NULL DEFAULT NULL,
            ADD COLUMN `client_last_visit` timestamp NULL DEFAULT NULL;');

         dibi::query('ALTER TABLE `nabidka`
            ADD COLUMN `client_first_visit` timestamp NULL DEFAULT NULL,
            ADD COLUMN `client_last_visit` timestamp NULL DEFAULT NULL;');

         dibi::query('ALTER TABLE `dodavatele_eventy`
            ADD COLUMN `client_first_visit` timestamp NULL DEFAULT NULL,
            ADD COLUMN `client_last_visit` timestamp NULL DEFAULT NULL;');

         dibi::query('ALTER TABLE `eventy`
            ADD COLUMN `client_first_visit` timestamp NULL DEFAULT NULL,
            ADD COLUMN `client_last_visit` timestamp NULL DEFAULT NULL;');
      });

      $this->resolver->add(250625.152235, function() {
         dibi::query('ALTER TABLE `polozky`
            ADD COLUMN `product_photo_src` varchar(255) NULL DEFAULT NULL AFTER `kategorie`;');
      });

      $this->resolver->add(250625.152228, function() {
         dibi::query('ALTER TABLE `baliky`
            ADD COLUMN `product_photo_src` varchar(255) NULL DEFAULT NULL;');
      });

      $this->resolver->add(250625.152217, function() {
         dibi::query('CREATE TABLE `baliky_popisy` (
           `id_balik` int(11) NOT NULL,
           `id_jazyk` int(11) NOT NULL,
           `popis` text NOT NULL,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           `updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
           PRIMARY KEY (`id_balik`,`id_jazyk`),
           CONSTRAINT `baliky_popisy_ibfk_1` FOREIGN KEY (`id_balik`) REFERENCES `baliky` (`id_baliky`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250625.152210, function() {
         dibi::query('CREATE TABLE `polozky_popis` (
           `id_polozka` int(11) NOT NULL,
           `id_jazyk` int(11) NOT NULL,
           `popis` text NOT NULL,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           `updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
           PRIMARY KEY (`id_polozka`,`id_jazyk`),
           CONSTRAINT `polozky_popis_ibfk_1` FOREIGN KEY (`id_polozka`) REFERENCES `polozky` (`id_polozky`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250610.142359, function() {
         if(!Environment::isProduction())
            return;

         UserData::get(16)?->delete();
      });

      $this->resolver->add(250520.220505, function() {
         dibi::query('CREATE TABLE `kalkulace_zamitnuti_vendor` (
           `id_kalkulace` int NOT NULL,
           `type` tinyint(1) NOT NULL DEFAULT 0,
           `text` text COLLATE utf8mb4_general_ci,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           PRIMARY KEY (`id_kalkulace`),
           CONSTRAINT `kalkulace_zamitnuti_vendor_ibfk_1` FOREIGN KEY (`id_kalkulace`) REFERENCES `dodavatele_nabidka` (`id`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250520.220504, function() {
         dibi::query('CREATE TABLE `kalkulace_zamitnuti_venue` (
           `id_kalkulace` int NOT NULL,
           `type` tinyint(1) NOT NULL DEFAULT 0,
           `text` text COLLATE utf8mb4_general_ci,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           PRIMARY KEY (`id_kalkulace`),
           CONSTRAINT `kalkulace_zamitnuti_venue_ibfk_1` FOREIGN KEY (`id_kalkulace`) REFERENCES `nabidka` (`id_nabidka`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250515.103231, function() {
         dibi::query('ALTER TABLE `variabilni_tisk_event_sada`
            ADD COLUMN `id_hlavicka` int(11) NOT NULL DEFAULT "0" AFTER `bloky`,
            ADD COLUMN `id_paticka` int(11) NOT NULL DEFAULT "0" AFTER `id_hlavicka`;');
      });

      $this->resolver->add(250325.114939, function() {
         dibi::query('ALTER TABLE `nabidka`
            ADD COLUMN `localtax` bigint(20) NULL DEFAULT NULL;
         ');

         dibi::query('ALTER TABLE `dodavatele_nabidka`
            ADD COLUMN `localtax` bigint(20) NULL DEFAULT NULL;
         ');
      });

      $this->resolver->add(250325.114938, function() {
         dibi::query('ALTER TABLE `eventy`
            ADD COLUMN `localtax` bigint(20) NULL DEFAULT NULL;
         ');

         dibi::query('ALTER TABLE `dodavatele_eventy`
            ADD COLUMN `localtax` bigint(20) NULL DEFAULT NULL;
         ');
      });

      $this->resolver->add(250325.114937, function() {
         dibi::query('DROP TABLE IF EXISTS `organizace_local_tax`');
         dibi::query('CREATE TABLE `organizace_nastaveni_local_tax` (
           `id_organizace` int(11) NOT NULL,
           `id_mena` tinyint(3) NOT NULL,
           `suma_celkem` bigint(20) NOT NULL,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           PRIMARY KEY (`id_organizace`,`id_mena`),
           CONSTRAINT `organizace_nastaveni_local_tax_ibfk_1` FOREIGN KEY (`id_organizace`) REFERENCES `organizace` (`id_organizace`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

//      VENDOR
      $this->resolver->add(250317.135146, function() {
         $eventSkupiny = dibi::select('%n AS eventID', VendorEventy::PK)
            ->select('cenove_skupiny')
            ->from(VendorEventy::TABLE)
            ->fetchPairs('eventID', 'cenove_skupiny');

         $insert = [];

         foreach($eventSkupiny as $eventID => $cenoveSkupinyJSON){
            if(
               !trim($cenoveSkupinyJSON)
               || empty($skupinyArr = json_decode($cenoveSkupinyJSON, true) ?: [])
            ){
               continue;
            }

            $baseRow = new EventHosteRow();
            $baseRow->id_event = $eventID;

            foreach($skupinyArr as $skupinaID => $pocet){
               $iRow = clone $baseRow;

               $iRow->id_skupiny = $skupinaID;
               $iRow->pocet = $pocet;
               $insert[] = $iRow;
            }
         }

         if(!empty($insert))
            EventHosteModel::multipleInsert(ApplicationVersion::VENDOR, $insert);
      });

      $this->resolver->add(250317.135144, function() {
         $eventSkupiny = dibi::select('%n AS kalkulaceID', VendorNabidky::PK)
            ->select('cenove_skupiny')
            ->from(VendorNabidky::TABLE)
            ->fetchPairs('kalkulaceID', 'cenove_skupiny');

         $insert = [];

         foreach($eventSkupiny as $kalkulaceID => $cenoveSkupinyJSON) {
            if(
               !trim($cenoveSkupinyJSON)
               || empty($skupinyArr = json_decode($cenoveSkupinyJSON, true) ?: [])
            ){
               continue;
            }

            $baseRow = new KalkulaceHosteRow();
            $baseRow->id_kalkulace = $kalkulaceID;

            foreach($skupinyArr as $skupinaID => $pocet){
               $iRow = clone $baseRow;

               $iRow->id_skupiny = $skupinaID;
               $iRow->pocet = $pocet;
               $insert[] = $iRow;
            }
         }

         if(!empty($insert))
            KalkulaceHosteModel::multipleInsert(ApplicationVersion::VENDOR, $insert);
      });

//      VENUE
      $this->resolver->add(250317.135141, function() {
         $eventSkupiny = dibi::select('%n AS eventID', VenueEventy::PK)
            ->select('cenove_skupiny')
            ->from(VenueEventy::TABLE)
            ->fetchPairs('eventID', 'cenove_skupiny');

         $insert = [];

         foreach($eventSkupiny as $eventID => $cenoveSkupinyJSON){
            if(
               !trim($cenoveSkupinyJSON)
               || empty($skupinyArr = json_decode($cenoveSkupinyJSON, true) ?: [])
            ){
               continue;
            }

            $baseRow = new EventHosteRow();
            $baseRow->id_event = $eventID;

            foreach($skupinyArr as $skupinaID => $pocet){
               $iRow = clone $baseRow;

               $iRow->id_skupiny = $skupinaID;
               $iRow->pocet = $pocet;
               $insert[] = $iRow;
            }
         }

         if(!empty($insert))
            EventHosteModel::multipleInsert(ApplicationVersion::VENUE, $insert);
      });

      $this->resolver->add(250317.135139, function() {
         $eventSkupiny = dibi::select('%n AS kalkulaceID', VenueNabidky::PK)
            ->select('cenove_skupiny')
            ->from(VenueNabidky::TABLE)
            ->fetchPairs('kalkulaceID', 'cenove_skupiny');

         $insert = [];

         foreach($eventSkupiny as $kalkulaceID => $cenoveSkupinyJSON) {
            if(
               !trim($cenoveSkupinyJSON)
               || empty($skupinyArr = json_decode($cenoveSkupinyJSON, true) ?: [])
            ){
               continue;
            }

            $baseRow = new KalkulaceHosteRow();
            $baseRow->id_kalkulace = $kalkulaceID;

            foreach($skupinyArr as $skupinaID => $pocet){
               $iRow = clone $baseRow;

               $iRow->id_skupiny = $skupinaID;
               $iRow->pocet = $pocet;
               $insert[] = $iRow;
            }
         }

         if(!empty($insert))
            KalkulaceHosteModel::multipleInsert(ApplicationVersion::VENUE, $insert);
      });

      $this->resolver->add(250317.135137, function() {
         dibi::query('ALTER TABLE `nabidka`
            CHANGE `cenove_skupiny` `cenove_skupiny` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL;');

         dibi::query('ALTER TABLE `dodavatele_nabidka`
            CHANGE `cenove_skupiny` `cenove_skupiny` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL;');

         dibi::query('ALTER TABLE `eventy`
            CHANGE `cenove_skupiny` `cenove_skupiny` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL;');

         dibi::query('ALTER TABLE `dodavatele_eventy`
            CHANGE `cenove_skupiny` `cenove_skupiny` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL;');
      });

      $this->resolver->add(250317.134442, function() {
         dibi::query('CREATE TABLE `kalkulace_hoste_venue` (
           `id_kalkulace` int NOT NULL,
           `id_skupiny` int NOT NULL,
           `pocet` int NOT NULL DEFAULT 0,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
           PRIMARY KEY (`id_kalkulace`,`id_skupiny`),
           KEY `id_skupiny` (`id_skupiny`),
           CONSTRAINT `kalkulace_hoste_venue_ibfk_1` FOREIGN KEY (`id_kalkulace`) REFERENCES `nabidka` (`id_nabidka`) ON DELETE CASCADE,
           CONSTRAINT `kalkulace_hoste_venue_ibfk_2` FOREIGN KEY (`id_skupiny`) REFERENCES `cenove_skupiny` (`id_skupina`)
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');

         dibi::query('CREATE TABLE `kalkulace_hoste_vendor` (
           `id_kalkulace` int NOT NULL,
           `id_skupiny` int NOT NULL,
           `pocet` int NOT NULL DEFAULT 0,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
           PRIMARY KEY (`id_kalkulace`,`id_skupiny`),
           KEY `id_skupiny` (`id_skupiny`),
           CONSTRAINT `kalkulace_hoste_vendor_ibfk_1` FOREIGN KEY (`id_kalkulace`) REFERENCES `dodavatele_nabidka` (`id`) ON DELETE CASCADE,
           CONSTRAINT `kalkulace_hoste_vendor_ibfk_2` FOREIGN KEY (`id_skupiny`) REFERENCES `cenove_skupiny` (`id_skupina`)
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');

         dibi::query('CREATE TABLE `event_hoste_venue` (
           `id_event` int NOT NULL,
           `id_skupiny` int NOT NULL,
           `pocet` int NOT NULL DEFAULT 0,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
           PRIMARY KEY (`id_event`,`id_skupiny`),
           KEY `id_skupiny` (`id_skupiny`),
           CONSTRAINT `event_hoste_venue_ibfk_1` FOREIGN KEY (`id_event`) REFERENCES `eventy` (`id_event`) ON DELETE CASCADE,
           CONSTRAINT `event_hoste_venue_ibfk_2` FOREIGN KEY (`id_skupiny`) REFERENCES `cenove_skupiny` (`id_skupina`)
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');

         dibi::query('CREATE TABLE `event_hoste_vendor` (
           `id_event` int NOT NULL,
           `id_skupiny` int NOT NULL,
           `pocet` int NOT NULL DEFAULT 0,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
           PRIMARY KEY (`id_event`,`id_skupiny`),
           KEY `id_skupiny` (`id_skupiny`),
           CONSTRAINT `event_hoste_vendor_ibfk_1` FOREIGN KEY (`id_event`) REFERENCES `dodavatele_eventy` (`id`) ON DELETE CASCADE,
           CONSTRAINT `event_hoste_vendor_ibfk_2` FOREIGN KEY (`id_skupiny`) REFERENCES `cenove_skupiny` (`id_skupina`)
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250303.213625, function() {
         $data = dibi::select('e.id, e.id_nabidka')
            ->from(VendorEventy::TABLE, 'e')
            ->where('e.id_nabidka != 0')
            ->fetchPairs('id', 'id_nabidka');

         $insert = [];

         foreach($data as $eventID => $nabidkaID){
            $vztah = new EventPredchoziEntitaRow();
            $vztah->entity_id = $nabidkaID;
            $vztah->id_event = $eventID;
            $vztah->entity_type = ApplicationEntityType::NABIDKA->value;
            $insert[] = $vztah;
         }

         if(!empty($insert))
            dibi::query('INSERT INTO %n %ex', EventPredchoziEntita::getTable(ApplicationVersion::VENDOR), $insert);
      });

      $this->resolver->add(250303.213608, function() {
         $data = dibi::select('e.id_event, e.id_nabidka')
            ->from(VenueEventy::TABLE, 'e')
            ->where('e.id_nabidka != 0')
            ->fetchPairs('id_event', 'id_nabidka');

         $insert = [];

         foreach($data as $eventID => $nabidkaID){
            $vztah = new EventPredchoziEntitaRow();
            $vztah->entity_id = $nabidkaID;
            $vztah->id_event = $eventID;
            $vztah->entity_type = ApplicationEntityType::NABIDKA->value;
            $insert[] = $vztah;
         }

         if(!empty($insert))
            dibi::query('INSERT INTO %n %ex', EventPredchoziEntita::getTable(ApplicationVersion::VENUE), $insert);
      });

      $this->resolver->add(250303.125930, function() {
         dibi::query('DROP TABLE IF EXISTS `event_predchazejici_entita`;');

         dibi::query('CREATE TABLE `venue_event_predchozi_entita` (
           `id` int NOT NULL AUTO_INCREMENT,
           `entity_type` varchar(40) COLLATE utf8mb4_general_ci NOT NULL,
           `entity_id` int NOT NULL,
           `id_event` int NOT NULL,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           PRIMARY KEY (`id`),
           KEY `id_event` (`id_event`),
           UNIQUE KEY `entityIndex` (`entity_type`,`entity_id`,`id_event`),
           CONSTRAINT `venue_event_predchozi_entita_ibfk_1` FOREIGN KEY (`id_event`) REFERENCES `eventy` (`id_event`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');

         dibi::query('CREATE TABLE `vendor_event_predchozi_entita` (
           `id` int NOT NULL AUTO_INCREMENT,
           `entity_type` varchar(40) COLLATE utf8mb4_general_ci NOT NULL,
           `entity_id` int NOT NULL,
           `id_event` int NOT NULL,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           PRIMARY KEY (`id`),
           KEY `id_event` (`id_event`),
           UNIQUE KEY `entityIndex` (`entity_type`,`entity_id`,`id_event`),
           CONSTRAINT `vendor_event_predchozi_entita_ibfk_1` FOREIGN KEY (`id_event`) REFERENCES `dodavatele_eventy` (`id`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250302.234232, function() {
         dibi::query('CREATE TABLE `system_logy` (
           `id` int unsigned NOT NULL AUTO_INCREMENT,
           `event_class` varchar(255) NOT NULL,
           `environment_class` varchar(255) NOT NULL,
           `id_entity` varchar(50) DEFAULT NULL,
           `id_user` int DEFAULT NULL,
           `message` text,
           `changes` text,
           `before_state` text,
           `after_state` text,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           PRIMARY KEY (`id`),
           KEY `idx_event_entity` (`event_class`,`id_entity`)
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250301.131447, function() {
         if(!Environment::isProduction())
            return;

         UserData::get(26)?->delete();
         UserOrganizationData::get(26, 19)?->delete();
      });

      $this->resolver->add(250219.215516, function() {
         dibi::query('ALTER TABLE `polozky`
            ADD COLUMN `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;');

         dibi::query('ALTER TABLE `polozky_cena`
            ADD COLUMN `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            ADD COLUMN `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;');

         dibi::query('ALTER TABLE `polozky_nazvy`
            ADD COLUMN `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP;');

         dibi::query('ALTER TABLE `kategorie_nazvy`
            ADD COLUMN `updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP;');

         dibi::query('UPDATE polozky_nazvy SET updated = created');
         dibi::query('UPDATE kategorie_nazvy SET updated = created');
      });

      $this->resolver->add(250217.141058, function() {
         $polozkyKategorie = Polozky::find()
            ->fetchPairs('id_polozky', 'kategorie');

         $insert = [];

         foreach($polozkyKategorie as $polozkaID => $kategorieID)
            $insert[] = [
               PolozkyKategorie::FK_POLOZKA => $polozkaID,
               PolozkyKategorie::FK_KATEGORIE => $kategorieID,
            ];

         if(!empty($insert))
            dibi::query('INSERT IGNORE INTO %n %ex', PolozkyKategorie::TABLE, $insert);
      });

      $this->resolver->add(250217.131629, function() {
         dibi::query('CREATE TABLE `polozky_kategorie` (
           `id_polozky` int NOT NULL,
           `id_kategorie` int NOT NULL,
           `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
           PRIMARY KEY (`id_polozky`,`id_kategorie`),
           KEY `id_kategorie` (`id_kategorie`),
           CONSTRAINT `polozky_kategorie_ibfk_1` FOREIGN KEY (`id_polozky`) REFERENCES `polozky` (`id_polozky`) ON DELETE CASCADE,
           CONSTRAINT `polozky_kategorie_ibfk_2` FOREIGN KEY (`id_kategorie`) REFERENCES `kategorie` (`id_kategorie`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250214.014059, function() {
         dibi::query('ALTER TABLE `organizace_kapacita`
            CHANGE `plocha_celkem` `plocha_celkem` decimal(6,2) NOT NULL;');

         dibi::query('ALTER TABLE `organizace_kapacita`
            CHANGE `kapacita_ubytovani` `kapacita_ubytovani` int NOT NULL DEFAULT 0;');
      });

      $this->resolver->add(250211.222751, function() {
         dibi::query('ALTER TABLE `dodavatele_event_aktivity`
            ADD COLUMN `time_start` time NULL DEFAULT NULL AFTER `termin`,
            ADD COLUMN `time_end` time NULL DEFAULT NULL AFTER `time_start`,
            ADD COLUMN `typ` tinyint NOT NULL DEFAULT 1 AFTER `stav`;');
      });

      $this->resolver->add(250211.194659, function() {
         dibi::query('ALTER TABLE `event_aktivity`
            ADD COLUMN `time_start` time NULL DEFAULT NULL AFTER `termin`,
            ADD COLUMN `time_end` time NULL DEFAULT NULL AFTER `time_start`,
            ADD COLUMN `typ` tinyint NOT NULL DEFAULT 1 AFTER `stav`;');
      });

      $this->resolver->add(250207.113348, function() {
         $adresy = [];

         $result = dibi::select('n.id, pa.id_misto')
            ->from(VendorNabidky::TABLE, 'n')
            ->join(VendorPoptavky::TABLE, 'p')->on('p.id = n.id_poptavka')
            ->join(PoptavkyDodavateleAdresy::TABLE, 'pa')->on('pa.id_poptavky = p.id')
            ->fetchAll();

         foreach($result as $row){
            $adresa = new NabidkyDodavateleAdresaRow();
            $adresa->id_nabidka = $row['id'];
            $adresa->id_misto = $row['id_misto'];

            $adresy[] = $adresa;
         }

         if(!empty($adresy))
            dibi::query('INSERT INTO %n %ex', NabidkyDodavateAdresy::TABLE, $adresy);
      });

      $this->resolver->add(250207.113343, function() {
         dibi::query('CREATE TABLE `dodavatele_nabidka_adresa` (
           `id` int(11) NOT NULL AUTO_INCREMENT,
           `id_nabidka` int(11) NOT NULL,
           `id_misto` int(11) NOT NULL,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           PRIMARY KEY (`id`),
           KEY `id_nabidka` (`id_nabidka`),
           KEY `id_misto` (`id_misto`),
           CONSTRAINT `dodavatele_nabidka_adresa_ibfk_1` FOREIGN KEY (`id_nabidka`) REFERENCES `dodavatele_nabidka` (`id`) ON DELETE CASCADE,
           CONSTRAINT `dodavatele_nabidka_adresa_ibfk_2` FOREIGN KEY (`id_misto`) REFERENCES `organizace_mista` (`id_mista`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250203.104100, function() {
         dibi::query('CREATE TABLE `organizace_zakaznici_prirazene_tagy` (
           `id_zakaznik` int(11) NOT NULL,
           `id_tag` int(11) NOT NULL,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           PRIMARY KEY (`id_zakaznik`,`id_tag`),
           KEY `id_tag` (`id_tag`),
           CONSTRAINT `organizace_zakaznici_prirazene_tagy_ibfk_1` FOREIGN KEY (`id_tag`) REFERENCES `organizace_zakaznici_tagy` (`id`) ON DELETE CASCADE,
           CONSTRAINT `organizace_zakaznici_prirazene_tagy_ibfk_2` FOREIGN KEY (`id_zakaznik`) REFERENCES `organizace_zakaznici` (`id`) ON DELETE CASCADE
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250203.104053, function() {
         dibi::query('CREATE TABLE `organizace_zakaznici_tagy` (
           `id` int(11) NOT NULL AUTO_INCREMENT,
           `id_organizace` int(11) NOT NULL,
           `nazev` varchar(60) NOT NULL,
           `id_color` int(3) NOT NULL DEFAULT 1,
           `created` timestamp NOT NULL DEFAULT current_timestamp(),
           PRIMARY KEY (`id`)
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
      });

      $this->resolver->add(250121.165004, function() {
         dibi::query('ALTER TABLE `organizace_mista`
            CHANGE `existed_misto` `id_stat` int(11) NULL DEFAULT NULL;');

         $statID = Staty::getForISO_A2('CZ')->id;

         dibi::update(OrganizaceMista::TABLE, [
            'id_stat' => $statID,
         ])->execute();

         dibi::query('ALTER TABLE `organizace_mista`
            CHANGE `psc` `psc` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL');
      });

//      delete špatně založeného místa z dema ID (70 a 71 a 73) - nesmazáno úplně
      $this->resolver->add(250116.215828, function() {
         if(Environment::isLocalhost() || Environment::isDevelopment())
            return;
         $organizaceIDS = [70, 71, 73];
         $usersToInvalidate = [];

         $personalToInvalidate = dibi::select([Personal::COLUMN_ID, Personal::FK_UZIVATEL])
            ->from(Personal::TABLE)
            ->where(
               '%n IN %in AND %n IS NOT NULL',
               Personal::COLUMN_MISTA, $organizaceIDS, Personal::FK_UZIVATEL,
            )->fetchPairs(Personal::COLUMN_ID, Personal::FK_UZIVATEL);

         foreach($personalToInvalidate as $personalID => $userID){
            Personal::delete($personalID);
            $usersToInvalidate[$userID] = $userID;
         }

         $usersToInvalidateOwners = dibi::select(Organizace::COLUMN_USER_ID)
            ->from(Organizace::TABLE)
            ->where('%n IN %in', Organizace::COLUMN_ID, $organizaceIDS)
            ->fetchPairs(Organizace::COLUMN_USER_ID, Organizace::COLUMN_USER_ID);

         foreach($usersToInvalidateOwners as $userID)
            $usersToInvalidate[$userID] = $userID;

         foreach($usersToInvalidate as $userID)
            UserData::get($userID)?->delete();

         dibi::update(Organizace::TABLE, [
            Organizace::COLUMN_USER_ID => 0
         ])->where('%n IN %in', Organizace::COLUMN_ID, $organizaceIDS)
            ->execute();

         bdump($personalToInvalidate, 'Invalidated personal');
         bdump($usersToInvalidateOwners, 'Invalidated owners users');
         bdump($usersToInvalidate, 'Invalidated users cache');
      });

      $this->resolver->add(250116.212013, function() {
         echo 'Happy NY';
         if(Environment::isLocalhost() || Environment::isDevelopment())
            return;

         UserData::get(16)?->delete();
      });
   }
}