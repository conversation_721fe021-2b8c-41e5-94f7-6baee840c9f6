<?php namespace app\system\model\tymy\event;

use dibi;
use Tracy\ILogger;
use Tracy\Debugger;
use app\system\event\Event;
use app\system\model\tymy\Tymy;
use app\system\model\tymy\TymyRow;
use app\system\event\FlashException;

class EditOrganizaceTymEvent extends Event
{

   protected array $toUpdate;
   protected array $old;

   function __construct(private readonly TymyRow $tym, string $newNazev) {
      $this->old = $tym->toArray(false);
      $tym->nazev = $newNazev;

      $this->toUpdate = [
         'tym old' => $this->old,
         'tym new' => $tym->toArray(false),
      ];
   }

   public function onCall() :void {
      $this->updateTeam();

      $this->getLogger()
         ->mergeLog(
            $this->getChangesArray(),
            [
               'Tym:' => $this->old,
            ],
            [
               'Tym:' => $this->tym->toArray(false),
            ]
         );
   }

   private function updateTeam() :void {
      try{
         dibi::begin();
         Tymy::save($this->tym);
         dibi::commit();
      } catch(\Exception $e){
         dibi::rollback();
         Debugger::log($e, ILOGGER::ERROR);
         throw FlashException::create('Při úpravě týmu došlo k chybě.');
      }
   }

   private function getChangesArray() :?array {
      $r = [];
      if(!empty($this->toUpdate))
         $r['toUpdate'] = $this->toUpdate;

      if(empty($r))
         return null;

      return $r;
   }
}