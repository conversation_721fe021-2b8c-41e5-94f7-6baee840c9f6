<?php namespace app\system\model\tymy\event;

use app\system\model\tymy\TymyPersonal;
use dibi;
use Tracy\ILogger;
use Tracy\Debugger;
use app\system\event\Event;
use app\system\model\tymy\Tymy;
use app\system\model\tymy\TymyRow;
use app\system\event\FlashException;

class DeleteOrganizaceTymEvent extends Event
{

   protected array $toDelete;
   protected array $toAdd;
   protected array $toUpdate;

   function __construct(private readonly TymyRow $tym) {
      $personal = [];
      foreach(TymyPersonal::getSelectedWorkers($tym->id_tym)->fetchAll() as $employee){
         $personal[] = $employee['id_personal'];
      }
      $this->toDelete = [
         'tym' => $tym->toArray(false),
         'personal' => $personal,
      ];
   }

   public function onCall() :void {
      $this->deleteTeam();

      $this->getLogger()
         ->addLog(
            null,
            $this->getChangesArray(),
            $this->toDelete
         );
   }

   private function deleteTeam() :void {
      try{
         dibi::begin();
         TymyPersonal::deleteByTym($this->tym->id_tym);
         Tymy::delete($this->tym->id_tym);
         dibi::commit();
      } catch(\Exception $e){
         dibi::rollback();
         Debugger::log($e, ILOGGER::ERROR);
         throw FlashException::create('Při mazání týmu došlo k chybě.');
      }
   }

   private function getChangesArray() :?array {
      $r = [];
      if(!empty($this->toDelete))
         $r['toDelete'] = $this->toDelete;

      if(empty($r))
         return null;

      return $r;
   }
}