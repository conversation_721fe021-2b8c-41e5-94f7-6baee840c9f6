<?php namespace app\system\model\tymy;

use dibi;
use dibi\Fluent;

/** Created by <PERSON>. Date: 14.01.2023 */
class Tymy
{
   const TABLE = 'organizace_tymy';
   const PK = 'id_tym';

   /** @return TymyRow[]|Fluent */
   public static function find() :Fluent {
      return dibi::select('t.*')
         ->from(self::TABLE, 't')
         ->setupResult('setRowClass', TymyRow::class);
   }

   public static function findByMisto(int $id_misto) :Fluent {
      return self::find()
         ->where('t.id_mista = %i', $id_misto);
   }

   /** @return ?TymyRow[] */
   public static function getTeamArrayByMisto(int $id_misto) :?array {
      return dibi::select('t.*')
         ->from(self::TABLE, 't')
         ->where('t.id_mista = %i', $id_misto)
         ->fetchAll();
   }

   public static function get(int $id_tym) :TymyRow {
      return self::find()
         ->where('t.id_tym = %i', $id_tym)
         ->fetch();
   }

   public static function create(string $nazev, int $id_mista) :void {
      dibi::insert(self::TABLE, array('id_mista' => $id_mista, 'nazev' => $nazev))
         ->execute();
   }

   public static function save(TymyRow $row) :TymyRow {
      if($row->{self::PK} ?? false){
         dibi::update(self::TABLE, $row->toUpdateArray())
            ->where('%sql = %i', self::PK, $row->{self::PK})
            ->execute();
      } else{
         dibi::insert(self::TABLE, $row->toUpdateArray())
            ->execute();
         $row->{self::PK} = dibi::getInsertId();
      }
      return $row;
   }

   public static function delete(int $teamID) :void {
		dibi::delete(self::TABLE)
		   ->where('%n = %i', self::PK, $teamID)
		   ->execute();
   }
}