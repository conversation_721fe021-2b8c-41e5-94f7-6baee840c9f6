<?php namespace app\system\model\tymy;

use app\system\flash\FlashMessages;
use dibi;
use Dibi\Fluent;

/** Created by <PERSON>. Date: 16.01.2023 */
class TymyPersonal
{
   const TABLE = 'organizace_tymy_personal';
   const PK = 'id_tymy_clenove';
   const ID_PERSONAL = 'id_personal';
   const ID_TYM = 'id_tym';

   /** @return TymyPersonalRow[]|Fluent */
   public static function find() :Fluent {
      return dibi::select('tp.*')
         ->from(self::TABLE, 'tp')
         ->setupResult('setRowClass', TymyPersonalRow::class);
   }

   public static function getSpecificWorker(int $id_tym, int $id_personal) :?int {
      return self::find()
         ->where('tp.id_tym = %i AND tp.id_personal = %i', $id_tym, $id_personal)
         ->fetchSingle();
   }

   public static function getSelectedWorkers(int $id_tym) :Fluent {
      return dibi::select('mp.jmeno, mp.prijmeni, mp.telefon, mp.id_personal')
         ->from('organizace_personal', 'mp')
         ->join(self::TABLE, 'tp')
         ->on('mp.id_personal = tp.id_personal')
         ->and('tp.id_tym = %i', $id_tym);
   }

   public static function insertWorkers(TymyPersonalRow $worker) {
      if(!empty(self::getSpecificWorker($worker->id_tym, $worker->id_personal))){
         FlashMessages::setWarning('Pracovník je již v týmu.');
      } else{
         dibi::insert(TymyPersonal::TABLE, $worker->toUpdateArray())
            ->execute();
         FlashMessages::setSuccess('Pracovník byl přidán do týmu.');
      }
   }

   public static function deleteWorker(int $id_personal, int $id_tym) {
      dibi::delete(self::TABLE)
         ->where('%sql = %i and %sql = %i', self::ID_PERSONAL, $id_personal, self::ID_TYM, $id_tym)
         ->execute();
      FlashMessages::setSuccess('Pracovník byl odebrán z týmu.');
   }

   public static function deleteByTym(int $id_tym): void {
	   dibi::delete(self::TABLE)
		   ->where('%n = %i', self::ID_TYM, $id_tym)
         ->execute();
   }
}