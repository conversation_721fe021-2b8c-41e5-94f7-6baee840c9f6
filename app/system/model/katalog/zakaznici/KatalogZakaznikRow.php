<?php namespace app\system\model\katalog\zakaznici;

use app\system\helpers\row\attributes\IgnoreUpdateArray;
use app\system\helpers\row\EntryRow;
use app\system\mailer\MailRecipient;
use app\system\model\katalog\zakaznici\telefon\KatalogZakazniciTelefonRow;
use app\system\model\katalog\zakaznici\telefon\KatalogZakazniciTelefony;
use app\system\model\zakaznici\IZakaznik;
use Dibi\DateTime;

/** Created by <PERSON><PERSON>. Date: 09.04.2025 */
class KatalogZakaznikRow extends EntryRow
   implements IZakaznik
{

   public int $id_zakaznik, $id_jazyk;
   public string $full_name;
   public DateTime $created, $updated;

   #[IgnoreUpdateArray]
   public string $email;

   public function save() :static {
      return KatalogZakaznici::save($this);
   }

   public function getZakaznikID() :int {
      return $this->id_zakaznik;
   }

   public function getRow() :KatalogZakaznikRow {
      return $this;
   }

   /** @return  array<int, KatalogZakazniciTelefonRow> */
   public function getTelefony() :array {
      return $this->seznamTelefonu ??= KatalogZakazniciTelefony::getForZakaznik($this->id_zakaznik);
   }

   public function getPrimarniTelefon() :?KatalogZakazniciTelefonRow {
      return $this->primarniTelefon ??= KatalogZakazniciTelefony::getPrimarniForZakaznik($this->id_zakaznik);
   }

   public function getFirstName() :?string {
      return explode(' ', $this->full_name, 2)[0];
   }

   public function getLastName() :?string {
      return explode(' ', $this->full_name, 2)[1];
   }

   public ?string $password;

   public function getEmailRecipient() :MailRecipient {
      return MailRecipient::create($this->full_name, $this->email);
   }

   private ?KatalogZakazniciTelefonRow $primarniTelefon = null;

   /** @var array<int, KatalogZakazniciTelefonRow> */
   private array $seznamTelefonu = [];
}