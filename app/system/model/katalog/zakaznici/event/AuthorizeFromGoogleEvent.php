<?php namespace app\system\model\katalog\zakaznici\event;

use app\System;
use app\system\event\Event;
use app\system\event\FlashException;
use app\system\event\IEventEntityLogger;
use app\system\model\katalog\zakaznici\email\RegistraceKatalogZakaznikEmail;
use app\system\model\katalog\zakaznici\KatalogZakaznici;
use app\system\model\katalog\zakaznici\KatalogZakaznikRow;
use app\system\model\zakaznici\Zakaznici;
use app\system\users\zakaznici\event\LoginZakaznikEvent;
use app\system\users\zakaznici\LoggedZakaznik;
use dibi;
use Google\Service\Oauth2\Userinfo;
use Tracy\Debugger;
use Tracy\ILogger;

/** Created by <PERSON><PERSON>. Date: 09.04.2025 */
class AuthorizeFromGoogleEvent extends Event
{

   public function __construct(private readonly Userinfo $userInfo) { }

   public function onCall() :void {
      $katalogZakaznikRow = KatalogZakaznici::getByEmail($this->userInfo->email);

      if(!$katalogZakaznikRow){
         $katalogZakaznikRow = $this->handleRegisterZakaznik();
      }

      $this->getLogger()
         ->mergeLog(
            after: $katalogZakaznikRow->toArray(false),
         );

      LoginZakaznikEvent::loginRegistered(LoggedZakaznik::getLogged(), $katalogZakaznikRow)?->call();
   }

   private function handleRegisterZakaznik() :KatalogZakaznikRow {
      $katalogZakaznikRow = new KatalogZakaznikRow();
      $katalogZakaznikRow->full_name = $this->userInfo->name;
      $katalogZakaznikRow->email = $this->userInfo->email;
      $katalogZakaznikRow->id_jazyk = System::get()->getApplication()->environment->getJazyk()->value;

      try{
         dibi::begin();

         $zakaznikID = Zakaznici::getId($this->userInfo->email);

         $katalogZakaznikRow->id_zakaznik = $zakaznikID;
         $katalogZakaznikRow->save();

         dibi::commit();
      } catch(\Exception $exception){
         dibi::rollback();
         Debugger::log($exception, ILogger::ERROR);
         throw FlashException::create('Při registraci přes google došlo k chybě');
      }

      (new RegistraceKatalogZakaznikEmail())
         ->send($katalogZakaznikRow->getEmailRecipient());

      return $katalogZakaznikRow;
   }
}