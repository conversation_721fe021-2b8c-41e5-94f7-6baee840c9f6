<?php namespace app\system\model\katalog\zakaznici\event;

use app\System;
use app\system\event\Event;
use app\system\event\FlashException;
use app\system\event\IEventEntityLogger;
use app\system\model\katalog\zakaznici\email\RegistraceKatalogZakaznikEmail;
use app\system\model\katalog\zakaznici\KatalogZakaznici;
use app\system\model\katalog\zakaznici\KatalogZakaznikRow;
use app\system\model\katalog\zakaznici\telefon\KatalogZakazniciTelefonRow;
use app\system\model\zakaznici\Zakaznici;
use app\system\users\zakaznici\event\LoginZakaznikEvent;
use app\system\users\zakaznici\LoggedZakaznik;
use dibi;
use Tracy\Debugger;
use Tracy\ILogger;

/** Created by Filip Pav<PERSON>. Date: 10.04.2025 */
class RegisterByEmailPostEvent extends Event
   implements IEventEntityLogger
{

   /**
    * @throws FlashException
    */
   public function __construct(private readonly array $post) {
      if(!isset($this->post['email']))
         throw FlashException::create('nebyl správně vyplněn email');

      if(!isset($this->post['heslo']) || !isset($this->post['heslo2']))
         throw FlashException::create('nebylo správně vyplněno jedno nebo obě hesla');

      if(!isset($this->post['jmeno']) || !isset($this->post['prijmeni']))
         throw FlashException::create('nebylo správně vyplněno jméno nebo příjmení');

      if(KatalogZakaznici::getByEmail($this->post['email']))
         throw FlashException::create('Email je již registrován, zkuste přenačíst stránku');

      $this->katalogZakaznikRow = new KatalogZakaznikRow();
      $this->katalogZakaznikRow->full_name = sprintf('%s %s', $this->post['jmeno'], $this->post['prijmeni']);
      $this->katalogZakaznikRow->email = $this->post['email'];
      $this->katalogZakaznikRow->id_jazyk = System::get()->getApplication()->environment->getJazyk()->value;
      $this->katalogZakaznikRow->password = password_hash((trim($this->post['heslo'])), PASSWORD_DEFAULT);

      if(!isset($this->post['telefon']) || !isset($this->post['prefixTelefon']))
         throw FlashException::create('nebyl správně vyplněn telefon');

      $this->zakaznikTelefon = new KatalogZakazniciTelefonRow();
      $this->zakaznikTelefon->telefon = $this->post['telefon'];
      $this->zakaznikTelefon->prefix = $this->post['prefixTelefon'];
   }

   public function onCall() :void {
      try{
         dibi::begin();

         $zakaznikID = Zakaznici::getId($this->post['email']);

         $this->katalogZakaznikRow->id_zakaznik = $zakaznikID;
         $this->katalogZakaznikRow->save();

         $this->zakaznikTelefon->id_zakaznik = $this->katalogZakaznikRow->id_zakaznik;
         $this->zakaznikTelefon->save();

         dibi::commit();

         $this->getLogger()
            ->mergeLog(
               after: $this->katalogZakaznikRow->toArray(false),
            );

         (new RegistraceKatalogZakaznikEmail())
            ->send($this->katalogZakaznikRow->getEmailRecipient());

         LoginZakaznikEvent::loginRegistered(
            LoggedZakaznik::getLogged(),
            $this->katalogZakaznikRow
         )->call();

      } catch(\Exception $exception){
         dibi::rollback();
         Debugger::log($exception, ILogger::ERROR);
         throw FlashException::create('Při registraci pomocí emailu došlo k chybě');
      }
   }

   public static function getUserLabel() :string {
      return 'Registrace zákaznika pomocí emailu';
   }

   public function getEntityId() :int|string {
      return $this->katalogZakaznikRow->id_zakaznik;
   }

   private ?KatalogZakaznikRow $katalogZakaznikRow;
   private ?KatalogZakazniciTelefonRow $zakaznikTelefon;
}