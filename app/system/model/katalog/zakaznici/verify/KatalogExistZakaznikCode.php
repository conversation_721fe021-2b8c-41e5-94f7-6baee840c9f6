<?php namespace app\system\model\katalog\zakaznici\verify;

use app\system\model\zakaznici\Zakaznici;
use dibi;
use Dibi\DateTime;
use Dibi\Fluent;
use Nette\Utils\Random;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 02.09.2025 */
class KatalogExistZakaznikCode
{

   const string TABLE = 'katalog_exist_zakaznik_code';
   const string FK_ZAKAZNIK = 'id_zakaznik';


   /** @return Fluent|KatalogExistZakaznikCodeRow[] */
   public static function find() :Fluent|array {
      return dibi::select('kezc.*')
         ->from(self::TABLE, 'kezc')
         ->setupResult('setRowClass', KatalogExistZakaznikCodeRow::class);
   }

   public static function get(int $id) :?KatalogExistZakaznikCodeRow {
      return self::find()
         ->where('kezc.id = %i', $id)
         ->fetch();
   }

   public static function getByCode(string $code, string $email) :?KatalogExistZakaznikCodeRow {
      return self::find()
         ->join(Zakaznici::TABLE, 'z')->on('kezc.%n = z.%n', self::FK_ZAKAZNIK, Zakaznici::COLUMN_ID)
         ->where('z.email = %s', $email)
         ->where('kezc.code = %s', $code)
         ->fetch();
   }

   public static function create(int $zakaznikID) :KatalogExistZakaznikCodeRow {
      $row = new KatalogExistZakaznikCodeRow();
      $row->id_zakaznik = $zakaznikID;
      $row->code = Random::generate(6, '0-9');
      $row->expired = (new DateTime())->add(new \DateInterval('P1D'));

      self::insert($row);

      return $row;
   }

   private static function insert(KatalogExistZakaznikCodeRow $row) :void {
      dibi::insert(self::TABLE, $row->toArray())->execute();
      $row->id = dibi::getInsertId();
   }

   public static function delete(KatalogExistZakaznikCodeRow $row) :void {
      dibi::delete(self::TABLE)
         ->where([
            'id' => $row->id
         ])->execute();
   }
}