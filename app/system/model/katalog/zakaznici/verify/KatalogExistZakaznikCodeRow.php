<?php namespace app\system\model\katalog\zakaznici\verify;

use app\system\helpers\row\EntryRow;
use Dibi\DateTime;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 02.09.2025 */
class KatalogExistZakaznikCodeRow extends EntryRow
{

   public int $id;
   public int $id_zakaznik;
   public string $code;

   public DateTime $expired;
   public DateTime $created;

   public function delete() :void {
      KatalogExistZakaznikCode::delete($this);
   }
}