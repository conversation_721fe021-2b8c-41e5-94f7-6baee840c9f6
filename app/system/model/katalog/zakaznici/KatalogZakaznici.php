<?php namespace app\system\model\katalog\zakaznici;

use app\system\model\zakaznici\Zakaznici;
use dibi;
use Dibi\Fluent;

/** Created by <PERSON><PERSON>. Date: 09.04.2025 */
class KatalogZakaznici
{

   const string TABLE = 'katalog_zakaznik';
   const string PK = 'id_zakaznik';

   /** @return KatalogZakaznikRow[]|Fluent */
   public static function find() :array|Fluent {
      return dibi::select('kz.*')
         ->select('z.email')
         ->from(self::TABLE, 'kz')
         ->join(Zakaznici::TABLE, 'z')->on('kz.%n = z.%n', self::PK, Zakaznici::COLUMN_ID)
         ->setupResult('setRowClass', KatalogZakaznikRow::class);
   }

   public static function get(int $id_zakaznik) :?KatalogZakaznikRow {
      return self::find()
         ->where('kz.%n = %i', self::PK, $id_zakaznik)
         ->fetch();
   }

   public static function getByEmail(string $email) :?KatalogZakaznikRow {
      return self::find()
         ->where('z.email = %s', $email)
         ->fetch();
   }

   public static function save(KatalogZakaznikRow $row) :KatalogZakaznikRow {
      dibi::insert(self::TABLE, $row->toUpdateArray())
         ->on('DUPLICATE KEY UPDATE full_name = VALUES(full_name), id_jazyk = VALUES(id_jazyk)')
         ->execute();

      return $row;
   }

   public static function delete(int $zakaznikID) :void {
      dibi::delete(self::TABLE)
         ->where([self::PK => $zakaznikID])
         ->execute();
   }
}