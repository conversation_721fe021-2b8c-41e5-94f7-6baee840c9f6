<?php namespace app\system\model\event\personal;

use app\system\model\organizace\meny\Meny;
use app\system\model\organizace\meny\OrganizaceMeny;
use Money\Currency;
use Money\Money;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 13.01.2023 */
class EventSmenyContainer
{

   public static function create() :EventSmenyContainer {
      return new self();
   }

   public function addItem(EventPersonalSmenaItem $item) :static {
      $this->items[] = $item;
      return $this;
   }

   public function getPocetPersonalu() :int {
      return $this->pocetPersonalu??= $this->preparePocetPersonalu();
   }

   public function getNakladPersonalu() :?Money {
      if(!isset($this->naklad))
         $this->prepareNakladPersonalu();

      return $this->naklad?? null;
   }

   public function getNakladOsoby(int $personalID, string $currencyCode = null) :?Money {
      $finance = [];
      $naklady = [];

      if(empty($this->items))
         return null;

      if(!$currencyCode){
         $primaryCurrency = Meny::from(
            OrganizaceMeny::getPrimary(
               $this->items[array_key_first($this->items)]->personal->id_misto
            ));

         $currencyCode = $primaryCurrency->getCode();
      }

      foreach($this->items as $smena){
         if($smena->personal->id_personal == $personalID)
            $naklady[] = $smena->smena->getMzda();
      }

      foreach($naklady as $naklad){
         if(!isset($finance[$naklad->getCurrency()->getCode()]))
            $finance[$naklad->getCurrency()->getCode()] = new Money(0, new Currency($naklad->getCurrency()->getCode()));

         $finance[$naklad->getCurrency()->getCode()] = $finance[$naklad->getCurrency()->getCode()]->add($naklad);
      }

      return $finance[$currencyCode] ?? null;
   }

   /** @return EventPersonalSmenaItem[]  */
   public function getSmenaItems() :array {
      return $this->items;
   }

   private function prepareNakladPersonalu() :void {
      foreach($this->items as $item)
            $this->naklad = !isset($this->naklad)
               ? clone $item->smena->getMzda()
               : $this->naklad->add($item->smena->getMzda());
   }

   private function preparePocetPersonalu() :int {
      if(empty($this->items))
         return 0;

      //$uniqePersonalIds = [];
      //foreach($this->items as $item)
      //   $uniqePersonalIds[$item->smena->id_prirazeny_uzivatel] = $item->smena->id_prirazeny_uzivatel;
      //
      //return count($uniqePersonalIds);

      return count($this->items);
   }

   /** @var EventPersonalSmenaItem[]  */
   private array $items = [];
   private Money $naklad;
   private int $pocetPersonalu;
}