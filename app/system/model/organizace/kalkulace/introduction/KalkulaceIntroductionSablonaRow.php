<?php namespace app\system\model\organizace\kalkulace\introduction;

use app\system\helpers\row\EntryRow;
use Dibi\DateTime;

/** Created by <PERSON><PERSON>. Date: 22.08.2025 */
class KalkulaceIntroductionSablonaRow extends EntryRow
{

   public int $id, $id_organizace, $id_jazyk;
   public string $title;
   public ?string $text;
   public DateTime $created;
   public DateTime $updated;

   public function save() :static {
      return KalkulaceIntroductionSablony::save($this);
   }
}