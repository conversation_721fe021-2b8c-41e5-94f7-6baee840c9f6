<?php namespace app\system\model\organizace\kalkulace\introduction;

use dibi;
use Dibi\Fluent;

/** Created by <PERSON><PERSON>. Date: 22.08.2025 */
class KalkulaceIntroductionSablony
{

   const string TABLE = 'organizace_kalkulace_introduction_sablony';
   const string PK = 'id';
   const string FK_ORAGANIZACE = 'id_organizace';
   const string FK_JAZYK = 'id_jazyk';

   /** @return KalkulaceIntroductionSablonaRow[]|Fluent */
   public static function find() :array|Fluent {
      return dibi::select('kis.*')
         ->from(self::TABLE, 'kis')
         ->setupResult('setRowClass', KalkulaceIntroductionSablonaRow::class);
   }

   /** @return KalkulaceIntroductionSablonaRow[]|Fluent */
   public static function findByOrganizace(int $id_organizace) :array|Fluent {
      return self::find()
         ->where('kis.%n = %i', self::FK_ORAGANIZACE, $id_organizace);
   }

   public static function delete(int $id) :void {
      dibi::delete(self::TABLE)
         ->where('%n = %i', self::PK, $id)
         ->execute();
   }

   public static function get(int $id) :?KalkulaceIntroductionSablonaRow {
      return self::find()
         ->where('kis.%n = %i', self::PK, $id)
         ->fetch();
   }

   /** @return KalkulaceIntroductionSablonaRow[] */
   public static function getByOrganizace(int $organizaceID) :array {
      return self::find()
         ->where('kis.%n = %i', self::FK_ORAGANIZACE, $organizaceID)
         ->fetchAssoc('id');
   }

   /** @return KalkulaceIntroductionSablonaRow[] */
   public static function getByJazykOrganizace(int $organizaceID, int $id_jazyk) :array {
      return self::find()
         ->where('kis.%n = %i AND kis.%n = %i', self::FK_ORAGANIZACE, $organizaceID, self::FK_JAZYK, $id_jazyk)
         ->fetchAssoc('id');
   }

   public static function save(KalkulaceIntroductionSablonaRow $row) :KalkulaceIntroductionSablonaRow {
      if($row->id ?? false){
         dibi::update(self::TABLE, $row->toUpdateArray())
            ->where('%n = %i', self::PK, $row->id)
            ->execute();
      } else{
         dibi::insert(self::TABLE, $row->toUpdateArray())
            ->execute();
         $row->id = dibi::getInsertId();
      }

      return $row;
   }
}