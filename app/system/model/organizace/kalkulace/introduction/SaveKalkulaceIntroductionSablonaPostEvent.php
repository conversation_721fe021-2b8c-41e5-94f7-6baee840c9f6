<?php namespace app\system\model\organizace\kalkulace\introduction;

use app\system\event\Event;

/** Created by <PERSON><PERSON>. Date: 25.08.2025 */
class SaveKalkulaceIntroductionSablonaPostEvent extends Event
{

   /** @param array{
    *    id_jazyk_sablona: ?int,
    *    nazev_sablona: ?string,
    *    introduction: string,
    * } $post */
   public function __construct(private KalkulaceIntroductionSablonaRow $sablonaRow, array $post) {
      if(
         !isset($this->sablonaRow->id_jazyk)
         || (isset($post['id_jazyk_sablona']) && $this->sablonaRow->id_jazyk !== $post['id_jazyk_sablona'])
      ){
         $this->sablonaRow->id_jazyk = $post['id_jazyk_sablona'];
         $this->hasSave = true;
      }

      if(
         !isset($this->sablonaRow->title)
         || (isset($post['nazev_sablona']) && $this->sablonaRow->title !== $post['nazev_sablona'])
      ){
         $this->sablonaRow->title = $post['nazev_sablona'];
         $this->hasSave = true;
      }

      if(!isset($this->sablonaRow->text) || $this->sablonaRow->text !== $post['introduction']){
         $this->sablonaRow->text = $post['introduction'];
         $this->hasSave = true;
      }
   }

   /** @param array{
    *    id_jazyk_sablona: int,
    *    nazev_sablona: string,
    *    introduction: string,
    * } $post */
   public static function createFromPost(int $id_organizace, array $post) :static {
      $sablona = new KalkulaceIntroductionSablonaRow();
      $sablona->id_organizace = $id_organizace;

      return new self($sablona, $post);
   }

   public function onCall() :void {
      if(!$this->hasSave)
         return;

      $this->sablonaRow->save();
   }

   public function get() :KalkulaceIntroductionSablonaRow {
      return $this->sablonaRow;
   }

   private bool $hasSave = false;
}