<?php namespace app\system\model\organizace\prilohy\kalkulace\data;

use app\front\organizace\modul\detail\nastaveni\modal\edit\EditOrganizaceKalkulacePrilohaModal;
use app\system\helpers\row\EntryRow;
use app\system\model\nabidka\prilohy\PrilohyTypyEnum;
use Dibi\DateTime;

/** Created by <PERSON><PERSON>. Date: 04.01.2024 */
class OrganizacePrilohaRow extends EntryRow
{

   public int $id, $id_organizace, $typ;
//   @TODO atribut URL přejmenovat na src, url je zavádějící
   public string $nazev, $url;
   public DateTime $created, $updated;

   public function save() :OrganizacePrilohaRow {
      return OrganizacePrilohy::save($this);
   }

   public function getTyp() :PrilohyTypyEnum {
      return PrilohyTypyEnum::from($this->typ);
   }

   public function getEditModalAttrs() :array {
      return EditOrganizaceKalkulacePrilohaModal::getShowAttributes($this->id);
   }

   public function getHrefLink() :string {
      if(str_starts_with($this->url, 'http'))
         return $this->url;

      return '/' . $this->url;
   }
}