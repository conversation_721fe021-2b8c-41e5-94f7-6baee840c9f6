<?php namespace app\system\model\organizace\zakaznici;

use app\system\helpers\row\EntryRow;
use app\system\mailer\MailRecipient;
use app\system\model\avatar\AvatarData;
use app\system\model\formular\enumy\FormularRespondentTypEnum;
use app\system\model\formular\IFormularRespondent;
use app\system\model\organizace\zakaznici\adresa\OrganizaceZakaznikAdresa;
use app\system\model\organizace\zakaznici\adresa\OrganizaceZakaznikAdresaRow;
use app\system\model\organizace\zakaznici\firma\OrganizaceZakaznikFirma;
use app\system\model\organizace\zakaznici\firma\OrganizaceZakaznikFirmaRow;
use app\system\model\organizace\zakaznici\keys\OrganizaceZakaznikKeys;
use app\system\model\organizace\zakaznici\souhlasy\OrganizaceZakazniciSouhlasy;
use app\system\model\organizace\zakaznici\souhlasy\SouhlasyTypy;
use app\system\model\organizace\zakaznici\tagy\prirazeni\ZakazniciPrirazeneTagy;
use app\system\model\organizace\zakaznici\tagy\prirazeni\ZakazniciPrirazenyTagRow;
use app\system\model\organizace\zakaznici\telefon\OrganizaceZakazniciTelefon;
use app\system\model\organizace\zakaznici\telefon\OrganizaceZakazniciTelefonRow;
use app\system\model\translator\Jazyky;
use app\system\model\zakaznici\IZakaznik;
use app\system\model\zakaznici\ZakazniciDruhyKontakt;
use app\system\model\zakaznici\ZakaznikDruhyKontaktRow;
use Dibi\DateTime;
use Endroid\QrCode\Builder\Builder;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\Writer\PngWriter;
use Latte\Runtime\Html;

/** Created by Kryštof Czyź. Date: 01.04.2022 */
class OrganizaceZakaznikRow extends EntryRow
   implements IFormularRespondent, IZakaznik
{
   public int $id, $id_zakaznik, $id_organizace;

//   @TODO Delete
   /**
    * @deprecated This property is deprecated. Use $zakaznik–>getTelefony() instead.
    */
   public int $is_firm;

   public ?int $rating;

   public string $full_name, $email;
   public ?string $poznamka;

   /**
    * @deprecated This property is deprecated. Use $zakaznik–>getTelefonPrimarni() instead.
    */
   public ?string $telefon;

   /**
    * @deprecated This property is deprecated. Use $zakaznik–>getTelefony() instead.
    */
   public ?string $telefon_nahradni;

   /**
    * @deprecated This property is deprecated. Use $zakaznik–>getFirma() instead.
    */
   public ?string $nazev_firmy,
      $ic,
      $dic;

   /**
    * @deprecated This property is deprecated. Use $zakaznik–>getAdresa() instead.
    */
   public ?string $ulice;

   /**
    * @deprecated This property is deprecated. Use $zakaznik–>getAdresa() instead.
    */
   public ?string $mesto;

   /**
    * @deprecated This property is deprecated. Use $zakaznik–>getAdresa() instead.
    */
   public ?string $psc;

   public int $id_jazyk;
   public DateTime $created, $updated;

   public function getInitials() :string {
      return $this->inicialy??= $this->prepareDefaultniInicialy();
   }

   public function getAvatar(int $size) :Html {
      $style = sprintf('style="background-color:%s; width: %dpx; height: %dpx; font-size: %dpx;"',
         AvatarData::COLORS[array_rand(AvatarData::COLORS, 1)], $size, $size, ($size/2.5));

      return Html::el( sprintf('div class="profile-pic-bg" %s', $style))->setHtml($this->getInitials());
   }

   public function saveDruhyKontakt(string $telefon, string $jmeno, ?string $email = null) {
      ZakazniciDruhyKontakt::saveDruhyKontakt($this->id, $telefon, $jmeno, $email, prava: 3);
   }

   public function getDruhyKontakt() :?ZakaznikDruhyKontaktRow {
      return $this->druhyKontakt ??= ZakazniciDruhyKontakt::get($this->id);
   }

   public function getFirstName() :string {
      return explode(' ', $this->full_name, 2)[0];
   }

   public function getLastName() :string {
      return explode(' ', $this->full_name, 2)[1];
   }

   public function generateLoginKey() :string {
      return $this->loginHash ??= OrganizaceZakaznikKeys::create($this)->hash;
   }

   public function hasSouhlas(SouhlasyTypy $typ) :bool {
      return !!OrganizaceZakazniciSouhlasy::getTypForZakaznik($typ, $this->id);
   }

   protected ?ZakaznikDruhyKontaktRow $druhyKontakt;

   private function prepareDefaultniInicialy() :string {
      $name_split = explode(' ', $this->full_name);
      $inicialy = '';

      foreach($name_split as $name){
         $inicialy .= substr($name,0,1);
      }

      return strtoupper($inicialy);
   }

   protected function save() {
      OrganizaceZakaznici::save($this);
   }

   public function getId() :int {
      return $this->id;
   }

   public function getFullName() :string {
      return $this->full_name;
   }

   public function getType() :FormularRespondentTypEnum {
      return FormularRespondentTypEnum::KLIENT;
   }

   public function getPrimarniTelefon() :?OrganizaceZakazniciTelefonRow {
      if(isset($this->telefonPrimarni))
         return $this->telefonPrimarni;

      foreach($this->getTelefony() as $telefonRow)
         if($telefonRow->is_primarni === 1)
            return $this->telefonPrimarni = $telefonRow;

      if(empty($this->getTelefony()))
         return $this->telefonPrimarni = null;

      return $this->telefonPrimarni = $this->getTelefony()[array_key_first($this->getTelefony())];
   }

   /** @return OrganizaceZakazniciTelefonRow[] */
   public function getTelefony() :array {
      return $this->telefony ??= OrganizaceZakazniciTelefon::getForZakaznik($this->id);
   }

   public function getAdresa() :?OrganizaceZakaznikAdresaRow {
      return $this->adresa ??= OrganizaceZakaznikAdresa::get($this->id);
   }

   public function getFirma() :?OrganizaceZakaznikFirmaRow {
      return $this->firma ??= OrganizaceZakaznikFirma::get($this->id);
   }

   public function getTelefonQR() :Html {
      $telefon = $this->getPrimarniTelefon();

      if(!$telefon)
         return new Html('');

      $qr = Builder::create()
         ->writer(new PngWriter())
         ->data('tel:' . str_replace(' ', '', $telefon))
         ->encoding(new Encoding('UTF-8'))
         ->errorCorrectionLevel(ErrorCorrectionLevel::Low)
         ->size(50)
         ->margin(5)
         ->build();

      return new Html(sprintf('<img class="bg-white p-1 radius-card" src="data:image/png;base64,%s" alt="call now">', base64_encode($qr->getString())));
   }

   public function getJazyk() :Jazyky {
      return Jazyky::from($this->id_jazyk);
   }

   public function getEmailRecipient() :MailRecipient {
      return new MailRecipient($this->email, $this->full_name, $this->getJazyk());
   }

   /** @return ZakazniciPrirazenyTagRow[] */
   public function getTagy() :array {
      return $this->tagy ??= ZakazniciPrirazeneTagy::getForZakaznik($this->id);
   }

   public function getZakaznikID() :int {
      return $this->id_zakaznik;
   }

   public function getRow() :OrganizaceZakaznikRow {
      return $this;
   }

   private ?string $loginHash;
   private ?OrganizaceZakazniciTelefonRow $telefonPrimarni;

   /** @var OrganizaceZakazniciTelefonRow[] */
   private array $telefony;
   private ?OrganizaceZakaznikAdresaRow $adresa;
   private ?OrganizaceZakaznikFirmaRow $firma;
   private string $inicialy;

   /** @var ZakazniciPrirazenyTagRow[] */
   private array $tagy;
}