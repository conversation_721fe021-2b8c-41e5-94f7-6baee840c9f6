<?php namespace app\system\model\organizace\zakaznici\email;

use app\system\mailer\templates\BaseEmail;
use app\system\mailer\templates\properties\groups\ClientCodeProperties;
use app\system\mailer\templates\Templates;
use app\system\model\katalog\zakaznici\verify\KatalogExistZakaznikCodeRow;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 03.09.2025 */
class VerifikaceExistZakaznikEmail extends BaseEmail
{

   public function getTemplate() :Templates {
      return Templates::VER_EXIST_ZAKAZNIK;
   }

   public function getPropertiesGroups() :array {
      return [
         ClientCodeProperties::class,
      ];
   }

   public function setCodeRow(KatalogExistZakaznikCodeRow $codeRow) :VerifikaceExistZakaznikEmail {
      $this->codeRow = $codeRow;
      return $this;
   }

   protected function preparePropertiesGroups() :void {
      if(!isset($this->codeRow))
         throw new \Exception('Verification session nen<PERSON>');

      $this->addPropertiesGroup(new ClientCodeProperties($this->codeRow));
   }

   private KatalogExistZakaznikCodeRow $codeRow;
}