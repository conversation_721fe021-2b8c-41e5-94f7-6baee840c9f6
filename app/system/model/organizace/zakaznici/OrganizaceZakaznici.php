<?php namespace app\system\model\organizace\zakaznici;

use app\system\application\ApplicationVersion;
use app\system\flash\FlashMessages;
use app\system\model\Eventy;
use app\system\model\nabidka\VendorNabidky;
use app\system\model\nabidka\VenueNabidky;
use app\system\model\organizace\zakaznici\keys\OrganizaceZakaznikKeys;
use app\system\model\poptavky\VendorPoptavky;
use app\system\model\poptavky\VenuePoptavky;
use app\system\model\zakaznici\logged\LoggedZakazniciSession;
use app\system\model\zakaznici\Zakaznici;
use app\system\Session;
use dibi;
use Dibi\Fluent;
use Nette\Utils\Random;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 01.04.2022 */
class OrganizaceZakaznici
{

   const TABLE = 'organizace_zakaznici';
   const ID = 'id';

   /** @return OrganizaceZakaznikRow[]|Fluent */
   public static function find() :array|Fluent {
      return dibi::select('mz.id, mz.id_zakaznik, mz.id_organizace, mz.full_name, mz.telefon, mz.telefon_nahradni, 
         mz.poznamka, mz.rating, mz.is_firm, mz.nazev_firmy, mz.ic, mz.dic, z.email, mz.ulice, mz.mesto, mz.psc, mz.id_jazyk, mz.created, mz.updated')
         ->from(self::TABLE, 'mz')
         ->join(Zakaznici::TABLE, 'z')->on('mz.id_zakaznik = z.id_zakaznik')
         ->setupResult('setRowClass', OrganizaceZakaznikRow::class);
   }

   /** @return OrganizaceZakaznikRow[]|Fluent */
   public static function findBy(?int $id_zakaznik = null, ?int $id_mista = null) :array|Fluent {
      if($id_zakaznik)
         return self::find()
            ->where('mz.%sql = %i', self::ID, $id_zakaznik);

      if($id_mista)
         return self::find()
            ->where('mz.id_organizace = %i', $id_mista);

      return self::find();
   }

   public static function checkExistingRelation(int $id_zakaznik, int $id_organizace) :?int {
      return dibi::select('id')->from(self::TABLE)
         ->where('id_zakaznik = %i AND id_organizace = %i', $id_zakaznik, $id_organizace)
         ->fetchSingle();
   }

   public static function get(int $id_zakaznik) :?OrganizaceZakaznikRow {
      static $zakaznici;
      return ($zakaznici[$id_zakaznik] ??= self::findBy($id_zakaznik)->fetch() ?: false) ?: null;
   }

   public static function getWhere(array $where) :?OrganizaceZakaznikRow {
      return self::find()
         ->where($where)
         ->fetch();
   }

   public static function getFromNabidka(int $id_nabidka, bool $isVendor = false) :?OrganizaceZakaznikRow {
      $query = self::find();

      if(!$isVendor)
         return $query
            ->join('leady as l')->on('l.id_zakaznika = mz.id')
            ->join('nabidka as n')->on('n.id_lead = l.id_lead')
            ->where('n.id_nabidka = %i', $id_nabidka)
            ->fetch();
      else
         return $query
            ->join(VendorPoptavky::TABLE, 'l')->on('l.id_zakaznika = mz.id')
            ->join(VendorNabidky::TABLE, 'n')->on('n.id_poptavka = l.id')
            ->where('n.id= %i', $id_nabidka)
            ->fetch();
   }

   /** @return OrganizaceZakaznikRow[] */
   public static function getFromNabidky(array $ids_nabidky, bool $isVendor = false) :array {
      $query = self::find();

      if(!$isVendor)
         return $query
            ->join(VenuePoptavky::TABLE, 'l')->on('l.id_zakaznika = mz.id')
            ->join(VenueNabidky::TABLE, 'n')->on('n.id_lead = l.id_lead')
            ->where('n.id_nabidka IN %in', $ids_nabidky)
            ->fetchAssoc('id');
      else
         return $query
            ->join(VendorPoptavky::TABLE, 'l')->on('l.id_zakaznika = mz.id')
            ->join(VendorNabidky::TABLE, 'n')->on('n.id_poptavka = l.id')
            ->where('n.id IN %in', $ids_nabidky)
            ->fetchAssoc('id');
   }

   public static function getFromEvent(int $id_event,
      ApplicationVersion $version = ApplicationVersion::VENUE) :?OrganizaceZakaznikRow
   {
      return self::find()
         ->join(Eventy::getTable($version), 'e')->on('e.id_zakaznik = mz.id')
         ->where('e.%sql = %i', Eventy::getPK($version), $id_event)
         ->fetch();
   }

   /** @return OrganizaceZakaznikRow[] */
   /** @param array<int> $id_events */
   public static function getFromMultipleEvents(array $id_events,
      ApplicationVersion $version = ApplicationVersion::VENUE) :array
   {
      return self::find()
         ->join(Eventy::getTable($version), 'e')->on('e.id_zakaznik = mz.id')
         ->where('e.%sql IN %in', Eventy::getPK($version), $id_events)
         ->fetchAssoc('id');
   }

   /** @return OrganizaceZakaznikRow[] */
   public static function getFromEventy(array $ids_eventy,
      ApplicationVersion $version = ApplicationVersion::VENUE) :array
   {
      return self::find()
         ->join(Eventy::getTable($version), 'e')->on('e.id_zakaznik = mz.id')
         ->where('e.%sql IN %in', Eventy::getPK($version), $ids_eventy)
         ->fetchAssoc('id');
   }

   /** @return OrganizaceZakaznikRow[] */
   public static function getForLoggedSession(int $idLoggedSession) :array {
      return self::find()
         ->join(OrganizaceZakaznikKeys::TABLE, 'ozk')->on('mz.id = ozk.id_zakaznik_organizace')
         ->join(LoggedZakazniciSession::TABLE_KEY_RELATION, 'lzk')->on('ozk.id = lzk.id_login')
         ->where('lzk.id_session = %i', $idLoggedSession)
         ->fetchAll();
   }

   public static function save(OrganizaceZakaznikRow $row) :OrganizaceZakaznikRow {
      if($row->{self::ID}?? false){
         dibi::update(self::TABLE, $row->toArray())
            ->where('%sql = %i',self::ID, $row->{self::ID})
            ->execute();
      } else{
         dibi::insert(self::TABLE, $row->toArray())
            ->execute();
         $row->{self::ID} = dibi::getInsertId();
      }

      return $row;
   }

   public static function saveRating(int $id_zakaznik, int $rating) :void {
      dibi::update(OrganizaceZakaznici::TABLE, ['rating%i' => $rating])
         ->where('%sql = %i', OrganizaceZakaznici::ID, $id_zakaznik)
         ->execute();
   }

   public static function getByEmail(string $email, int $id_misto) :?OrganizaceZakaznikRow{
      return self::find()
         ->where('(mz.email = %s AND mz.id_organizace = %i) OR (z.email = %s AND mz.id_organizace = %i)',
            $email, $id_misto, $email, $id_misto)
         ->fetch();
   }

   public static function checkExistEmail(string $email) :?int {
      return self::find()
         ->select(false)->select('z.id_zakaznik')
         ->where('z.email = %s', $email)
         ->fetchSingle();
   }

   public static function delete(int $id_zakaznika) :int {
      $affected = 0;
      $zkz = self::get($id_zakaznika);

      $affected += dibi::delete(self::TABLE)
         ->where([self::ID => $zkz->id])
         ->execute()->getRowCount();

      if(empty(self::getForZakaznikUnique($zkz->id_zakaznik)))
         $affected += Zakaznici::delete($zkz->id_zakaznik);

      return $affected;
   }

   public static function isLogged() :bool {
      return !!Session::get('zakaznik');
   }

   public static function logout() {
      Session::clear('zakaznik');
      FlashMessages::setInfo('Byl jsi odhlášen')->setTimeout(100000);
   }

   public static function insertRow(OrganizaceZakaznikRow $zakaznik) :void {
      dibi::query('INSERT INTO organizace_zakaznici %v', $zakaznik->toArray());
   }

   public static function generateLoginHash() :string {
      return Random::generate(20, 'a-zA-Z0-9');
   }

   /** @return OrganizaceZakaznikRow[] */
   public static function getByIds(array $ids) :array {
      return self::find()
         ->where('mz.id IN %in', $ids)
         ->fetchAssoc('id');
   }

   /** @return OrganizaceZakaznikRow[] */
   public static function getForZakaznikUnique(int $id_zakaznik, array $excludedOrganizaceID = []) :array {
      $query = self::find()
         ->where('mz.id_zakaznik = %i', $id_zakaznik);

      if(!empty($excludedOrganizaceID))
         $query->where('mz.id_organizace NOT IN %in', $excludedOrganizaceID);

      return $query->fetchAll();
   }
}