<?php namespace app\system\model\organizace\zdroje;

use dibi;
use Dibi\Fluent;

/** Created by <PERSON><PERSON><PERSON>. Date: 14.12.2022 */
class OrganizaceZdroje
{

   const TABLE = 'organizace_zdroje';
   const COLUMN_ID = 'id';
   const COLUMN_MISTO = 'id_mista';
   const COLUMN_NAZEV = 'nazev';
   const COLUMN_AKTIVNI = 'is_aktivni';

   /** @return OrganizaceZdrojRow[]|Fluent*/
   public static function find() :Fluent {
      return dibi::select('z.*')
         ->from(self::TABLE . ' as z')
         ->setupResult('setRowClass', OrganizaceZdrojRow::class);
   }

   public static function findByMisto(int $id_misto)  :Fluent {
      return self::find()
         ->where('%sql = %i', self::COLUMN_MISTO, $id_misto);
   }

   public static function get(int $id_zdroj) :?OrganizaceZdrojRow {
      return self::find()
         ->where('%sql = %i', self::COLUMN_ID, $id_zdroj)
         ->fetch();
   }

   /** @return OrganizaceZdrojRow[] */
   public static function getByMisto(int $id_misto) :array|null {
      return self::find()
         ->where('%sql = %i', self::COLUMN_MISTO, $id_misto)
         ->fetchAssoc('id');
   }

   public static function save(OrganizaceZdrojRow $row) :OrganizaceZdrojRow{
      if($row->id?? false){
         dibi::update(self::TABLE, $row->toArray())
            ->where('%sql = %i',self::COLUMN_ID, $row->id)
            ->execute();
      } else{
         dibi::insert(self::TABLE, $row->toArray())
            ->execute();
         $row->id = dibi::getInsertId();
      }
      return $row;
   }

   public static function saveFromPost(array $post) :void {
      $is_aktivni = 0;

      if(($post['is_aktivni_zdroj']?? false) === 'true' || ($post['is_aktivni_zdroj_novy']?? false) === 'true')
         $is_aktivni = 1;

      $zdroj = self::prepareFromPost($post, $is_aktivni, $post['id_zdroj'] ?? null);

      self::save($zdroj);
   }

   public static function prepareFromPost(array $post, int $is_aktivni, ?int $id_zdroj = null) :OrganizaceZdrojRow {
      $zdroj = new OrganizaceZdrojRow();
      $zdroj->is_aktivni = $is_aktivni;

      if($id_zdroj)
         $zdroj->id = $id_zdroj;

      if(isset($post['nazev_zdroj']))
         $zdroj->nazev = $post['nazev_zdroj'];

      if(isset($post['popis_zdroj']))
         $zdroj->popis = $post['popis_zdroj'];

      if(isset($post['id_mista']))
         $zdroj->id_mista = $post['id_mista'];

      return $zdroj;
   }

   public static function delete(int|OrganizaceZdrojRow $zdroj): void {
      if ($zdroj instanceof OrganizaceZdrojRow) {
         $zdrojRow = $zdroj;
      } else {
         $zdrojRow = self::get($zdroj);
         if (!$zdrojRow) {
            throw new \Exception('Zdroj s ID ' . $zdroj . ' nebyl nalezen');
         }
      }

      (new eventy\SmazatZdrojEvent($zdrojRow))->call();
   }
}