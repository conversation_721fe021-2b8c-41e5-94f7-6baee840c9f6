<?php namespace app\system\model\organizace\zdroje\eventy;

use app\system\application\ApplicationVersion;
use app\system\event\Event;
use app\system\flash\FlashMessages;
use app\system\model\organizace\zdroje\OrganizaceZdroje;
use app\system\model\organizace\zdroje\OrganizaceZdrojRow;
use app\system\model\poptavky\VendorPoptavky;
use app\system\model\poptavky\VenuePoptavky;
use dibi;

/** Created by Assistant. Date: 2025-01-XX */
class SmazatZdrojEvent extends Event
{

   public function __construct(
      private readonly OrganizaceZdrojRow $zdroj
   ) {}

   public function onCall(): void {
      // Check if zdroj is used by any poptavka
      if ($this->isZdrojUsedByPoptavka()) {
         FlashMessages::setError(
            sprintf('Zdroj "%s" nelze smazat, protože je přiřazen k jedné nebo více poptávkám. Nejprve odeberte zdroj ze všech poptávek nebo zdroj pouze deaktivujte.',
               $this->zdroj->nazev
            )
         );
         return;
      }

      // Delete the zdroj
      $this->deleteZdroj();

      $this->getLogger()->addLog(
         'Smazání zdroje',
         before: [
            'id_zdroj' => $this->zdroj->id,
            'nazev' => $this->zdroj->nazev,
            'id_mista' => $this->zdroj->id_mista
         ],
         after: ['deleted' => true]
      );

      FlashMessages::setSuccess(sprintf('Zdroj "%s" byl úspěšně smazán.', $this->zdroj->nazev));
   }

   private function isZdrojUsedByPoptavka(): bool {
      // Check in venue poptavky (table: 'leady', field: 'zdroj')
      $venueCount = dibi::select('COUNT(*)')
         ->from('leady')
         ->where('zdroj = %i', $this->zdroj->id)
         ->fetchSingle();

      if ($venueCount > 0) {
         return true;
      }

      // Check in vendor poptavky (table: 'dodavatele_poptavky', field: 'zdroj')
      $vendorCount = dibi::select('COUNT(*)')
         ->from('dodavatele_poptavky')
         ->where('zdroj = %i', $this->zdroj->id)
         ->fetchSingle();

      return $vendorCount > 0;
   }

   private function deleteZdroj(): void {
      dibi::delete(OrganizaceZdroje::TABLE)
         ->where(OrganizaceZdroje::COLUMN_ID . ' = %i', $this->zdroj->id)
         ->execute();
   }

   public function getZdroj(): OrganizaceZdrojRow {
      return $this->zdroj;
   }
}
