<?php namespace app\system\model\organizace\jazyk;

use app\system\model\translator\Jazyky;
use dibi;
use Dibi\Fluent;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 16.11.2022 */
class OrganizaceJazyky
{

   const TABLE = 'organizace_jazyky';
   const PK1 = 'id_mista';
   const PK2 = 'id_jazyk';

   /** @return OrganizaceJazykRow[]|Fluent */
   public static function find() :Fluent {
      return dibi::select('mj.*')
         ->from(self::TABLE, 'mj')
         ->setupResult('setRowClass', OrganizaceJazykRow::class);
   }

   public static function insert(OrganizaceJazykRow $row) :void {
      dibi::query('INSERT INTO %sql %v', self::TABLE, [
         self::PK1 => $row->{self::PK1},
         self::PK2 => $row->{self::PK2},
         'is_primary' => $row->is_primary,
      ]);
   }

   public static function save(OrganizaceJazykRow $row) :void {
      dibi::update(self::TABLE, ['is_primary' => $row->is_primary])
         ->where([
            self::PK1 => $row->{self::PK1},
            self::PK2 => $row->{self::PK2},
         ])->execute();
   }

   public static function delete(OrganizaceJazykRow $row) :void {
      dibi::delete(self::TABLE)
         ->where([
            self::PK1 => $row->{self::PK1},
            self::PK2 => $row->{self::PK2},
         ])->execute();
   }

   public static function getPrimary(int $id_mista) :int {
      static $primary;
      return $primary ??= self::find()
         ->select(false)->select('id_jazyk')
         ->where('id_mista = %i AND is_primary = 1', $id_mista)
         ->fetchSingle();
   }

//   @TODO ukládat a getovat z redisu, expiraci řešit nemusíme, ale musíme vyřešit že když dojde k úpravě tak musíme cache smazat a načíst znovu
   /** @return OrganizaceJazykRow[] */
   public static function getAll(int $id_mista) :array {
      static $c;
      return $c[$id_mista] ??= self::find()
         ->where('%n = %i', self::PK1, $id_mista)
         ->fetchAll();
   }

   public static function getOrganizaceJazyk(int $id_organizace, Jazyky $jazyk) :?OrganizaceJazykRow {
      return self::find()
         ->where('%n = %i AND %n = %i', self::PK1, $id_organizace, self::PK2, $jazyk->value)
         ->fetch();
   }

   public static function getPairs(int $id_mista) :array {
      $ret = [];

      foreach(self::getAll($id_mista) as $jazykRow)
         $ret[$jazykRow->id_jazyk] = Jazyky::from($jazykRow->id_jazyk)->getTitle();

      return $ret;
   }

   public static function setPrimary(int $id_organizace, Jazyky $jazyk) :void {
      foreach(self::getAll($id_organizace) as $jazykRow){
         if($jazykRow->is_primary === 1){
            $jazykRow->is_primary = 0;
            self::save($jazykRow);
            continue;
         }

         if($jazykRow->id_jazyk === $jazyk->value){
            $jazykRow->is_primary = 1;
            self::save($jazykRow);
         }
      }
   }

   /** @return Jazyky[] */
   public static function getAllJazyky(int $organizaceID) :array {
      $rows = self::getAll($organizaceID);
      $jazyky = [];

      foreach($rows as $jazykRow){
         $jazyk = Jazyky::from($jazykRow->id_jazyk);
         $jazyky[$jazyk->value] = $jazyk;
      }

      return $jazyky;
   }

   /** @return array<int, array<int, OrganizaceJazykRow>> */
   public static function getOrganizaceAssoc(int ...$organizaceIDs) :array {
      return self::find()
         ->where('mj.%n IN %in', self::PK1, $organizaceIDs)
         ->fetchAssoc(sprintf('%s|%s', self::PK1, self::PK2));
   }
}