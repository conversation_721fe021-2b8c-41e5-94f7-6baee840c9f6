<?php namespace app\system\model\organizace\kalendar;

use app\front\controllers\kalendar\IcalController;
use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\System;
use app\system\Environment;
use app\system\helpers\row\EntryRow;
use app\system\Redirect;
use app\system\SystemVersion;
use Dibi\DateTime;

class OrganizaceKalendarRow extends EntryRow
{

   public int $id;
   public int $id_organizace;
   public int $eventy;
   public int $smeny;
   public int $aktivity;
   public int $anonymni;
   public string $nazev;
   public string $unique_token;
   /**
    * @var OrganizaceKalendarPersonalRow[] $personalRows
    */
   private array $personalRows;

   public DateTime $created, $updated;

   public function getOrganizace() :OrganizaceRow {
      return Organizace::getMisto($this->id_organizace);
   }

   /**
    * @return OrganizaceKalendarPersonalRow[]
    */
   public function getPersonalRows() :array {
      return $this->personalRows??= OrganizaceKalendarPersonal::getByKalendarID($this->id);
   }

   public function getUrlLink(bool $withHostAndProtocol = true) :string {
      if(!$withHostAndProtocol)
         return IcalController::getUrl($this->unique_token);

      return Redirect::getHost(SystemVersion::APP)
         . ltrim(IcalController::getUrl($this->unique_token), '/');
   }
}