<?php namespace app\system\model\organizace\kalendar\export;

use app\front\organizace\model\organizace\Organizace;
use app\system\application\ApplicationVersion;
use app\system\model\event\adresy\EventDodavateleAdresy;
use app\system\model\event\adresy\EventDodavateleAdresyRow;
use app\system\model\event\BaseEventRow;
use app\system\model\event\data\aktivity\EventAktivityRow;
use app\system\model\event\EventAktivity;
use app\system\model\event\EventStavyEnum;
use app\system\model\event\mistnosti\EventMistnostRow;
use app\system\model\event\mistnosti\VenueEventMistnosti;
use app\system\model\event\personal\EventPersonal;
use app\system\model\event\personal\EventPersonalRow;
use app\system\model\event\prirazeni\EventPrirazenyPersonal;
use app\system\model\event\VenueEventRow;
use app\system\model\Eventy;
use app\system\model\mista\OrganizaceMista;
use app\system\model\mista\OrganizaceMistaRow;
use app\system\model\organizace\kalendar\OrganizaceKalendarPersonal;
use app\system\model\organizace\kalendar\OrganizaceKalendarPersonalRow;
use app\system\model\organizace\kalendar\OrganizaceKalendarRow;
use app\system\model\organizace\mistnosti\Mistnosti;
use app\system\model\organizace\mistnosti\MistnostRow;
use app\system\model\organizace\personal\Personal;
use app\system\model\organizace\pozice\PozicePersonal;
use DateTimeZone;
use Dibi\DateTime;

/** Created by Kryštof Czyź. Date: 10.07.2025 */
class ExportOrganizaceKalendar
{

   public static function generateIcalFeed(OrganizaceKalendarRow $kalendarRow) :string {
      return (new static($kalendarRow))->getICalFeed();
   }

   public function __construct(
      private readonly OrganizaceKalendarRow $kalendarRow,
      private readonly string $timezone = 'Europe/Prague',
   ) {
      $this->dtz = new DateTimeZone($this->timezone);
      $this->now = new DateTime('now', $this->dtz);
      $this->actualYear = intval(date('Y'));

      $this->headParams = [
         'VERSION:2.0',
         'PRODID:-//Qvamp Calendar Export//EN',
         'CALSCALE:GREGORIAN',
         'METHOD:PUBLISH',
      ];

      $this->prepareTimezoneHead();
   }

   public function getICalFeed() :string {
      $this->prepareData();
      $data = [];

      $data[] = 'BEGIN:VCALENDAR';

      $data[] = implode("\r\n", $this->headParams);
      $data[] = implode("\r\n", $this->iCalEventsData);

      $data[] = 'END:VCALENDAR';

      return implode("\r\n", $data) . "\r\n";
   }

   private function prepareTimezoneHead() :void {
      $this->headParams[] = 'BEGIN:VTIMEZONE';
      $this->headParams[] = sprintf('TZID:%s', $this->timezone);

      $startTs = (new DateTime("$this->actualYear-01-01", new DateTimeZone('UTC')))->getTimestamp();
      $endTs   = (new DateTime(($this->actualYear + 1)."-01-01", new DateTimeZone('UTC')))->getTimestamp();

      $transitions = $this->dtz->getTransitions($startTs, $endTs);

      for ($i = 1, $iMax = count($transitions); $i < $iMax; $i++) {
         $prev = $transitions[$i - 1];
         $curr = $transitions[$i];

         $type = $curr['isdst'] ? 'DAYLIGHT' : 'STANDARD';
         $dt = (new DateTime("@{$curr['ts']}"))->setTimezone($this->dtz);

         $fmtOffset = function(int $offsetSec) {
            $h = intdiv($offsetSec, 3600);
            $m = abs(($offsetSec % 3600) / 60);
            return sprintf("%+03d%02d", $h, $m);
         };

         $this->headParams[] = "BEGIN:{$type}";
         $this->headParams[] = "DTSTART:".$dt->format('Ymd\THis');
         $this->headParams[] = "TZOFFSETFROM:".$fmtOffset($prev['offset']);
         $this->headParams[] = "TZOFFSETTO:".$fmtOffset($curr['offset']);
         $this->headParams[] = "TZNAME:".$curr['abbr'];
         $this->headParams[] = "END:{$type}";
      }

      $this->headParams[] = 'END:VTIMEZONE';
   }


   private function prepareData() {
      $this->iCalEventsData = [];
      $this->personalRows = OrganizaceKalendarPersonal::getByKalendarID($this->kalendarRow->id);
      $version = $this->kalendarRow->getOrganizace()->getVersion();

      if($this->kalendarRow->eventy)
         $this->prepareEventy($version);

      if($this->kalendarRow->smeny)
         $this->prepareSmeny($version);

      if($this->kalendarRow->aktivity)
         $this->prepareAktivity($version);
   }

   private function prepareEventy(ApplicationVersion $version) :void {
      $queryEventy = Eventy::find($version)
         ->where('e.id_stav NOT IN %in', [EventStavyEnum::ZRUSENY->value])
         ->where('e.date_start >= %d', $this->kalendarRow->created)
         ->where('e.%n = %i', Eventy::getOrganizaceFK($version), $this->kalendarRow->id_organizace);

      if(!empty($this->personalRows))
         $queryEventy
            ->join(EventPrirazenyPersonal::getTable($version), 'epp')
            ->on('e.%n = epp.%n', Eventy::getPK($version), EventPrirazenyPersonal::PK_EVENT)
            ->where(
               'epp.%n IN %in',
               EventPrirazenyPersonal::PK_PERSONAL,
               $this->getPersonalIDS(),
            );

      /** @var BaseEventRow[] $eventy */
      $eventy = $queryEventy->fetchAll();

      if(empty($eventy)) return;

      $eventsID = array_map(function(BaseEventRow $row) { return $row->getID(); }, $eventy);
      $smeny = EventPersonal::getByMultipleEventID($eventsID, $version);

      $this->prepareEventsLocations($eventsID, $version);

      $personalFindID = $poziceFindID = [];

      foreach($smeny as $smenyEventu)
         foreach($smenyEventu as $smenaRow){
            $personalFindID[$smenaRow->id_prirazeny_uzivatel] ??= $smenaRow->id_prirazeny_uzivatel;

            if($smenaRow->id_pozice !== null)
               $poziceFindID[$smenaRow->id_pozice] ??= $smenaRow->id_pozice;
         }

      $personal = $pozice = [];

      if(!empty($personalFindID))
         $personal = Personal::getByIds($personalFindID);

      if(!empty($poziceFindID))
         $pozice = PozicePersonal::getByIds($poziceFindID);

      foreach($eventy as $eventRow){
         $uid = sprintf('event-%d-%d', $version->value, $eventRow->getID());

         if($this->kalendarRow->anonymni === 0)
            $nazev = $this->getToC($eventRow->nazev ?: $eventRow->getDefaultNazev());
         else
            $nazev = $this->getToC(sprintf('Obsazeno - %s', Organizace::getMisto($this->kalendarRow->id_organizace)->nazev));

         $descParts = [];

         if(!$this->kalendarRow->anonymni && (!empty($smeny[$eventRow->getID()] ?? []))) {
            $descParts[] = 'Rozpis personálu:';

            foreach($smeny[$eventRow->getID()] as $smenaRow){
               $descParts[] = sprintf(
                 '• %s%s (%s–%s)',
                 $personal[$smenaRow->id_prirazeny_uzivatel]->getFullName(),
                 $smenaRow->id_pozice ? " - {$pozice[$smenaRow->id_pozice]->nazev}" : '',
                 $smenaRow->dtStart->format('G:i'),
                 $smenaRow->dtEnd->format('G:i'),
               );
            }
         }

         $desc = !empty($descParts)
            ? str_replace("\n", "\\n", implode(PHP_EOL, $descParts))
            : '';

         $this->iCalEventsData[] = $this->implodeData([
            "BEGIN:VEVENT",
            "UID:$uid",
            "DTSTAMP:{$this->now->format('Ymd\THis')}",
            "DTSTART;TZID=$this->timezone:{$eventRow->date_start->format('Ymd\THis')}",
            "DTEND;TZID=$this->timezone:{$eventRow->date_end->format('Ymd\THis')}",
            "SUMMARY:$nazev",
            "DESCRIPTION:$desc",
            "LOCATION:{$this->getEventLocationString($version, $eventRow)}",
            "END:VEVENT",
         ]);
      }
   }

   private function prepareEventsLocations(array $eventsID, ApplicationVersion $version) :void {
      if($version === ApplicationVersion::VENUE){
         $this->eventProstory = VenueEventMistnosti::getForEventy(...$eventsID);
         $prostoryID = [];

         foreach($this->eventProstory as $eventProstory)
            foreach($eventProstory as $eventProstoryRow)
               $prostoryID[$eventProstoryRow->id_mistnost] ??= $eventProstoryRow->id_mistnost;

         $this->prostory = empty($prostoryID)
            ? []
            : Mistnosti::getByIDs($prostoryID);

         return;
      }

      if($version === ApplicationVersion::VENDOR){
         $this->dodavatelAdresy = EventDodavateleAdresy::getByEvents($eventsID);
         $mista = [];

         foreach($this->dodavatelAdresy as $dodavatel)
            foreach($dodavatel as $dodavatelRow)
               $mista[$dodavatelRow->id_misto] ??= $dodavatelRow->id_misto;

         $this->mista = empty($mista)
            ? []
            : OrganizaceMista::getByIds($mista);
      }
   }

   private function getEventLocationString(ApplicationVersion $version, BaseEventRow $event) :string {
      if($version === ApplicationVersion::VENUE && $event instanceof VenueEventRow){
         if($event->is_vsechny_mistnosti === 1)
            return 'Celý prostor';

         if(!isset($this->eventProstory[$event->getID()]))
            return '';

         return implode(
            ',',
            array_map(
               function(EventMistnostRow $mistnostRow){
                  return ($this->prostory[$mistnostRow->id_mistnost] ?? null)?->nazev ?: '';
               },
               $this->eventProstory[$event->getID()]
            )
         );
      }

      if($version === ApplicationVersion::VENDOR){
         if(!isset($this->dodavatelAdresy[$event->getID()]))
            return '';

         return implode(
            ',',
            array_map(
               function(EventDodavateleAdresyRow $dodavatelRow){
                  if(!isset($this->mista[$dodavatelRow->id_misto]))
                     return '';

                  $misto = $this->mista[$dodavatelRow->id_misto];

                  return $misto->nazev
                     . ($misto->mesto ? " $misto->mesto" : '')
                     . ($misto->ulice ? " $misto->ulice" : '')
                     . ($misto->psc ? " $misto->psc" : '');
               },
               $this->dodavatelAdresy[$event->getID()]
            )
         );
      }

      return '';
   }

   private function prepareSmeny(ApplicationVersion $version) :void {
      $smenyQuery = EventPersonal::find($version)
         ->where('e.%n = %i', Eventy::getOrganizaceFK($version), $this->kalendarRow->id_organizace)
         ->where('ep.date >= %d', date('Y-m-d'));

      if(!empty($this->personalRows))
         $smenyQuery
            ->where(
               'ep.%n IN %in',
               EventPersonal::COLUMN_PRIRAZENY,
               $this->getPersonalIDS(),
            );

      /** @var EventPersonalRow[] $smeny */
      if(empty($smeny = $smenyQuery->fetchAll())) return;

      $eventsID = $poziceID = [];

      foreach($smeny as $smenaRow){
         $eventsID[$smenaRow->id_event] ??= $smenaRow->id_event;

         if($smenaRow->id_pozice !== null)
            $poziceID[$smenaRow->id_pozice] ??= $smenaRow->id_pozice;
      }

      $eventy = Eventy::getMultiple($version, $eventsID);
      $pozice = !empty($poziceID) ? PozicePersonal::getByIds($poziceID) : [];

      foreach($smeny as $smenaRow){
         $uid = sprintf('smena-%d-%d', $version->value, $smenaRow->id);

         $nazev = ($eventy[$smenaRow->id_event]->nazev ?: $eventy[$smenaRow->id_event]->getDefaultNazev());

         if($smenaRow->id_pozice)
            $nazev .= " - {$pozice[$smenaRow->id_pozice]->nazev}";

         $this->iCalEventsData[] = $this->implodeData([
            "BEGIN:VEVENT",
            "UID:$uid",
            "DTSTAMP:{$this->now->format('Ymd\THis')}",
            "DTSTART;TZID=$this->timezone:{$smenaRow->dtStart->format('Ymd\THis')}",
            "DTEND;TZID=$this->timezone:{$smenaRow->dtEnd->format('Ymd\THis')}",
            "SUMMARY:{$this->getToC($nazev)}",
            "DESCRIPTION:",
            "LOCATION:",
            "END:VEVENT",
         ]);
      }
   }

   private function prepareAktivity(ApplicationVersion $version) :void {
      $queryAktivity = EventAktivity::find($version)
         ->where('ea.%n = %i', EventAktivity::FK_ORGANIZACE, $this->kalendarRow->id_organizace)
         ->where('ea.termin >= %d', date('Y-m-d'));

      if(!empty($this->personalRows))
         $queryAktivity
            ->where('ea.%n IN %in', EventAktivity::FK_PRIRAZENY_PERSONAL, $this->getPersonalIDS());

      /** @var EventAktivityRow[] $aktivity */
      if(empty($aktivity = $queryAktivity->fetchAll())) return;

      foreach($aktivity as $aktivitaRow){
         $uid = sprintf('aktivita-%d-%d', $version->value, $aktivitaRow->id_aktivita);

         $nazev = $aktivitaRow->nazev;
         $desc = htmlspecialchars($aktivitaRow->popis ?: '');

         $dtStart = ($aktivitaRow->dtStart ?: $aktivitaRow->termin->setTime(10,0))->format('Ymd\THis');
         $dtEnd = ($aktivitaRow->dtEnd ?: $aktivitaRow->termin->setTime(12,0))->format('Ymd\THis');

         $this->iCalEventsData[] = $this->implodeData([
            "BEGIN:VEVENT",
            "UID:$uid",
            "DTSTAMP:{$this->now->format('Ymd\THis')}",
            "DTSTART;TZID=$this->timezone:$dtStart",
            "DTEND;TZID=$this->timezone:$dtEnd",
            "SUMMARY:{$this->getToC($nazev)}",
            "DESCRIPTION:{$this->getToC($desc)}",
            "LOCATION:",
            "END:VEVENT",
         ]);
      }
   }

   private function getToC(string $data) :string {
      return addcslashes($data, ",;\\");
   }

   private function implodeData(array $data) :string {
      return implode("\r\n", $data);
   }

   private function getPersonalIDS() :array {
      return array_map(function(OrganizaceKalendarPersonalRow $row) { return $row->id_personal; }, $this->personalRows);
   }

   /**
    * @var EventDodavateleAdresyRow[][]
    */
   protected array $dodavatelAdresy;
   /**
    * @var OrganizaceMistaRow[]
    */
   protected array $mista;
   /**
    * @var EventMistnostRow[][]
    */
   protected array $eventProstory;
   /**
    * @var MistnostRow[]
    */
   protected array $prostory;

   private DateTime $now;

   /** @var string[] */
   private array $iCalEventsData;

   /** @var OrganizaceKalendarPersonalRow[] */
   private array $personalRows;

   private readonly DateTimeZone $dtz;
   private readonly int $actualYear;

   private array $headParams;
}