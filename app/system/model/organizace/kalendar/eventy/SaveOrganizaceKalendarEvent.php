<?php namespace app\system\model\organizace\kalendar\eventy;

use app\system\event\Event;
use app\system\event\FlashException;
use app\system\event\IEventEntityLogger;
use app\system\model\organizace\kalendar\OrganizaceKalendar;
use app\system\model\organizace\kalendar\OrganizaceKalendarPersonal;
use app\system\model\organizace\kalendar\OrganizaceKalendarPersonalRow;
use app\system\model\organizace\kalendar\OrganizaceKalendarRow;
use dibi;
use Tracy\Debugger;
use Tracy\ILogger;

class SaveOrganizaceKalendarEvent extends Event
   implements IEventEntityLogger
{

   /**
    * @param array{
    *     id_kalendar?: string,
    *     nazev: string,
    *     personal?: string[],
    *     eventy?: string,
    *     smeny?: string,
    *     aktivity?: string,
    *     anonymni?: string,
    * } $post
    * @param int $id_organizace
    * @return SaveOrganizaceKalendarEvent
    * @throws FlashException
    */
   public static function create(array $post, int $id_organizace) :self {
      $row = new OrganizaceKalendarRow();;
      $row->id_organizace = $id_organizace;
      $row->unique_token = bin2hex(random_bytes(16));

      return new self($row, $post);
   }

   /**
    * @param OrganizaceKalendarRow $row
    * @param array{
    *     id_kalendar?: string,
    *     nazev: string,
    *     personal?: string[],
    *     eventy?: string,
    *     smeny?: string,
    *     aktivity?: string,
    *     anonymni?: string,
    * } $post
    * @throws FlashException
    */
   public function __construct(private readonly OrganizaceKalendarRow $row, private readonly array $post) {
      if (!isset($this->post['nazev'])) {
         throw FlashException::create('Nebyl zadán název kalendáře');
      }

      if (!isset($this->post['eventy']) && !isset($this->post['smeny']) && !isset($this->post['aktivity'])) {
         throw FlashException::create('Nebyla vybrána ani jedna možnost');
      }

      $this->row->nazev = $this->post['nazev'];
      $this->row->smeny = isset($this->post['smeny']) ? 1 : 0;
      $this->row->eventy = isset($this->post['eventy']) ? 1 : 0;
      $this->row->aktivity = isset($this->post['aktivity']) ? 1 : 0;
      $this->row->anonymni = isset($this->post['anonymni']) ? 1 : 0;

      $personalRows = isset($this->row->id) ? $row->getPersonalRows() : [];
      $postPersonalIDs = array_map('intval', $this->post['personal']?? []);

      $existingPersonalIDs = array_map(
         fn(OrganizaceKalendarPersonalRow $row) => $row->id_personal,
         $personalRows
      );

      $postPersonalIDs = array_unique($postPersonalIDs);
      $this->toAdd = array_diff($postPersonalIDs, $existingPersonalIDs);
      $this->toDelete = array_diff($existingPersonalIDs, $postPersonalIDs);
   }


   public function onCall(): void {
      try {
         dibi::begin();

         OrganizaceKalendar::save($this->row);

         if(!empty($this->toDelete))
            OrganizaceKalendarPersonal::deleteByKalendar($this->row->id, $this->toDelete);

         if(!empty($this->toAdd)){
            $personalToAddRows = [];

            foreach ($this->toAdd as $id_personal) {
               $r = new OrganizaceKalendarPersonalRow();
               $r->id_kalendar = $this->row->id;
               $r->id_personal = $id_personal;
               $personalToAddRows[] = $r;
            }

            OrganizaceKalendarPersonal::save(...$personalToAddRows);
         }

         dibi::commit();
      }catch (\Throwable $exception){
         dibi::rollback();
         Debugger::log($exception, ILogger::ERROR);
         throw FlashException::create('Chyba při vytvoření kalendáře', $exception);
      }
   }

   public static function getUserLabel() :string {
      return 'Přidání kalendáře organizace';
   }

   public function getEntityId() :int|string {
      return $this->row->id;
   }

   protected array $toAdd;
   protected array $toDelete;
}