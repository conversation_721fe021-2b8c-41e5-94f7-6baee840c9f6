<?php namespace app\system\model\organizace\subscription\faktury;

use app\system\helpers\row\EntryRow;
use app\system\model\organizace\subscription\faktury\generator\FakturaGenerator;
use app\system\model\organizace\subscription\platby\MistoSubscriptionPlatby;
use app\system\model\organizace\subscription\platby\MistoSubscriptionPlatbyRow;
use Dibi\DateTime;
use Endroid\QrCode\Builder\Builder;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\Writer\PngWriter;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 19.04.2023 */
class SubscriptionFakturaRow extends EntryRow
{

   public int $id,
      $id_mista,
      $id_mena,
      $rok,
      $variabilni_symbol;

   public int $id_platby;

   public SubscriptionFakturaTypy $typ = SubscriptionFakturaTypy::KLASICKA;

   public DateTime $vystaveni, $splatnost, $duzp;
   public DateTime $updated, $created;

   public function __construct($arr = []) {
      if(isset($arr['typ'])){
         $this->typ = SubscriptionFakturaTypy::from($arr['typ']);
         unset($arr['typ']);
      }

      parent::__construct($arr);
   }

   public function save() :static {
      SubscriptionFaktury::save($this);
      return $this;
   }

   public function getTableName() :string {
      return SubscriptionFaktury::TABLE;
   }

   public function generateVariabilni() :static {
      if(!isset($this->rok))
         $this->rok = (int)date('Y');

      $this->variabilni_symbol = sprintf('%d%06d', $this->rok, SubscriptionFaktury::getNewVariabil($this->rok) + 1);

      return $this;
   }

   public function getPdfFile() :string {
      return FakturaGenerator::getOrGenerate($this);
   }

   public function getPaymentQR() :Html {
      $iban = '************************';
      $amount = bcdiv($this->getPlatba()->getPriceDph()->getAmount(), '100', 2);
      $currency = $this->getPlatba()->getPriceDph()->getCurrency()->getCode();
      $vs = $this->variabilni_symbol;
      $msg = "Předplatné QVAMP";

      $payload = 'SPD*1.0'
         . '*ACC:' . $iban
         . '*AM:'  . $amount
         . '*CC:'  . $currency
         . '*X-VS:' . $vs
         . '*MSG:' . str_replace('*', '%2A', $msg);

      $qr = Builder::create()
         ->writer(new PngWriter())
         ->data($payload)
         ->encoding(new Encoding('UTF-8'))
         ->size(80)
         ->margin(5)
         ->build();

      return new Html(sprintf('<img class="bg-white" src="data:image/png;base64,%s" alt="qrCode">', base64_encode($qr->getString())));
   }

   public function getPlatba() :?MistoSubscriptionPlatbyRow {
      return $this->platba ??= MistoSubscriptionPlatby::get($this->id_platby);
   }

   protected ?MistoSubscriptionPlatbyRow $platba;
}