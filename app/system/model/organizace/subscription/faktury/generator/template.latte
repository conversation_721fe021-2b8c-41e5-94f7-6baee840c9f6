{varType app\system\model\organizace\subscription\faktury\generator\FakturaGenerator $generator}
{varType app\system\model\organizace\subscription\platby\MistoSubscriptionPlatbyRow $platba}
{varType app\system\model\organizace\subscription\faktury\SubscriptionFakturaRow $faktura}
{varType app\system\model\organizace\Misto $misto}
{varType string $style}
{varType string $vatRate}
{varType string $lang}

<!DOCTYPE html
        PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
{*@TODO lang*}
<html xmlns="http://www.w3.org/1999/xhtml" lang="{$lang}">

<head>
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
   <meta name="viewport" content="width=device-width, initial-scale=1.0"/>

   <style type="text/css">
      @font-face {
         font-family: 'Roboto';
         font-style: normal;
         font-weight: normal;
         src: url('/app/system/model/organizace/subscriptionption/faktury/generator/fonts/Roboto-Regular.ttf') format('truetype');
      }

      {$style|noescape}
   </style>
   <title>{_'Faktura'} {$faktura->variabilni_symbol}</title>
</head>

<body>
<table width="100%" border="0" cellpadding="0" cellspacing="0">
   <tr>
      <td>
         <!-- // START CONTAINER -->
         <table class="container bg-white" width="600px" align="center" border="0" cellpadding="0" cellspacing="0">
            <tr>
               <td>
                  <table class="bg-white table-set" width="100%" align="center" border="0" cellpadding="0" cellspacing="0">
                     <tr>
                        <td>
                           <img src="{$generator->latteImgData('files/system/img/icons/qvamp_logo_default.svg')|nocheck}"
                                alt="Qvamp Logo" width="180px"
                                height="auto">
                        </td>
                        <td align="right">
                           <p class="title">{_'Faktura'} | {$faktura->variabilni_symbol}</p>
                        </td>
                     </tr>
                  </table>
               </td>
            </tr>
            <tr>
               <td>
                  <table class="bg-greywhite table-set" width="100%" border="0" cellpadding="0" cellspacing="0">
                     <tr>
                        <td>
                           <table width="100%" border="0" cellspacing="0" cellpadding="0">
                              <tr>
                                 <td width="50%">
                                    <table class="invoice-left" width="100%" align="left" border="0" cellspacing="0"
                                           cellpadding="0">
                                       <tr>
                                          <td>
                                             <p class="contact-label">{_'Dodavatel'}</p>
                                          </td>
                                       </tr>
                                       <tr>
                                          <td class="pt-10">
                                             <p class="contact-address">‹beat›app, s.r.o.</p>
                                             <p class="contact-address">Chalupníkova 1234/1</p>
                                             <p class="contact-address">70030 Ostrava</p>
                                             <p class="contact-address">{_'Česká republika'}</p>
                                             <span class="contact-address">{_'IČO'}: 14270552</span><span> | {_'DIČ'}: CZ 14270552</span>
                                             <p class="contact-address mt-10">{_'Krajský soud v Ostravě, odd. C vložka 88504'}</p>
                                          </td>
                                       </tr>
                                    </table>
                                 </td>
                                 <td width="50%">
                                    <table class="invoice-right" width="100%" align="right" border="0" cellpadding="0"
                                           cellspacing="0">
                                       <tr>
                                          <td>
                                             <p class="contact-label">{_'Odběratel'}</p>
                                          </td>
                                       </tr>
                                       <tr>
                                          <td class="pt-10">
                                             <p class="contact-address">{$misto->getFakturacniAdresa()->jmeno}</p>
                                             <p class="contact-address">{$misto->getFakturacniAdresa()->ulice}</p>
                                             <p class="contact-address">{$misto->getFakturacniAdresa()->mesto}, {$misto->getFakturacniAdresa()->psc}</p>
                                             <span n:if="$misto->getFakturacniAdresa()->ico"
                                                     class="contact-address">{_'IČO'}: {$misto->getFakturacniAdresa()->ico}</span>
                                             <span n:if="$misto->getFakturacniAdresa()->dic"
                                                     class="contact-address"> | {_'DIČ'}: {$misto->getFakturacniAdresa()->dic}</span>
                                          </td>
                                       </tr>
                                    </table>
                                 </td>
                              </tr>
                           </table>
                        </td>
                     </tr>
                  </table>
               </td>
            </tr>
            <tr>
               <td>
                  <table class="bg-greywhite table-set" width="100%" border="0" cellpadding="0" cellspacing="0">
                     <tr>
                        <td>
                           <table width="100%" border="0" cellspacing="0" cellpadding="0">
                              <tr>
                                 <td width="50%">
                                    <table class="invoice-left" width="100%" align="left" border="0" cellspacing="0"
                                           cellpadding="0">
                                       <tr>
                                          <td class="pt-10">
                                             <p class="contact-address">{_'Banka'}: Česká Spořitelna, a.s.</p>
                                             <p class="contact-address">{_'Číslo účtu'}: ********** / 0800</p>
                                             <p class="contact-address">{_'IBAN'}: CZ45 0800 0000 0064 9952 4379</p>
                                             <p class="contact-address">{_'SWIFT'}: GIBACZPX</p>
                                          </td>
                                       </tr>
                                    </table>
                                 </td>
                                 <td width="50%">
                                    <table class="invoice-right" width="100%" align="right" border="0"
                                           cellpadding="0"
                                           cellspacing="0">
                                       <tr>
                                          <td class="pt-10">
                                             <p class="contact-address">{_'Datum vystavení'}: {$faktura->created|date: 'j.n.Y'}</p>
                                             <p class="contact-address">{_'Datum splatnosti'}: {$faktura->splatnost|date: 'j.n.Y'}</p>

                                             <p class="contact-address">{_'DUZP'}: {$faktura->duzp|date: 'j.n.Y'}</p>
                                             <p></p>
                                          </td>
                                       </tr>
                                    </table>
                                 </td>
                              </tr>
                           </table>
                        </td>
                     </tr>
                  </table>
               </td>
            </tr>
            <tr>
               <td>
                  <table class="platba" width="100%" border="0" cellspacing="0" cellpadding="0">
                     <tr>
                        <td n:if="$platba->paid">
                           <p class="contact-label text-white">{_'Datum úhrady'}</p>
                           <p class="contact-text">{$platba->paid|date: 'j.n.Y'}</p>
                        </td>
                        <td>
                           <p class="contact-label text-white">{_'Variabilní symbol'}</p>
                           <p class="contact-text">{$faktura->variabilni_symbol}</p>
                        </td>
                        <td>
                           <p class="contact-label text-white">{_'ID platby'}</p>
                           <p class="contact-uid">{$platba->uid_subscription}</p>
                        </td>
                        <td n:if="!$platba->paid" align="right">
                           {$faktura->getPaymentQR()}
                        </td>
                     </tr>
                  </table>
               </td>
            </tr>
            <tr>
               <td class="pt-30">
                  <table class="apple-services" width="100%" border="0" cellpadding="0" cellspacing="0">
                     <tr class="table-set">
                        <td width="55%">
                           <p class="contact-label">
                              {_'Položky'}
                           </p>
                        </td>
                        <td width="10%" align="center">
                           <p class="contact-label">
                              {_'Počet m.j.'}
                           </p>
                        </td>
                        <td width="10%" align="center">
                           <p class="contact-label">
                              {_'Cena za m.j.'}
                           </p>
                        </td>
                        <td width="25%" align="right">
                           <p class="contact-label">
                              {_'Cena'}
                           </p>
                        </td>
                     </tr>
                  </table>
               </td>
            </tr>

            <tr>
               <td style="padding-left: 20px;">
                  <table width="100%" border="0" cellpadding="0" cellspacing="0" style="padding: 0 0 15px;">
                     {foreach $platba->getPolozky() as $polozka}
                        <tr>
                           <td n:class="!$iterator->isFirst()? 'pt-10 border-top', pb-10">
                              <table class="table-set" width="100%" border="0" cellpadding="0" cellspacing="0">
                                 <tr>
                                    <td width="55%" align="left">
                                       <p style="margin: 0; font-size: 12px; font-weight: 600; color: #333333;">
                                          {$polozka->nazev}
                                       </p>
                                       <p style="margin: 0; font-size: 12px; color: #666666;">
                                          {$polozka->nazev}
                                       </p>
                                       <p style="margin: 0; font-size: 12px; color: #666666;">
                                          {_'Předplatné do'}: {$misto->getSubscription()->getRenewDate()|date: 'j.n.Y'}
                                       </p>
                                    </td>
                                    <td width="10%" align="left">
                                       {$polozka->pocet}
                                    </td>
                                    <td width="10%" align="center">
                                       {$polozka->getCena()->divide($polozka->pocet)|price}
                                    </td>
                                    <td width="25%" align="right">
                                       <p style="margin: 0 0 5px; font-size: 12px; font-weight: 600;">{$polozka->getCenaDph()|price}</p>
                                       <p style="margin: 0; font-size: 10px; font-weight: 400;"><span style="font-size: 8px">{_'Bez DPH:'}</span> {$polozka->getCena()|price}</p>
                                    </td>
                                 </tr>
                              </table>
                           </td>
                        </tr>
                     {/foreach}
                  </table>
               </td>
            </tr>

            <tr class="mt-20">
               <td class="border-top border-bottom" style="padding-top:10px;padding-bottom: 10px;">
                  <table width="100%" border="0" cellpadding="0" cellspacing="0">
                     <tr>
                        <td align="right" style="border-right: 1px solid #eeeeee;">
                           <p style="padding-right: 35px; font-size: 16px; text-transform: uppercase; color: #666666;">
                              {_'Celkem k úhradě'}
                           </p>
                        </td>
                        <td>

                        </td>
                        <td align="right" width="10%">
                           <p class="total-price"
                              style="padding-left: 80px; font-size: 16px; font-weight: 600;">
                              {$platba->getPriceDph()|price}
                           </p>
                        </td>
                     </tr>
                  </table>
               </td>
            </tr>

            <tr>
               <td>
                  <table width="100%" border="0" cellpadding="0" cellspacing="0" style="padding: 10px 0;">
                     <tr>
                        <td align="right">
                           <p class="total-price" style="margin: 4px; font-size: 12px; color: #666666;">
                              {_'Cena bez DPH'} <span style="font-weight: 600; color:#000000;padding-left: 20px;">{$platba->getPrice()|price}</span>
                           </p>
                           <p class="total-price" style="margin: 4px; font-size: 12px; color: #666666;">
                              {_'DPH'} {$vatRate}%
                              <span style="font-weight: 600; color:#000000;padding-left: 20px;">{$platba->getPriceDph()->subtract($platba->getPrice())|price}</span>
                           </p>
                           <p class="total-price" style="margin: 4px; font-size: 12px; color: #666666;">
                              {_'Cena s DPH'} <span style="font-weight: 600; color:#000000;padding-left: 20px;">{$platba->getPriceDph()|price}</span>
                           </p>
                        </td>
                     </tr>
                  </table>
               </td>
            </tr>

            <tr n:if="$platba->paid !== null">
               <td>
                  <table class="mt-20" width="100%" border="0" cellpadding="0" cellspacing="0">
                     <tr class="my-3">
                        <td align="right"><p style="margin: 20px; font-size: 24px; color: #fd000b;">{_'Faktura je uhrazena'}</p></td>
                     </tr>
                  </table>
               </td>
            </tr>

            <tr>
               <td>
                  <table class="mt-20" width="100%" border="0" cellpadding="0" cellspacing="0">
                     <tr>
                        <td align="center" class="mt-20">
                           <p style="margin: 20px; font-size: 12px; color: #666666;">
                              {_'Potřebujete pomoct s nastavením předplatného?'}.
                              <a href="https://app.qvamp.com/navody"
                                 style="text-decoration: none; color: #007eff;" target="_blank">{_'Obraťte se na naši podporu'}</a>
                           </p>

                           <p style="margin: 20px; font-size: 12px; color: #666666;">
                              {_'Tento email chodí automaticky po zaplacení dalšího období, pokud si nepřejete emaily dostávat, změňte si email pro zasílání faktur v'}
                              <a href="#" style="text-decoration: none; color: #007eff;">{_'nastavení místa'}</a>.</p>
                        </td>
                     </tr>
                  </table>
               </td>
            </tr>
            <tr>
               <td>
                  <table width="100%" border="0" cellpadding="0" cellspacing="0" style="padding-top: 5px;">
                     <tr>
                        <td align="center">
                           <img src="{$generator->latteImgData('files/system/img/icons/qvamp_icon.png')|nocheck}"
                                alt="qvamp" width="20px"
                                height="auto">
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <table width="100%" border="0" cellpadding="0" cellspacing="0">
                              <tr class="mt-20">
                                 <td align="center">
                                    <p style="margin-bottom: 0; font-size: 12px; color: #666666;">
                                       Copyright © {date('Y')} <a href="https://qvamp.com" style="text-decoration: none; color: #007eff;" target="_blank">Qvamp</a> by ‹beat›app, s.r.o. | {_'email'}: <EMAIL> | {_'tel'}: +420 731 163 851
                                       <br>
                                       <p style="margin-bottom: 0; font-size: 12px; color: #666666;">{_'Všechna práva vyhrazena'}</p>
                                    </p>
                                 </td>
                              </tr>
                           </table>
                        </td>
                     </tr>
                  </table>
               </td>
            </tr>
         </table>
      </td>
   </tr>
</table>
<!-- END CONTAINER \\ -->
</body>

</html>