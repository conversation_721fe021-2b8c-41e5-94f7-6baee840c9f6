<?php namespace app\system\model\organizace\subscription\faktury\generator;

use app\system\component\Templater;
use app\system\helpers\Files;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\organizace\Misto;
use app\system\model\organizace\subscription\faktury\SubscriptionFakturaRow;
use app\system\model\organizace\subscription\MistoSubscription;
use app\system\model\translator\Jazyky;
use Dompdf\Dompdf;
use Dompdf\Options;
use Mpdf\Mpdf;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 19.04.2023 */
class FakturaGenerator
{

   public static function getOrGenerate(SubscriptionFakturaRow $faktura) :string {
      $instance = new self($faktura);

      if(file_exists($instance->getFilePathName()))
         return $instance->getFilePathName();

      return $instance->generate()->getFilePathName();
   }

   public function __construct(public readonly SubscriptionFakturaRow $faktura) { }

   public function getFilePathName() :string {
      return $this->fileName ??= sprintf('%s/%s', Files::prepareRootDirPath(Files::DATA_FAKTURY_DIR), $this->getFileName());
   }

   public function getFileName() :string {
      return $this->name ??= sprintf('qvamp_invoice_VS%d_%d.pdf', $this->faktura->variabilni_symbol, $this->faktura->id);
   }

//   @TODO připravit Latte makro pro fotky
   public function latteImgData(string $path) :string {
      $type = pathinfo($path, PATHINFO_EXTENSION);
      $data = file_get_contents($path);

      if($type === 'svg')
         $type = 'svg+xml';

      return sprintf('data:image/%s;base64,%s', $type, base64_encode($data));
   }

   private function generate() :static {
      $this->handleMpdf();
//      Zatím se nepoužívá
//      $this->handleDomPdf();
      return $this;
   }

   private function handleMpdf() :void {
      $this->prepareMpdf();
      $this->mpdf->WriteHTML($this->getFakturaTemplate());
      $this->mpdf->Output($this->getFilePathName());
   }

   private function prepareMpdf() :void {
      $logDir = Files::prepareRootDirPath(Files::DATA_FAKTURY_DIR);
      Files::checkDir($logDir);

      $this->mpdf = new Mpdf([
         'mode' => 'utf-8',
         'format' => [190, 236],
         'orientation' => 'P'
      ]);
   }

   private function handleDomPdf() {
      $this->prepareDomPdf();
      $this->dompdf->loadHtml($this->getFakturaTemplate(), 'UTF-8');

      $this->dompdf->setPaper('A4');

      $this->dompdf->render();
//      file_put_contents($this->getFilePathName(), $this->dompdf->output());
      $this->dompdf->stream($this->getFilePathName(), ['Attachment' => 0]);
      exit;
   }

   private function prepareDomPdf() :void {
      Files::checkDir(Files::prepareRootDirPath(Files::DATA_FAKTURY_DIR));

      $options = new Options();
      $options->setIsRemoteEnabled(true);
      $root = realpath(__DIR__ . '/../generator');
      $options->setChroot([
         $root
      ]);
      $options->set('defaultFont', 'Helvetica');

      $this->dompdf = new Dompdf($options);
   }
   private function getFakturaTemplate() :string {
      return Templater::prepare(__DIR__ . '/template.latte', [
         'style' => file_get_contents(__DIR__ . '/faktura.css'),
         'generator' => $this,
         'platba' => $this->faktura->getPlatba(),
         'faktura' => $this->faktura,
         'misto' => Misto::get($this->faktura->id_mista),
         'vatRate' => MistoSubscription::DPH,
         'lang' => Jazyky::from(OrganizaceJazyky::getPrimary($this->faktura->id_mista))->getISO6391(),
      ], false)->render();
   }

   private Mpdf $mpdf;
   private Dompdf $dompdf;
   private string $fileName;
   private string $name;
}