<?php namespace app\system\model\organizace\sluzby;

use app\system\helpers\row\attributes\IgnoreUpdateArray;
use app\system\helpers\row\EntryRow;
use Dibi\DateTime;
use Nette\Utils\Strings;

class OrganizaceSluzbaNazevRow extends EntryRow
{

   public int $id_sluzba;
   public int $id_jazyk;
   public string $nazev;
   public ?string $popis;

   public DateTime $created;
   public DateTime $updated;

   #[IgnoreUpdateArray]
   public string $icon;

   public function getPopisTextTruncate(int $limit = 100) :string {
      return Strings::truncate(
         strip_tags($this->popis ?: ''),
         $limit,
      );
   }
}