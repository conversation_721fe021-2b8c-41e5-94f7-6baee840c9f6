<?php namespace app\system\model\organizace\sluzby;

use app\system\model\organizace\sluzby\factory\OrganizaceSluzbaFactory;
use app\system\model\translator\Jazyky;
use dibi;
use Dibi\Fluent;

class OrganizaceSluzby
{

   const string TABLE = 'organizace_sluzby';
   const string PK = 'id';
   const string FK_ORGANIZACE = 'id_organizace';
   const string FK_SLUZBA = 'id_sluzba';

   /** @return OrganizaceSluzbyRow[]|Fluent */
   public static function find() :array|Fluent {
      return dibi::select('os.*')
         ->from(self::TABLE, 'os')
         ->setupResult('setRowClass', OrganizaceSluzbyRow::class);
   }

   public static function getAllInLanguage(int|Jazyky $jazyk, int $id_organizace = null) :array {
      return OrganizaceSluzbaFactory::getAll($jazyk, $id_organizace);
   }

   public static function getAllInLanguageById(int|Jazyky $jazyk, int $id_sluzba) :OrganizaceSluzbyRow {
      return OrganizaceSluzbaFactory::get($jazyk, $id_sluzba);
   }

   public static function deleteById(int $id_sluzba) :void {
      dibi::delete(self::TABLE)
         ->where('%n = %i', self::PK, $id_sluzba)
         ->execute();
   }

   public static function save(OrganizaceSluzbyRow $row) :void {
      if (!isset($row->id)) {
         dibi::insert(self::TABLE, $row->toArray())
            ->execute();
         $row->id = dibi::getInsertId();
      } else {
         dibi::update(self::TABLE, $row->toArray())
            ->where('id = %i', $row->id)
            ->execute();
      }
   }

   public static function get(int $id_sluzba) :?OrganizaceSluzbyRow {
      return self::find()
         ->where("os.%n = %i", self::PK, $id_sluzba)
         ->fetch();
   }

   /** @return OrganizaceSluzbyRow[] */
   public static function getForOrganizace(int $organizaceID) :array {
      static $cSluzby;
      return $cSluzby[$organizaceID] ??= self::find()
         ->where('os.%n = %i', self::FK_ORGANIZACE, $organizaceID)
         ->fetchAssoc(self::PK);
   }

   /** @return OrganizaceSluzbyRow[] */
   public static function getByIDS(int ...$sluzbaID) :array {
      return self::find()
         ->where('os.%n IN %in', self::PK, $sluzbaID)
         ->fetchAssoc(self::PK);
   }
}