<?php namespace app\system\model\formular;

use app\front\organizace\model\organizace\Organizace;
use app\katalog\organizace\DetailOrganizaceEntitaController;
use app\katalog\organizace\RouteEntityType;
use app\system\application\IEntityClientDetail;
use app\system\model\formular\data\FormularEntityData;
use app\system\model\formular\data\FormularRawRow;
use app\system\model\formular\data\FormularRespondentData;
use app\system\model\formular\data\InputData;
use app\system\model\formular\enumy\FormulareTypyEnum;
use app\system\model\formular\enumy\FormularRespondentTypEnum;
use app\system\model\formular\enumy\FormularStavy;
use app\system\model\formular\eventy\NovyFormularRespondent;
use app\system\model\translator\Jazyky;
use app\system\Redirect;
use app\system\SystemVersion;
use Dibi\DateTime;
use Nette\Utils\Strings;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 13.12.2023 */
class Formular
   implements IEntityClientDetail
{

   public static function get(int $id_formular) :?Formular {
      if(!($row = FormulareModel::getRaw($id_formular)))
         return null;

      return new self($row);
   }

   public readonly string $nazev;
   public readonly int $id_organizace;
   public readonly ?string $popis;
   public readonly Jazyky $jazyk;
   public readonly FormularStavy $stav;
   public readonly FormulareTypyEnum $typ;

   public function __construct(
      private readonly FormularRawRow $formularRow
   ) {
      $this->prirazeni = FormularEntityData::get($this->formularRow->id);

      $this->prepareMainData();
      $this->prepareInputs();
   }

   public function getAssignedEntityTypeTitle() :string {
      return $this->prirazeni
         ->getTyp()
         ->getTranslatedTitle();
   }

   /** @return InputData[] */
   public function getInputs() :array {
      return $this->inputy;
   }

   /** @return InputData[] */
   public function getInputsNameAssoc() :array {
      $inputy = [];

      foreach($this->inputy as $input)
         $inputy[$input->name] = $input;
      return $inputy;
   }

   public function getId() :int {
      return $this->formularRow->id;
   }

   public function getAssignedEntity() :IEntityFormular {
      return $this->prirazeni->getEntityItem();
   }

   public function getAssignedEntityId() :int {
      return $this->prirazeni->getEntityItem()->getEntityId();
   }

   public function isRespondent(IFormularRespondent $userEntity) :bool {
      return !!$this->getRespondent($userEntity);
   }

   public function getRespondent(IFormularRespondent $user) :?FormularRespondentData {
      return $this->getRespondents()[FormularRespondentTypEnum::fromUserEntity($user)->value][$user->getId()] ?? null;
   }

   /** @return FormularRespondentData[][] */
   public function getRespondents() :array {
      return $this->respondents ??= FormularRespondent::getAssocTypByFormular($this->formularRow->id);
   }

   public function isClientVisible() :bool {
      return $this->formularRow->is_interni === 0 && $this->stav->isClientVisible();
   }

   private function prepareMainData() :void {
      $this->nazev = $this->formularRow->title;
      $this->id_organizace = $this->formularRow->id_organizace;
      $this->popis = $this->formularRow->description;

      $this->typ = FormulareTypyEnum::from($this->prirazeni->type_entity);
      $this->jazyk = Jazyky::from($this->formularRow->id_jazyk);
      $this->stav = FormularStavy::from($this->formularRow->id_stav);
   }

   private function prepareInputs() :void {
      $this->inputy = FormularInput::getForFormular($this->formularRow->id);
      $this->findAndPrepareOptions();
   }

   private function findAndPrepareOptions() :void {
      $optionsInputs = [];

      foreach($this->inputy as $input){
         if($input->getTyp()->hasInputTypeOptions())
            $optionsInputs[$input->id] = $input->id;
      }

      if(empty($optionsInputs))
         return;

      $optionsData = FormularInputOption::getForInputs(...$optionsInputs);

      foreach($optionsInputs as $id_input){
         if(!isset($this->inputy[$id_input]) || !isset($optionsData[$id_input]))
            continue;

         $this->inputy[$id_input]->setOptions(...$optionsData[$id_input]);
      }
   }

   private readonly FormularEntityData $prirazeni;

   /** @var InputData[] */
   private readonly array $inputy;

   public function getPortalName() :string {
      return Strings::webalize($this->nazev);
   }

   public function isInterni() :bool{
      return $this->formularRow->is_interni;
   }

   public function isActive() :bool {
      return $this->formularRow->is_active;
   }

   public function getCreatedDate() :DateTime {
      return $this->formularRow->created;
   }

   public function getDatumUpozorneni() :?DateTime {
      return $this->formularRow->datum_pred_akci;
   }

   public function getPocetDniPredAkci() :int {
      return $this->formularRow->dny_pred_akci;
   }

   public function getClientDetailUrl(bool $fullUrl = true) :string {
      $portal = Organizace::getMisto($this->id_organizace);

      $url = DetailOrganizaceEntitaController::getUrl(
         $portal->getKatalogRegion()->value,
         $portal->portal_odkaz,
         $this->getPortalName(),
         RouteEntityType::FORMULAR->value,
         $this->getId(),
      );

      return $fullUrl ? Redirect::check($url, SystemVersion::KATALOG) : $url;
   }

   public function addRespondent(IFormularRespondent $user) :void {
      (new NovyFormularRespondent())
         ->setIdFormular($this->getId())
         ->setIdUser($user->getId(), $user->getType())
         ->call();
   }

   private array $respondents;
}