<?php namespace app\system\model\translator;

use app\system\Environment;
use app\system\helpers\BeatEnum;
use app\system\model\organizace\meny\Meny;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 18.04.2023 */
enum Jazyky: int
{

   use BeatEnum;

   case CZ = 1;
   case EN = 2;
   case PL = 3;
   case DE = 4;
   case SK = 5;
   case HU = 6;

   public const DIR_FLAGS = '/files/system/img/flags/';

   public static function getFromRequestLanguages(array $getLanguages) :self {
      foreach($getLanguages as $language){
         foreach(self::cases() as $jazyk){
            if(str_contains($language, $jazyk->getISO6391()))
               return $jazyk;
         }
      }

      return self::EN;
   }

   /** @return Jazyky[] */
   public static function getAllowed() :array {
      $allowed = [];

      foreach(self::cases() as $jazyk){
         if((!Environment::isLocalhost() && !Environment::isQvampAdmin()) && $jazyk->isNotVisible())
            continue;

         $allowed[$jazyk->value] = $jazyk;
      }

      return $allowed;
   }

   /** @return Jazyky[] */
   public static function getAllowedOrganizationLanguages() :array {
      return [self::CZ, self::PL, self::HU, self::EN];
   }

   public static function tryFromISO(string $iso_lang_code) :?Jazyky {
      foreach(self::cases() as $case)
         if($case->getISO6391() === $iso_lang_code)
            return $case;

      return null;
   }

   /** @return static[] */
   public static function getOthers(int|Jazyky $jazyk) :array {
      $r = [];

      if(!($jazyk instanceof Jazyky))
         $jazyk = Jazyky::from($jazyk);

      foreach(self::cases() as $jazykCase){
         if($jazykCase === $jazyk)
            continue;

         $r[$jazykCase->value] = $jazykCase;
      }

      return $r;
   }

   public function isRootLanguage() :bool {
      return $this === self::CZ;
   }

   public function getTitle() :string {
      return match ($this) {
         self::CZ => 'Čeština',
         self::EN => 'English',
         self::PL => 'Polski',
         self::DE => 'Deutsch',
         self::SK => 'Slovenčina',
         self::HU => 'Magyar',
      };
   }

   public function getISO6391() :string {
      return match ($this) {
         self::CZ => 'cs',
         self::PL => 'pl',
         self::EN => 'en',
         self::DE => 'de',
         self::SK => 'sk',
         self::HU => 'hu',
      };
   }

   public function getISO3166_1() :string {
      return match ($this) {
         default => $this->name
      };
   }

   public function getIcon() :?string {
      $iconFile = self::DIR_FLAGS . match ($this) {
            self::CZ => 'cz.svg',
            self::EN => 'gb.svg',
            self::PL => 'pl.svg',
            self::DE => 'de.svg',
            self::SK => 'sk.svg',
            self::HU => 'hu.svg',
         };

      return file_exists(ltrim($iconFile, '/')) ? $iconFile : null;
   }

   public function getMainMena() :Meny {
      return match ($this) {
         self::CZ => Meny::CZK,
         self::PL => Meny::PLN,
         self::HU => Meny::HUF,
         default => Meny::EUR,
      };
   }

   public function getFullIsoFormat() :string {
      return match ($this) {
         self::EN => 'en-US',
         default => $this->getISO6391(),
      };
   }

   public function isNotVisible() :bool {
      return match ($this) {
         self::SK => true,
         default => false,
      };
   }
   protected const TINY_TRANSLATE_DIR = 'layout/js/tiny/langs/';

   public function getTinyTranslateFile() :?string {
      return match ($this) {
         self::CZ => self::TINY_TRANSLATE_DIR . 'tiny-cs.js',
         self::PL => self::TINY_TRANSLATE_DIR . 'tiny-pl.js',
         self::DE => self::TINY_TRANSLATE_DIR . 'tiny-de.js',
         self::SK => self::TINY_TRANSLATE_DIR . 'tiny-sk.js',
         self::HU => self::TINY_TRANSLATE_DIR . 'tiny-hu.js',
         default => null,
      };
   }

   public function getMainCurrencyCode() :string {
      return $this->getMainMena()->getCode();
   }

   public function digiSignLanguage() {
//      "cs""en""sk""nl""ro""hu""pl"
      return match ($this) {
         self::DE => self::EN->getISO6391(),
         default => $this->getISO6391()
      };
   }
}