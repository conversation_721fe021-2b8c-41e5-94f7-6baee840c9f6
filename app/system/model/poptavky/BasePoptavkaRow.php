<?php namespace app\system\model\poptavky;

use app\front\controllers\poptavky\DetailPoptavkyController;
use app\front\organizace\model\organizace\Organizace;
use app\katalog\organizace\DetailOrganizaceEntitaController;
use app\katalog\organizace\RouteEntityType;
use app\System;
use app\system\application\FrontApplicationEnvironment;
use app\system\application\IEntityClientDetail;
use app\system\helpers\row\attributes\IgnoreUpdateArray;
use app\system\helpers\row\attributes\IgnoreUpdateDTFormat;
use app\system\helpers\row\EntryRow;
use app\system\model\akce\typ\AkceTyp;
use app\system\model\akce\typ\AkceTypNazev;
use app\system\model\akce\typ\AkceTypNazevRow;
use app\system\model\organizace\akce\OrganizaceAkce;
use app\system\model\organizace\akce\OrganizaceAkceRow;
use app\system\model\organizace\personal\Personal;
use app\system\model\organizace\personal\PersonalRow;
use app\system\model\organizace\personal\prirazeni\IEntityPrirazenyPersonal;
use app\system\model\organizace\personal\prirazeni\IPrirazenyPersonal;
use app\system\model\organizace\personal\prirazeni\PersonalAssignmentType;
use app\system\model\organizace\zakaznici\OrganizaceZakaznici;
use app\system\model\organizace\zakaznici\OrganizaceZakaznikRow;
use app\system\model\organizace\zdroje\OrganizaceZdroje;
use app\system\model\organizace\zdroje\OrganizaceZdrojRow;
use app\system\model\poptavky\prirazeni\PoptavkaPrirazenyPersonal;
use DateInterval;
use Dibi\DateTime;
use Nette\Utils\Strings;

/** Created by Kryštof Czyź. Date: 22.07.2023 */
abstract class BasePoptavkaRow extends EntryRow
   implements IPoptavka, IEntityClientDetail, IEntityPrirazenyPersonal
{

//   @TODO vytvořit getter pro zakaznikRow, někdy a refaktorovat všude tam kde se tato property používá
   public int $id_organizace, $id_zakaznika, $id_stav;

   public ?int $id_event_type = null;
   public ?int $id_system_event_type = null;

   public ?int $zdroj, $pocet_hostu;
   public ?string $poznamka;
   public DateTime $created;

   /**
    * @deprecated This property is deprecated. Use date_start AND date_end instead.
    */
   public ?DateTime $datum;

   public ?DateInterval $time_end, $time_start;

   public ?int $pevne_datum;

   #[IgnoreUpdateDTFormat]
   public ?DateTime $date_start;

   #[IgnoreUpdateDTFormat]
   public ?DateTime $date_end;

   #[IgnoreUpdateArray]
   public readonly ?DateTime $dtStart;

   #[IgnoreUpdateArray]
   public readonly ?DateTime $dtEnd;

   public function __construct($arr = []) {
      parent::__construct($arr);

      if(($this->date_start ?? null) === null){
         $this->dtStart = null;
         $this->dtEnd = null;
         return;
      }

      $this->dtStart = $this->time_start ? $this->date_start->add($this->time_start) : $this->date_start;

      $end = $this->time_end ? $this->date_end?->add($this->time_end) : $this->date_end;

      if($end === null){
         $endTime = $this->time_end ?: new DateInterval('PT23H59M');
         $end = $this->date_start->add($endTime);

         if($this->dtStart->diff($end)->invert === 1)
            $end = $end->add(new DateInterval('P1D'));
      }

      $this->dtEnd = $end;
   }

   public function getStav() :PoptavkaStavy {
      return PoptavkaStavy::from($this->id_stav);
   }

   public function getDetailUrl() :string {
      return DetailPoptavkyController::getUrl($this->getID());
   }

   public function getClientDetailUrl() :string {
      $portal = Organizace::getMisto($this->id_organizace);

      return DetailOrganizaceEntitaController::getUrl(
         $portal->getKatalogRegion()->value,
         $portal->portal_odkaz,
         $this->getPortalName(),
         RouteEntityType::fromEntity($this)->value,
         $this->getID(),
      );
   }

   public function getPortalName() :string {
      return Strings::webalize($this->getTypAkceString() ?: sprintf('Poptávka %d', $this->getID()));
   }

   /** @return IPrirazenyPersonal[] */
   public function getPrirazenyPersonalAssoc() :array {
      return $this->personalAssoc ??= PoptavkaPrirazenyPersonal::getByPoptavka($this);
   }

   /** @return int[] */
   public function getPrirazenyPersonalID() :array {
      return $this->personalID ??= array_keys($this->getPrirazenyPersonalAssoc());
   }

   /** @return array<int, PersonalRow> */
   public function getPrirazenyPersonalArray() :array {
      if(empty($this->getPrirazenyPersonalID()))
         return [];

      return $this->personalData ??= Personal::getByIds($this->getPrirazenyPersonalID());
   }

   public function getPrirazenyPersonal(int $id_personal) :?IPrirazenyPersonal {
      return  $this->getPrirazenyPersonalAssoc()[$id_personal] ?: null;
   }

   public function getZdroj() :?OrganizaceZdrojRow {
      if(!$this->zdroj)
         return null;

      return ($this->zdrojRow ??= (OrganizaceZdroje::get($this->zdroj) ?: false)) ?: null;
   }

   public function getIdOrganizace() :int {
      return $this->id_organizace;
   }

   public function getZakaznik() :?OrganizaceZakaznikRow {
      return $this->zakaznik ??= OrganizaceZakaznici::get($this->id_zakaznika);
   }

   public function getTypAkce() :?OrganizaceAkceRow {
      return $this->id_event_type
         ? $this->typAkce ??= OrganizaceAkce::get($this->id_event_type)
         : null;
   }

   public function getSystemTypAkce() :?AkceTypNazevRow {
      return $this->id_system_event_type ? AkceTypNazev::getNazev($this->id_system_event_type, System::get()->getApplication()->environment->getJazyk()) : null;
   }

   public function getTypAkceString() :?string {
      if(!!$this->id_system_event_type)
         return $this->getSystemTypAkce()->nazev;

      if(!!$this->id_event_type)
         return $this->getTypAkce()?->nazev_eventu;

      return null;
   }

   public function getAsignmentEntityType() :PersonalAssignmentType {
      return PersonalAssignmentType::POPTAVKA;
   }

   /** @var prirazeni\PoptavkaPrirazenyPersonalRow[] */
   protected array $personalAssoc;

   private OrganizaceZdrojRow|false $zdrojRow;
   private ?OrganizaceZakaznikRow $zakaznik;
   private ?OrganizaceAkceRow $typAkce;
}