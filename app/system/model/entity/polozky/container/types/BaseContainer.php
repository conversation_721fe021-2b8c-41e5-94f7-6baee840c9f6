<?php namespace app\system\model\entity\polozky\container\types;

use app\system\model\entity\polozky\BaseEntityPolozkaRow;
use app\system\model\entity\polozky\container\IEntityProducts;
use app\system\model\entity\polozky\container\item\ProductItem;
use app\system\model\entity\polozky\container\ProductPrice;
use app\system\model\entity\polozky\container\ProductsContainer;
use app\system\model\entity\polozky\data\BaseModel;
use app\system\model\entity\polozky\finder\SearchSelectedProductItem;
use app\system\model\kategorie\KategorieNazvy;
use app\system\model\organizace\cenove\CenoveSkupiny;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\translator\Jazyky;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 03.06.2025 */
abstract class BaseContainer
{

   abstract protected function getProductsModel() :BaseModel;
   abstract protected function prepareProductItem(BaseEntityPolozkaRow $product, ProductPrice $productPrice, ?array $cenoveKategorieNazvy = null, ?array $kategorieNazvy = null) :?ProductItem;

   final public function __construct(
      protected readonly IEntityProducts $entity,
      protected readonly ProductsContainer $productsContainer
   ) {
      $this->model = $this->getProductsModel();
      $this->mainLang = Jazyky::from(OrganizaceJazyky::getPrimary($this->entity->getIdOrganizace()));
      $this->reloadData();
   }

   final public function reloadData() :static {
      $products = $this->model->getAll();

      foreach ($products as $product){
         $this->products[$product->id] = $product;
         $this->productsPrices[$product->id] = ProductPrice::fromEntityProductRow($product, $this->entity->getCasovyBlok() ?: 1);
         $this->callbackConstructorProducts($product);
      }

      return $this;
   }

   public function updateProduct(ProductItem $product) :ProductItem {
      if(!isset($this->products[$product->productId]))
         throw new \Exception('Unknown product id ' . $product->productId);

      $savedProductData = $this->products[$product->productId];

      $savedProductData->mnozstvi = $product->productPrice->amount;
      $savedProductData->dph = $product->productPrice->vat;
      $savedProductData->is_hodinovy = $product->productPrice->hourMultiplier > 1 ? 1 : 0;

      if($product->productPrice->discount > 100)
         $product->productPrice->discount = 100;

      $savedProductData->sleva = $product->productPrice->discount;

      $savedProductData
         ->setPrice($product->productPrice->price)
         ->setCost($product->productPrice->cost);

      $this->productsPrices[$product->productId] = ProductPrice::fromEntityProductRow(
         $savedProductData,
         $product->productPrice->hourMultiplier
      );

      $this->model->saveUpdatedProduct($savedProductData);
      unset($this->ceny);

      return $product;
   }

   public function insertSearchItem(SearchSelectedProductItem $product) :?ProductItem {
      if($index = $this->searchProductIndex($product->productId, $product->productPriceCategoryId)){
         $exist = $this->products[$index];

         $pItem = $product->createProductItem($index);
         $pItem->setProductData($product->productData);

         $productListRow = $this->productsContainer->getProductListRows()[$pItem->_cid] ?? null;
         $pItem->order = $productListRow?->order ?: 0;
         $pItem->note = $productListRow?->note;

         $productPrice = $product->productPrice;

         $productPrice->amount = $exist->mnozstvi + $productPrice->amount;
         $productPrice->price = $exist->cena_za_kus;
         $productPrice->cost = $exist->naklad_za_kus;

         $pItem->setProductPrice($productPrice);

         $this->updateProduct($pItem);
         return $pItem;
      }

      $newRow = $this->model->insertProduct(
         $product->productId,
         $product->productPriceCategoryId,
         $product->productPrice,
         $product->productData
      );

      $pItem = $product->createProductItem($newRow->id);
      $pItem->setProductData($product->productData);
      $pItem->setProductPrice($product->productPrice);

      $pItem->order = 0;

      $this->products[$newRow->id] = $newRow;
      $this->productsPrices[$newRow->id] = ProductPrice::fromEntityProductRow($newRow);

      return $pItem;
   }

   public function deleteProduct(int $relationID) {
      $this->model->deleteProduct($this->products[$relationID]);

      unset($this->productsPrices[$relationID], $this->products[$relationID]);
   }

   final public function getSuma(bool $recount = false) :SumaPriceContainer {
      if($recount)
         return $this->ceny = $this->recountCeny();

      return $this->ceny ??= $this->recountCeny();
   }

   public function isEmpty() : bool {
      return empty($this->products);
   }

   /** @return ProductItem[] */
   public function getProductItems(array $cenoveKategorieNazvy = null, array $kategorieNazvy = null) :array {
      if($this->isEmpty())
         return [];

      $items = [];

      if($cenoveKategorieNazvy === null)
         $cenoveKategorieNazvy = $this->getCenoveKategorieNazvy();

      if($kategorieNazvy === null)
         $kategorieNazvy = $this->getKategorieNazvy();

      foreach($this->products as $product){
         if(!($productItem = $this->prepareProductItem($product, $this->productsPrices[$product->id], $cenoveKategorieNazvy, $kategorieNazvy))){
            trigger_error('prepareProductItem was not successfull', E_USER_WARNING);
            continue;
         }

         $items[$productItem->_cid] = $productItem;
      }

      return $items;
   }

   public function getUsedCenoveKategorie() :?array {
      return null;
   }

   public function getUsedKategorie() :?array {
      return null;
   }

   protected function callbackConstructorProducts(BaseEntityPolozkaRow $product) :void { }

   /** @return array<int, string>|null */
   final protected function getCenoveKategorieNazvy() :?array {
      if($this->getUsedCenoveKategorie() === null)
         return null;

      if(empty($this->getUsedCenoveKategorie()))
         return [];

      return $this->cenoveSkupinyNazvy ??= CenoveSkupiny::getNazvyPairs(
         $this->getUsedCenoveKategorie(),
         $this->entity->getJazyk(),
         $this->mainLang,
      );
   }

   /** @return array<int, string>|null */
   final protected function getKategorieNazvy() :?array {
      if($this->getUsedKategorie() === null)
         return null;

      if(empty($this->getUsedKategorie()))
         return [];

      return $this->kategorie ??= KategorieNazvy::getNazvyPairs(
         $this->getUsedKategorie(),
         $this->entity->getJazyk(),
         $this->mainLang,
      );
   }

   private function recountCeny() :SumaPriceContainer {
      $empty = $this->entity->getMena()->getMoney();
      $pocetHodin = $this->entity->getCasovyBlok() ?: 1;

      $celkem = clone $empty;
      $celkemBezDph = clone $empty;
      $celkemNaklady = clone $empty;

      $celkemSleva = clone $empty;
      $celkemSlevaBezDph = clone $empty;

      $vatRates = [];

      foreach ($this->productsPrices as $product) {
         $cena = $product->cena_celkem_dph;
         $cenaBezDph = $product->cena_celkem;

         $sleva = $product->sleva_celkem_dph;
         $slevaBezDph = $product->sleva_celkem;

         $naklad = $product->naklad_celkem;

         $celkem = $celkem->add($cena);
         $celkemBezDph = $celkemBezDph->add($cenaBezDph);
         $celkemNaklady = $celkemNaklady->add($naklad);
         $celkemSleva = $celkemSleva->add($sleva);
         $celkemSlevaBezDph = $celkemSlevaBezDph->add($slevaBezDph);

         if($product->vat !== 0.00){
            $vatRates[$product->vat] = ($vatRates[$product->vat] ??= $empty)->add(
               $cena->subtract($cenaBezDph),
            );
         }
      }

      return new SumaPriceContainer(
         $celkem,
         $celkemBezDph,
         $celkemSleva,
         $celkemSlevaBezDph,
         $vatRates,
         $celkemNaklady,
      );
   }

   private function searchProductIndex(int $productID, ?int $productCategoryID) :?int {
      foreach($this->products as $product)
         if(
            $product->getProductID() === $productID
            && $product->getProductCategoryID() === $productCategoryID
         )
            return $product->id;

      return null;
   }

   /** @return BaseEntityPolozkaRow[] */
   public function getProductsRows() :array {
      return $this->products;
   }

   public function import(BaseEntityPolozkaRow ...$product) :static {
      $this->model->multipleInsertProducts(...$product);
      return $this;
   }

   /** @var BaseEntityPolozkaRow[] */
   protected array $products = [];
   protected SumaPriceContainer $ceny;

   /** @var array<int, ProductPrice> */
   protected array $productsPrices = [];
   protected readonly Jazyky $mainLang;
   protected readonly BaseModel $model;
}