<?php namespace app\system\sitemap;

use app\katalog\about\KatalogAboutController;
use app\katalog\faq\KatalogFaqController;
use app\katalog\filtrace\FiltraceProstoryController;
use app\katalog\filtrace\FiltraceSluzbyController;
use app\katalog\homepage\HomepageController;
use app\katalog\kodex\KatalogKodexController;
use app\katalog\kontakt\KatalogKontaktController;
use app\katalog\organizace\DetailOrganizaceController;
use app\system\helpers\Files;
use app\system\model\katalog\KatalogOrganizaceRegion;
use app\system\model\organizace\jazyk\OrganizaceJazykRow;
use app\system\Redirect;
use app\system\sitemap\generator\OrganizationHydratedGenerator;
use app\system\sitemap\writer\OrganizationSitemapWriter;
use app\system\sitemap\writer\SitemapWriter;
use app\system\sitemap\writer\StaticSitemapWriter;
use app\system\SystemVersion;
use Generator;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.08.2025 */
class SitemapGenerator
{

   const string OUTPUT_DIR = 'sitemaps';
   const int URLS_PER_FILE = 50000;

   public static function generate() :void {
      (new self())
         ->makeFile('sitemap.xml');
   }

   public readonly string $baseUrl;
   public readonly string $lastmod;

   public function __construct() {
      $this->baseUrl = rtrim(Redirect::getHost(SystemVersion::KATALOG), '/');
      $this->lastmod = (new \DateTimeImmutable('now'))->format('c');
   }

   public function makeFile(string $outputPath) :void {
      Files::checkDir(self::OUTPUT_DIR);

      $this->sitemapWriter = new SitemapWriter(
         $this,
         $outputPath,
      );

      $this->handleOrganizations(
         $orgWriter = new OrganizationSitemapWriter(
            $this,
            'organizations',
            self::URLS_PER_FILE
         )
      );

      $this->handleStaticRoutes(
         $staticWriter = new StaticSitemapWriter(
            $this,
            'pages',
            self::URLS_PER_FILE
         )
      );

      $this->sitemapWriter
         ->addSitemap($staticWriter)
         ->addSitemap($orgWriter)
         ->finish();
   }

   private function handleOrganizations(OrganizationSitemapWriter $orgWriter) :void {
      foreach((new OrganizationHydratedGenerator())->generator() as $org){
         $canonicalLang = $this->getPrimaryLang($org['jazyky']);

         if($canonicalLang === null) continue;

         $canonicalUrl = DetailOrganizaceController::getUrl(
            $org['organizace']->getKatalogRegion()->value,
            $org['organizace']->portal_odkaz,
            $canonicalLang->getJazyk()->getISO6391(),
         );

         $hrefLangs = [];

         foreach($this->getOtherLangs($org['jazyky']) as $otherLang){
            $lang = $otherLang->getJazyk()->getISO6391();
            $hrefLangs[$lang] = DetailOrganizaceController::getUrl(
               $org['organizace']->getKatalogRegion()->value,
               $org['organizace']->portal_odkaz,
               $lang,
            );
         }

         $orgWriter->writeUrl(
            $canonicalUrl,
            $org['organizace']->getProfilePhotoSrc(),
            $hrefLangs,
         );
      }

      $orgWriter->finish();
   }

   private function handleStaticRoutes(StaticSitemapWriter $writer) :void {
      HomepageController::subscribeSitemapWriter($writer, KatalogOrganizaceRegion::CZ->value);

      FiltraceProstoryController::subscribeSitemapWriter($writer, KatalogOrganizaceRegion::CZ->value);
      FiltraceSluzbyController::subscribeSitemapWriter($writer, KatalogOrganizaceRegion::CZ->value);
      KatalogAboutController::subscribeSitemapWriter($writer);
      KatalogKontaktController::subscribeSitemapWriter($writer, KatalogOrganizaceRegion::CZ->value);
      KatalogFaqController::subscribeSitemapWriter($writer, KatalogOrganizaceRegion::CZ->value);
      KatalogKodexController::subscribeSitemapWriter($writer);


      $writer->finish();
   }

   /** @param OrganizaceJazykRow[] $jazyky */
   private function getPrimaryLang(array $jazyky) :?OrganizaceJazykRow {
      foreach ($jazyky as $jazyk) {
         if($jazyk->is_primary === 1)
            return $jazyk;
      }

      return null;
   }

   /**
    * @param OrganizaceJazykRow[] $jazyky
    * @return Generator<int, OrganizaceJazykRow>
    */
   private function getOtherLangs(array $jazyky) :Generator {
      foreach ($jazyky as $jazyk) {
         if($jazyk->is_primary === 1)
            continue;

         yield $jazyk;
      }
   }

   private SitemapWriter $sitemapWriter;
}