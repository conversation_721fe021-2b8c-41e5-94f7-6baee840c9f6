<?php namespace app\system\sitemap\generator;

use app\front\organizace\model\organizace\Organizace;
use app\system\model\organizace\jazyk\OrganizaceJazykRow;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\organizace\portal\OrganizacePortal;
use app\system\model\organizace\subscription\MistoSubscriptionData;
use Generator;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.08.2025 */
class OrganizationHydratedGenerator
{

   /**
    * @return Generator<int, array{
    *    organizace: OrganizaceSitemapRow,
    *    jazyky: array<int, OrganizaceJazykRow>
    * }>
    */
   public function generator() :Generator {
      /** @var OrganizaceSitemapRow[] $organizace */
      $organizace = \dibi::select('o.*')
         ->from(Organizace::TABLE, 'o')
         ->select('op.profile_photo_src')
         ->join(MistoSubscriptionData::TABLE, 'ms')
         ->on('o.%n = ms.%n', Organizace::COLUMN_ID, MistoSubscriptionData::FK_ORGANIZACE)
         ->join(OrganizacePortal::TABLE, 'op')
         ->on('o.%n = op.%n', Organizace::COLUMN_ID, OrganizacePortal::PK)
         ->where('op.is_hidden_catalog = 0')
         ->where('ms.is_trial = 0 AND DATE(ms.valid_to) > DATE(NOW())')
         ->setupResult('setRowClass', OrganizaceSitemapRow::class)
         ->fetchAll();

      $organizaceIDs = array_map(static fn(OrganizaceSitemapRow $row) => $row->id_organizace, $organizace);

      $organizaceJazyky = OrganizaceJazyky::getOrganizaceAssoc(...$organizaceIDs);

      foreach($organizace as $sitemapRow){
         yield [
            'organizace' => $sitemapRow,
            'jazyky' => $organizaceJazyky[$sitemapRow->id_organizace],
         ];
      }
   }
}