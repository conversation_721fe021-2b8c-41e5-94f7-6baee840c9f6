<?php namespace app\system\sitemap\generator;

use app\front\organizace\model\organizace\OrganizaceRow;
use app\system\model\mista\images\ImageUploadHandler;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 01.09.2025 */
class OrganizaceSitemapRow extends OrganizaceRow
{

   public ?string $profile_photo_src = null;

   public function getProfilePhotoSrc() :?string {
      if (!$this->profile_photo_src)
         return null;

      $src = ImageUploadHandler::createImagePath(
         $this->getImagesDirectory() . '/profile',
         $this->profile_photo_src,
      );

      if(!file_exists($src))
         return null;

      return $src;
   }
}