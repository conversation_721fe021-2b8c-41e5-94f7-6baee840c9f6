<?php namespace app\system\sitemap\event;

use app\system\event\Event;
use app\system\event\queue\IEventQueue;
use app\system\sitemap\SitemapGenerator;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 01.09.2025 */
class GenerateSitemapEvent extends Event
   implements IEventQueue
{

   public function onCall() :void {
      ini_set('memory_limit', '1024M');
      SitemapGenerator::generate();
   }

   public function onBeforeQueuedCall(array $params) :void { }

   public function getQueueParams() :array {
      return [];
   }
}