<?php namespace app\system\sitemap\writer;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 01.09.2025 */
class StaticSitemapWriter extends BaseSitemapWriter
{


   public function writeUrl(string $loc, ?string $image = null, array $hreflangs = []) :void {
      $x = $this->xml;
      $x->startElement('url');
      $x->writeElement('loc', $this->generator->baseUrl . $loc);
      $x->writeElement('lastmod', $this->generator->lastmod);

      foreach($hreflangs as $lang => $href){
         $x->startElement('xhtml:link');
         $x->writeAttribute('rel', 'alternate');
         $x->writeAttribute('hreflang', $lang);
         $x->writeAttribute('href', $this->generator->baseUrl . $href);
         $x->endElement();
      }
      if($image){
         $x->startElement('image:image');
         $x->writeElement('image:loc', $this->generator->baseUrl . '/' . $image);
         $x->endElement();
      }
      $x->endElement();
   }
}