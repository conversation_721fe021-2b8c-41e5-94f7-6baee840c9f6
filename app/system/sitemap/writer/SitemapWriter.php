<?php namespace app\system\sitemap\writer;

use app\system\sitemap\SitemapGenerator;
use RuntimeException;
use XMLWriter;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 01.09.2025 */
class SitemapWriter
{

   public function __construct(
      private readonly SitemapGenerator $generator,
      private readonly string $outputPath
   ) {
      $this->xml = new XMLWriter();
      if (!$this->xml->openURI($this->outputPath)) {
         throw new RuntimeException("Cannot open $this->outputPath");
      }
      $this->xml->startDocument('1.0', 'UTF-8');
      $this->xml->startElement('sitemapindex');
      $this->xml->writeAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');
   }

   public function addSitemap(ISitemapWriter $writer) :static {
      foreach ($writer->publicUrls() as $url) {
         $this->xml->startElement('sitemap');
         $this->xml->writeElement('loc', $this->prependSitemapUrl($url));
         $this->xml->writeElement('lastmod', $this->generator->lastmod);
         $this->xml->endElement();
      }

      return $this;
   }

   public function finish() :void {
      $this->xml->endElement();
      $this->xml->endDocument();
      $this->xml->flush();
   }

   private function prependSitemapUrl(string $fileName) :string {
      return $this->generator->baseUrl . '/' . $this->generator::OUTPUT_DIR . '/' . $fileName;
   }

   private XMLWriter $xml;
}