<?php namespace app\system\sitemap\writer;

use app\system\sitemap\SitemapGenerator;
use RuntimeException;
use XMLWriter;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 01.09.2025 */
abstract class BaseSitemapWriter
   implements ISitemapWriter
{

   public function __construct(
      protected readonly SitemapGenerator $generator,
      protected readonly string $filePrefix,
      protected readonly int $limit,
   ) {
      $this->openNewFile();
   }

   final public function finish(): void {
      $this->closeFile();
   }

   final public function publicUrls(): array {
      return $this->publicUrls;
   }

   final protected function openNewFile(): void {
      $this->fileNo++;
      $this->countInFile = 0;

      $basename = sprintf('%s-%04d.xml', $this->filePrefix, $this->fileNo);
      $fsPath = $this->generator::OUTPUT_DIR . '/' . $basename;

      $x = new XMLWriter();

      $uri = $fsPath;
      if (!$x->openURI($uri)) {
         throw new RuntimeException("Cannot open $uri for writing");
      }
      $x->startDocument('1.0', 'UTF-8');
      $x->startElement('urlset');
      $x->writeAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');
      $x->writeAttribute('xmlns:image', 'http://www.google.com/schemas/sitemap-image/1.1');
      $x->writeAttribute('xmlns:xhtml', 'http://www.w3.org/1999/xhtml');

      $this->xml = $x;
      $this->publicUrls[] = $basename;
   }

   final protected function closeFile(): void {
      if ($this->xml === null) return;
      $this->xml->endElement(); // urlset
      $this->xml->endDocument();
      $this->xml->flush();
      $this->xml = null;
   }

   protected int $fileNo = 0;
   protected int $countInFile = 0;
   protected ?XMLWriter $xml = null;
   protected array $publicUrls = [];
}