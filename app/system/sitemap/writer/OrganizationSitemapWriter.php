<?php namespace app\system\sitemap\writer;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.08.2025 */
class OrganizationSitemapWriter extends BaseSitemapWriter
{

   public function writeUrl(string $loc, ?string $image = null, array $hreflangs = []): void {
      if ($this->countInFile >= $this->limit) {
         $this->closeFile();
         $this->openNewFile();
      }

      $x = $this->xml;
      $x->startElement('url');
      $x->writeElement('loc', $this->generator->baseUrl . $loc);
      $x->writeElement('lastmod', $this->generator->lastmod);

      // hreflang alternates
      foreach ($hreflangs as $lang => $href) {
         $x->startElement('xhtml:link');
         $x->writeAttribute('rel', 'alternate');
         $x->writeAttribute('hreflang', $lang);
         $x->writeAttribute('href', $this->generator->baseUrl . $href);
         $x->endElement();
      }
      if ($hreflangs) {
         // x-default -> canonical
         $x->startElement('xhtml:link');
         $x->writeAttribute('rel', 'alternate');
         $x->writeAttribute('hreflang', 'x-default');
         $x->writeAttribute('href', $this->generator->baseUrl . $loc);
         $x->endElement();
      }

      // images (jen klíčové/hero)
      if($image){
         $x->startElement('image:image');
         $x->writeElement('image:loc', $this->generator->baseUrl . '/' . $image);
         $x->endElement();
      }

      $x->endElement(); // url
      $this->countInFile++;
   }
}