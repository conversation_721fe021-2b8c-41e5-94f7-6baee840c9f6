<?php namespace app\system\controller;

use app\system\application\IEnvironment;
use app\system\lay\error\Error404Page;
use app\system\model\translator\Jazyky;
use app\system\router\NewRouter;
use app\system\router\Route;
use app\system\sitemap\writer\ISitemapWriter;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 05.06.2022 */
trait Controller
{

   public function __construct(
      public readonly ?IEnvironment $environment,
      private readonly Route $route
   ) { }

   abstract public function call() :void;

   public static function getUrl(...$parameters) :string {
      return NewRouter::get()->getUrl(static::class, ...$parameters)->__toString();
   }

   public static function subscribeSitemapWriter(ISitemapWriter $writer, ...$parameters) :void {
      $routes = static::getAllUrls(...$parameters);

      if(count($routes) > 1){
         $csRoute = $routes[Jazyky::CZ->getISO6391()];
         unset($routes[Jazyky::CZ->getISO6391()]);
      } else {
         $routes = [];
         $csRoute = reset($routes);
      }

      $writer->writeUrl($csRoute, hreflangs: $routes);
   }

   public function exit403() :never {
      header('HTTP/1.0 403 Forbidden');
      die('You are not allowed to access this file.');
   }

   public function exit404() :never {
      Error404Page::show();
   }

   public static function getAllUrls(...$parameters) :array {
      return NewRouter::get()->getAllUrlMutations(static::class, ...$parameters);
   }

   final protected function getRequestBody() {
      $entityBody = file_get_contents('php://input');
      return json_decode($entityBody, true);
   }

   final protected function getRouteParameters() :array {
      return $this->route->getParametrs();
   }

   final protected function getRouteUrl(int|string ...$parameters) :string {
      return self::getUrl(...$parameters);
   }
}