<?php namespace app\system;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 30.09.2024 */
class Cookies
{

   public static function set(
      string $name,
      string $value,
      int $expiration = 0,
      string $domain = '',
      bool $httpOnly = true,
      ?bool $secure = null,
      ?string $sameSite = null
   ) :void {
      setcookie($name, $value, [
         'expires' => $expiration ? time() + $expiration : 0,
         'path' => '/',
         'domain' => $domain,
         'httponly' => $httpOnly,
         'secure' => $secure === null ? !(Environment::isLocalhost() && BrowserDetection::get()->isSafari()) : $secure,
         'samesite' => $sameSite === null ? 'Lax' : $sameSite,
      ]);
   }

   public static function delete(string $name) :void {
      setcookie($name, '', time() - 3600, '/');
      unset($_COOKIE['cookie_name']);
   }

   public static function get($name) :?string {
      return $_COOKIE[$name] ?? null;
   }
}