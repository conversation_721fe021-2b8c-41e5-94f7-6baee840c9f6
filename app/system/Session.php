<?php namespace app\system;

use app\System;
use app\system\redis\RedisClientFactory;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 19.07.2021 */
class Session
{

   static function start(System $system, string $name = 'qvamp') :void {
//      Detection of bots
      if($system->detection->isBot){
         return;
      }

      RedisClientFactory::setSessionSettingINI();

      session_set_cookie_params([
         'path' => '/',
         'httponly' => true,
         'secure' => true,
         'samesite' => 'Lax'
      ]);

      session_name($name);
      session_start();
   }

   static function set($name, $value){
      $target = &self::getNamespace($name);
      return $target = $value;
   }

   static function get($name, $default = false){
      $target = &self::getNamespace($name);
      if(isset($target))
         return $target;
      else return $default;
   }

   static function clear($name){
      $target = &self::getNamespace($name);
      $target = null;
      unset($target);
   }

   private static function &getNamespace($name){
      $keys = explode('.', $name);
      $target = &$_SESSION;
      foreach($keys as $key)
         $target = &$target[$key];
      return $target;
   }
}