<?php namespace app\system\users\zakaznici;

use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\system\Cookies;
use app\system\encryption\CryptohraphyTrait;
use app\system\encryption\ICryptography;
use app\system\model\katalog\zakaznici\KatalogZakaznici;
use app\system\model\katalog\zakaznici\KatalogZakaznikRow;
use app\system\model\katalog\zakaznici\oblibeneOrganizace\OblibeneOrganizaceUzivatel;
use app\system\model\organizace\zakaznici\keys\OrganizaceZakaznikKeysRow;
use app\system\model\organizace\zakaznici\OrganizaceZakaznici;
use app\system\model\organizace\zakaznici\OrganizaceZakaznikRow;
use app\system\model\zakaznici\logged\LoggedZakazniciSession;
use app\system\model\zakaznici\logged\LoggedZakazniciSessionRow;
use app\system\model\zakaznici\Zakaznici;
use app\system\Session;
use Dibi\DateTime;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 28.09.2023 */
class LoggedZakaznik
{

   const SESSION_KEY = 'loggedZakaznik';
   const COOKIE_KEY = '_qvamp-data';

   public static function getLogged() :?LoggedZakaznik {
      if(Session::get(self::SESSION_KEY) && ($z = self::prepareFromSession()))
         return $z;

      if(isset($_COOKIE[self::COOKIE_KEY]) && trim($_COOKIE[self::COOKIE_KEY]))
         return self::prepareFromCookie();

      return null;
   }

   public static function setSessionHash(string $hash) :void {
      $hashed = self::getCryptographyEncoder()
         ->encodeValue($hash);

      Session::set(self::SESSION_KEY, $hashed);
   }

   public static function setCookieHash(string $hash) :void {
      $hashed = self::getCryptographyEncoder()
         ->encodeValue($hash);

      Cookies::set(self::COOKIE_KEY, $hashed, 60 * 60 * 24 * 14);
   }

   public static function invalidate() :void {
      Session::clear(self::SESSION_KEY);
      Cookies::delete(self::COOKIE_KEY);
   }

   private static function prepareFromSession() :?self {
      $hash = Session::get(self::SESSION_KEY);

      $realHash = self::getCryptographyEncoder()
         ->decodeValue($hash);

      $zkLog = LoggedZakazniciSession::getForSessionHash($realHash);

      if(!$zkLog)
         return null;

      return new self($zkLog);
   }

   private static function prepareFromCookie() :?self {
      $hash = $_COOKIE[self::COOKIE_KEY];

      $realHash = self::getCryptographyEncoder()
         ->decodeValue($hash);

      $zkLog = LoggedZakazniciSession::getForCookieHash($realHash);

      if(!$zkLog || $zkLog->expired < new DateTime())
         return null;

//      @TODO add počítadlo
      $zkLog
         ->generateSessionHash()
         ->save();
      self::setSessionHash($zkLog->session_hash);

      return new self($zkLog);
   }

   private static function getCryptographyEncoder() :ICryptography {
      return self::$encoder ??= new class implements ICryptography {
         use CryptohraphyTrait;

         public function decodeValue(string $value) :string {
            return $this->decrypt($value);
         }

         public function encodeValue(string $value) :string {
            return $this->encrypt($value);
         }

         protected function getCryptKey() :string {
            return 'e64d346b-e43a-4844-a846-dec37324cecd';
         }
      };
   }

   private static ICryptography $encoder;

   public readonly int $id_zakaznik, $id_session;
   public readonly string $full_name;
   public readonly bool $isRegistered;

   public function getOrganizaceZakaznik(int $id_organizace) :?LoggedOrganization {
      return $this->organizations[$id_organizace] ?? null;
   }

   public function getKatalogZakaznik() :?KatalogZakaznikRow {
      if(!$this->isRegistered)
         return null;

      return ($this->katalogZakaznikRow ??= KatalogZakaznici::get($this->id_zakaznik) ?: false) ?: null;
   }

   public function getEmail() :string {
      return $this->email ??= Zakaznici::get($this->id_zakaznik)->email;
   }

   /** @var LoggedOrganization[]  */
   protected array $organizations;

   public function __construct(LoggedZakazniciSessionRow $zakaznikSession) {
      $this->id_session = $zakaznikSession->id;
      $this->id_zakaznik = $zakaznikSession->id_zakaznik;
      $this->isRegistered = (bool)$zakaznikSession->is_register;

      if($zakaznikSession->is_register)
         $this->prepareAllEvents();
      else
         $this->prepareAllowedForSession();
   }

   private function prepareAllEvents() :void {
      $zakaznik = KatalogZakaznici::get($this->id_zakaznik);

      $this->full_name = $zakaznik->full_name;

      foreach(OrganizaceZakaznici::getForZakaznikUnique($this->id_zakaznik) as $zakaznikRow){
         $this->addOrganizace($zakaznikRow);
      }
   }

   private function prepareAllowedForSession() :void {
      foreach(OrganizaceZakaznici::getForLoggedSession($this->id_session) as $zakaznikRow){
         if(!isset($this->full_name))
            $this->full_name = $zakaznikRow->full_name;

         $this->addOrganizace($zakaznikRow);
      }
   }

   private function addOrganizace(OrganizaceZakaznikRow $zakaznikRow) :void {
      $this->organizations[$zakaznikRow->id_organizace] = new LoggedOrganization(
         $zakaznikRow->id,
         $zakaznikRow->id_organizace,
      );
   }

   public function checkLoggedZakaznik(OrganizaceZakaznikKeysRow $loginRow) :bool {
      foreach($this->organizations as $organization){
         if($organization->id_zakaznik_organizace === $loginRow->id_zakaznik_organizace)
            return true;
      }

      return false;
   }

   /** @return LoggedOrganization[] */
   public function getOrganizations() :array {
      return $this->organizations??= [];
   }

   public function getBudouciEventyCount() :int {
      $count = 0;
      foreach ($this->getOrganizations() as $userOrg) {
         $organizace = Organizace::getMisto($userOrg->id_organizace);

         if (!$organizace instanceof OrganizaceRow)
            continue;

         $appVersion = $organizace->getVersion();
         $eventy = $appVersion->getEventy($organizace->id_organizace, $userOrg->id_zakaznik_organizace);

         foreach ($eventy as $event) {
            if($event->date_start > new DateTime()) {
               $count++;
            }
         }
      }
      return $count;
   }

   public function getUlouzeneOrganizaceCount(): int {
      return OblibeneOrganizaceUzivatel::getCount($this->id_zakaznik);
   }

   private string $email;
   private false|KatalogZakaznikRow $katalogZakaznikRow;
}