<?php namespace app\system\users\zakaznici\event;

use app\system\event\Event;
use app\system\model\katalog\zakaznici\KatalogZakaznikRow;
use app\system\model\organizace\zakaznici\keys\OrganizaceZakaznikKeysRow;
use app\system\model\organizace\zakaznici\OrganizaceZakaznici;
use app\system\model\organizace\zakaznici\OrganizaceZakaznikRow;
use app\system\model\zakaznici\IZakaznik;
use app\system\model\zakaznici\logged\LoggedZakazniciSession;
use app\system\model\zakaznici\logged\LoggedZakazniciSessionRow;
use app\system\users\zakaznici\LoggedZakaznik;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.09.2023 */
class LoginZakaznikEvent extends Event
{

   public static function login(?LoggedZakaznik $zakaznik, OrganizaceZakaznikKeysRow $loginRow) :?LoginZakaznikEvent {
      if($zakaznik && $zakaznik->checkLoggedZakaznik($loginRow))
         return null;

      if(
         $zakaznik
         && (($zkz = OrganizaceZakaznici::get($loginRow->id_zakaznik_organizace)) && $zakaznik->id_zakaznik !== $zkz->id_zakaznik)
      ){
         LogoutZakaznikEvent::logout($zakaznik)->call();
         $zakaznik = null;
      }

      return new self(
         $zakaznik ? LoggedZakazniciSession::get($zakaznik->id_session) : null,
         $loginRow
      );
   }

   public static function loginRegistered(?LoggedZakaznik $loggin, KatalogZakaznikRow $zakaznikRow) :LoginZakaznikEvent {
      if($loggin)
         LogoutZakaznikEvent::logout($loggin)->call();

      return new self(null, $zakaznikRow);
   }

//   @TODO druhý parametr bude union
   public function __construct(
      protected ?LoggedZakazniciSessionRow $session,
      OrganizaceZakaznikKeysRow|KatalogZakaznikRow $login,
   ) {
      if($login instanceof OrganizaceZakaznikKeysRow){
         $this->zakaznik = OrganizaceZakaznici::get($login->id_zakaznik_organizace);
         $this->login = $login;
      } else {
         $this->zakaznik = $login;
      }
   }

   public function onCall() :void {
      $firstLogin = false;

      if(!$this->session){
         $this->session = LoggedZakazniciSession::create($this->zakaznik);
         $firstLogin = true;
      }

      if(isset($this->login))
         $this->session->addLoginEntry($this->login->id);

      LoggedZakaznik::setSessionHash($this->session->session_hash);
      LoggedZakaznik::setCookieHash($this->session->cookie_hash);

      $logArray = [
         'id_zakaznik' => $this->zakaznik->getZakaznikID(),
         'first_login' => $firstLogin,
      ];

      if($this->zakaznik->getRow() instanceof OrganizaceZakaznikRow)
         $logArray['id_organizace'] = $this->zakaznik->id_organizace;

      $this->addLog('Zakaznik succesfull login', after: $logArray);
   }

   protected readonly IZakaznik $zakaznik;
   protected readonly ?OrganizaceZakaznikKeysRow $login;
}