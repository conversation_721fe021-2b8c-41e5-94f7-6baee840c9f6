<?php namespace app\client\homepage;

use app\client\homepage\component\ClientOrganizationVypisBlok;
use app\system\component\Templater;
use app\system\layout\client\BaseClientLayout;
use app\system\model\organizace\portal\OrganizacePortal;
use app\system\model\organizace\portal\OrganizacePortalDetailRow;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.09.2023 */
class ClientHomepage extends BaseClientLayout
{

   protected function prepareTemplate(Templater $templater) {
      $loggedOrganizations = $this->environment->zakaznik?->getOrganizations() ?: [];

      $templater->addData([
         '_page' => $this,
         'loggedOrganizations' => OrganizacePortal::getOrganizationInfo(array_keys($loggedOrganizations)),
         'otherOrganizations' => OrganizacePortal::getActiveOrganizace(array_keys($loggedOrganizations)),
      ]);
   }

   public function renderBlok(OrganizacePortalDetailRow $organizace) :Html {
      return new Html(
         ClientOrganizationVypisBlok::getComponent()
            ->setOrganizaceDetail($organizace)
            ->prepareTemplater(cache: false)->render()
      );
   }
}