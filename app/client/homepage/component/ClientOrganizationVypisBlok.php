<?php namespace app\client\homepage\component;

use app\system\application\ClientApplicationEnvironment;
use app\system\component\Component;
use app\system\model\organizace\portal\OrganizacePortalDetailRow;
use Exception;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 02.05.2024 */
class ClientOrganizationVypisBlok extends Component
{

   function setData() :array {
      if(!isset($this->organizace))
         throw new Exception('Organizace musí být nastavená');

      $env = ClientApplicationEnvironment::get();

      $showLoginBtn = !$env->isLogged() || !isset($env->zakaznik->getOrganizations()[$this->organizace->id_organizace]);

      return [
         'organizace' => $this->organizace,
         'showLoginBtn' => $showLoginBtn,
      ];
   }

   public function setOrganizaceDetail(OrganizacePortalDetailRow $organizace) :ClientOrganizationVypisBlok {
      $this->organizace = $organizace;
      return $this;
   }

   protected OrganizacePortalDetailRow $organizace;
}