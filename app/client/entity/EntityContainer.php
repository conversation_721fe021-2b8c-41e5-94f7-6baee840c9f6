<?php namespace app\client\entity;

use app\system\component\Templater;
use app\system\model\event\BaseEventRow;
use app\system\model\nabidka\BaseNabidkaRow;
use app\system\model\poptavky\BasePoptavkaRow;
use Dibi\DateTime;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 05.10.2023 */
class EntityContainer
{

   public function __construct(public readonly bool $oneOrganization = true, public readonly bool $klientView = true) { }

   /** @param BaseEventRow[] $events */
   public function addEvents(array $events) :static {
      if(empty($events))
         return $this;

      foreach($events as $event){
         if($this->klientView && $event->getStatus()->isNovy())
            continue;

         $this->all[] = $event;
         $this->events[$event->getID()] = $event;
         $this->organizations[$event->getIdOrganizace()] = $event->getIdOrganizace();
      }

      return $this;
   }

   /** @param BaseNabidkaRow[] $nabidky */
   public function addNabi<PERSON>ky(array $nabidky) :static {
      if(empty($nabidky))
         return $this;

      foreach($nabidky as $nabidka){
         $this->all[] = $nabidka;
         $this->nabidky[$nabidka->getID()] = $nabidka;
//         $this->organizations[$event->getIdOrganizace()] = $event->getIdOrganizace();
      }

      return $this;
   }

   /** @param BasePoptavkaRow[] $poptavky */
   public function addPoptavky(array $poptavky) :static {
      if(empty($poptavky))
         return $this;

      foreach($poptavky as $poptavka){
         $this->all[] = $poptavka;
         $this->poptavky[$poptavka->getID()] = $poptavka;
         $this->organizations[$poptavka->id_organizace] = $poptavka->id_organizace;
      }

      return $this;
   }

   /** @return BasePoptavkaRow[]|BaseNabidkaRow[]|BaseEventRow[]  */
   public function getSorted() :array {
      if(!$this->sorted)
         $this->sort();

      return $this->all;
   }

   public function render() :Html {
      return new Html(Templater::prepare(__DIR__ . '/container.latte', [
         'container' => $this,
      ])->render());
   }

   public function renderEntity(BasePoptavkaRow|BaseNabidkaRow|BaseEventRow $entity) :string|Html {
      if($entity instanceof BasePoptavkaRow)
         return new Html(Templater::prepare(__DIR__ . '/typ/poptavka.latte', [
            'poptavka' => $entity,
            'container' => $this,
         ], false)->render());

      if($entity instanceof BaseNabidkaRow)
         return new Html(Templater::prepare(__DIR__ . '/typ/nabidka.latte', [
            'nabidka' => $entity,
            'container' => $this,
         ], false)->render());

      if($entity instanceof BaseEventRow)
         return new Html(Templater::prepare(__DIR__ . '/typ/event.latte', [
            'event' => $entity,
            'container' => $this,
         ], false)->render());

      return '';
   }

   public function getEntityLocationName(BasePoptavkaRow|BaseNabidkaRow|BaseEventRow $entity) :?string {
      if($this->oneOrganization)
         return $this->getEntityLocation($entity);
      else
         return $this->getEntityOrganization($entity);
   }

   protected function sort() :static {
      uksort($this->all, function($a, $b) {
         return (
               ($this->getDateTimeFromEntity($this->all[$a])?->getTimestamp() ?: 0)
               <=>
               ($this->getDateTimeFromEntity($this->all[$b])?->getTimestamp() ?: 0)
            ) * -1;
      });
      $this->sorted = true;
      return $this;
   }

   protected function getDateTimeFromEntity(BasePoptavkaRow|BaseNabidkaRow|BaseEventRow $entity) :?DateTime {
      if($entity instanceof BasePoptavkaRow)
         return $entity->date_start;

      if($entity instanceof BaseNabidkaRow)
         return $entity->date_start;

      if($entity instanceof BaseEventRow)
         return $entity->date_start;

      return null;
   }

   private function getEntityLocation(BaseEventRow|BaseNabidkaRow|BasePoptavkaRow $entity) :?string {
      return $entity->getLocationName();
   }

   private function getEntityOrganization(BaseEventRow|BaseNabidkaRow|BasePoptavkaRow $entity) :?string {
      return 'TODO';
   }

   /** @var BaseEventRow[]  */
   protected array $events = [];

   /** @var BaseNabidkaRow[]  */
   protected array $nabidky = [];

   /** @var BasePoptavkaRow[]  */
   protected array $poptavky = [];

   /** @var BasePoptavkaRow[]|BaseNabidkaRow[]|BaseEventRow[]  */
   protected array $all = [];

   protected array $organizations;
   private bool $sorted = false;
}