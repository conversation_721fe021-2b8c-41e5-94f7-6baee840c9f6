{extends '_default.latte'}

{varType app\system\model\nabidka\BaseNabidkaRow $nabidka}
{varType app\client\entity\EntityContainer $container}

{block content}
   <div class="row d-flex align-content-center mt-2">
      <h1 class="h2 col-md col-12">{_'Kalkulace'}:
         {if $nabidka->nazev}
            {$nabidka->nazev}
         {else}
            {if $nabidka->date_end}
               {$nabidka->date_start|date: 'j.n.'}&nbsp;-&nbsp;{$nabidka->date_end|date: 'j.n.Y'}
            {else}
               {$nabidka->date_start|date: 'j.n.Y'}
            {/if}
         {/if}
      </h1>
      <div class="col-md d-flex align-items-center justify-content-md-end justify-content-start mb-2 mb-md-2">
         <small>{_'ID'}: {$nabidka->getID()}</small><span class="ms-2 badge bg-secondary rounded-pill text-white">{$nabidka->getStatus()->getTranslatedTitle()}</span>
      </div>
   </div>
   <hr>
   <div class="row">
      <div class="col-md-auto d-sm-inline gap-2 form-group">
         <label>{_'Datum'}</label>
         <h2 class="lead">
            {if $nabidka->date_end}
               {$nabidka->date_start|date: 'j.n.'}&nbsp;-&nbsp;{$nabidka->date_end|date: 'j.n.Y'}
            {else}
               {$nabidka->date_start|date: 'j.n.Y'}
            {/if}
         </h2>
      </div>
      {if $nabidka->time_start}
         <div class="vr p-0 ms-3 me-3 d-none d-sm-inline-block"></div>
         <div class="col-md-auto col d-sm-inline gap-2 form-group">
            <label>{_'Začátek'}</label>
            <h2 class="lead">{$nabidka->time_start->format('%h:%I')}</h2>
         </div>
      {/if}

      {if $nabidka->time_end}
         <div class="vr p-0 ms-3 me-3 d-none d-sm-inline-block"></div>
         <div class="col-md-auto col d-sm-inline gap-2 form-group">
            <label>{_'Konec'}</label>
            <h2 class="lead">{$nabidka->time_end->format('%h:%I')}</h2>
         </div>
      {/if}


      {var $location = $container->getEntityLocationName($nabidka)}
      {if $location}
         <div class="vr p-0 ms-3 me-3 d-none d-sm-inline-block"></div>
         <div class="col-sm-auto d-sm-inline gap-2 form-group">
            {if $nabidka instanceof app\system\model\nabidka\VenueNabidkaRow}
               <label>{_'Místnost/Prostor'}</label>
            {elseif $nabidka instanceof app\system\model\nabidka\VendorNabidkaRow}
               <label>{_'Místo pořádání'}</label>
            {/if}
            <h2 class="lead">{$container->getEntityLocationName($nabidka)}</h2>{*@todo - dopojit místo pořádání u dodavatele*}
         </div>
      {/if}
      <div class="col-md col-12 my-md-3 d-flex justify-content-end">
         <div class="col-md-auto col-12 form-group my-2 my-md-0">
            <a class="form-control btn btn-primary px-4" href="{$nabidka->getClientDetailUrl()}">{_'Detail'}</a>
         </div>
      </div>
   </div>
{/block}