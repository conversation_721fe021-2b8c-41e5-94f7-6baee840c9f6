{extends '_default.latte'}

{varType app\system\model\event\BaseEventRow $event}
{varType app\client\entity\EntityContainer $container}

{block content}
   <div class="row d-flex align-content-center mt-2">
      <h1 class="h2 col-md col-12">{_'Event'}: {$event->nazev}</h1>
      <div class="col-md d-flex align-items-center justify-content-md-end justify-content-start mb-2 mb-md-2">
         <small>{_'ID'}: {$event->getID()}</small><span class="ms-2 badge bg-secondary rounded-pill text-white">{$event->getStatus()->getTranslatedTitle()}</span>
      </div>
   </div>
   <hr>
   <div class="row">
      <div class="col-md-auto d-sm-inline gap-2 form-group">
         <label>{_'Datum'}</label>
         <h2 class="lead">{$event->getDatumString()}</h2>
      </div>
      <div class="vr p-0 ms-3 me-3 d-none d-sm-inline-block"></div>
      <div class="col-md-auto col d-sm-inline gap-2 form-group">
         <label>{_'Začátek'}</label>
         <h2 class="lead">{$event->date_start|date: 'H:i'}</h2>
      </div>
      <div class="vr p-0 ms-3 me-3 d-none d-sm-inline-block"></div>
      <div class="col-md-auto col col-6 d-sm-inline gap-2 form-group">
         <label>{_'Konec'}</label>
         <h2 class="lead">{$event->date_end|date: 'H:i'}</h2>
      </div>

      {var $location = $container->getEntityLocationName($event)}
      {if $location}
         <div class="vr p-0 ms-3 me-3 d-none d-sm-inline-block"></div>
         <div class="col-sm-auto d-sm-inline gap-2 form-group">
            {if $event instanceof app\system\model\event\VenueEventRow}
               <label>{_'Místnost/Prostor'}</label>
            {elseif $event instanceof app\system\model\event\VendorEventRow}
               <label>{_'Místo pořádání'}</label>
            {/if}
            <h2 class="lead">{$container->getEntityLocationName($event)}</h2> {*@todo - dopojit místo pořádání u dodavatele*}
         </div>
      {/if}

      <div class="col-md col-12 my-md-3 d-flex justify-content-end">
         <div class="col-md-auto col-12 form-group my-2 my-md-0">
            <a class="form-control btn btn-primary px-4" href="{$event->getClientDetailUrl()}">{_'Detail'}</a>
         </div>
      </div>
   </div>
{/block}