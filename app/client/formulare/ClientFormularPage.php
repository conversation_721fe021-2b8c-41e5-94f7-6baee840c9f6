<?php namespace app\client\formulare;

use app\client\controllers\HomepageController;
use app\front\formular\FormularViewerComponent;
use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\katalog\organizace\DetailOrganizaceController;
use app\system\application\ClientApplicationEnvironment;
use app\system\component\Templater;
use app\system\layout\client\BaseClientLayout;
use app\system\model\formular\eventy\VyplnitFormularEvent;
use app\system\model\formular\Formular;
use app\system\Redirect;
use app\system\traits\PostListener;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON>las. Date: 01.12.2023 */
class ClientFormularPage extends BaseClientLayout
{

   use PostListener;

   protected function prepareTemplate(Templater $templater) {
      $this->pageData['organizace'] = $this->organizace = Organizace::getMisto($this->formular->id_organizace);
      $this->appendFormular();
      $this->appendBreadcrumbsUrl();

      $templater->addData($this->pageData);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnSubmitFormular', function($post) {
         (new VyplnitFormularEvent(
            $this->formular,
            $this->formular->getRespondent(
               ClientApplicationEnvironment::get()->zakaznik->getOrganizaceZakaznik($this->formular->id_organizace)->getZakaznikRow()
            ),
            $post
         ))->call();

         Redirect::self();
      });
   }

   public function setEntity(Formular $formular) :static {
      $this->formular = $formular;
      return $this;
   }

   private function appendFormular() {
      $this->pageData['formular'] = $this->formular;
      $this->pageData['formularComponent'] = new Html(FormularViewerComponent::getComponent()
         ->setForm($this->formular)
         ->setViewerU($this->environment->zakaznik->getOrganizaceZakaznik($this->formular->id_organizace)->getZakaznikRow())
         ->setViewMode($this->formular->stav->getViewmodeForClient())
      );
   }

   private function appendBreadcrumbsUrl() {
      $this->pageData['clientHpUrl'] = HomepageController::getUrl();
      $this->pageData['urlOrganizace'] = DetailOrganizaceController::getUrlOrganizace(($this->organizace));
   }

   protected Formular $formular;
   private OrganizaceRow $organizace;
   private array $pageData = [];
}