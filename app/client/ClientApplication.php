<?php namespace app\client;

use app\client\controllers\ClientActionController;
use app\client\controllers\organization\OrganizationController;
use app\client\controllers\organization\OrganizationDetailController;
use app\front\controllers\stazene\StazeneSouboryController;
use app\front\controllers\TinyEditorController;
use app\katalog\organizace\OrganizationPoptavkaController;
use app\System;
use app\system\Application;
use app\system\application\ClientApplicationEnvironment;
use app\system\application\IEnvironment;
use app\system\Environment;
use app\system\layout\client\modals\souhlasy\cookie\CookieNastaveniModal;
use app\system\layout\client\modals\souhlasy\zakaznik\SouhlasyZakaznikModal;
use app\system\Redirect;
use app\system\router;
use app\system\SystemVersion;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 28.09.2023 */
class ClientApplication extends Application
{

   protected function onPrepare() :void {
      if(!($this->environment instanceof ClientApplicationEnvironment))
         throw new \Exception('Environment není instance ClientApplicationEnvironment');

      $this->setBeforeStart(function() {
         if(Environment::isHttpAuthorization())
            $this->checkAuthentication('qvamp', 'qvamp');
      });

      $this->setAfterStart(function() {
         SouhlasyZakaznikModal::init()->checkPost();
         CookieNastaveniModal::init()->checkPost();
      });
   }

   public function prepareRoutes(router\RoutesContainer $container) :void {
//      @TODO přidat podmínky na routy, nepřihlášený jich nepotřebuje tolik
      $container->addControllers(
//         HomepageController::class,
//         VerificationController::class,
//         DynamicController::class,
//         VerificationCodeController::class,
//         ChatActionController::class,
//         @TODO HandleController dočasný pro testování přihlašování
//         HandleController::class,
//         EnvironmentController::class,
         OrganizationPoptavkaController::class,
         OrganizationDetailController::class,
//         Vždy poslední
         OrganizationController::class,
      );

      if($this->environment->zakaznik !== null)
         $container->addControllers(
            StazeneSouboryController::class,
            ClientActionController::class,
            TinyEditorController::class,
         );
   }

   public function prepareSystem(System $system) :void {
      $this->realUrl = $system->realPath;
   }

   protected string $realUrl;

   protected function prepareApplicationEnvironment(System $system) :IEnvironment {
      return ClientApplicationEnvironment::init($system);
   }

   public function notFoundCallback() :?callable {
      return function() {
         Redirect::homepage(version: SystemVersion::KATALOG);
      };
   }
}