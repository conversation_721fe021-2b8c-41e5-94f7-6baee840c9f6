<?php namespace app\client\login;

use app\client\homepage\component\ClientOrganizationVypisBlok;
use app\front\uzivatel\LoginController;
use app\system\component\Templater;
use app\system\layout\client\BaseClientLayout;
use app\system\model\organizace\portal\OrganizacePortal;
use app\system\model\organizace\portal\OrganizacePortalDetailRow;
use app\system\Redirect;
use app\system\SystemVersion;
use app\system\traits\PostListener;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.09.2023 */
class ClientLoginPage extends BaseClientLayout
{

   use PostListener;

   protected function prepareTemplate(Templater $templater) {
      $orgs = OrganizacePortal::getActiveOrganizace();

      $templater->addData([
         '_page' => $this,
         'organizace' => $orgs,
         'mainAppLoginLink' => Redirect::check(LoginController::getUrl(), SystemVersion::APP),
      ]);
   }

   public function renderBlok(OrganizacePortalDetailRow $organizace) :Html {
      return new Html(
         ClientOrganizationVypisBlok::getComponent()
            ->setOrganizaceDetail($organizace)
            ->prepareTemplater(cache: false)->render()
      );
   }
}