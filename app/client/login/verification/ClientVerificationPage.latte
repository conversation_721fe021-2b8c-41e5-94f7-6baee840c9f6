{varType string $verifyUrl}
{varType string $controllUrl}
{varType int $id_mista}
{varType string $emailPattern}
{varType string $organizaceNazev}
{varType ?string $zkzEmail}

{varType bool $existVerification}

<div class="col-sm-10 col-md-8 col-lg-6 mx-auto d-table h-100 mt-5">
   <div class="d-table-cell align-middle">
      <div class="row">
         <h1 class="text-center my-5">{$organizaceNazev}</h1>
      </div>
      <div class="card p-3 mb-2">
         <div class="card-body js-form-container">
               <div class="js-email-form" style="display: none">
                  <div class="mb-3">
                     <label for="email">{_'Email pro přihlášení'}</label>
                     <input class="form-control form-control-lg" type="email" id="email" name="email"
                            placeholder="{_'Zadejte email'}" value="{$zkzEmail ?: ''}"/>
                  </div>
                  <div class="row mt-3 justify-content-center">
                     <div class="col-md-auto col-12">
                        <button n:if="$existVerification" type="button" class="btn btn-secondary w-auto js-has-code">{_'Mám kód'}</button>
                     </div>
                     <div class="col-md-auto col-12">
                        <button type="button" class="form-control btn btn-primary w-auto js-verify-email">{_'Zaslat kód'}</button>
                     </div>
                  </div>
               </div>
               <div class="js-code-form" style="display: none">
                  <form method="post">
                     <div class="mb-3">
                        <label for="kod">{_'Ověřovací přístupový kód'}</label>
                        <input class="form-control form-control-lg" type="text" name="code" id="kod"
                               placeholder="{_'Zadejte kód'}" required autofocus/>
                     </div>
                     <div class="row mt-3 justify-content-center">
                        <div class="col-md-4 col-6">
                           <button type="button" class="form-control btn btn-outline-secondary js-change-email" id="otevritMail">{_'Znovu zaslat'}</button>
                        </div>
                        <div class="col-md-4 col-6">
                           <button type="button" class="form-control btn btn-primary js-verify-code">{_'Ověřit'}</button>
                        </div>
                     </div>
                  </form>
               </div>
         </div>
      </div>
      <div class="mt-4 text-center">
         <small>{_'Na Váš email příjde přístupový kód. Ten prosím poté zadejte do pole pro ověření'}</small>
      </div>
   </div>
</div>

<script>
   $(function() {
      const body = $('body');
      const emailForm = $('div.js-email-form');
      const codeForm = $('div.js-code-form');
      const mailRegexp = new RegExp({$emailPattern|noescape}, '');
      const id_mista = {$id_mista};

      const existVerification = {$existVerification ? 'true' : 'false'|noescape};
      toggleInputs(existVerification);


      body.on('click', 'button.js-verify-email', function(e) {
         const btn = $(this);
         const formContainer = btn.closest('.js-form-container');
         const emailInp = formContainer.find('input[name="email"]');
         const email = emailInp.val();

         if(!mailRegexp.test(email)){
            alert("{_'Zadejte platný email'}");
            emailInp.focus();
            emailInp.select();
            return;
         }

         btn.prop('disabled', true);

         ajaxHandler.post({$verifyUrl}, { id_mista, email }, function(response) {
            if(response['status'])
               toggleInputs(true);

            btn.prop('disabled', null);
         });
      });

      body.on('click', 'button.js-verify-code', function() {
         const btn = $(this);
         const formContainer = btn.closest('.js-form-container');
         const codeInp = formContainer.find('input[name="code"]');
         const code = codeInp.val();

         if(!new RegExp('[0-9]').test(code)){
            codeInp.focus();
            codeInp.select();
            alert("{_'Kód může být pouze číselný'}");
            return;
         }

         btn.prop('disabled', true);
         ajaxHandler.post({$controllUrl}, { id_mista, code: code }, function(response) {
            if(response['status'] === 'badSession')
               toggleInputs(false);

            if(response['status'] === 'badCode'){
               codeInp.focus();
               codeInp.select();
            }

            if(response['status'] === 'success'){
               window.location.replace(location.pathname)
               return;
            }

            btn.prop('disabled', null);
         });
      })

      body.on('click', 'button.js-has-code', function(e) {
         toggleInputs(true);
      });

      body.on('click', 'button.js-change-email', function(e) {
         toggleInputs(false);
      });

      function toggleInputs(showCodeInput = false) {
         if(showCodeInput){
            emailForm.hide();
            codeForm.show();
         }else{
            emailForm.show();
            codeForm.hide();
         }
      }
   });
</script>