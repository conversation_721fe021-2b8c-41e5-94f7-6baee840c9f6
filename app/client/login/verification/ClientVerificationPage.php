<?php namespace app\client\login\verification;

use app\client\controllers\verification\VerificationCodeController;
use app\client\controllers\verification\VerificationController;
use app\client\controllers\verification\VerificationTempSession;
use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\katalog\layout\BaseKatalogContentLayout;
use app\system\component\Templater;
use app\system\helpers\RegexPatterns;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.09.2023 */
class ClientVerificationPage extends BaseKatalogContentLayout
{

   public function setZakaznikEmail(string $email) :static {
      $this->email = $email;
      return $this;
   }

   protected function prepareTemplate(Templater $templater) {
       $templater->addData([
          'id_mista' => $this->id_organizace,
          'emailPattern' => RegexPatterns::MAIL_PATTERN,
          'verifyUrl' => VerificationController::getUrl(),
          'controllUrl' => VerificationCodeController::getUrl(),
          'existVerification' => VerificationTempSession::get() !== null,
          'organizaceNazev' => $this->getOrganization()?->nazev,
          'zkzEmail' => $this->email ?? null,
       ]);
   }

   public function setIdOrganizace(int $id_organizace) :static {
      $this->id_organizace = $id_organizace;
      return $this;
   }

   public function getOrganization() :?OrganizaceRow {
      return Organizace::getMisto($this->id_organizace);
   }

   protected int $id_organizace;
   private string $email;
}