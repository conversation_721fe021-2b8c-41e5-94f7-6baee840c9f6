<?php namespace app\client\controllers;

use app\client\homepage\ClientHomepage;
use app\client\login\ClientLoginPage;
use app\system\controller\Controller;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 28.09.2023 */
#[Route('^/$')]
class HomepageController
{

   use Controller;

   public function call() :void {
      if($this->environment->isLogged())
         ClientHomepage::echoThis();
      else
         ClientLoginPage::echoThis();
   }
}