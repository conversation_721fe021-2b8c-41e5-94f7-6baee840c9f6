<?php namespace app\client\controllers\organization;

use app\front\organizace\model\organizace\Organizace;
use app\katalog\organizace\RouteEntityType;
use app\system\application\ApplicationVersion;
use app\system\controller\Controller;
use app\system\controller\exceptions\page404\Exception404;
use app\system\Redirect;
use app\system\router\NewRouter;
use app\system\router\Route;
use app\system\SystemVersion;
use app\system\users\zakaznici\event\HashLoginEvent;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>zy<PERSON>. Date: 28.09.2023 */
#[Route('^/(?<venue_code>[a-z0-9-]+)/(?<entity_name>[a-z0-9-]+)-x(?<entity_type>[FELO])(?<entity_id>[0-9]+)$')]
class OrganizationDetailController
{

   use Controller;

   public function call() :void {
      $params = NewRouter::get()->getRoute()->getParametrs();
      $nazev = $params['venue_code'];
      $entityId = intval($params['entity_id']);
      $entityType = RouteEntityType::tryFrom(trim($params['entity_type']));

      if(!($misto = Organizace::getByPortalOdkaz($nazev)))
         throw new Exception404();

      if(!$entityId || !$entityType)
         throw new Exception404();

      $organizationVersion = ApplicationVersion::from($misto->typ);
      $entity = $entityType->getForVersion($organizationVersion, $entityId);

      if(isset($_GET[HashLoginEvent::LOGIN_PARAMETR]))
         Redirect::to(
            "{$entity->getClientDetailUrl()}?" . http_build_query([
               HashLoginEvent::LOGIN_PARAMETR => $_GET[HashLoginEvent::LOGIN_PARAMETR],
            ]),
            SystemVersion::KATALOG
         );

      Redirect::to($entity->getClientDetailUrl(), SystemVersion::KATALOG);
   }
}