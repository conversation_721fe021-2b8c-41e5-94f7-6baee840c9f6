<?php namespace app\client\controllers\organization;

use app\client\organization\all\ClientPrehledOrganizacePage;
use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\katalog\organizace\DetailOrganizaceController;
use app\system\controller\Controller;
use app\system\controller\exceptions\page404\Exception404;
use app\system\Redirect;
use app\system\router\NewRouter;
use app\system\router\Route;
use app\system\SystemVersion;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 28.09.2023 */
#[Route('^/(?<venue_code>[a-z-0-9]*)$')]
class OrganizationController
{

   use Controller;

   public function call() :void {
      $params = NewRouter::get()->getRoute()->getParametrs();

      $nazev = $params['venue_code'];

      if(!($this->misto = Organizace::getByPortalOdkaz($nazev)))
         throw new Exception404();

      if(
         !$this->environment->isLogged()
         || !$this->environment->zakaznik->getOrganizaceZakaznik($this->misto->id_organizace)
      ){
         Redirect::to(
            DetailOrganizaceController::getUrlOrganizace($this->misto),
            SystemVersion::KATALOG
         );
      }

      ClientPrehledOrganizacePage::getComponent()
         ->setOrganizace($this->misto)
         ->setZakaznik($this->environment->zakaznik)
         ->echo();
   }

   private ?OrganizaceRow $misto;
}