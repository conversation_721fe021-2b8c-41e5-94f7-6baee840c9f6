<?php namespace app\client\controllers;

use app\system\controller\ActionController;
use app\system\Redirect;
use app\system\router\Route;
use app\system\users\zakaznici\event\LogoutZakaznikEvent;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.09.2023 */
#[Route('^/handle(?:-*)(?<action>\w*)$')]
class HandleController
{

   use ActionController;

   public function action_logout() {
      if($this->environment->zakaznik){
         LogoutZakaznikEvent::logout($this->environment->zakaznik)->call();
         Redirect::homepage();
      }
   }
}