<?php namespace app\client\controllers\verification;

use app\system\Session;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 30.09.2023 */
class VerificationTempSession
{

   const SESSION_NAME = 'waitingVerification';

   public static function set(int $id_session) :void {
      Session::set(self::SESSION_NAME, $id_session);
   }

   public static function get() :?int {
      static $c;

      return $c ??= Session::get(self::SESSION_NAME, null);
   }

   public static function clear() :void {
      Session::clear(self::SESSION_NAME);
   }
}