<?php namespace app\client\controllers\verification;

use app\system\controller\Controller;
use app\system\Environment;
use app\system\flash\FlashMessages;
use app\system\helpers\Files;
use app\system\lay\error\Error404Page;
use app\system\model\organizace\zakaznici\keys\OrganizaceZakaznikKeys;
use app\system\model\organizace\zakaznici\OrganizaceZakaznici;
use app\system\router\Route;
use app\system\traits\JsonAjax;
use app\system\users\zakaznici\event\LoginZakaznikEvent;
use Dibi\DateTime;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>zy<PERSON>. Date: 30.09.2023 */
#[Route('^/~verify-code$')]
class VerificationCodeController
{

   use Controller;
   use JsonAjax;

   const STATUS_BAD_SESSION = 'badSession';
   const STATUS_BAD_CODE = 'badCode';
   const STATUS_SUCCESS = 'success';

   public function call() :void {
      if(!Environment::isAjax())
         Error404Page::show();

      if(!VerificationTempSession::get()){
         FlashMessages::setWarning('Platnost vašeho ověření vyprš<PERSON>, zadejte znovu email');
         $this->logFailure('Expired session');
         $this->sendAjaxResponse([
            'status' => self::STATUS_BAD_SESSION
         ]);
      }

      $id_organizace = intval($_POST['id_mista']);

      if(
         !($key = OrganizaceZakaznikKeys::get(VerificationTempSession::get()))
         || OrganizaceZakaznici::get($key->id_zakaznik_organizace)->id_organizace !== $id_organizace
         || $key->expired < new DateTime()
      ){
         FlashMessages::setWarning('Platnost vašeho ověření vypršela, zadejte znovu email');
         $this->logFailure(sprintf(
            '%s | zakaznik: %d | key_id: %d',
            !!$key ? 'Expired code': 'Neexistujici code',
            $key->id_zakaznik_organizace,
            $key->id,
         ));
         $this->sendAjaxResponse([
            'status' => self::STATUS_BAD_SESSION
         ]);
      }

      if(!($code = trim($_POST['code'])) || $key->code !== $code){
         FlashMessages::setWarning('Neplatný kód, zadejte ho prosím znovu');
         $this->logFailure(sprintf(
            'Neplatný code | zakaznik: %d | key_id: %d | input: %s',
            $key->id_zakaznik_organizace, $key->id, $code,
         ));
         $this->sendAjaxResponse([
            'status' => self::STATUS_BAD_CODE
         ]);
      }
//      @TODO kontrola příznaku used
      $key->setUsed()
         ->save();
      VerificationTempSession::clear();
      LoginZakaznikEvent::login($this->environment->zakaznik, $key)?->call();

      $this->sendAjaxResponse([
         'status' => self::STATUS_SUCCESS
      ]);
   }

   private function logFailure(string $message) :void {
      Files::checkDir($logDir = Files::prepareRootDirPath(Files::LOG_CLIENT_DIR));
      file_put_contents(
         sprintf('%s/%s', $logDir, 'code-verification-errors.log'),
         sprintf(
            '[%s] | %s | IP: %s',
            date('Y-m-d H:i:s'),
            $message,
            Environment::getIpAddress()
         ) . PHP_EOL,
         FILE_APPEND
      );
   }
}