<?php namespace app\client\controllers\verification;

use app\front\organizace\model\organizace\Organizace;
use app\system\controller\Controller;
use app\system\Environment;
use app\system\flash\FlashMessages;
use app\system\helpers\Files;
use app\system\lay\error\Error404Page;
use app\system\model\organizace\zakaznici\email\VerifikacePrihlaseniZakaznikaEmail;
use app\system\model\organizace\zakaznici\keys\OrganizaceZakaznikKeys;
use app\system\model\organizace\zakaznici\OrganizaceZakaznici;
use app\system\router\Route;
use app\system\traits\JsonAjax;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.09.2023 */
#[Route('^/~verify-email$')]
class VerificationController
{

   use Controller;
   use JsonAjax;

   public function call() :void {
      if(!Environment::isAjax())
         Error404Page::show();

      $email = trim(strip_tags($_POST['email']));
      $organizace = intval($_POST['id_mista']);

      if(!$email){
         FlashMessages::setError('Neplatný e-mail');
         $this->sendResponse(false);
      }

      if(!($zakaznik = OrganizaceZakaznici::getByEmail($email, $organizace))){
         FlashMessages::setWarning('Bohužel nebyl nalezen žádný zákazník, zkontrolujte prosím správnost Vašeho e-mailu');
         $this->logFailure(sprintf('Unkwnown zakaznik | email: %s | organizace: %d', $email, $organizace));
         $this->sendResponse(false);
      }

      $session = OrganizaceZakaznikKeys::create($zakaznik);

      VerificationTempSession::clear();
      VerificationTempSession::set($session->id);

      if(Environment::isLocalhost() || Environment::isDevelopment()){
         FlashMessages::setInfo($session->code)
            ->setTitle('LOCALHOST: Verifikační kód')
            ->setTimeout(120000)
            ->setNoTranslate();

         $this->sendResponse(true);
      }

      (new VerifikacePrihlaseniZakaznikaEmail())
         ->setOrganizace(Organizace::getMisto($organizace))
         ->setSession($session)
         ->send($zakaznik->getEmailRecipient());

      FlashMessages::setSuccess('Kód pro ověření byl odeslán na Váš email');
      $this->sendResponse(true);
   }

   private function sendResponse(bool $status) :void {
      $this->sendAjaxResponse([
         'status' => $status
      ]);
   }

   private function logFailure(string $message) :void {
      Files::checkDir($logDir = Files::prepareRootDirPath(Files::LOG_CLIENT_DIR));
      file_put_contents(
         sprintf('%s/%s', $logDir, 'email-verification-errors.log'),
         sprintf(
            '[%s] | %s | IP: %s',
            date('Y-m-d H:i:s'),
            $message,
            Environment::getIpAddress()
         ) . PHP_EOL,
         FILE_APPEND
      );
   }
}