<?php namespace app\client\organization;

use app\client\controllers\HomepageController;
use app\client\organization\modal\mistnost\PrehledOrganizaceMistnostiModal;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\katalog\organizace\OrganizationPoptavkaController;
use app\system\application\ApplicationVersion;
use app\system\component\Templater;
use app\system\layout\client\BaseClientLayout;
use app\system\layout\client\head\meta\organizace\DetailOrganizaceMetaTags;
use app\system\model\organizace\mistnosti\kapacita\TypUsporadaniMistnostiEnum;
use app\system\model\organizace\mistnosti\Mistnosti;
use app\system\model\organizace\portal\galerie\OrganizacePortalGalerie;
use app\system\model\organizace\portal\OrganizacePortal;
use app\system\model\translator\TextVariablesFactory;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.09.2023 */
class ClientOrganizationPage extends BaseClientLayout
{

   public function setOrganizace(OrganizaceRow $misto) :static {
      $this->organizace = $misto;
      return $this;
   }

   public function getOrganization() :?OrganizaceRow {
      return $this->organizace;
   }

   protected function prepareTemplate(Templater $templater) {
      $this->appendOdkazy();
      $this->pageData['organizace'] = $this->organizace;

      $this->setTitle(new TextVariablesFactory($this->organizace->nazev));

      $this->pageData['portalInfo'] = OrganizacePortal::get($this->organizace->id_organizace);
      $this->pageData['galerieFotky'] = OrganizacePortalGalerie::getForOrganizace($this->organizace->id_organizace);
      $this->pageData['homepageUrl'] = HomepageController::getUrl();

      if($this->organizace->getVersion() === ApplicationVersion::VENUE){
         $mistnosti = Mistnosti::getAktivniByMisto($this->organizace->id_organizace);

         $this->pageData['largestRoom'] = Mistnosti::getLargestRoom($this->organizace->id_organizace);
         $this->pageData['mistnosti'] = $mistnosti;
         $this->pageData['kapacita'] = $this->organizace->getCapacity();
         $this->pageData['typyUsporadani'] = TypUsporadaniMistnostiEnum::cases();
         $this->pageData['detailModal'] = PrehledOrganizaceMistnostiModal::init()->render();
         $this->pageData['detailModalBtn'] = PrehledOrganizaceMistnostiModal::getShowAttributes();
      }

      $templater->addData($this->pageData);

      $this->addMetaTags(new DetailOrganizaceMetaTags($this->organizace));
   }

   private function appendOdkazy() {
      $this->pageData['urlNovaPoptavka'] = OrganizationPoptavkaController::getUrl($this->organizace->portal_odkaz);
   }

   private array $pageData = [];
   private OrganizaceRow $organizace;
}