{varType app\system\model\organizace\mistnosti\MistnostRow $mistnost}
{varType app\system\model\organizace\mistnosti\kapacita\TypUsporadaniMistnostiEnum[] $typyUsporadani}
{varType app\system\model\vlastnosti\VlastnostiNazevRow[] $pouzivaneVlastnosti}
{varType int $systemLanguage}

<div class="content-win">
   <div class="photo-show" n:if="$mistnost->getPhotoGalery()">
      <img src="/{$mistnost->getMainPhoto()->getPhotoFullSrc()}" alt="Obrázek 2" class="w-100 radius-card">
   </div>

   <h3 class="pt-4">{$mistnost->nazev}</h3>
   <p class="mt-4" n:if="$mistnost->popis">{$mistnost->getPopisFormated()}</p>

   <div class="table-responsive">
      <table class="table my-4" n:if="$mistnost->getRoomDimensions()">
         <thead>
            <tr>
               <th>{_'Typ prostoru'}</th>
               <th><img src="/files/layout/icons/plocha.png" height="20px" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-custom-class="custom-tooltip"
                        data-bs-title="Celková plocha"></th>
               <th><img src="/files/layout/icons/vyska.png" height="20px" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-custom-class="custom-tooltip"
                        data-bs-title="Výška"></th>
               <th><img src="/files/layout/icons/kapacita.png" height="20px" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-custom-class="custom-tooltip"
                        data-bs-title="Maximální kapacita"></th>
               {foreach $typyUsporadani as $usporadani}
                  <th><img src="{$usporadani->getIconPath()}" height="20px" data-bs-toggle="tooltip"
                           data-bs-placement="top" data-bs-custom-class="custom-tooltip"
                           data-bs-title="{$usporadani->getTranslatedTitle()}"></th>
               {/foreach}
            </tr>
         </thead>
         <tbody>
            <tr>
               <td id="typ">{$mistnost->getTypMistnosti()->getTranslatedTitle()}</td>
               <td id="rozmer">{$mistnost->getRoomDimensions()?->getDimension() ?? '-'}m
                  ({$mistnost?->getRoomDimensions()?->getCalculatedSpace() ?? '-'} m2)
               </td>
               <td id="vyska">{$mistnost?->getRoomDimensions()->ceiling_height ?? '-'}m</td>
               <td id="max">{$mistnost->kapacita}</td>
               {foreach $typyUsporadani as $usporadani}
                  <td id="{$usporadani->name}">{$mistnost->getRoomCapacities()[$usporadani->value]->kapacita ?? '-'}</td>
               {/foreach}
            </tr>
         </tbody>
      </table>

      {if !empty($pouzivaneVlastnosti)}
         <h5 class="lead">{_"Vybavení prostoru"}</h5>
         <div class="row">
            <div class="col">
               {foreach $pouzivaneVlastnosti as $vlastnost}
                  <p class="my-0"><i class="fa {$vlastnost->icon}"></i>{$vlastnost->nazev}</p>
               {/foreach}
            </div>
         </div>
      {/if}
   </div>
</div>