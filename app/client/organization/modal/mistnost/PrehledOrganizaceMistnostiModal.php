<?php namespace app\client\organization\modal\mistnost;

use app\System;
use app\system\component\Templater;
use app\system\model\organizace\mistnosti\galerie\OrganizaceMistnostiFotky;
use app\system\model\organizace\mistnosti\kapacita\TypUsporadaniMistnostiEnum;
use app\system\model\organizace\mistnosti\Mistnosti;
use app\system\model\organizace\mistnosti\MistnostRow;
use app\system\model\organizace\mistnosti\TypyMistnosti;
use app\system\model\vlastnosti\Vlastnosti;
use app\system\model\vlastnosti\VlastnostiNazev;
use app\system\modul\modal\AjaxModal;
use app\system\modul\modal\styles\ModalScaleEnum;

/** Created by Filip Pavlas. Date: 06.01.2025 */
class PrehledOrganizaceMistnostiModal extends AjaxModal
{
   public function prepareAjaxData() :void {
      $this->mistnost = Mistnosti::get($_POST['slug']);
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::LARGE;

   public function prepareModal(Templater $templater) :void {
      $jazyk = System::get()->getApplication()->environment->getJazyk();

      $templater->addData([
         'mistnost' => $this->mistnost,
         'typyMistnosti' => TypyMistnosti::cases(),
         'photoGalery' => OrganizaceMistnostiFotky::getForMistnost($this->mistnost->id_mistnost),
         'typyUsporadani' => TypUsporadaniMistnostiEnum::cases(),
         'pouzivaneVlastnosti' => !empty($this->mistnost->getVlasnostiIDs()) ? VlastnostiNazev::getForJazyk($jazyk, $this->mistnost->getVlasnostiIDs()) : [],
         'systemLanguage' => $jazyk->value,
      ]);
   }

   public function getTitleName() :string {
      return 'Náhled místnosti';
   }

   public MistnostRow $mistnost;
}