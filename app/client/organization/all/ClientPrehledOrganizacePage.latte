{varType string $clientHpUrl}
{varType Latte\Runtime\Html $entityList}
{varType app\front\organizace\model\organizace\OrganizaceRow $organizace}
{varType app\system\model\nabidka\smlouva\obalka\SmlouvaObalkaRow[] $obalky}

{varType string $urlNovaPoptavka}
{varType string $urlSign}
{varType string $urlDownload}

{varType Latte\Runtime\Html $udajeForm}
{varType app\system\model\organizace\portal\galerie\OrganizacePortalGalerieRow[] $galerieFotky}
{varType app\system\model\organizace\portal\OrganizacePortalRow $portalInfo}

<div class="container mt-4">
   <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
         <li class="breadcrumb-item"><a class="text-black" href="{$clientHpUrl}"><i class="align-middle me-2 bi bi-houses"></i></a></li>
         <li class="breadcrumb-item active text-primary" aria-current="page">{$organizace->nazev}</li>
      </ol>
   </nav>

   <section class="p-md-2">
      <div class="row">
         <h1 class="display-5 text-center">{$organizace->nazev}</h1>

      </div>

         <div class="row mt-4 d-flex justify-content-center">
            <div class="btn-group col-auto" role="group" aria-label="btnsPortal">
               <a class="btn btn-outline-primary w-auto" href="{$urlNovaPoptavka}">
                  {_'Poptat akci'}
               </a>
               <a class="btn btn-outline-primary w-auto shaddow-hover" data-bs-toggle="scroll" href="#clientAkce" role="button" aria-expanded="false" aria-controls="clientAkce">
                  {_'Moje akce'}
               </a>
               <a class="btn btn-outline-primary w-auto shaddow-hover" data-bs-toggle="collapse" href="#clientUdaje" role="button" aria-expanded="false" aria-controls="clientUdaje">
                  {_'Moje údaje'}
               </a>
               <a n:if="!empty($obalky)" class="btn btn-outline-primary w-auto shaddow-hover" href="#clientDokumenty" role="button">
                  {_'Moje dokumenty'}
               </a>
            </div>
         </div>
   </section>

<div class="collapse" id="clientUdaje">
   <section class="container card mt-md-4 mt-2 p-md-5 p-3">
      {$udajeForm}
   </section>
</div>

<section class="container">
         <div id="info" class="card p-md-5 p-3 my-5">
            <div class="row d-flex justify-content-center align-items-center gap-3">
               <div class="col-auto">
                  <small class="text-secondary"><i class="bi bi-geo-alt me-2 text-primary"></i>{$organizace->getKontakt()->ulice}, {$organizace->getKontakt()->psc} {$organizace->getKontakt()->mesto}, {$organizace->getKontakt()->getStat()->name}</small>
               </div>
               <div class="col-auto">
                  <small class="text-secondary"><i class="bi bi-building me-2 text-primary"></i>{_$organizace->getTypOrganizace()->getTitle()}</small>
               </div>
               <div class="col-auto btn-group">
                  <a n:if="$portalInfo->getPopis()" class="form-control btn bt-sm btn-outline-secondary" data-bs-toggle="collapse" href="#organizationClientPopis" role="button" aria-expanded="false">
                     <i class="bi bi-info-circle"></i>
                  </a>

                  <a n:if="!empty($galerieFotky)" class="form-control btn bt-sm btn-outline-secondary" data-bs-toggle="collapse" href="#collapseProfilGalerie" role="button" aria-expanded="false">
                     <i class="bi bi-images"></i>
                  </a>

                  <a n:if="$organizace->getKontakt()->website"
                          href="//{$organizace->getKontakt()->website}"
                          class="form-control btn bt-sm btn-outline-secondary" target="_blank"><i class="bi bi-link-45deg"></i>
                  </a>

                  <a n:if="$portalInfo->instagram_link"
                          href="//{$portalInfo->instagram_link}"
                          class="form-control btn bt-sm btn-outline-secondary" target="_blank"><i class="bi bi-instagram"></i></a>

                  <a n:if="$portalInfo->fb_link"
                          href="//{$portalInfo->fb_link}"
                          class="form-control btn bt-sm btn-outline-secondary" target="_blank"><i class="bi bi-facebook"></i></a>
               </div>
            </div>

            <div class="collapse" id="organizationClientPopis">
               {if $portalInfo->getPopis()}
                  <div class="d-flex justify-content-center">
                     <hr class="w-80">
                  </div>
                  <div>
                     {$portalInfo->getPopis()}
                  </div>
               {/if}
            </div>

            <div class="collapse" id="collapseProfilGalerie">
               <div n:if="!empty($galerieFotky)" id="profilGalerie" class="row my-3">
                  {if count($galerieFotky) === 1}
                     {foreach $galerieFotky as $fotka}
                        <img src="{$fotka->getPhotoFullSrc()}" class="w-100">
                     {/foreach}
                  {else}
                     <div id="carouselOrganizationGallery" class="carousel slide">
                        <div class="carousel-indicators">
                           {foreach $galerieFotky as $fotka}
                              <button type="button" data-bs-target="#carouselOrganizationGallery" data-bs-slide-to="{$iterator->counter0}" {if $iterator->first}class="active" aria-current="true"{/if}></button>
                           {/foreach}
                        </div>
                        <div class="carousel-inner">
                           {foreach $galerieFotky as $fotka}
                              <div n:class="carousel-item, $iterator->first ? active">
                                 <img src="{$fotka->getPhotoFullSrc()}" alt="{$fotka->name}" class="w-100">
                                 {*                     <div class="carousel-caption d-none d-md-block">*}
{*                        <h5>First slide label</h5>*}
{*                        <p>Some representative placeholder content for the first slide.</p>*}
{*                     </div>*}
                              </div>
                           {/foreach}
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#carouselOrganizationGallery" data-bs-slide="prev">
                           <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                           <span class="visually-hidden">{_'Předchozí'}</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#carouselOrganizationGallery" data-bs-slide="next">
                           <span class="carousel-control-next-icon" aria-hidden="true"></span>
                           <span class="visually-hidden">{_'Další'}</span>
                        </button>
                     </div>
                  {/if}
               </div>
            </div>
         </div>
</section>



   <section class="container" id="clientAkce">
      <div class="row">
         <h2 class="col-12">{_'Vaše akce'}</h2>
      </div>
      <hr class="m-0 mb-4">
      {$entityList}
   </section>

   <section class="container mt-7" id="clientDokumenty">
      {if !empty($obalky)}
         <div class="row">
            <h2 class="col-12">{_'Vaše dokumenty'}</h2>
         </div>
         <hr class="m-0 mb-4">
         <div class="px-5">
            <div class="row">
               {foreach $obalky as $obalka}
                  <div class="col col-md-4">
                     <div class="card m-2 p-3">
                        <div class="card-title pt-2 px-4">
                           <div class="row">
                              <div class="mt-0 col-8">
                                 <h5 class="lead">{_'Smlouva ke kalkulaci č: $1', sprintf('%04d', $obalka->id_nabidka)}</h5>
                              </div>
                              <div class="col-4">
                                 <span n:class="badge, $obalka->getStatus()->badgeClass()">{$obalka->getStatus()->getTranslatedTitle()}</span>
                              </div>
                           </div>
                        </div>
                        <div class="card-body px-4">
                           <div class="row gap-3 justify-content-end">
                              <div class="col-auto" n:if="$obalka->getStatus()->canDownload()">
                                 <button class="w-auto form-control btn btn-sm btn-primary w-100 js-download-pdf" type="button"
                                         data-envelope="{$obalka->id}" value="{$obalka->id}">{_'Stáhnout smlouvu'}</button>
                              </div>
                              <div class="col-auto" n:if="$obalka->getStatus()->canSign()">
                                 {var $signers = $obalka->getCustomerSigners()}
                              {if !is_array($signers)}
                                    <div class="row">
                                       {if $signers->getStatus()->canSign()}
                                          <button class="form-control btn btn-sm btn-primary w-100 js-sign-envelope" data-envelope="{$obalka->id}" data-signer="{$signers->id}" type="button">{_'Podepsat'}</button>
                                       {elseif $signers->getStatus()->isSigned()}
                                          <p>{_'Podepsáno'}</p>
                                       {/if}
                                    </div>
                                 {else}
                                    {foreach $signers as $signer}
                                       <div class="row mb-2">
                                          {if $signer->getStatus()->canSign()}
                                             <button class="form-control btn btn-sm btn-primary w-100 js-sign-envelope" data-envelope="{$obalka->id}" data-signer="{$signer->id}" type="button">{_'Podepsat za $1', $signer->getName()}</button>
                                          {elseif $signer->getStatus()->isSigned()}
                                             <p>{_'$1 je podepsaný', $signer->getName()}</p>
                                          {/if}
                                       </div>
                                    {/foreach}
                                 {/if}
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               {/foreach}
            </div>
         </div>
      {/if}
   </section>

   <div class="py-6"></div>

   <section class="container">
      <div class="row d-flex justify-content-center fixed-bottom">
         <div class="d-flex col-md-8 bg-white shadow rounded-4 border gap-3 my-2 p-2">
            <div class="flex-grow-1 row d-flex justify-content-center gap-3">
               <div class="btn-group col-auto" role="group" aria-label="btnsPortal">
                  <a class="btn btn-outline-primary w-auto" href="{$urlNovaPoptavka}">
                     {_'Poptat akci'}
                  </a>
                  <a class="btn btn-outline-primary w-auto" data-bs-toggle="scroll" href="#clientAkce" role="button" aria-expanded="false" aria-controls="clientAkce">
                     {_'Moje akce'}
                  </a>
                  <a n:if="!empty($obalky)" class="btn btn-outline-primary w-auto" href="#clientDokumenty" role="button">
                     {_'Moje dokumenty'}
                  </a>
               </div>
            </div>
         </div>
      </div>
   </section>
</div>


<script n:if="!empty($obalky)">
   $(function() {
      $('body').on('click', 'button.js-sign-envelope', function() {
         const btn = $(this);
         btn.attr('disabled', true);

         ajaxHandler.post({$urlSign}, { envelope: btn.attr('data-envelope'), signer: btn.attr('data-signer') }, function(response) {
            if(response['url'])
               openNewWindow(response['url']);

            btn.attr('disabled', null);
         });
      })
         .on('click', 'button.js-download-pdf' , function() {
            const btn = $(this);
            btn.attr('disabled', true);

            ajaxHandler.post({$urlDownload}, { envelope: btn.attr('data-envelope') }, function(response) {
               if(response['url'])
                  openNewWindow(response['url']);

               btn.attr('disabled', null);
            });
         });

      function openNewWindow(url) {
         const smlouvaWindow = window.open(url, '_blank');

         if (!smlouvaWindow || smlouvaWindow.closed || typeof smlouvaWindow.closed === 'undefined') {
            // The popup was blocked
            toastr['error']("{_'Váš prohlížeč zablokoval otevření vyskakovacího okna se smlouvou'}", '', {
               positionClass: 'toast-bottom-left',
               progressBar: true,
            });
            return false;
         }

         return true;
      }
   })
</script>
