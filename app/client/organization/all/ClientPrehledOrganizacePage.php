<?php namespace app\client\organization\all;

use app\client\controllers\ClientActionController;
use app\client\controllers\HomepageController;
use app\client\entity\EntityContainer;
use app\front\controllers\stazene\StazeneSouboryController;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\front\zakaznici\component\form\EditZakaznikaFormComponent;
use app\front\zakaznici\component\form\ZakaznikFormPostFactoriesTrait;
use app\katalog\organizace\OrganizationPoptavkaController;
use app\System;
use app\system\application\ApplicationVersion;
use app\system\component\Templater;
use app\system\Environment;
use app\system\flash\FlashMessages;
use app\system\layout\client\BaseClientLayout;
use app\system\model\nabidka\smlouva\obalka\SmlouvaObalka;
use app\system\model\organizace\portal\galerie\OrganizacePortalGalerie;
use app\system\model\organizace\portal\OrganizacePortal;
use app\system\model\organizace\zakaznici\OrganizaceZakaznici;
use app\system\model\organizace\zakaznici\souhlasy\OrganizaceZakazniciSouhlasRow;
use app\system\model\organizace\zakaznici\souhlasy\OrganizaceZakazniciSouhlasy;
use app\system\model\organizace\zakaznici\souhlasy\SouhlasyTypy;
use app\system\traits\PostListener;
use app\system\users\zakaznici\LoggedOrganization;
use app\system\users\zakaznici\LoggedZakaznik;
use Latte\Runtime\Html;

/** Created by Kryštof Czyź. Date: 29.09.2023 */
class ClientPrehledOrganizacePage extends BaseClientLayout
{

   use PostListener;
   use ZakaznikFormPostFactoriesTrait;

   public function setOrganizace(OrganizaceRow $misto) :static {
      $this->organizace = $misto;
      $this->appVersion = ApplicationVersion::from($this->organizace->typ);
      return $this;
   }

   public function setZakaznik(LoggedZakaznik $zakaznik) :static {
      $this->zakaznik = $zakaznik;
      $this->loggedOrganizace = $zakaznik->getOrganizaceZakaznik($this->organizace->id_organizace);
      return $this;
   }

   public function getOrganization() :?OrganizaceRow {
      return $this->organizace;
   }

   protected function prepareTemplate(Templater $templater) {
      $this->appendOdkazy();
      $this->appendOrganizace();
      $this->appendEntity();
      $this->appendKontakt();
      $this->appendDokumenty();
      $this->appendPortal();

      $this->setTitle(System::getTranslator()->layoutTranslate('Přehled organizace $1', $this->organizace->nazev));
      $templater->addData($this->pageData);
   }

   protected function preparePostListeners() :void {
      EditZakaznikaFormComponent::preparePost();

      $this->isset('btnUlozitZakaznikaClient', function($post) {
         $loggedOrganizace = $this->zakaznik->getOrganizaceZakaznik($this->organizace->id_organizace);
         if(!$loggedOrganizace || !($zkz = OrganizaceZakaznici::get($loggedOrganizace->id_zakaznik_organizace))){
            FlashMessages::setError('Chyba při ukládání');
            return;
         }

         $this->updateZakaznik($zkz, $post);
         FlashMessages::setSuccess('Údaje byly uložené');

         $souhlasRow = OrganizaceZakazniciSouhlasy::getTypForZakaznik(SouhlasyTypy::MARKETING, $zkz->id);

         if(
            (isset($post['marketingovy_souhlas']) && $souhlasRow)
            || (!isset($post['marketingovy_souhlas']) && !$souhlasRow)
         ){
            return;
         }

         if(isset($post['marketingovy_souhlas'])){
            $row = new OrganizaceZakazniciSouhlasRow();
            $row->id_zakaznik = $zkz->id;
            $row->ip_address = Environment::getIpAddress();
            $row->web_engine = Environment::getUserAgent();
            $row->typ = SouhlasyTypy::MARKETING->value;
            OrganizaceZakazniciSouhlasy::save($row);
            return;
         }

         OrganizaceZakazniciSouhlasy::delete($souhlasRow);
      });
   }

   private function appendOdkazy() {
      $this->pageData['clientHpUrl'] = HomepageController::getUrl();
      $this->pageData['urlNovaPoptavka'] = OrganizationPoptavkaController::getUrl($this->organizace->portal_odkaz);
   }

   private function appendOrganizace() {
      $this->pageData['organizace'] = $this->organizace;
   }

   private function appendEntity() {
      $loggedOrganizace = $this->loggedOrganizace;
      $appVersion = $this->appVersion;

      $entityContainer = new EntityContainer();

      $ev = $appVersion->getEventy($loggedOrganizace->id_organizace, $loggedOrganizace->id_zakaznik_organizace);

      $entityContainer->addEvents($ev);

      $entityContainer
         ->addNabidky(
            $appVersion->getNabidky($loggedOrganizace->id_organizace, $loggedOrganizace->id_zakaznik_organizace)
         )
         ->addPoptavky(
            $appVersion->getPoptavkyBezEntit($loggedOrganizace->id_organizace, $loggedOrganizace->id_zakaznik_organizace)
         )
      ;

      $this->pageData['entityList'] = $entityContainer->render();
   }

   private function appendKontakt() {
      $zkz = OrganizaceZakaznici::get(
         $this->environment->zakaznik
            ->getOrganizaceZakaznik($this->organizace->id_organizace)
            ->id_zakaznik_organizace
      );

      $this->pageData['udajeForm'] = new Html(EditZakaznikaFormComponent::getComponent()
         ->setZakaznik($zkz)
         ->prepareTemplater('client')
         ->render());
   }

   private function appendDokumenty() {
      $this->pageData['urlSign'] = ClientActionController::getUrl(ClientActionController::ACTION_SIGN_LINK);
      $this->pageData['urlDownload'] = StazeneSouboryController::getUrl(StazeneSouboryController::ACTION_DOWNLOAD_LINK);

      $obalky = [];
      $ids_nabidek = [];

      foreach($this->appVersion->getNabidky(
         $this->loggedOrganizace->id_organizace,
         $this->loggedOrganizace->id_zakaznik_organizace
      ) as $nabidka)
         $ids_nabidek[$nabidka->getID()] = $nabidka->getID();

      if(empty($ids_nabidek)){
         $this->pageData['obalky'] = $obalky;
         return;
      }

      foreach(SmlouvaObalka::getForNabidky($ids_nabidek, $this->appVersion) as $obalkaRow)
         if($obalkaRow->getStatus()->isVisibleZakaznik())
            $obalky[$obalkaRow->id] = $obalkaRow;

      $this->pageData['obalky'] = $obalky;
   }

   private function appendPortal() :void {
      $this->pageData['portalInfo'] = OrganizacePortal::get($this->organizace->id_organizace);
      $this->pageData['galerieFotky'] = OrganizacePortalGalerie::getForOrganizace($this->organizace->id_organizace);
   }

   protected array $pageData;
   protected OrganizaceRow $organizace;
   protected LoggedZakaznik $zakaznik;
   protected ApplicationVersion $appVersion;
   protected LoggedOrganization $loggedOrganizace;
}