{varType app\front\organizace\model\organizace\OrganizaceRow $organizace}
{varType string $urlNovaPoptavka}

{varType app\system\model\organizace\portal\OrganizacePortalRow $portalInfo}
{varType app\system\model\organizace\portal\galerie\OrganizacePortalGalerieRow[] $galerieFotky}

{varType app\system\model\organizace\mistnosti\MistnostRow[] $mistnosti}
{varType ?app\front\organizace\model\organizace\OrganizaceKapacitaRow $kapacita}
{varType string $homepageUrl}

{varType app\system\model\organizace\mistnosti\kapacita\TypUsporadaniMistnostiEnum[] $typyUsporadani}
{varType ?app\system\model\organizace\mistnosti\MistnostRow $largestRoom}
{varType Latte\Runtime\Html $detailModal}
{varType array $detailModalBtn}

<div class="container mt-4">
   <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
         <li class="breadcrumb-item"><a class="text-black" href="{$homepageUrl}"><i class="align-middle me-2 bi bi-houses"></i></a></li>
         <li class="breadcrumb-item active text-primary" aria-current="page">{$organizace->nazev}</li>
      </ol>
   </nav>
   <div class="row d-flex gap-3 justify-content-between align-items-center">
      <div class="col-auto">
         <h1>{$organizace->nazev}</h1>
      </div>
      <div class="col-auto btn-group">
         <a class="btn btn-primary w-auto shaddow-hover" href="{$urlNovaPoptavka}">{_'Poptat akci'}<i
                    class="bi bi-check-circle ms-2"></i></a>
         <a class="btn btn-outline-primary w-auto shaddow-hover" href="?verify">{_'Přihlásit se'}<i
                    class="bi bi-arrow-right-circle ms-2"></i></a>
      </div>
   </div>

   <div n:if="!empty($galerieFotky)" id="profilGalerie" class="row my-3 d-flex justify-content-center">
      {if count($galerieFotky) === 1}
         {foreach $galerieFotky as $fotka}
            <img src="{$fotka->getPhotoFullSrc()}" style="max-height: 60vh;object-fit: contain;">
         {/foreach}
      {else}
         <div id="carouselOrganizationGallery" class="carousel slide col-lg-10">
            <div class="carousel-indicators">
               {foreach $galerieFotky as $fotka}
                  <button type="button" data-bs-target="#carouselOrganizationGallery" data-bs-slide-to="{$iterator->counter0}" {if $iterator->first}class="active" aria-current="true"{/if}></button>
               {/foreach}
            </div>
            <div class="carousel-inner">
               {foreach $galerieFotky as $fotka}
                  <div n:class="carousel-item, $iterator->first ? active">
                     <img src="{$fotka->getPhotoFullSrc()}" alt="{$fotka->name}" class="w-100">
{*                     <div class="carousel-caption d-none d-md-block">*}
{*                        <h5>First slide label</h5>*}
{*                        <p>Some representative placeholder content for the first slide.</p>*}
{*                     </div>*}
                  </div>
               {/foreach}
            </div>
            <button class="carousel-control-prev" type="button" data-bs-target="#carouselOrganizationGallery" data-bs-slide="prev">
               <span class="carousel-control-prev-icon" aria-hidden="true"></span>
               <span class="visually-hidden">{_'Předchozí'}</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#carouselOrganizationGallery" data-bs-slide="next">
               <span class="carousel-control-next-icon" aria-hidden="true"></span>
               <span class="visually-hidden">{_'Další'}</span>
            </button>
         </div>
      {/if}
   </div>

   <div id="info" class="card p-md-4 p-3 my-5">
      <div class="row d-flex justify-content-center align-items-center gap-3">
         <div class="col-auto">
            <small class="text-secondary"><i class="bi bi-geo-alt me-2 text-primary"></i>{$organizace->getKontakt()->ulice}, {$organizace->getKontakt()->psc} {$organizace->getKontakt()->mesto}, {$organizace->getKontakt()->getStat()->name}</small>
         </div>
         <div class="col-auto">
            <small class="text-secondary"><i class="bi bi-building me-2 text-primary"></i>{_$organizace->getTypOrganizace()->getTitle()}</small>
         </div>
         <div n:if="$kapacita ?? null" class="col-auto">
            <small class="text-secondary"><i class="bi bi-people-fill me-2 text-primary"></i>{$kapacita->kapacita_celkem}</small>
         </div>
         <div class="col-auto btn-group">
            <a n:if="$organizace->getKontakt()->website"
                    href="//{$organizace->getKontakt()->website}"
                    class="form-control btn bt-sm btn-outline-secondary" target="_blank"><i class="bi bi-link-45deg"></i>
            </a>

            <a n:if="$portalInfo->instagram_link"
                    href="//{$portalInfo->instagram_link}"
                    class="form-control btn bt-sm btn-outline-secondary" target="_blank"><i class="bi bi-instagram"></i></a>

            <a n:if="$portalInfo->fb_link"
                    href="//{$portalInfo->fb_link}"
                    class="form-control btn bt-sm btn-outline-secondary" target="_blank"><i class="bi bi-facebook"></i></a>
         </div>
      </div>
      {if $portalInfo->getPopis()}
         <div class="d-flex justify-content-center">
            <hr class="w-80">
         </div>
         <div>
            {$portalInfo->getPopis()}
         </div>
      {/if}
   </div>

   <div n:if="isset($mistnosti) && count($mistnosti) > 0" id="prostoryTabulka" class="card p-md-4 p-3 my-5">
      <h2>{_'Prostory'}</h2>

      <div class="row my-3" n:if="$kapacita ?? null">
         <div class="col-auto">
            <label>{_'Celková kapacita'}</label>
            <p>{$kapacita->kapacita_celkem}</p>
         </div>
         <div class="col-auto">
            <label>{_'Celková plocha'}</label>
            <p>{$kapacita->plocha_celkem} m2</p>
         </div>
         <div class="col-auto" n:if="$largestRoom?->getRoomDimensions()->getCalculatedSpace()">
            <label>{_'Plocha největšího prostoru'}</label>
            <p>{$largestRoom?->getRoomDimensions()->getCalculatedSpace() ?? '-'} m2</p>
         </div>
         <div n:if="$largestRoom" class="col-auto">
            <label>{_'Kapacita největšího prostoru'}</label>
            <p>{$largestRoom->kapacita}</p>
         </div>
         <div class="col-auto">
            <label>{_'Kapacita ubytování'}</label>
            <p>{$kapacita->kapacita_ubytovani}</p>
         </div>
      </div>

      <div class="table-responsive">
         <table class="table table-hover">
            <thead>
            <tr>
               <th class="col-3">{_'Název prostoru'}</th>
               <th>{_'Typ prostoru'}</th>
               <th><img src="/files/layout/icons/plocha.png" height="20px" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-custom-class="custom-tooltip"
                        data-bs-title="Celková plocha"></th>
               <th><img src="/files/layout/icons/vyska.png" height="20px" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-custom-class="custom-tooltip"
                        data-bs-title="Výška"></th>
               <th><img src="/files/layout/icons/kapacita.png" height="20px" data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-custom-class="custom-tooltip"
                        data-bs-title="Maximální kapacita"></th>
               {foreach $typyUsporadani as $usporadani}
                  <th><img src="{$usporadani->getIconPath()}" height="20px" data-bs-toggle="tooltip"
                           data-bs-placement="top" data-bs-custom-class="custom-tooltip"
                           data-bs-title="{$usporadani->getTranslatedTitle()}"></th>
               {/foreach}
            </tr>
            </thead>
            <tbody>

            {foreach $mistnosti as $mistnost}
               <tr>
                  <td class="text-truncate" scope="row"><strong>{$mistnost->nazev}</strong>
                     <a href="#" n:attr="$detailModalBtn" data-slug="{$mistnost->id_mistnost}"><i class="bi bi-info-circle mx-2"></i></a>
{*                     <a href="#"><i class="bi bi-badge-3d mx-2"></i></a>*}
                  </td>
                  <td id="typ">{$mistnost->getTypMistnosti()->getTranslatedTitle()}</td>
                  <td id="rozmer">{$mistnost->getRoomDimensions()?->getDimension() ?? '-'}m
                     ({$mistnost?->getRoomDimensions()?->getCalculatedSpace() ?? '-'} m2)
                  </td>
                  <td id="vyska">{$mistnost?->getRoomDimensions()->ceiling_height ?? '-'}m</td>
                  <td id="max">{$mistnost->kapacita}</td>
                  {foreach $typyUsporadani as $usporadani}
                     <td id="{$usporadani->name}">{$mistnost->getRoomCapacities()[$usporadani->value]->kapacita ?? '-'}</td>
                  {/foreach}
               </tr>
            {/foreach}
            </tbody>
         </table>
      </div>

      {$detailModal|noescape}
   </div>

   <div class="py-4"></div>

   <div class="row d-flex justify-content-center fixed-bottom">
      <div class="d-flex col-md-8 bg-white shadow rounded-4 border gap-3 my-2 p-2">
         <div class="flex-grow-1 row d-flex justify-content-center gap-3">
            <a class="form-control btn btn-primary w-auto" href="{$urlNovaPoptavka}">{_'Poptat akci'}<i
                       class="bi bi-check-circle ms-2"></i></a>
            <a class="form-control btn btn-outline-secondary w-auto" href="?verify">{_'Přihlásit se'}<i
                       class="bi bi-arrow-right-circle ms-2"></i></a>
         </div>
         <div class="d-flex align-items-center gap-2 mx-2">
            <div class="vr p-0 mx-1 d-none d-md-inline-block"></div>
            <a href="#profilGalerie" class="text-secondary"><i class="bi bi-images"></i></a>
            <a href="#info" class="text-secondary"><i class="bi bi-info-circle"></i></a>
            <a n:if="isset($mistnosti) && count($mistnosti) > 0" href="#prostoryTabulka" class="text-secondary"><i class="bi bi-diagram-3"></i></a>
         </div>
      </div>
   </div>
</div>
