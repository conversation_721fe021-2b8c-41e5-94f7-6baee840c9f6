<?php namespace app\libs\oauth;

use app\katalog\uzivatel\ZakaznikOauthPrihlaseniController;
use app\system\Environment;
use app\system\Redirect;
use app\system\SystemVersion;
use Google\Client;

/** Created by <PERSON><PERSON>. Date: 09.04.2025 */
class GoogleAuthApiClient
{

   const string CLIENT_ID = '587418206479-fd7ef2888ua31hk3d8q8igg5v255313d.apps.googleusercontent.com';
   const string CLIENT_SECRET = '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';

   public static function getClient() :Client {
      return self::$client ??= self::createClient();
   }

   public static function getAuthUrl() :string {
      return self::getClient()->createAuthUrl();
   }

   private static function createClient() :Client {
      $client = new Client();

      $client->setClientId(self::CLIENT_ID);
      $client->setClientSecret(self::CLIENT_SECRET);

      $client->setRedirectUri(
         Environment::isLocalhost()
            ? 'http://localhost' . ZakaznikOauthPrihlaseniController::getUrl(ZakaznikOauthPrihlaseniController::GOOGLE)
            : Redirect::check(
            ZakaznikOauthPrihlaseniController::getUrl(ZakaznikOauthPrihlaseniController::GOOGLE),
            SystemVersion::KATALOG
         )
      );

      $client->addScope('email');
      $client->addScope('profile');
      $client->setLogger(new GoogleLogger());

      return $client;
   }

   private static Client $client;
}