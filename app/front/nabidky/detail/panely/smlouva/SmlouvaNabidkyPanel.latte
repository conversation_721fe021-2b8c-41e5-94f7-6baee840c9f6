{varType app\system\model\nabidka\smlouva\NabidkaSmlouvaRow $smlouva}
{varType app\front\organizace\model\organizace\OrganizaceRow $misto}
{varType Latte\Runtime\Html $tinyEditor}

{varType app\system\model\nabidka\smlouva\podepisujici\SmlouvaPodepisujiciRow[] $podepisujici}
{varType array $attrAddOsobu}
{varType string $urlDelete}
{varType string $urlSign}
{varType bool $isLocalhost}
{varType ?app\system\model\nabidka\smlouva\obalka\SmlouvaObalkaRow $obalka}
{varType string $urlDownload}
{varType ?int $defaultPlatnost}

{varType app\front\nabidky\detail\panely\smlouva\modal\edit\EditOsobaSmlouvaModal $_modal}
{varType app\system\model\organizace\personal\PersonalRow $personal}

<form method="post">
   <div>
      <div class="row gap-3 mb-4">
         <div class="col-md-9">
            <div class="row card p-3">
               <div class="row d-flex align-content-center mt-2">
                  <div class="d-flex justify-content-between align-items-center gap-2">
                     <h1 class="lead col">{_'Smlouva'}</h1>
                     <span class="badge bg-secondary rounded-pill text-white col-auto"><i class="bi bi-file-text me-1"></i>
                        {if $smlouva->getObalka() && $smlouva->getObalka()->getStatus()->isKorekce()}{_'Korekce'}
                        {else}{$smlouva->getStav()->getTranslatedTitle()}{/if}</span>
                  </div>
               </div>
            </div>
            <div class="row card p-3 mb-0">
               <div class="row gap-2">
                  <div class="col-auto">
                     <h2 class="lead">{_'Podepisující strany'}</h2>
                  </div>
                  <div class="col-md col-12 d-md-flex justify-content-end">
                     <div n:if="$smlouva->getStav()->isEditable() && count($podepisujici) < 2 && isset($attrAddOsobu)" class="form-group">
                        <a href="#" n:attr="$attrAddOsobu" class="btn btn-sm btn-outline-primary"><i class="me-2 fas fa-fw fa-user-plus"></i>{_'Přidat osobu'}</a>
                     </div>
                  </div>
               </div>
               <div class="row mt-3">
                  <div class="col-12 col-md">
                     <div class="row">
                        <div class="col">
                           <h5 class="card-title">{_'Za $1', $misto->nazev}</h5>
                        </div>
                        <div class="col">
                           {*                     @TODO zobrazení že personál je v digi sign a zobrazení zda byl autosign nebo čeká na podpis *}
                           <div n:if="!!$smlouva->getObalka()" class="card-actions float-end">
                              {var $signer = $smlouva->getObalka()->getPersonalSigner()}
                              <div class="d-inline-block">
                                 <span n:if="!$signer" class="badge bg-danger">{_'Není v DigiSign'}</span>
                                 <span n:if="$signer" n:class="badge, $signer->getStatus()->badgeClass()">{$signer->getStatus()->getTranslatedTitle()}</span>
                              </div>
                              <div n:if="$obalka->getStatus()->canSign() && $signer->getStatus()->canSign()" class="d-inline-block">
                                 <button class="btn btn-sm btn-primary form-control js-sign-envelope" data-envelope="{$obalka->id}" data-signer="{$signer->id}">{_'Podepsat'}</button>
                              </div>
                              <div n:if="!$signer" class="d-inline-block dropdown show">
                                 <a href="#" data-bs-toggle="dropdown" data-bs-display="static">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-more-vertical align-middle"><circle cx="12" cy="12" r="1"></circle><circle cx="12" cy="5" r="1"></circle><circle cx="12" cy="19" r="1"></circle></svg>
                                 </a>

                                 <div class="dropdown-menu dropdown-menu-end">
                                    <button type="submit" class="btn btn-link dropdown-item" name="btnTryAddPersonalSigner">{_'Zkusit znovu nahrát'}</button>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <div class="card-body">
                           <div class="row">{$personal->getFullName()}</div>
                           <div class="row">{$personal->email}</div>
                           <div class="row">{$personal->telefon}</div>
                        </div>
                     </div>
                  </div>
                  <div class="vr p-0 d-none d-lg-inline"></div>
                  <hr class="d-lg-none my-2">
                  <div class="col-12 col-md mt-4 mt-lg-0">
                     <div class="row">
                        <div n:foreach="$podepisujici as $osoba">
                           <div class="">
                              {* @TODO upravit na editovatelnou pokud bude založená obálka, nebo bude v korekci, nyní jen pouze pokud chybí v obálce, upravit SaveObalkaSignerEvent*}
                              <div n:if="$smlouva->getStav()->isEditable() || !$osoba->isInEnvelope() || ($smlouva->getObalka()->getStatus()->isKorekce() && !$osoba->isInEnvelope()->getStatus()->isCompleted())" class="card-actions float-end">
                                 <div class="d-inline-block dropdown show">
                                    <a href="#" class="btn btn-sm btn-outline-primary dropdown-toggle w-auto shaddow-light ms-1" data-bs-toggle="dropdown" data-bs-display="static">
                                       {_'Další možnosti'}
                                    </a>

                                    <div class="dropdown-menu dropdown-menu-end rounded-3">
                                       <a class="dropdown-item text-sm" href="#" n:attr="$_modal::getShowAttributes($osoba->id)">{_'Upravit'}</a>
                                       <a n:if="($urlDelete?? false) && $smlouva->getStav()->isEditable()"
                                               class="dropdown-item text-danger text-sm js-odebrat-podpis" href="#"
                                               data-confirm="{_'Opravdu chcete odebrat podepisující stranu?'}" data-osoba="{$osoba->id}">{_'Odebrat'}</a>
                                    </div>
                                 </div>
                              </div>
                              <div n:if="!!$smlouva->getObalka()" class="card-actions float-end">
                                 <div class="d-inline-block">
                                    <span n:if="!$osoba->isInEnvelope()" class="badge bg-danger">{_'Není v DigiSign'}</span>
                                    <span n:if="$osoba->isInEnvelope()" n:class="badge, $osoba->isInEnvelope()->getStatus()->badgeClass()">{$osoba->isInEnvelope()->getStatus()->getTranslatedTitle()}</span>
                                 </div>
                              </div>
                              <h5 class="card-title">{_'Zákazník'}</h5>
                           </div>
                           <div class="card-body">
                              <div class="row">{$osoba->cele_jmeno}</div>
                              <div class="row">{$osoba->email}</div>
                              <div class="row">{$osoba->telefon}</div>
                           </div>
                        </div>


                        <div class="card" n:if="$smlouva->getStav()->isPodepsana()">
                           <div class="card-body">
                              <div class="row text-center">
                                 <button class="btn btn-sm btn-primary w-100 js-download-pdf" type="button"
                                         data-envelope="{$obalka->id}" value="{$obalka->id}">{_'Stáhnout smlouvu'}</button>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            <div n:if="$smlouva->getObalka() && $smlouva->getObalka()->getStatus()->isKorekce()" class="alert alert-primary alert-outline alert-dismissible my-3 p-2" role="alert">
               <div class="alert-icon">
                  <i class="bi bi-exclamation-circle lead text-white"></i>
               </div>
               <div class="alert-message">
                  <strong>{_'Aby korekce smlouvy proběhla správně, je potřeba po úpravách smlouvu nejprve uložit ( 1 Uložit změny ) a následně dokončit korekci ( 2 Dokončit korekci ).'}
                  </strong>
               </div>
               <button type="button" class="btn-close m-1" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
         </div>
         <div class="col-md col-12">
            <div class="row card p-3 h-100">
               <h3 class="lead mb-3">{_'Další informace'}</h3>
               <div class="row form-group">
                  <label>{_'Vytvořeno'}</label>
                  <p>{$smlouva->created|date: 'j.n.Y H:i'}</p>
               </div>
               <div n:if="$smlouva->created->getTimestamp() !== $smlouva->updated->getTimestamp()" class="row form-group">
                  <label>{_'Upraveno'}</label>
                  <p>{$smlouva->updated|date: 'j.n.Y H:i'}</p>
               </div>
               {if $smlouva->getObalka()?->expire_at}
                  <div class="row form-group">
                     <label>{_'Platnost do'}</label>
                     {if $smlouva->getObalka()->getStatus()->isKorekce()}
                        <div class="form-group form-group-sm mb-2">
                           <div class="input-group date js-datepicker" id="platnost_smlouva" data-target-input="nearest" data-datepicker-min="{date('m/d/Y')}">
                              <input type="text" class="form-control datetimepicker-input" data-target="#platnost_smlouva"
                                     name="platnost_smlouva" value="{$smlouva->getObalka()->expire_at|date: 'j.n.Y'}" required>
                              <div class="input-group-text" data-target="#platnost_smlouva" data-toggle="datetimepicker"><i
                                         class="bi bi-calendar2-event"></i></div>
                           </div>
                        </div>
                     {else}
                        <p>{$smlouva->getObalka()->expire_at|date: 'j.n.Y H:i'}</p>
                     {/if}
                  </div>
               {elseif !$smlouva->getObalka()}
                  <div class="row form-group">
                     <label>{_'Platnost'}</label>
                     <p>{_'$1 dní od odeslání smlouvy', $defaultPlatnost ?: 30}</p>
                  </div>
               {/if}
            </div>
         </div>
      </div>

      <div class="row mt-2">
         {$tinyEditor}
      </div>
      <div class="col-lg-auto col-12 mx-1 d-flex justify-content-center fixed-bottom">
         <div class="row d-flex justify-content-center bg-white border border-light-gray radius-card shaddow-light shaddow-hover gap-3 my-2 p-2">
            <div class="btn-group">
               <button n:if="$smlouva->getStav()->isEditable()" type="submit" name="btnUlozitZmenySmlouva" class="btn btn-outline-primary w-auto shaddow-hover">{_'Uložit změny'}</button>
               <button n:if="$smlouva->getObalka() && $smlouva->getObalka()->getStatus()->isKorekce()" type="submit" name="btnUlozitZmenyKorekce" class="btn btn-outline-primary w-auto shaddow-hover"><i class="bi bi-1-circle me-1"></i>{_'Uložit změny'}</button>
               <button n:if="$smlouva->getStav()->canOdeslatSmlouvu()" type="submit" name="btnOdeslatSmlouvu" class="btn btn-primary w-auto shaddow-hover"
                          data-confirm="{_'Opravdu chcete odeslat smlouvu ke zpracování?'}"><i class="bi bi-send-check me-1"></i>{_'Odeslat smlouvu'}</button>
               <button n:if="$smlouva->getStav()->isSchvaleni()" type="submit" name="btnSchvalitSmlouvu" class="btn btn-primary w-auto shaddow-hover">{_'Schválit'}</button>
               <button n:if="$smlouva->getStav()->isZalozenaObalka()" type="submit" name="btnOdeslatObalku" class="btn btn-primary w-auto shaddow-hover"><i class="bi bi-send-check me-1"></i>{_'Odeslat Smlouvu'}</button>
               {*         @TODO připravit event *}
               <button n:if="$smlouva->getObalka() && $smlouva->getObalka()->getStatus()->canKorekce()" type="submit" name="btnKorekceSmlouvy" class="btn btn-outline-primary w-auto shaddow-hover">
                  {if !$smlouva->getObalka()->getStatus()->isKorekce()}{_'Korekce smlouvy'}{else}<i class="bi bi-2-circle me-1"></i>{_'Dokončit korekci'}{/if}
               </button>
               <button n:if="($isLocalhost && !!$smlouva->getObalka() && $smlouva->getObalka()->getStatus()->canUpdate())" type="submit" name="btnAktualizovatObalku" class="btn btn-primary w-auto shaddow-hover">{_'Aktualizovat data'}</button>
               <button n:if="$smlouva->getStav()->isSchvaleni()" type="submit" name="btnZamitnoutSmlouvu" class="btn btn-secondary w-auto shaddow-hover">{_'Zamítnout'}</button>
                <button n:if="$smlouva->getStav()->isEditable()  || ($smlouva->getObalka() && ($smlouva->getObalka()->getStatus()->canDelete()))" type="submit" name="btnSmazatSmlouva" class="btn btn-outline-secondary w-auto shaddow-hover"
                  data-confirm="{_'Opravdu chcete smazat smlouvu?'}">{_'Smazat'}</button>
            </div>
         </div>
      </div>
   </div>
</form>

<script n:ifset="$urlDelete">
   $(function() {
      $('body').on('click', 'a.js-odebrat-podpis', function(e) {
         if(e.isDefaultPrevented())
            return;

         ajaxHandler.post({$urlDelete}, { osoba: $(this).attr('data-osoba') }, function(response) {
            location.reload();
         });
      })
   });
</script>
<script>
    $(function() {
        $('body').on('click', 'button.js-download-pdf', function () {
            const btn = $(this);
            btn.attr('disabled', true);

            ajaxHandler.post({$urlDownload}, { envelope: btn.attr('data-envelope') }, function (response) {
                if (response['url'])
                   openNewWindow(response['url']);

                btn.attr('disabled', null);
            });
        }).on('click', 'button.js-sign-envelope', function() {
           const btn = $(this);
           btn.attr('disabled', true);

           ajaxHandler.post({$urlSign}, { envelope: btn.attr('data-envelope'), signer: btn.attr('data-signer') }, function(response) {
              if(response['url'])
                 openNewWindow(response['url']);

              btn.attr('disabled', null);
           });
        });

        function openNewWindow(url) {
           const smlouvaWindow = window.open(url, '_blank');

           if (!smlouvaWindow || smlouvaWindow.closed || typeof smlouvaWindow.closed === 'undefined') {
              // The popup was blocked
              toastr['error']("{_'Váš prohlížeč zablokoval otevření vyskakovacího okna se smlouvou'}", '', {
                 positionClass: 'toast-bottom-left',
                 progressBar: true,
              });
              return false;
           }

           return true;
        }
    });
</script>