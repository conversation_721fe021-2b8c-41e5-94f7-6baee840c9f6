<?php namespace app\front\nabidky\detail\panely;

use app\front\controllers\availability\AvailabilityCheckerType;
use app\front\controllers\nabidky\DetailNabidkyActionsControllerWH;
use app\front\controllers\nabidky\NahledKalkulaceController;
use app\front\controllers\platby\scenare\PlatebniScenareActionController;
use app\front\nabidky\detail\panely\modal\ZamitnutiKalkulcaceModal;
use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceNastaveni;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\front\prilohy\kalkulace\KalkulacePrilohyComponent;
use app\front\zakaznici\modal\info\ZakaznikInfoModal;
use app\System;
use app\system\application\ApplicationVersion;
use app\system\application\FrontApplicationEnvironment;
use app\system\chat\ChatUser;
use app\system\chat\component\ChatWindowComponent;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\entity\polozky\component\BasePolozkyComponent;
use app\system\model\event\eventy\CreateFromNabidkaEvent;
use app\system\model\leady\lead\LeadZmenaStavuEvent;
use app\system\model\maily\bloky\data\ObsahBlokyTypyEnum;
use app\system\model\maily\bloky\OrganizaceObsahBloky;
use app\system\model\mista\event\CreateOrganizaceMistoEvent;
use app\system\model\mista\OrganizaceMista;
use app\system\model\mista\smlouvy\OrganizaceSmlouvy;
use app\system\model\nabidka\BaseNabidkaRow;
use app\system\model\nabidka\event\DeleteNabidkaEvent;
use app\system\model\nabidka\event\UpravaNabidkyEvent;
use app\system\model\nabidka\event\ZmenaStatusNabidkaEvent;
use app\system\model\nabidka\export\pdf\NabidkaPdfExport;
use app\system\model\nabidka\mista\NabidkyDodavateAdresy;
use app\system\model\nabidka\NabidkaStatus;
use app\system\model\nabidka\prilohy\KalkulacePrilohy;
use app\system\model\nabidka\smlouva\NabidkaSmlouva;
use app\system\model\nabidka\smlouva\podepisujici\SmlouvaPodepisujici;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\organizace\kalkulace\introduction\KalkulaceIntroductionSablony;
use app\system\model\organizace\localtax\component\EntityLocalTaxComponent;
use app\system\model\organizace\meny\OrganizaceMeny;
use app\system\model\organizace\personal\Personal;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\model\organizace\personal\prava\OrganizacePersonalPrava;
use app\system\model\organizace\personal\prirazeni\component\PersonalAssignmentComponent;
use app\system\model\organizace\zakaznici\OrganizaceZakaznici;
use app\system\model\platby\scenare\refactor\component\osa\CasovaOsaComponent;
use app\system\model\platby\scenare\refactor\PlatebniScenar;
use app\system\model\poptavky\PoptavkaStavy;
use app\system\model\uzivatele\SessionUzivatel;
use app\system\modul\panels\Panel;
use app\system\Redirect;
use app\system\tiny\TinyEditor;
use Latte\Runtime\Html;

/** Created by Kryštof Czyź. Date: 28.07.2023 */
abstract class BaseNabidkaPanel extends Panel
{

   abstract protected function getNabidkaRow() :BaseNabidkaRow;

   function getMenuName() :string {
      return 'Kalkulace';
   }

   public function preparePanel(Templater $templater) :void {
      $this->nabidka = $this->getNabidkaRow();
      $this->organizace = Organizace::getMisto($this->nabidka->getPoptavka()->id_organizace);
      $poptavka = $this->nabidka->getPoptavka();

      $this->data = [
         'isMobil' => System::get()->detect->isMobile(),
         'nabidka' => $this->nabidka,
         'poptavka' => $poptavka,
         'eventType' => $poptavka->getTypAkceString(),
         'zakaznik' => OrganizaceZakaznici::get($poptavka->id_zakaznika),
         'zakaznikModalAttrs' => ZakaznikInfoModal::getShowAttributes($poptavka->id_zakaznika),
         'jazyky' => OrganizaceJazyky::getAll($this->organizace->id_organizace),
         'meny' => OrganizaceMeny::getAll($this->organizace->id_organizace),
         'assignmentIcons' => PersonalAssignmentComponent::getForEntity(
            $this->getNabidkaRow(),
            FrontApplicationEnvironment::get()->personal,
         )->render(),
         'localTaxComponent' => (new EntityLocalTaxComponent($this->nabidka))->render(),
         'nahledUrl' => NahledKalkulaceController::getUrl($this->nabidka->getID()),
         'polozkyComponent' => BasePolozkyComponent::getComponent()->setEntity($this->nabidka)->renderHtml(),
      ];

      $this->appendScenare();
      $this->appendVyberSmlouvy();
      $this->appendCanSchvalovat();
      $this->appendTemplateData();
      $this->appendIntroduction();
      $this->appendPrilohy();
      $this->appendChat();
      $this->appendTerminChecker();
      $this->appendHlavicky();
      $this->appendZamitnutiModal();

      $templater->addData($this->data);
   }

   protected function preparePostListeners(): void{
      $this->isset('btnHotovaNabidka', function($post) {
         (new UpravaNabidkyEvent($this->getNabidkaRow(), $post))->call();

         ZmenaStatusNabidkaEvent::change(
            $nabidka = $this->getNabidkaRow(),
            NabidkaStatus::ODESLANA
         )->call();

         if($nabidka->stav === NabidkaStatus::ODESLANA->value)
            FlashMessages::setSuccess('Kalkulace byla odeslána');
         else
            FlashMessages::setWarning('Kalkulace čeká na schválení');
      });

      $this->isset('btnSchvalitNabidku', function($post) {
         ZmenaStatusNabidkaEvent::change(
            ($nabidka = $this->getNabidkaRow()),
            $post['btnSchvalitNabidku'] === 'korekce' ? NabidkaStatus::KOREKCE : NabidkaStatus::ODESLANA
         )->call();

         if($nabidka->stav === NabidkaStatus::ODESLANA->value)
            FlashMessages::setSuccess('Kalkulace byla odeslána!');
         else
            FlashMessages::setWarning('Kalkulace byla odeslána zpět ke korekci');
      });

      $this->isset('btnPrijmoutNabidku', function() {
         ZmenaStatusNabidkaEvent::change(
            $this->getNabidkaRow(),
            NabidkaStatus::PRIJATA
         )->setNotification(false)->call();

         FlashMessages::setSuccess('Kalkulace byla uspěšně potvrzena!');
      });

      $this->isset('btnUlozitNabidku', function($post) {
         (new UpravaNabidkyEvent($this->getNabidkaRow(), $post))->call();
      });

      $this->isset('btnPrepareSmlova', function($post) {
         $nabidka = $this->getNabidkaRow();

         if(($post['smlouva']?? false) && is_numeric($post['smlouva'])
            && ($sablona = OrganizaceSmlouvy::get($post['smlouva']))
            && $sablona->id_organizace === $nabidka->getPoptavka()->id_organizace
         ){
            $podepisujici = SmlouvaPodepisujici::createForZakaznikMista(
               $smlouva = NabidkaSmlouva::createFromSablona($sablona, $nabidka),
               OrganizaceZakaznici::get($nabidka->getPoptavka()->id_zakaznika)
            );

            $smlouva->text = str_replace(OrganizaceSmlouvy::SIGNATURE_PLACEHOLDER_ORGANIZATION, NabidkaSmlouva::SIGNATURE_PLACEHOLDER_MAIN, $smlouva->text);
            $smlouva->text = str_replace(OrganizaceSmlouvy::SIGNATURE_PLACEHOLDER_CUSTOMER, $podepisujici->getPlaceholder(), $smlouva->text);
            $smlouva->save();
         }
      });

      $this->isset('btnVytvoritBezSmlouvy', function() {
         $nabidka = $this->getNabidkaRow();
         $createdEvent = (new CreateFromNabidkaEvent($nabidka))
            ->call()
            ->getEvent();

         ZmenaStatusNabidkaEvent::change(
            $this->getNabidkaRow(),
            NabidkaStatus::BEZ_SMLOUVY,
         )->call();

//         Redirect nepoužívat v isset funkci, dočasně řešeno
         Redirect::to($createdEvent->getDetailUrl());
      });

      $this->isset('btnRemoveMisto', function($post) {
         NabidkyDodavateAdresy::delete($post['id-mista-poradani'], $this->getNabidkaRow()->getID());

         FlashMessages::setSuccess('Místo bylo odebráno');
      });

//      Duplicita CreatePlainEvents, MistoPoradaniEventPanel
      $this->isset('btnPridatMistoPoradani', function($post) {
         $misto = OrganizaceMista::getByName($this->getNabidkaRow()->getPoptavka()->id_organizace, trim($post['misto-nazev']));

         if(!$misto){
            $psc = trim($post['misto-psc']);
            $ulice = trim($post['misto-ulice']);

            if(
               !$psc
               || !$ulice
               || !($misto = OrganizaceMista::getByAddress($this->getNabidkaRow()->getPoptavka()->id_organizace, $post['misto-ulice'], $post['misto-psc']))
            ){
               $misto = (new CreateOrganizaceMistoEvent($this->getNabidkaRow()->getPoptavka()->id_organizace, [
                  'nazev' => trim($post['misto-nazev']),
                  'id_stat' => intval($post['misto-stat']),
                  'mesto' => trim($post['misto-mesto']),
                  'ulice' => $post['misto-ulice'],
                  'psc' => $post['misto-psc'],
                  'popis' => null,
               ]))->call()->getMisto();
            }
         }

         if($misto) {
            NabidkyDodavateAdresy::create($this->getNabidkaRow()->getID(), $misto->id_mista);
            FlashMessages::setSuccess('Místo bylo přidano');
         }
      });

      $this->isset('btnStornoNabidka', function() {
         ZmenaStatusNabidkaEvent::change(
            $this->getNabidkaRow(),
            NabidkaStatus::STORNO
         )->call();

         FlashMessages::setSuccess('Kalkulace byla stornována!');
      });

      $this->isset('btnSmazatNabidku', function() {
         $this->deleteNabidka();
      }, $this->getNabidkaRow()->getPoptavka()->getDetailUrl());

      $this->isset('btnSmazatNabidkuPoptavku', function() {
         LeadZmenaStavuEvent::handlePoptavkaChange($this->getNabidkaRow()->getPoptavka(), PoptavkaStavy::ZAMITNUTO->value);
         $this->deleteNabidka();
      }, $this->getNabidkaRow()->getPoptavka()->getDetailUrl());

      $this->isset('btnZrusitNabidku', function() {
         ZmenaStatusNabidkaEvent::change(
            $this->getNabidkaRow(),
            NabidkaStatus::ZAMITNUTO_MANAGEREM
         )->call();

         FlashMessages::setSuccess('Kalkulace byla zrušena!');
      });

      $this->isset('btnZrusitNabidkuPoptavku', function() {
         ZmenaStatusNabidkaEvent::change(
            $this->getNabidkaRow(),
            NabidkaStatus::ZAMITNUTO_MANAGEREM
         )->call();
         LeadZmenaStavuEvent::handlePoptavkaChange($this->getNabidkaRow()->getPoptavka(), PoptavkaStavy::ZAMITNUTO->value);

         FlashMessages::setSuccess('Kalkulace i poptávka byly zrušeny!');
      });

      $this->isset('btnDownloadPdfCalculation', function() {
         NabidkaPdfExport::get($this->getNabidkaRow())->download();
      });
   }

   protected function appendScenare() {
      $this->data['scenarePairs'] = Organizace::getAktivniPlatebniScenare($this->organizace->id_organizace, $this->nabidka->id_mena);
      $this->data['scenarOsa'] = CasovaOsaComponent::getForDefinition(
         PlatebniScenar::get($this->nabidka->selected_scenar), $this->nabidka->getSuma());
      $this->data['urlScenarComp'] = PlatebniScenareActionController::getUrl('component');
   }

   protected function appendVyberSmlouvy() {
      if(NabidkaSmlouva::getForNabidka($this->nabidka->getID(), FrontApplicationEnvironment::get()->versionType))
         return;

      $this->data['smlouvy'] = OrganizaceSmlouvy::findForOrganizace($this->organizace->id_organizace)
         ->where('os.id_jazyk = %i AND os.id_personal_podpis != 0', $this->nabidka->id_jazyk)
         ->select(false)->select('os.id, os.nazev')
         ->fetchPairs('id', 'nazev');
   }

   private function appendCanSchvalovat() {
      $this->data['hasSchvalovani'] = OrganizaceNastaveni::getByOrganizace($this->organizace->id_organizace)?->schvalovaci_proces === 1;
      $this->data['canSchvalovat'] = (
         SessionUzivatel::getUzivatel()->isMajitel || SessionUzivatel::getUzivatel()->isSpravce
         || in_array(
            PersonalAccessRightsEnum::SCHVALOVACI->value,
            OrganizacePersonalPrava::getByPersonal(Personal::getByUzivatelMisto(SessionUzivatel::getId(), $this->organizace->id_organizace)->id_personal)
         )
      );

   }

   private function appendChat() {
      $this->data['chatComponent'] = new Html(ChatWindowComponent::getComponent()
         ->setEntity($this->nabidka)
         ->setZobrazuje(new ChatUser(FrontApplicationEnvironment::get()->personal)));
   }

   private function deleteNabidka() {
      (new DeleteNabidkaEvent($this->getNabidkaRow()))
         ->call();

      FlashMessages::setSuccess('Kalkulace byla smazána');
   }

   protected function appendIntroduction() :void {
      $introduction = TinyEditor::init()
         ->setInputName('introduction')
         ->setContent($this->getNabidkaRow()->getIntroduction()?->text ?: '')
         ->setId('introduction-editor')
         ->setPlaceholder('Toto je místo pro úvodní text ke kalkulaci'); // Přidání placeholderu

      if(!$this->nabidka->isEditovatelna()) {
         $introduction->setReadonly();
      }

      $this->data['sablony'] = KalkulaceIntroductionSablony::getByJazykOrganizace($this->organizace->id_organizace, $this->nabidka->id_jazyk);
      $this->data['introduction'] = $introduction->render();
      $this->data['sablonyLoadURL'] = DetailNabidkyActionsControllerWH::getUrl(
         $this->getNabidkaRow()->getID(),
         DetailNabidkyActionsControllerWH::ACTION_LOADINTRODUCTION,
      );
      $this->data['sablonySaveURL'] = DetailNabidkyActionsControllerWH::getUrl(
         $this->getNabidkaRow()->getID(),
         DetailNabidkyActionsControllerWH::ACTION_SAVEINTRODUCTION,
      );
      $this->data['sablonyCreateURL'] = DetailNabidkyActionsControllerWH::getUrl(
         $this->getNabidkaRow()->getID(),
         DetailNabidkyActionsControllerWH::ACTION_CREATEINTRODUCTION,
      );
      $this->data['version'] = ApplicationVersion::getFromNabidka($this->nabidka)->value;
   }

   protected function appendPrilohy() :void {
      $this->data['dostupnePrilohy'] = KalkulacePrilohy::getDostupneByKalkulace(
         $this->organizace->id_organizace,
         $this->nabidka->getPrilohy() ? array_keys($this->nabidka->getPrilohy()) : [0],
      );

      $this->data['prilohy'] = new Html((string)KalkulacePrilohyComponent::getComponent()->setKalkulace($this->nabidka));
      $this->data['pridatPrilohuUrl'] = DetailNabidkyActionsControllerWH::getUrl(
         $this->getNabidkaRow()->getID(),
         DetailNabidkyActionsControllerWH::ACTION_PRIDATPRILOHU,
      );
   }

   protected function appendTerminChecker() :void {
      $this->data['checkerMode'] = AvailabilityCheckerType::KALKULACE;
   }

   private function appendHlavicky() :void {
      if(!empty($hlavicky = OrganizaceObsahBloky::getForOrganizace($this->organizace->id_organizace, ObsahBlokyTypyEnum::HLAVICKA)))
         $this->data['hlavicky'] = $hlavicky;
   }

   private function appendZamitnutiModal() :void {
      if($this->nabidka->getStatus()->canZamitnout())
         $this->data['zamitnutiKalkulaceModal'] = ZamitnutiKalkulcaceModal::init()->btnToggleAttributes();
   }

   protected array $data;
   protected OrganizaceRow $organizace;
   protected BaseNabidkaRow $nabidka;
}