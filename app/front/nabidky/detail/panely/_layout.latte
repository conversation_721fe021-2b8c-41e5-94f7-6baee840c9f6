{varType bool $isMobil}
{varType app\system\model\nabidka\BaseNabidkaRow $nabidka}
{varType app\system\model\poptavky\BasePoptavkaRow $poptavka}

{varType app\system\model\organizace\zakaznici\OrganizaceZakaznikRow $zakaznik}
{varType app\system\model\organizace\jazyk\OrganizaceJazykRow[] $jazyky}
{varType app\system\model\organizace\meny\OrganizaceMenaRow[] $meny}
{varType app\system\model\organizace\prilohy\kalkulace\data\OrganizacePrilohaRow[] $dostupnePrilohy}
{varType Latte\Runtime\Html $prilohy}
{varType Latte\Runtime\Html $chatComponent}
{varType Latte\Runtime\Html $assignmentIcons}

{varType Latte\Runtime\Html $polozkyComponent}

{varType string $urlScenarComp}
{varType ?string $scenarOsa}
{varType string $eventType}
{varType array $scenarePairs}
{varType array $smlouvy}
{varType array $zakaznikModalAttrs}
{varType bool $canSchvalovat}
{varType bool $hasSchvalovani}
{varType string $introduction}
{varType string $pridatPrilohuUrl}
{varType string $nahledUrl}
{varType array $zamitnutiKalkulaceModal}
{varType string $sablonyCreateURL}
{varType string $sablonySaveURL}
{varType string $sablonyLoadURL}
{varType int $version}

{varType app\system\model\maily\bloky\data\OrganizaceObsahBlokRow[] $hlavicky}
{varType app\system\model\organizace\kalkulace\introduction\KalkulaceIntroductionSablonaRow[] $sablony}

{varType app\front\controllers\availability\AvailabilityCheckerType $checkerMode}

{varType Latte\Runtime\Html $localTaxComponent}

<div id="kalkulaceInfo" class="mb-7 mb-lg-4">
   <form method="post" id="editNabidka">
      <div class="row gap-3">
         <div class="col-lg-9 col-12">
            <div class="row">
               <div class="card p-3 border border-secondary">
                  <div class="row d-lg-flex align-content-lg-center mt-2">
                     <div class="col-lg-10 col-12">
                        <h1><input type="text" name="nazev" class="border-0 h1 col-12 js-form-change" maxlength="160"
                                   {if isset($nabidka->nazev)}value="{$nabidka->nazev}"{/if}></h1>
                     </div>
                     <div class="col-sm d-flex align-items-center justify-content-end">
                        <p class="badge bg-secondary rounded-pill text-white">{$nabidka->getStatus()->getTranslatedTitle()}
                        {if $nabidka->getStatus()->isZalozena()}
                           <i class="bi bi-question-circle ms-1 text-info" data-bs-toggle="tooltip" data-bs-title="{_'Zákazník aktuálně kalkulaci nemá k dispozici. Až bude kalkulace připravena, je potřeba mu jí odeslat.'}"></i>
                           {elseif $nabidka->getStatus()->isOdeslana()}
                           <i class="bi bi-question-circle ms-1 text-info" data-bs-toggle="tooltip" data-bs-title="{_'Počkejte, až zákazník kalkulaci příjme'}"></i>
                           {elseif $nabidka->getStatus()->isPrijata()}
                           <i class="bi bi-question-circle ms-1 text-info" data-bs-toggle="tooltip" data-bs-title="{_'Nyní můžete poslat smlouvu nebo vytvořit event bez smlouvy'}"></i>
                           {elseif $nabidka->getStatus()->isOdeslanaSmlouva()}
                           <i class="bi bi-question-circle ms-1 text-info" data-bs-toggle="tooltip" data-bs-title="{_'Podepište smlouvu a počkejte, až jí podepíše zákazník'}"></i>
                           {/if}
                        </p>
                     </div>
                  </div>
                  <div class="row">
                     <div class="row">
                        <div class="col-lg col-12 form-group">
                           <div class="input-group ">
                              <span class="input-group-text text-uppercase">{_'Začátek'}</span>
                              {if !$isMobil}
                                 <input n:attr="disabled: !$nabidka->getStatus()->isEditable()"
                                         name="datumStart" type="text" class="form-control datetimepicker-input date js-datepicker"
                                         id="editNabidkaDatumInputStart" autocomplete="off"
                                         value="{$nabidka->date_start?? ''|date: 'j.n.Y'}" data-target-input="nearest" data-toggle="datetimepicker" required>

                                 <input n:attr="disabled: !$nabidka->getStatus()->isEditable()"
                                         id="editNabidkaTimeInputStart" type="text" class="form-control text-end datetimepicker-input js-form-change date js-timepicker"
                                         autocomplete="off" pattern="{app\system\helpers\RegexPatterns::TIME}"
                                         name="timeStart" value="{$nabidka->time_start?->format('%h:%I') ?: ''}" data-target-input="nearest" data-toggle="datetimepicker" required>
                              {else}
                                 <input n:attr="disabled: !$nabidka->getStatus()->isEditable()"
                                         id="editNabidkaDatumInputStart" type="date" class="form-control datetimepicker-input"
                                         name="datumStart" autocomplete="off" value="{$nabidka->date_start?? ''|date: 'Y-m-d'}" required>
                                 <input n:attr="disabled: !$nabidka->getStatus()->isEditable()"
                                         id="editNabidkaTimeInputStart" type="time" class="form-control text-end"
                                         name="timeStart" autocomplete="off" value="{$nabidka->time_start?->format('%h:%I') ?: ''}" required>
                              {/if}

                           </div>
                        </div>
                        <div class="col-md-auto col-12 align-content-lg-end text-center">
                           <i class="lead bi bi-arrow-right d-none d-md-block"></i>
                           <i class="lead bi bi-arrow-down d-block d-md-none"></i>
                        </div>
                        <div class="col-lg col-12 form-group">
                           <div class="input-group">
                              <span class="input-group-text text-uppercase">{_'Konec'}</span>
                              {if !$isMobil}
                                 <input n:attr="disabled: !$nabidka->getStatus()->isEditable()"
                                         type="text" class="form-control datetimepicker-input date js-datepicker"
                                         id="editNabidkaDatumInputEnd" autocomplete="off"
                                         name="datumEnd" value="{$nabidka->date_end ?? ''|date: 'j.n.Y'}" data-target-input="nearest"
                                         data-toggle="datetimepicker">
                                 <input n:attr="disabled: !$nabidka->getStatus()->isEditable()"
                                         id="editNabidkaTimeInputStart" class="form-control text-end datetimepicker-input js-form-change date js-timepicker"
                                         type="text" autocomplete="off" pattern="{app\system\helpers\RegexPatterns::TIME}"
                                         name="timeEnd" value="{$nabidka->time_end?->format('%h:%I') ?: ''}" data-target-input="nearest" data-toggle="datetimepicker" required>
                              {else}
                                 <input n:attr="disabled: !$nabidka->getStatus()->isEditable()"
                                         id="novyEventDatumKonecInput" type="date" class="form-control datetimepicker-input"
                                         name="datumEnd" autocomplete="off" value="{$nabidka->date_end ?? ''|date: 'Y-m-d'}">
                                 <input n:attr="disabled: !$nabidka->getStatus()->isEditable()"
                                         id="time_konec" type="time" class="form-control text-end"
                                         name="timeEnd" autocomplete="off" value="{$nabidka->time_end?->format('%h:%I') ?: ''}" required>
                              {/if}

                           </div>
                        </div>
                     </div>
                     <div class="col-12 col-md-12 col-xl form-group mt-2">
                        {block mistnostBody}{/block}
                     </div>
                  </div>
               </div>

               <div class="row">
                  <div class="js-termin-checker-block p-0">
                     {$terminChecker ?? ''}
                  </div>
               </div>

               <div class="row mb-3 gap-2 gap-md-0">
                  <div class="col-md-6 col-xl-4" n:if="count($jazyky) > 1">
                     <div class="form-group">
                        <label for="id_jazyk">{_'Jazyk'}</label>
                        <select class="form-select js-form-change" id="id_jazyk"
                                name="id_jazyk" n:attr="disabled: !$nabidka->getStatus()->isEditable()">
                           {foreach $jazyky as $jazyk}
                              <option value="{$jazyk->id_jazyk}" n:attr="selected: $jazyk->id_jazyk === $nabidka->id_jazyk">{$jazyk->getJazykTitle()}</option>
                           {/foreach}
                        </select>
                     </div>
                  </div>

                  <div class="col-md-6 col-xl-4" n:if="count($meny) > 1">
                     <div class="row">
                        <div class="form-group">
                           <label for="id_mena">{_'Měna'}</label>
                           <select class="form-select js-form-change" id="id_mena"
                                   name="id_mena" n:attr="disabled: !$nabidka->getStatus()->isEditable() || !$nabidka->getProductsContainer()->isEmpty()">
                              {foreach $meny as $mena}
                                 <option value="{$mena->id_mena}" n:attr="selected: $mena->id_mena === $nabidka->id_mena">{$mena->getMena()->getTitle()}</option>
                              {/foreach}
                           </select>
                        </div>
                     </div>
                     <div class="row mt-2 js-mena-change-info" style="display: none">
                        <small class="text-danger">{_'Pro změnu měny nejdříve uložte kalkulaci'}</small>
                     </div>
                  </div>

                  <div class="col-md-6 col-xl-4 d-flex align-items-end mt-2 mt-lg-2 mt-xl-0">
                     <div class="form-check form-switch form-group">
                        <input type="checkbox" name="bez_dph" id="bez_dph"
                               class="form-check-input js-form-change" n:attr="checked: $nabidka->zobrazovat_bez_dph === 1">
                        <label for="bez_dph" class="form-check-label">{_'Zobrazovat ceny bez DPH'}<i class="bi bi-question-circle ms-1 text-info" data-bs-toggle="tooltip" data-bs-title="{_'Zákazník uvidí ceny v kalkulaci s nebo bez DPH'}"></i></label>
                     </div>
                  </div>
               </div>

               <div n:if="$hlavicky ?? false" class="row">
                  <div class="col-md-6 col-xl-4">
                     <div class="form-group">
                        <label for="id_hlavicka">{_'Hlavička'}</label>
                        <select name="id_hlavicka" id="id_hlavicka" class="form-select js-form-change">
                           <option value=0>{_'Žádná'}</option>
                           {foreach $hlavicky as $hlavicka}
                              <option value="{$hlavicka->id_blok}" n:attr="selected: $hlavicka->id_blok === $nabidka->id_hlavicka">{$hlavicka->nazev}</option>
                           {/foreach}
                        </select>
                     </div>
                  </div>
               </div>
            </div>

            {block mistoPoradaniBlock}{/block}
               {block pridatMistoPoradaniBlock}{/block}

            <div class="row card mt-4 p-3">
                  <div class="row">
                     <h3 class="lead mb-3">{_'Hosté'}</h3>
                  </div>
                  <div class="row">
                     <div class="col-auto form-group align-content-center mb-3 mb-lg-0">
                        <label>{_'Celkem:'}</label>
                        <input class="form-control form-control-sm js-pocet-hostu" type="number"
                               name="pocet_hostu_celkem" value="{$nabidka->getPocetHostu()}" disabled
                               style="max-width: 125px">
                     </div>
                     <div class="vr p-0 d-none d-lg-inline"></div>
                     <hr class="d-lg-none my-2">
                     <div class="col-lg col-12 form-group">
                        <div class="row d-flex justify-content-between">
                           {foreach $nabidka->getHosteContainer()->getVisiableSkupinyHostu() as $skupina}
                              <div class="col-auto my-1">
                                 <div class="form-group" style="width: 80px">
                                    <label n:attr="title: strlen($skupina->nazev_skup) > 18? $skupina->nazev_skup"
                                            n:class="$skupina->isNepocitatelna ? 'is-invalid text-danger'">
                                       <span n:if="$skupina->isNepocitatelna">
                                          <i class="bi bi-question-circle ms-1 text-danger"
                                             data-bs-toggle="tooltip"
                                             data-bs-title="{_'Kategorie je nastavena jako nepočítatelná skupina hostů v nastavení'}">
                                          </i>&nbsp;
                                       </span>
                                       <span>{$skupina->nazev_skup|truncate: 18}</span>:
                                    </label>
                                    <input type=number min="0" n:class="'form-control form-control-sm js-pocet-skupiny', $skupina->isNepocitatelna ? 'border-danger bg-gray-100'"
                                       pattern="[0-9]*" inputmode="numeric"
                                           name="pocet_hostu[{$skupina->id_skupiny}]"
                                           value="{$skupina->getPocet()}" n:attr="disabled: !$nabidka->getStatus()->isEditable()">
                                 </div>
                              </div>
                           {/foreach}
                           <div class="col-md-12 col-xl-5 mt-2 mt-lg-auto">
                              {$localTaxComponent}
                           </div>
                        </div>
                     </div>
                  </div>
            </div>

            {if $nabidka->getStatus()->isZamitnuta()}
               {var $info = $nabidka->getZamitnutiInfo()}

               <div n:if="$info" class="row card mt-4 p-3">
                  <div class="row">
                     <h3 class="lead mb-3">{_'Důvod zamítnutí'}</h3>
                  </div>
                  <div class="row">
                     <p>{$info->getTyp()->getTranslatedTitle()}</p>
                     <p n:if="$info->text">{$info->text}</p>
                  </div>
               </div>
            {/if}

            {if isset($smlouvy) && $nabidka->getStatus()->isPrijata()}
               <div class="row">
                  <div class="card p-3">
                     <div class="row">
                        <h3 class="lead">{_'Smlouva'}</h3>
                     </div>
                     <div class="row form-group">
                        <div class="col-md-9">
                           <label for="smlouva">{_'Vyberte šablonu pro smlouvu'}</label>
                           <select name="smlouva" id="smlouva" class="form-select">
                              <option value="0">{_'Vyberte šablonu'}</option>
                              {foreach $smlouvy as $id => $nazev}
                                 <option value="{$id}">{$nazev}</option>
                              {/foreach}
                           </select>
                        </div>
                        <div class="col-md d-flex align-content-center mt-2 mt-lg-0">
                           <button name="btnPrepareSmlova" class="btn btn-primary w-auto shaddow-hover js-prepare-smlouva"
                                   disabled><i class="bi bi-file-earmark-plus me-1"></i>{_'Připravit smlouvu'}</button>
                        </div>
                     </div>
                  </div>
               </div>
            {/if}
         </div>
         <div class="col">
            <div class="row">
               {$assignmentIcons}
            </div>

            <div class="row card p-2 bg-secondary form-group">
               <span class="text-white"><i class="bi bi-person-circle me-2"></i><strong>{$zakaznik->full_name}</strong>
                  <a href="#" n:attr="$zakaznikModalAttrs" class="ms-2 text-white text-end lead"><i
                             class="bi bi-arrow-up-right-circle hover-icon-arrow"></i></a>
               </span>
            </div>

            <div class="row d-flex align-content-center justify-content-center mb-2">
               <div class="col-auto align-content-center">
                  <i class="bi bi-file-earmark-arrow-up"></i>
               </div>
               <div class="col-auto align-content-center px-0">
                  <i class="bi bi-arrow-right lead text-gray-light"></i>
               </div>
               <div class="col-auto align-content-center">
                  <i n:class="'bi bi-receipt', $nabidka->getStatus()->isZalozena() ? 'text-primary' : 'text-secondary'"
                          data-bs-toggle="tooltip"
                          data-bs-title="Jakmile kalkulaci dokončíte, jednoduše jí zašlete zákazníkovi přímo tady v Qvampu."></i>
               </div>
               <div class="col-auto align-content-center px-0">
                  <i class="bi bi-arrow-right lead text-gray-light"></i>
               </div>
               <div class="col-auto align-content-center">
                  <i n:class="'bi bi-send-check', $nabidka->getStatus()->isOdeslana() ? 'text-primary' : 'text-secondary'"
                          data-bs-toggle="tooltip"
                          data-bs-title="{_'Zákazník má v tuto chvíli kalkulaci ke schválení, poté se můžete rozhodnout zda budete posílat elektronickou smlouvu nebo vytvoříte event bez smlouvy.'}"></i>
               </div>
               <div class="col-auto align-content-center px-0">
                  <i class="bi bi-arrow-right lead text-gray-light"></i>
               </div>
               <div class="col-auto align-content-center">
                  <i n:class="'bi bi-pencil-square', $nabidka->getStatus()->isPrijata() ? 'text-primary' : 'text-secondary'"
                          data-bs-toggle="tooltip"
                          data-bs-title="{_'Po odeslánní smlouvy a jejím podpisu z vaší strany počkejte až smlouvu podepíše také zákazník. Ihned po podpisu se automaticky vytvoří event.'}"></i>
               </div>
               <div class="col-auto align-content-center px-0">
                  <i class="bi bi-arrow-right lead text-gray-light"></i>
               </div>
               <div class="col-auto align-content-center">
                  <i n:class="'bi bi-calendar2-event', $nabidka->getStatus()->isHotova() ? 'text-primary' : 'text-secondary'"></i>
               </div>
            </div>
            <div class="row card mt-3 p-3">
               <h3 class="lead mb-3">{_'Další informace'}</h3>

               <div n:if="$nabidka->getStatus()->isEditable() || $nabidka->platnost" class="form-group row">
                  <label for="editPlatnostNabidky">{_'Platnost kalkulace do'}:</label>
                  <div n:if="$nabidka->getStatus()->isEditable()" n:attr="data-datepicker-min: $nabidka->platnost && new Dibi\DateTime() > $nabidka->platnost ? $nabidka->platnost->format('m/d/Y') : ''"
                          class="input-group date js-datepicker" id="platnost_nabidky_cont" data-target-input="nearest">
                     <input type="text" class="form-control datetimepicker-input js-form-change"
                            data-target="#platnost_nabidky_cont" name="platnost_nabidky"
                            id="editPlatnostNabidky"
                            {if $nabidka->platnost}value="{$nabidka->platnost|date:'j.n.Y'}"{/if}>
                     <div class="input-group-text" data-target="#platnost_nabidky_cont" data-toggle="datetimepicker">
                        <i class="bi bi-calendar2-event"></i>
                     </div>
                  </div>

                  <p n:if="!$nabidka->getStatus()->isEditable()">{$nabidka->platnost|date: 'j.n.Y'}</p>
               </div>
               {if $nabidka->getStatus()->canViewKlient()}
                  {if $nabidka->client_first_visit}
                     <div class="row mt-3">
                        <div class="col-12 form-group">
                           <label>{_'Poprvé zobrazeno'}</label>
                           <p>{$nabidka->client_first_visit|date:'j.n.Y H:i'}</p>
                        </div>
                        <div n:if="$nabidka->client_first_visit->getTimestamp() !== $nabidka->client_last_visit->getTimestamp()" class="col-md form-group">
                           <label>{_'Naposledy zobrazeno'}</label>
                           <p>{$nabidka->client_last_visit|date:'j.n.Y H:i'}</p>
                        </div>
                     </div>
                  {else}
                     <div class="row mt-3 form-group">
                        <label>{_'Poprvé zobrazeno'}</label>
                        <p>{_'Zatím nezobrazeno'}</p>
                     </div>
                  {/if}
               {/if}
               <div class="row form-group mt-3">
                  <label>{_'Typ'}</label>
                  <p>{$eventType}</p>
               </div>
               <div n:if="$nabidka->getPoptavka()->getZdroj()" class="row form-group">
                  <label>{_'Zdroj'}</label>
                  <p>{$nabidka->getPoptavka()->getZdroj()->nazev}</p>
               </div>
               <div class="row form-group">
                  <label>{_'ID poptávky / kalkulace'}</label>
                  <p>
                     <a href="{$nabidka->getPoptavka()->getDetailUrl()}">{$nabidka->getPoptavkaID()}</a>/{$nabidka->getID()}
                  </p>
               </div>
               <div class="row form-group" n:if="$nabidka->getEvent()">
                  <label>{_'Event'}:</label>
                  <a href="{$nabidka->getEvent()->getDetailUrl()}">{$nabidka->getEvent()->getID()}</a>
               </div>
            </div>
            <div class="row d-flex justify-content-center">
               <div class="btn-group w-auto">
                  <button class="btn btn-sm btn-outline-secondary shaddow-hover w-auto" name="btnDownloadPdfCalculation"><i class="bi bi-filetype-pdf me-1"></i>&nbsp;Export</button>
                  <a href="{$nahledUrl}" class="btn btn-sm btn-outline-secondary shaddow-hover w-auto" target="_blank" data-bs-toggle="tooltip"
                     data-bs-title="{_'Náhled kalkulace tak, jak jí vidí zákazník'}"><i class="bi bi-search me-1"></i>{_'Náhled'}</a>
               </div>
            </div>
         </div>

      </div>
      <div id="intro" class="js-introduction">
         <div class="my-3">
            <h2 class="text-center">{_'Úvodní text kalkulace'}</h2>
         </div>
         <div n:if="$nabidka->isEditovatelna()" class="btn-group gap-1 mb-1">
            <!-- nahravani -->
            <div class="dropdown">
               <button class="btn btn-sm btn-outline-secondary dropdown-toggle shaddow-hover"
                  id="sablona-intoroduction"
                  data-bs-auto-close="outside"
                  aria-expanded="false"
                  data-bs-toggle="dropdown"
                  data-bs-display="static"
               >
                  <i class="me-1 bi bi-upload"></i>{_'Nahrát šablonu'}
               </button>
               <div class="dropdown-menu" aria-labelledby="sablony-introduction-dropdown">
                  <div class="px-4 py-3">
                     <div class="row">
                        <div class="input-group">
                           <select id="js-introduction-select" class="form-select js-select-introduction" name="id_introduction_template">
                              {foreach $sablony as $sablona}
                                 <option value="{$sablona->id}">{$sablona->title}</option>
                              {/foreach}
                           </select>
                        </div>
                     </div>
                     <div class="row mt-1">
                        <div class="col">
                           <button type="button" id="load-introduction" class="btn btn-sm btn-outline-secondary w-auto">{_'Nahrát'}</button>
                        </div>
                     </div>
                  </div>
               </div>
            </div>

            <!-- ukladani -->
            <div class="dropdown">
               <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                       type="button"
                       data-bs-toggle="dropdown"
                       data-bs-auto-close="outside"
                       data-bs-display="static">
                  <i class="me-1 bi bi-save"></i>{_'Uložit šablonu'}
               </button>

               <ul class="dropdown-menu p-3" style="min-width:280px;">
                  <li>
                     <button type="button" id="ulozitExistujiciSablonu" class="dropdown-item px-2">
                        {_'Uložit existující'}
                     </button>
                  </li>
                  <li>
                     <button type="button" class="dropdown-item js-toggle-save px-2">{_'Uložit jako nový'}</button>
                  </li>

                  <div id="newTemplateForm" class="mt-2 px-2">
                     <label for="templateName" class="form-label mb-1">{_'Název šablony'}</label>
                     <div class="input-group">
                        <input type="text"
                               id="templateName"
                               class="form-control"
                               placeholder="{_'Zadejte název'}"
                               maxlength="80">
                        <button type="button" id="confirmSaveNew" class="btn btn-primary">{_'Uložit'}</button>
                     </div>
                     <button type="button" id="cancelSaveNew" class="btn btn-link mt-2 p-0">{_'Zrušit'}</button>
                  </div>
               </ul>
            </div>

         </div>
         <div class="row">
            <div class="form-group px-0 js-introduction-text">
               {$introduction|noescape}
            </div>
         </div>
         <div class="row mt-3 mb-5">
            <label>{_'Přílohy'}</label>
            <div class="form-group col-md-9 col-7">
               <select class="form-select js-priloha-template"
                       name="templatePriloha" n:attr="disabled: !$nabidka->getStatus()->isEditable()">
                  <option value="">{_'Vyberte přílohu'}</option>
                  {foreach $dostupnePrilohy as $dostupna}
                     <option value="{$dostupna->id}">{$dostupna->nazev}</option>
                  {/foreach}
               </select>
            </div>
            <div class="col">
               <a n:if="$nabidka->getStatus()->isEditable()"
                       class="btn btn-secondary shaddow-hover js-pridat-prilohu-btn w-auto" disabled>
                  <i class="bi bi-paperclip"></i>{_'Vložit přílohu'}
               </a>
            </div>
         </div>

         <div class="js-prilohy-blok my-2">
            {$prilohy}
         </div>
      </div>

      {include polozkyComponent}

      <div class="row my-5 border border-radius border-light-gray p-0">
         <div class="d-flex justify-content-between align-items-center mb-3 p-0">
            <small class="label-card lb-left px-2 py-1">{_'Platební scénář'}</small>
         </div>
         {if $nabidka->isEditovatelna()}
            <div class="px-lg-5 px-2 text-center text-lg-start">
               <h3 class="d-lg-inline-block col-lg-auto pe-2 lead">{_'Vyberte scénář'}</h3>
               <div class="d-lg-inline-block ml-1 mb-4 col-lg-4">
                  <select class="form-select js-platebni-scenar" name="selected_scenar">
                     {foreach $scenarePairs as $id_scenar => $nazev}
                        <option value="{$id_scenar}"
                                {if $id_scenar === $nabidka->selected_scenar}selected{/if}>{$nazev}</option>
                     {/foreach}
                  </select>
               </div>
            </div>
         {elseif !$nabidka->isEditovatelna() && !$nabidka->selected_scenar}
            {_'Bez platebního scénáře'}
         {/if}

         <div class="d-flex flex-wrap my-3 align-content-center justify-content-center justify-content-lg-start px-lg-5 px-2">
            <div class="js-scenar-container">
               {$scenarOsa|noescape}
            </div>
         </div>
      </div>

      <input type="hidden" name="suma" value="{$nabidka->getSuma()->getAmount()}">
      <input type="hidden" name="stav" id="stav_nabidka" value="{$nabidka->stav}">
      <input type="hidden" name="id_nabidka" id="id_nabidka" value="{$nabidka->getID()}">

      <div class="fixed-bottom pe-none d-flex justify-content-center">
         <div class="d-inline-flex flex-column flex-lg-row flex-wrap align-items-center bg-white border border-light-gray radius-card shaddow-light shaddow-hover my-2 p-2 pe-auto">

            {if $nabidka->getStatus()->canSmazat()}
               <div class="p-1">
                  <div class="btn-group dropup mx-1" role="group">
                     <button type="button" class="btn btn-outline-secondary dropdown-toggle text-nowrap shaddow-hover"
                             data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-x-circle me-1"></i>{_'Smazat'}
                     </button>
                     <ul class="dropdown-menu radius-card">
                        <li>
                           <input type="submit" name="btnSmazatNabidku" class="dropdown-item"
                                  value="{_'Smazat kalkulaci'}">
                        </li>
                        <li>
                           <input type="submit" name="btnSmazatNabidkuPoptavku" class="dropdown-item"
                                  value="{_'Smazat kalkulaci a zrušit poptávku'}">
                        </li>
                     </ul>
                  </div>
               </div>
            {/if}

            {if $nabidka->getStatus()->canStorno()}
               <div class="p-1">
                  <button type="submit"
                          name="btnStornoNabidka"
                          class="btn btn-outline-secondary text-nowrap shaddow-hover mx-1"
                          data-confirm="{_'Opravdu chcete stornovat kalkulaci'}">
                     {_'Storno'}
                  </button>
               </div>
            {/if}

            {if $nabidka->getStatus()->canZrusit()}
               <div class="p-1">
                  <div class="btn-group dropup mx-1" role="group">
                     <button type="button" class="btn btn-outline-secondary dropdown-toggle text-nowrap shaddow-hover"
                             data-bs-toggle="dropdown" aria-expanded="false">
                        {_'Zrušit'}
                     </button>
                     <ul class="dropdown-menu radius-card">
                        <li>
                           <input type="submit" name="btnZrusitNabidku" class="dropdown-item"
                                  value="{_'Zrušit kalkulaci'}">
                        </li>
                        <li>
                           <input type="submit" name="btnZrusitNabidkuPoptavku" class="dropdown-item"
                                  value="{_'Zrušit kalkulaci a poptávku'}">
                        </li>
                     </ul>
                  </div>
               </div>
            {/if}

            {if ($nabidka->getStatus()->isPrijata() && $poptavka->getStav()->canSendSmlouvu() && $nabidka->getSmlouva() === null)}
               <div class="p-1">
                  <button type="submit"
                          name="btnVytvoritBezSmlouvy"
                          class="btn btn-secondary text-nowrap shaddow-hover mx-1"
                          data-confirm="{_'Opravdu chcete vytvořit event bez smlouvy?'}">
                     {_'Vytvořit event bez smlouvy'}
                  </button>
               </div>
            {/if}

            {if $nabidka->getStatus()->canZamitnout()}
               <div class="p-1">
                  <div class="btn-group mx-1" role="group">

                     <button type="button" name="btnZamitnoutNabidku" n:attr="$zamitnutiKalkulaceModal"
                             class="btn btn-outline-secondary text-nowrap shaddow-hover">
                        {_'Zamítnout za zákazníka'}
                     </button>

                     <button n:if="$nabidka->getStatus()->isOdeslana()" type="submit" name="btnPrijmoutNabidku"
                             class="btn btn-outline-primary text-nowrap shaddow-hover">
                        {_'Přijmout za zákazníka'}
                     </button>
                  </div>
               </div>
            {/if}

            {if $nabidka->getStatus()->waitingSchvaleni()}
               {if $canSchvalovat}
                  <div class="p-1">
                     <div class="btn-group mx-1" role="group">
                        <button type="submit"
                                name="btnSchvalitNabidku"
                                class="btn btn-outline-secondary text-nowrap shaddow-hover"
                                value="korekce">
                           {_'Zpět ke korekci'}
                        </button>
                        <button type="submit"
                                name="btnSchvalitNabidku"
                                class="btn btn-outline-primary text-nowrap shaddow-hover"
                                value="schvalit">
                           <i class="bi bi-send-check me-1"></i>{_'Schválit'}
                        </button>
                     </div>
                  </div>
               {else}
                  <div class="p-1">
                     <button type="button" class="btn btn-outline-secondary text-nowrap shaddow-hover" disabled>
                        {_'Čeká na schválení'}
                     </button>
                  </div>
               {/if}
            {/if}

            <div class="p-1">
               <button n:if="$nabidka->getStatus()->isZalozena() || $nabidka->getStatus()->isKorekce()"
                       type="submit"
                       name="btnHotovaNabidka"
                       class="btn btn-outline-primary text-nowrap shaddow-hover mx-1"
                       data-confirm="{_'Opravdu chcete odeslat kalkulaci zákazníkovi?'}">
                  <i class="bi bi-send-check me-1"></i>
                  {if !$hasSchvalovani || ($hasSchvalovani && $canSchvalovat)}
                     {_'Odeslat'}
                  {else}
                     {_'Odeslat ke schválení'}
                  {/if}
               </button>
            </div>

            <div class="p-1">
               <button n:if="$nabidka->isEditovatelna()"
                       type="submit"
                       name="btnUlozitNabidku"
                       class="btn btn-primary text-nowrap shaddow-hover mx-1"
                       disabled>
                  <i class="bi bi-save me-1"></i>{_'Uložit'}
               </button>
            </div>
         </div>
      </div>
   </form>
   <div class="row my-5">
      {$chatComponent}
   </div>
</div>

{define polozkyComponent}
   {$polozkyComponent}
{/define}

<script>

    $(function () {
        const body = $('body');

        const form = $('form#editNabidka');
        const saveBtn = form.find('button[name="btnUlozitNabidku"]');
        const inputDateEnd = form.find('input[name=datumEnd]');

       form.validate({
          rules: {
             'datumStart': {
                date: false,
                dateBefore: inputDateEnd[0],
             },
             'datumEnd': {
                date: false,
             },
             'timeStart': {
                date: false,
             },
             'timeEnd': {
                date: false,
             }
          },
       });

       const checkerContainer = form.find('div.js-termin-checker-block');

       window.qvampSystem.availabilityChecker.initContainer({
          container: checkerContainer[0],
          dateStart: $('input#editNabidkaDatumInputStart')[0],
          dateEnd: $('input#editNabidkaDatumInputEnd')[0],
          entity: {
             entityID: Number({$nabidka->getID()}),
             mode: String({$checkerMode->value}),
          },
          beforeRefresh: () => {
             saveBtn.prop('disabled', true);
          },
          afterRefresh: () => {
             saveBtn.prop('disabled', null);
          }
       });

        form.on('change', '.js-form-change', undisableSaveBtn)
            .on('change::qvamp.datepicker', '.js-form-change', undisableSaveBtn)
            .on('change', 'textarea[name="introduction"]', undisableSaveBtn);

       form[0].addEventListener('qvamp::prirazeni.change', function(e) {
          undisableSaveBtn();
       });

        function undisableSaveBtn() {
            saveBtn.prop('disabled', null);
        }

        body
         .on('change', '.js-select-osoby', function() {
            saveBtn.prop('disabled', null);
         })
          .on('change', '.js-priloha-template', function(e) {
              $('.js-pridat-prilohu-btn').prop('disabled', ($(this).val() <= 0))
          })
            .on('click', '.js-pridat-prilohu-btn', function(e) {
                let id_template = $('.js-priloha-template').val();

                if(id_template > 0) {
                    ajaxHandler.post( {$pridatPrilohuUrl} , { id_template }, function (response) {
                        $('.js-prilohy-blok').html(response['prilohy']);
                        $('.js-priloha-template option[value= ' + id_template + ']').remove();
                    });
                }
            })
            .on('change', '.js-platebni-scenar', function() {
                const item = $(this);
                item.prop('disabled', true);
                ajaxHandler.post({$urlScenarComp}, { id_scenar: item.val(), id_nabidka:  {$nabidka->getID()}}, function(response) {
                    $('.js-scenar-container').html(response['component']);
                    item.prop('disabled', false);
                });
            }).on('change', 'select[name=smlouva]', function() {
            $('.js-prepare-smlouva').prop('disabled', $(this).val() === '0');
        });

        body.on('change', 'input.js-pocet-skupiny', function() {
            const arrInputs = $(this).closest('form').find('input.js-pocet-skupiny');
            let pocet = 0;

            arrInputs.each((i, el) => {
                pocet = pocet + Number(el.value);
            });

            $('input.js-pocet-hostu').val(pocet);
            undisableSaveBtn();
        });

       {if count($meny) > 1}
        body.on('change', 'select[name="id_mena"]', function() {
            $('.js-mena-change-info').show();
        })
       {/if}
    });
</script>

<script n:if="$nabidka->isEditovatelna()">
   $(function() {
      const introductionSelect = $('#js-introduction-select');
      const el = document.querySelector(`textarea[name="introduction"]`);
      const editor = tinymce.get(el.id);
      const saveExistingBtn = $('#ulozitExistujiciSablonu');
      const saveAsNewBtn = $('#confirmSaveNew');
      const loadIntroductionBtn = $('#load-introduction');
      const saveToggleBtn = $('.js-toggle-save');
      const saveAsNewBlock = $('#newTemplateForm');
      const inputTemplateName = $('#templateName');
      const cancelSaveBtn = $('#cancelSaveNew');

      saveExistingBtn.prop('disabled', true);
      saveAsNewBtn.prop('disabled', true);
      saveAsNewBlock.prop('hidden', true);

      introductionSelect
         .select2({
            placeholder: "{_'Vyber šablonu'}",
            width: '400px',
         });

      cancelSaveBtn.on('click', function() {
         saveAsNewBlock.prop('hidden', true);
      });

      loadIntroductionBtn.on('click', function() {
         let id_introduction = introductionSelect.val();
         loadIntroductionBtn.prop('disabled', true);

         if(id_introduction > 0) {
            promiseHandler.post(
                    {$sablonyLoadURL},
               {
                  id_template: id_introduction,
               },
            ).then(
               (r) => {
                  if(!r.success){
                     return;
                  }

                  editor.setContent(r.text);
                  editor.save();
                  saveExistingBtn.prop('disabled', false);
                  loadIntroductionBtn.prop('disabled', false);
               },
            );
         }
      })

      saveAsNewBtn.on('click', async function(e) {
         e.target.disabled = true;
         const textToSave = editor.getContent();

         if(textToSave.trim() === ''){
            e.target.disabled = false;
            toastr.error("{_'Nelze uložit šablonu bez textu'}");
            return;
         }

         const r = await promiseHandler.post({$sablonyCreateURL}, {
            version: {$version},
            content: editor.getContent(),
            name: inputTemplateName.val(),
         });

         e.target.disabled = false;

         if(!r.success)
            return;

         editor.setContent(r.text);
         editor.save();

         if(!introductionSelect.find('option[value="' + r.id_template + '"]').length) {
            const newOption = new Option(r.title, r.id_template, true, true);
            introductionSelect.append(newOption).trigger('change');
         } else {
            introductionSelect.val(r.id_template).trigger('change');
         }

         saveAsNewBlock.prop('hidden', true);
         inputTemplateName.val('').trigger('input').trigger('change');
         saveExistingBtn.prop('disabled', false);
      })

      saveExistingBtn.on('click', async (e) => {
         const id_introduction = introductionSelect.val();
         e.target.disabled = true;

         await promiseHandler.post({$sablonySaveURL}, {
               content: editor.getContent(),
               id_template: id_introduction,
         });

         e.target.disabled = false;
      })

      saveToggleBtn.on('click', function() {
         saveAsNewBlock.prop('hidden', false);
      });

      inputTemplateName.on('input change', syncDisabled);
      syncDisabled();

      function syncDisabled() {
         saveAsNewBtn.prop('disabled', !inputTemplateName.val().trim());
      }
   });
</script>