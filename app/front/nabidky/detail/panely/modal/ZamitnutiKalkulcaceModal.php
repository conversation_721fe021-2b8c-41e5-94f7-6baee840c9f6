<?php namespace app\front\nabidky\detail\panely\modal;

use app\System;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\nabidka\BaseNabidkaRow;
use app\system\model\nabidka\zamitnuti\ZamitnutiKalkulaceTyp;
use app\system\model\nabidka\zamitnuti\ZamitnutiKalkulaceZakaznikemPostEvent;
use app\system\model\translator\TextVariablesFactory;
use app\system\modul\modal\Modal;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 20.05.2025 */
class ZamitnutiKalkulcaceModal extends Modal
{

   public function getTitleName() :TextVariablesFactory {
      return System::getTranslator()->layoutTranslate('Zamítnutí kalkulace $1', $this->nabidka->nazev ?: $this->nabidka->getID());
   }

   protected function preparePostListeners() :void {
      $this->isset('btnZamitnoutKalkulaci', function($post) {
         if((new ZamitnutiKalkulaceZakaznikemPostEvent($this->nabidka, $post))->setNotification(false)->call()->getSuccess())
            FlashMessages::setSuccess('Kalkulace byla zamítnuta zákazníkem!');
      });
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'typyZamitnuti' => ZamitnutiKalkulaceTyp::cases(),
      ]);
   }

   public function setKalkulaceRow(BaseNabidkaRow $nabidka) :static {
      $this->nabidka = $nabidka;
      return $this;
   }

   protected BaseNabidkaRow $nabidka;
}