{varType bool $showCeny}
{varType bool $showCenyDph}
{varType bool $showPolozkyBaliku}

{varType app\system\model\polozky\PolozkaBalikuRow[][] $polozkyBalik}
{varType app\system\model\entity\polozky\container\ProductsContainer $productsContainer}

<style>
   .items-section h3 {
      font-size: 1.125rem;
      margin-bottom: 0.5rem;
      color: #3A3A3A;
   }
   .items-table {
      width: 100%;
      border-collapse: collapse;
      margin: 0;
   }
   .items-table th,
   .items-table td {
      padding: 6px 8px;
      border-bottom: 1px solid #dee2e6;
      font-size: 0.875rem;
   }
   .items-table thead th {
      border-bottom: 2px solid #dee2e6;
      text-align: left;
      color: #a9a9a9;
      font-weight: 600;
      font-size: 0.575rem;
   }
   .items-table tbody tr:nth-child(odd) {
      background: #fafafa;
   }
   .text-end {
      text-align: right;
   }
   .breakdown-row td {
      padding-top: 4px;
      padding-bottom: 4px;
      padding-left: 10px;
      font-size: 0.8125rem;
      color: #6c757d;
   }

   .price-stack {
      line-height: 1.15;
   }
   .price-old {
      display: block;
      text-decoration: line-through;
      color: #a9a9a9;
      font-size: 0.75rem;
      white-space: nowrap;
   }
   .price-new {
      display: block;
      font-weight: 600;
      white-space: nowrap;
   }
   .discount-badge {
      display: inline-block;
      font-size: 0.65rem;
      font-weight: 600;
      color: #dc3545;
      margin-top: 2px;
      white-space: nowrap;
   }


   /* ---- totals (sum table) ---- */
   .totals-wrap {
      width: 340px;
      margin-left: auto;
   }
   /* zarovnání vpravo */
   .totals-table {
      width: 100%;
      border-collapse: collapse;
   }
   .totals-table td {
      padding: 6px 8px;
      font-size: 0.875rem;
   }
   .totals-table .label {
      color: #6c757d;
   }
   .totals-table .value {
      text-align: right;
      white-space: nowrap;
   }
   .totals-table .sum-row td {
      border-top: 2px solid #dee2e6;
      font-weight: 600;
   }
</style>

<div class="items-section mt-3">
   <span class="fw-bold fs-5"><i class="bi bi-list-task me-2 text-primary"></i>{_'Položky'}</span>
   <hr class="mt-1 mb-1">
   {if !empty($productsContainer->getProductItems())}
      <table class="items-table">
         <thead>
         <tr>
            <th>{_'Název'}</th>
            <th>{_'Kategorie'}</th>
            <th class="text-end">{_'Množství'}</th>
            {if $showCeny}
               {if $showCenyDph}
                  <th class="text-end">{_'Cena'}</th>
                  <th class="text-end">{_'DPH'}</th>
               {/if}
               <th class="text-end">{_'Cena s DPH'}</th>
            {/if}
         </tr>
         </thead>
         <tbody>
         {foreach $productsContainer->getProductItems() as $product}
            {var $ceny = $product->getProductPrice()}
            <tr>
               <td>{$product->productData->name}</td>
               <td>{$product->productPriceCategoryName}</td>
               <td class="text-end">{$product->productPrice->amount}</td>
               {if $showCeny}
                  {if $showCenyDph}
                     <td class="text-end">
                        {if $ceny->discount > 0 && isset($ceny->sleva_celkem)}
                           <div class="price-stack">
                              <span class="price-old">{$ceny->cena_celkem->add($ceny->sleva_celkem)|price}</span>
                              <span class="price-new">{$ceny->cena_celkem|price}</span>
                              <span class="discount-badge">-{$ceny->discount}%</span>
                           </div>
                        {else}
                           {$ceny->cena_celkem|price}
                        {/if}
                     </td>

                     <td class="text-end">{$ceny->vat}&nbsp;%</td>
                  {/if}

                  <td class="text-end">
                     {if $ceny->discount > 0}
                        <div class="price-stack">
                           <span class="price-old">{$ceny->cena_celkem_dph->add($ceny->sleva_celkem_dph)|price}</span>
                           <span class="price-new">{$ceny->cena_celkem_dph|price}</span>
                           <span class="discount-badge">-{$ceny->discount}%</span>
                        </div>
                     {else}
                        {$ceny->cena_celkem_dph|price}
                     {/if}
                  </td>
               {/if}
            </tr>
            {if $showPolozkyBaliku && $product->getProductType()->isBalik()}
               {foreach $polozkyBalik[$product->productData->productId] as $polozka}
                  <tr class="breakdown-row">
                     <td><i class="bi bi-arrow-return-right me-2 text-primary"></i>{$polozka->nazev}</td>
                     <td></td>
                     <td class="text-end">{$polozka->mnozstvi * $product->productPrice->amount}</td>
                     {if $showCenyDph}
                        <td></td>
                        <td></td>
                     {/if}
                     <td></td>
                  </tr>
               {/foreach}
            {/if}
         {/foreach}
         </tbody>
      </table>
   {else}
      <p><strong>{_'Neobsahuje žádné položky'}</strong></p>
   {/if}
</div>
<hr>

{if $showCeny}
   {var $ceny = $productsContainer->getTotalPrice()}

   <div class="totals-wrap">
      <table class="totals-table" align="right">
         <tbody>
         {if $showCenyDph}
            <tr>
               <td class="label">{_'Celkem bez DPH'}</td>
               <td class="value">{$ceny->celkem_bez_dph|price}</td>
            </tr>

            {if !empty($ceny->vat_rates)}
               {foreach $ceny->vat_rates as $rate => $value}
                  <tr n:if="!$value->isZero()">
                     <td class="label">{_'DPH'} {$rate}%</td>
                     <td class="value">{$value|price}</td>
                  </tr>
               {/foreach}
            {/if}
         {/if}

            {if !$ceny->sleva->isZero()}
            <tr>
               <td class="label">{_'Sleva'}</td>
               <td class="value">{$ceny->sleva|price}</td>
            </tr>
         {/if}

         <tr class="sum-row">
            <td class="label">{_'Celkem s DPH'}</td>
            <td class="value">{$ceny->celkem|price}</td>
         </tr>
         </tbody>
      </table>
   </div>
{/if}