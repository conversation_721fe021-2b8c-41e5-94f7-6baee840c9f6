<?php namespace app\front\tisk\variabilni\bloky\polozky\products;

use app\front\tisk\variabilni\bloky\BaseTiskBlok;
use app\front\tisk\variabilni\bloky\IBlokSetting;
use app\front\tisk\variabilni\bloky\polozky\products\settings\ceny\CenyProduktuSetting;
use app\front\tisk\variabilni\bloky\polozky\products\settings\ceny\DphCenyProduktuSetting;
use app\front\tisk\variabilni\bloky\polozky\products\settings\rozpad\RozpisPolozekSetting;
use app\system\component\Templater;
use app\system\model\entity\polozky\ProductType;
use app\system\model\event\BaseEventRow;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\polozky\PolozkaBalikuRow;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 24.06.2025 */
class EventProduktyBlok extends BaseTiskBlok
{

   public function getTitle() :string {
      return '<PERSON><PERSON><PERSON> a balíčky';
   }

   public function getSettingsClassesNames() :array {
      return array_merge(
         [
            CenyProduktuSetting::class,
            DphCenyProduktuSetting::class,
            RozpisPolozekSetting::class,
         ],
         parent::getSettingsClassesNames()
      );
   }

   /** @param IBlokSetting[] $activeSettings */
   public function render(BaseEventRow $eventRow, array $activeSettings = []) :Html {
      $showCeny = false;
      $showDph = false;
      $showPolozkyBaliku = false;
      $polozkyBalik = [];

      $container = $eventRow->getProductsContainer();

      foreach($activeSettings as $setting){
         if($setting instanceof CenyProduktuSetting)
            $showCeny = true;

         if($setting instanceof DphCenyProduktuSetting)
            $showDph = true;

         if($setting instanceof RozpisPolozekSetting){
            $showPolozkyBaliku = true;

            $balikContainer = $container->getContainer(ProductType::BALIK);

            if($balikContainer->isEmpty())
               continue;

            $balikyID = array_column($balikContainer->getProductsRows(), 'id_balik');

            $polozkyBalik = PolozkaBalikuRow::getForBaliky(
               $balikyID,
               $eventRow->id_jazyk,
               OrganizaceJazyky::getPrimary($eventRow->getIdOrganizace())
            );
         }
      }


      return new Html(Templater::prepare(__DIR__ . '/template.latte', [
         'showCeny' => $showCeny,
         'showCenyDph' => $showDph,
         'showPolozkyBaliku' => $showPolozkyBaliku,
         'polozkyBalik' => $polozkyBalik,
         'productsContainer' => $container,
      ]));
   }

   private function preparePolozkyBaliku() {

   }
}