<?php namespace app\front\aktivity;

use app\front\controllers\aktivity\SeznamAktivitTable;
use app\front\eventy\detail\modaly\aktivity\EditAktivityModal;
use app\system\lay\dashboard\DashboardLayout;
use app\system\traits\PostListener;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 06.12.2024 */
class PrehledAktivitPage extends DashboardLayout
{

   use PostListener;

   function getPageName() :string {
      return 'Aktivity';
   }

   protected function preparePostListeners() :void {
      EditAktivityModal::init()->checkPost();
   }

   protected function prepareFromString() :?string {
      $this->addModal(EditAktivityModal::init());

      return (new SeznamAktivitTable());
   }

}