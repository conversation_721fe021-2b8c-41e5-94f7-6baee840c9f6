<?php namespace app\front\organizace\modul\detail\zakaznici;

use app\front\controllers\eventy\DetailEventuController;
use app\front\zakaznici\modal\edit\EditZakaznikaModal;
use app\system\application\FrontApplicationEnvironment;
use app\system\helpers\HtmlBuilder;
use app\system\model\event\BaseEventRow;
use app\system\model\event\VendorEventy;
use app\system\model\event\VenueEventy;
use app\system\model\organizace\zakaznici\firma\OrganizaceZakaznikFirma;
use app\system\model\organizace\zakaznici\firma\OrganizaceZakaznikFirmaRow;
use app\system\model\organizace\zakaznici\OrganizaceZakaznici;
use app\system\model\organizace\zakaznici\OrganizaceZakaznikRow;
use app\system\model\organizace\zakaznici\tagy\OrganizaceZakazniciTagRow;
use app\system\model\organizace\zakaznici\tagy\OrganizaceZakazniciTagy;
use app\system\model\organizace\zakaznici\tagy\prirazeni\ZakazniciPrirazeneTagy;
use app\system\model\organizace\zakaznici\telefon\OrganizaceZakazniciTelefon;
use app\system\model\organizace\zakaznici\telefon\OrganizaceZakazniciTelefonRow;
use app\system\table\DynamicTable;
use app\system\table\DynamicTableColumn;
use Dibi\Fluent;

/** Created by Kryštof Czyź. Date: 01.04.2022 */
class ZakazniciMistaTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addDataPreparator(function($rows) {
         $this->telefony = OrganizaceZakazniciTelefon::getForMultiple(array_column($rows, 'id'));
         $this->firmy = OrganizaceZakaznikFirma::getMultipleZakaznikAssoc(array_column($rows, 'id'));
         $this->tagy = OrganizaceZakazniciTagy::getAsignedForZakaznikMultiple(array_column($rows, 'id'));

         $this->eventy = FrontApplicationEnvironment::get()->versionType->isVenue()
            ? VenueEventy::getNejblizsiForZakaznikMultiple(array_column($rows, 'id'))
            : VendorEventy::getNejblizsiForZakaznikMultiple(array_column($rows, 'id'));
      });

      $this->appendJmeno();
      $this->appendFirma();
      $this->appendTagy();

      $this->addText('email', 'E-mail');

      $this->appendTelefon();
      $this->appendNadchazejiciEventy();

      $this->addDate('created', 'Vytvořeno')
         ->setWidth(DynamicTableColumn::WIDTH_10);

      $this->appendDetailBtn();
   }

   public function setIdMista(int $id_mista) :static {
      $this->addParametrQuery('misto', $id_mista);
      return $this;
   }

   function getQuery() :Fluent {
      return OrganizaceZakaznici::findBy(id_mista: $_GET['misto']);
   }

   private function appendJmeno() :void {
      $this->addText('full_name', 'Jméno')
         ->setFormatter(function($value, $item) {
            return HtmlBuilder::buildElement('a', array_merge([
               'href' => '#'
            ], EditZakaznikaModal::getShowAttributes($item['id'])))
               ->setHtml($value)->get();
         })
         ->setDefaultOrder(DynamicTableColumn::SORT_ASC);
   }

   private function appendFirma() :void {
      $this->addText('id2.id', 'Firma')
         ->setFormatter(function($value) {
            if(!isset($this->firmy[$value]))
               return '';

            return $this->firmy[$value]->nazev;
         })->setSearchCallback(function(Fluent $query, $search) {
            $query
               ->join(OrganizaceZakaznikFirma::TABLE, 'ozf')->on('mz.id = ozf.id_zakaznik')
               ->where('ozf.nazev LIKE %~like~', $search);
         });
   }

   private function appendTagy() :void {
      $tagyOrganizace = OrganizaceZakazniciTagy::getNazvyForOrganizace(FrontApplicationEnvironment::get()->id_organizace);

      if(empty($tagyOrganizace))
         return;

      $this->addSelect('id3.id', 'Tagy', $tagyOrganizace)
         ->setFormatter(function($value) {
            if(isset($this->tagy[$value])){
               $badges = [];
               foreach($this->tagy[$value] as $id => $tag){
                  $badges[$id] = HtmlBuilder::buildElement('a', array_merge([
                     'class' => 'badge',
                     'style' => sprintf('background-color: %s; color: #000; text-decoration: none; cursor: default;', $tag->getHEXCode()),
                  ]))->setHtml($tag->nazev)->get();
               }
               return implode(' ', $badges);
            }
            else
               return '';
         })->setSearchCallback(function(Fluent $query, $search) {
            $query
               ->join(ZakazniciPrirazeneTagy::TABLE, 'zpp')->on('zpp.id_zakaznik = mz.id')
               ->where('zpp.id_tag = %s', $search);
         });
   }

   private function appendTelefon() :void {
      $this->addText('id4.id', 'Telefon')
         ->setFormatter(function($value, OrganizaceZakaznikRow $row) {
            $telCont = [];

            foreach($this->telefony[$row->id] as $telefonRow)
               $telCont[] = $telefonRow->__toString();

            return implode(',<br>', $telCont);
         })->setSearchCallback(function(Fluent $query, $search) {
            $query
               ->join(OrganizaceZakazniciTelefon::TABLE, 'ozt')->on('mz.id = ozt.id_zakaznik')
               ->where('ozt.telefon LIKE %like~ OR CONCAT(ozt.prefix, ozt.telefon) LIKE %~like~', $search, $search)
               ->groupBy('mz.id, ozt.id_zakaznik');
         })->setOrderable(false);
   }

   private function appendNadchazejiciEventy() :void {
      $this->addText('id5.id', 'Nejbližší eventy')
         ->setSearchable(false)
         ->setOrderable(false)
         ->setWidth(DynamicTableColumn::WIDTH_10)
         ->setFormatter(function($value) {
            if(!isset($this->eventy[$value]))
               return '';

            $links = [];

            foreach($this->eventy[$value] as $event)
               $links[] = HtmlBuilder::buildElement('a', array_merge([
                  'href' => DetailEventuController::getUrl($event->getID()),
               ]))->setHtml($event->getID())->get();

            return implode(', ', $links);
         });
   }

   private function appendDetailBtn() :void {
      $this->addText('id6.id',' ')
         ->setFormatter(function($value) {
            return HtmlBuilder::buildElement('a', array_merge([
               'href' => '#'
            ], EditZakaznikaModal::getShowAttributes($value)))
               ->setHtml('<span class="text-secondary"><i class="bi bi-arrow-right-circle hover-icon-arrow"></i></span>')->get();
         })->setOrderable(false)->setSearchable(false);
   }

   /** @var OrganizaceZakazniciTelefonRow[][] */
   private array $telefony;

   /** @var OrganizaceZakaznikFirmaRow[] */
   private array $firmy;

   /** @var array<int, array<int, OrganizaceZakazniciTagRow>> */
   private array $tagy;

   /** @var BaseEventRow[][] */
   private array $eventy;
}