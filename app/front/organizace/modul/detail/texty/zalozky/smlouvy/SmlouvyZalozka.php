<?php namespace app\front\organizace\modul\detail\texty\zalozky\smlouvy;

use app\front\organizace\modul\detail\texty\zalozky\smlouvy\modals\nova\NovaSmlouvaModal;
use app\front\organizace\modul\detail\texty\zalozky\smlouvy\table\SmlouvyMistaTable;
use app\system\component\Templater;
use app\system\model\organizace\Misto;
use app\system\modul\zalozky\Zalozka;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 02.05.2023 */
class SmlouvyZalozka extends Zalozka
{

   public function getTitle() :string {
      return 'Smlouvy';
   }

   public function setMisto(Misto $misto) :static {
      $this->misto = $misto;
      return $this;
   }

   protected function prepareTemplater(Templater $templater) :void {
      $templater->addData([
         'tableFormulare' => new Html((new SmlouvyMistaTable())->setIdMista($this->misto->getMistoRow()->id_organizace)),
         'attrAddFormularModal' => NovaSmlouvaModal::init()->btnToggleAttributes(),
      ]);
   }

   private Misto $misto;
}