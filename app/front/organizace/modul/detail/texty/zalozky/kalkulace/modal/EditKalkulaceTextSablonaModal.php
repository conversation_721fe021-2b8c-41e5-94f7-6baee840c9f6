<?php namespace app\front\organizace\modul\detail\texty\zalozky\kalkulace\modal;

use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\organizace\kalkulace\introduction\KalkulaceIntroductionSablony;
use app\system\model\organizace\kalkulace\introduction\SaveKalkulaceIntroductionSablonaPostEvent;
use app\system\modul\modal\AjaxModal;
use app\system\tiny\TinyEditor;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON>. Date: 25.08.2025 */
class EditKalkulaceTextSablonaModal extends AjaxModal
{

   public function getTitleName(): string {
      return 'Editace šablony';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUlozitSablonuTextu', function($post) {
         if(
            !($sablona = KalkulaceIntroductionSablony::get($post['id_sablona']))
            || $sablona->id_organizace !== FrontApplicationEnvironment::get()->id_organizace
         ){
            throw FlashException::create('Tuto šablonu nelze uložit');
         }

         (new SaveKalkulaceIntroductionSablonaPostEvent(
            $sablona,
            $post,
         ))->call();

         FlashMessages::setSuccess('Šablona $1 byla uložena', $sablona->title);
      });

      $this->isset('btnSmazatSablonu', function($post) {
         if(
            !($sablona = KalkulaceIntroductionSablony::get($post['id_sablona']))
            || $sablona->id_organizace !== FrontApplicationEnvironment::get()->id_organizace
         ){
            throw FlashException::create('Tuto šablonu nelze smazat');
         }

         KalkulaceIntroductionSablony::delete($post['id_sablona']);

         FlashMessages::setSuccess('Šablona $1 byla smazána', $sablona->title);
      });
   }

   public function prepareModal(Templater $templater) :void {
      $sablonaRow = KalkulaceIntroductionSablony::get((int)($_POST['slug']));

      $templater->addData([
         'jazyky' => OrganizaceJazyky::getPairs($sablonaRow->id_organizace),
         'sablonaRow' => $sablonaRow,
         'textEditor' => new Html(TinyEditor::init()
            ->setInputName('introduction')
            ->setPlaceholder('Zadejte text kalkulace')
            ->setContent($sablonaRow->text ?? null)
            ->render()),
      ]);
   }

   protected int $organizaceID;
}