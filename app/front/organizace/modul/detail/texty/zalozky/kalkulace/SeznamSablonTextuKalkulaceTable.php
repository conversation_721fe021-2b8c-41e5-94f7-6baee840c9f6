<?php namespace app\front\organizace\modul\detail\texty\zalozky\kalkulace;

use app\front\organizace\modul\detail\texty\zalozky\kalkulace\modal\EditKalkulaceTextSablonaModal;
use app\System;
use app\system\helpers\HtmlBuilder;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\organizace\kalkulace\introduction\KalkulaceIntroductionSablony;
use app\system\model\translator\Jazyky;
use app\system\table\DynamicTable;
use Dibi\Fluent;

/** Created by Filip Pavlas. Date: 22.08.2025 */
class SeznamSablonTextuKalkulaceTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addText('id', 'ID')->setWidth(5);
      $this->addText('title', 'Název');

      if(count($jazyky = OrganizaceJazyky::getAllJazyky($this->getOrganizaceID())) > 1)
         $this->addSelect('id_jazyk', 'Jazyk', Jazyky::getTitles($jazyky));

      $this->addText('id.id', ' ')
         ->setOrderable(false)
         ->setSearchable(false)
         ->setFormatter(function ($value) {
            return HtmlBuilder::buildElement('a', array_merge([
               'href' => '#'
            ], EditKalkulaceTextSablonaModal::getShowAttributes($value)))->setHtml(System::getTranslator()->translate('Detail'))->get();
         });
   }

   public function setOrganizaceID(int $id_organizace) :static {
      $this->addParametrQuery('organizace', $id_organizace);
      $this->id_organizace = $id_organizace;
      return $this;
   }

   public function getOrganizaceID() :int {
      return $this->id_organizace ??= intval($_GET['organizace']);
   }

   function getQuery() :Fluent {
      return KalkulaceIntroductionSablony::findByOrganizace($this->getOrganizaceID());
   }

   private int $id_organizace;
}