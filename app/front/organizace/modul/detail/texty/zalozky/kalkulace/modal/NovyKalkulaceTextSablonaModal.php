<?php namespace app\front\organizace\modul\detail\texty\zalozky\kalkulace\modal;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\organizace\kalkulace\introduction\SaveKalkulaceIntroductionSablonaPostEvent;
use app\system\modul\modal\Modal;
use app\system\modul\modal\styles\ModalScaleEnum;
use app\system\tiny\TinyEditor;
use Latte\Runtime\Html;

/** Created by Filip Pavlas. Date: 22.08.2025 */
class NovyKalkulaceTextSablonaModal extends Modal
{

   public function getTitleName() :string {
      return 'Nová šablona textu kalkulace';
   }

   public function setOrganizaceID(int $organizaceID) :static {
      $this->organizaceID = $organizaceID;
      return $this;
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'jazyky' => OrganizaceJazyky::getPairs($this->organizaceID),
         'sablonaRow' => null,
         'textEditor' => new Html(TinyEditor::init()
            ->setInputName('introduction')
            ->setPlaceholder('Zadejte text kalkulace')
            ->render()),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnVytvoritSablonuTextu', function($post) {
         SaveKalkulaceIntroductionSablonaPostEvent::createFromPost(
            $this->organizaceID,
            $post,
         )->call();

         FlashMessages::setSuccess('Šablona byla uložena');
      });
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::LARGE;

   protected int $organizaceID;
}