<?php namespace app\front\organizace\modul\detail\texty\zalozky\kalkulace;

use app\front\organizace\model\organizace\OrganizaceRow;
use app\front\organizace\modul\detail\texty\zalozky\kalkulace\modal\NovyKalkulaceTextSablonaModal;
use app\system\component\Templater;
use app\system\modul\zalozky\Zalozka;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON>. Date: 22.08.2025 */
class SeznamKalkulaceIntroductionZalozka extends Zalozka
{
   public function getTitle() :string {
      return 'Texty kalkulace';
   }

   public function setOrganizaceRow(OrganizaceRow $organizaceRow) :static {
      $this->organizaceRow = $organizaceRow;
      return $this;
   }

   protected function prepareTemplater(Templater $templater) :void {
      $templater->addData([
         'tableTexty' => new Html((new SeznamSablonTextuKalkulaceTable())->setOrganizaceID($this->organizaceRow->id_organizace)),
         'novaSablonaAttr' => NovyKalkulaceTextSablonaModal::init()->btnToggleAttributes(),
      ]);
   }

   protected OrganizaceRow $organizaceRow;
}