<?php namespace app\front\organizace\modul\detail\texty;

use app\front\organizace\modul\detail\texty\zalozky\bloky\SeznamBlokuZalozka;
use app\front\organizace\modul\detail\texty\zalozky\emaily\EmailyZalozka;
use app\front\organizace\modul\detail\texty\zalozky\emaily\log\EmailOrganizaceLogZalozka;
use app\front\organizace\modul\detail\texty\zalozky\formulare\FormulareZalozka;
use app\front\organizace\modul\detail\texty\zalozky\kalkulace\SeznamKalkulaceIntroductionZalozka;
use app\front\organizace\modul\detail\texty\zalozky\nastaveni\NastaveniSmtp;
use app\front\organizace\modul\detail\texty\zalozky\smlouvy\SmlouvyZalozka;
use app\system\flash\FlashMessages;
use app\system\mailer\data\MailsModel;
use app\system\mailer\EmailSender;
use app\system\model\mista\email\OrganizaceEmailRow;
use app\system\model\mista\email\OrganizaceEmaily;
use app\system\model\organizace\Misto;
use app\system\modul\panels\Panel;

/** Created by Pavlas Filip. Date: 10.12.2022 */
class TextyPanel extends Panel
{

   protected function preparePostListeners() :void {
      $this->isset('btnSaveEmailSMTP', function($post) {
         $misto = $this->getParameter('id_mista');
         $email = OrganizaceEmaily::getForOrganizace($misto)
            ?: new OrganizaceEmailRow(['id_organizace' => $this->getParameter('id_mista')]);

         foreach(['host', 'port', 'username', 'password'] as $item)
            $email->{$item} = $post[$item];

         if(!$email->test()){
            FlashMessages::setError('Nepodařilo se připojit k Vašemu mail serveru!');
            return;
         }

         $email->is_active = 1;
         $email->save();
      });

      $this->isset('btnDeleteSMTPLogin', function($post) {
         $organizaceID = $this->getParameter('id_mista');
         $login = OrganizaceEmaily::getForOrganizace($organizaceID);

         if(!$login)
            return;

         OrganizaceEmaily::delete($login);
      });

      $this->isset('btnResendEmail', function($post) {
         $organizaceID = intval($this->getParameter('id_mista'));

         if(
            !($mailID = intval($post['btnResendEmail']))
            || !($mail = MailsModel::get($mailID))
            || $mail->id_organizace !== $organizaceID
         ) {
            FlashMessages::setError('Email nelze znovu odeslat');
            return;
         }

         $recipients = $post['recipientEmail'];
         foreach($mail->getRecipients() as $recipient){
            if(!($newEmail = trim($recipients[$recipient->id] ?? ''))) {
               FlashMessages::setError('Email je povinný');
               return;
            }

            $recipient->email = $newEmail;
            $recipient->save();
         }

         (new EmailSender($mail))->send();
      });

      $this->isset('btnDeleteEmail', function($post) {
         $organizaceID = intval($this->getParameter('id_mista'));

         if(
            !($mailID = intval($post['btnDeleteEmail']))
            || !($mail = MailsModel::get($mailID))
            || $mail->id_organizace !== $organizaceID
         ) {
            FlashMessages::setError('Tento email nelze smazat');
            return;
         }

         MailsModel::delete($mail);
      });
   }

   function getMenuName() :string {
      return 'Texty';
   }

   public function returnString() :?string {
      $misto = Misto::get($this->getParameter('id_mista'));

      $this
         ->addZalozka(EmailyZalozka::init()
            ->setMisto($misto))
         ->addZalozka(FormulareZalozka::init()
            ->setMisto($misto))
         ->addZalozka(SeznamBlokuZalozka::init()
            ->setMisto($misto));

         $this->addZalozka(SmlouvyZalozka::init()
            ->setMisto($misto));

      $this->addZalozka(SeznamKalkulaceIntroductionZalozka::init()
         ->setOrganizaceRow($misto->mistoRow));

         $this->addZalozka(NastaveniSmtp::init()
            ->setMisto($misto));

         $this->addZalozka(EmailOrganizaceLogZalozka::init()
            ->setOrganizace($misto->mistoRow));

      return '';
   }
}