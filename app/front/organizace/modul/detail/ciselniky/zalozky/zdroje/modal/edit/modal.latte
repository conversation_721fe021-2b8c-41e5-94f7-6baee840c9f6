{varType app\system\model\organizace\zdroje\OrganizaceZdrojRow $zdroj}

<form method="post" id="organizationEditRequestSource">
   <div class="container">
      <div class="row gap-2">
         <div class="col-md-8">
            <div class="form-floating">
               <input type="text" name="nazev_zdroj" class="form-control" value="{$zdroj->nazev}" maxlength="80" required>
               <label for="nazev_zdroj">{_'Název zdroje'}</label>
            </div>
         </div>
         <div class="col-md">
            <div class="form-group">
                <label for="scenar">{_'Chcete zdroj používat?'}</label>
                <div class="input-group">
                    <div class="btn-group" data-radioswitch="is_aktivni_zdroj">
                        <a class="btn btn-primary btn-sm notActive" data-value="true">{_'Ano'}</a>
                       <input type="hidden" name="is_aktivni_zdroj" id="is_aktivni_zdroj" value="{$zdroj->isAktivni()?'true':'false'}">
                        <a class="btn btn-primary btn-sm notActive" data-value="false">{_'Ne'}</a>
                    </div>
                </div>
            </div>
         </div>
      </div>

      <div class="row mt-3">
         <div class="col-12">
            <div class="form-floating">
               <textarea name="popis_zdroj" class="form-control" placeholder="{_'Např. za jakých situací budete používat tento zdroj poptávky/kalkulace/eventu,...'}" style="height: 100px;">{$zdroj->popis}</textarea>
               <label for="nazev_eventu">{_'Poznámka'}</label>
            </div>
         </div>
      </div>
   </div>
   <div class="row mt-4 d-flex justify-content-center gap-3">
      <div class="col-auto">
         <input type="hidden" name="id_zdroj" value="{$zdroj->id}">
         <input type="submit" name="btnSaveZdroj" class="btn btn-primary w-auto shaddow-hover" value="{_'Uložit změny'}">
      </div>
      <div class="col-auto">
         <input type="submit" name="btnSmazatZdroj" class="btn btn-danger w-auto shaddow-hover" value="{_'Smazat zdroj'}"
                onclick="return confirm('Opravdu chcete smazat tento zdroj? Tato akce je nevratná.')">
      </div>
   </div>
</form>
<script>
   $(function() {
      const form = $("form#organizationEditRequestSource");

      form.validate();
   })
</script>