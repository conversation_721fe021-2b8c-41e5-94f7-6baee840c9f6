<?php namespace app\front\organizace\modul\detail\ciselniky\zalozky\zdroje\modal\edit;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\organizace\zdroje\OrganizaceZdroje;
use app\system\model\organizace\zdroje\OrganizaceZdrojRow;
use app\system\model\organizace\zdroje\eventy\SmazatZdrojEvent;
use app\system\modul\modal\AjaxModal;
use app\system\modul\modal\styles\ModalScaleEnum;

/** Created by <PERSON><PERSON>las Filip. Date: 14.12.2022 */
class EditMistaZdrojeModal extends AjaxModal
{

   protected function preparePostListeners() :void {
      $this->isset('btnSaveZdroj', function($post) {
         OrganizaceZdroje::saveFromPost($post);
      });

      $this->isset('btnSmazatZdroj', function($post) {
         $zdrojId = (int)$post['id_zdroj'];
         OrganizaceZdroje::delete($zdrojId);
      });
   }

   public function checkPost(array $post = []) {
      parent::checkPost($post);

      // If this is a POST request to an AJAX modal, send JSON response instead of redirecting
      if (!empty($_POST)) {
         $this->sendAjaxResponse();
      }
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::LARGE;

   public function getTitleName() :string {
      return 'Upravit zdroj eventu';
   }

   public function prepareAjaxData() :void {
      $this->zdroj = OrganizaceZdroje::get($_POST['slug']);
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'zdroj' => $this->zdroj,
      ]);
   }

   protected OrganizaceZdrojRow $zdroj;
}