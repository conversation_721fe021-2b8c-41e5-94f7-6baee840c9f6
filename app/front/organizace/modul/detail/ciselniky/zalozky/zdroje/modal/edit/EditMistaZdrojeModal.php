<?php namespace app\front\organizace\modul\detail\ciselniky\zalozky\zdroje\modal\edit;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\organizace\zdroje\OrganizaceZdroje;
use app\system\model\organizace\zdroje\OrganizaceZdrojRow;
use app\system\model\organizace\zdroje\eventy\SmazatZdrojEvent;
use app\system\modul\modal\AjaxModal;
use app\system\modul\modal\styles\ModalScaleEnum;
use app\system\redirect\Redirect;

/** Created by <PERSON><PERSON><PERSON>. Date: 14.12.2022 */
class EditMistaZdrojeModal extends AjaxModal
{

   protected function preparePostListeners() :void {
      $this->isset('btnSaveZdroj', function($post) {
         OrganizaceZdroje::saveFromPost($post);
      });

      $this->isset('btnSmazatZdroj', function($post) {
         $zdrojId = (int)$post['id_zdroj'];
         OrganizaceZdroje::delete($zdrojId);

         // Redirect to refresh the page and show flash messages
         Redirect::self();
      });
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::LARGE;

   public function getTitleName() :string {
      return 'Upravit zdroj eventu';
   }

   public function prepareAjaxData() :void {
      $this->zdroj = OrganizaceZdroje::get($_POST['slug']);
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'zdroj' => $this->zdroj,
      ]);
   }

   protected OrganizaceZdrojRow $zdroj;
}