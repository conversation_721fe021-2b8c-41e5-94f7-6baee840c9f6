<?php namespace app\front\organizace\modul\detail\ciselniky\zalozky\zdroje\modal\edit;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\organizace\zdroje\OrganizaceZdroje;
use app\system\model\organizace\zdroje\OrganizaceZdrojRow;
use app\system\model\organizace\zdroje\eventy\SmazatZdrojEvent;
use app\system\modul\modal\AjaxModal;
use app\system\modul\modal\styles\ModalScaleEnum;

/** Created by <PERSON><PERSON>las Filip. Date: 14.12.2022 */
class EditMistaZdrojeModal extends AjaxModal
{

   protected function preparePostListeners() :void {
      $this->isset('btnSaveZdroj', function($post) {
         OrganizaceZdroje::saveFromPost($post);
      });

      $this->isset('btnSmazatZdroj', function($post) {
         $zdrojId = (int)$post['id_zdroj'];
         $zdroj = OrganizaceZdroje::get($zdrojId);

         if (!$zdroj) {
            FlashMessages::setError('Zdroj nebyl nalezen');
            return;
         }

         // Call the event directly to handle validation and deletion
         $event = new SmazatZdrojEvent($zdroj);
         $event->call();

         // Handle the result and show appropriate flash message
         if ($event->getSuccess()) {
            FlashMessages::setSuccess($event->getSuccessMessage());
         } else {
            FlashMessages::setError($event->getErrorMessage());
         }
      });
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::LARGE;

   public function getTitleName() :string {
      return 'Upravit zdroj eventu';
   }

   public function prepareAjaxData() :void {
      $this->zdroj = OrganizaceZdroje::get($_POST['slug']);
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'zdroj' => $this->zdroj,
      ]);
   }

   protected OrganizaceZdrojRow $zdroj;
}