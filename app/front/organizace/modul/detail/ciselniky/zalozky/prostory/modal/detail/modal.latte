{varType app\system\model\organizace\mistnosti\MistnostRow $mistnost}
{varType app\system\model\organizace\mistnosti\TypyMistnosti[] $typyMistnosti}
{varType app\system\model\organizace\mistnosti\galerie\OrganizaceMistnostFotkaRow[] $photoGalery}
{varType Latte\Runtime\Html $tinyEditor}
{varType app\system\model\organizace\mistnosti\kapacita\TypUsporadaniMistnostiEnum[] $typyUsporadani}
{varType array $pouzivaneVlastnostiID}
{varType array $vlastnosti}

<form method="post" enctype="multipart/form-data" id="editMistnostiModalForm">
   <div class="container">
      <div class="row d-flex">
         <div class="col-md-8">
            <div class="form-floating">
               <input class="form-control" type="text" id="nazev" name="nazev" value="{$mistnost->nazev}" maxlength="100" required>
               <label>{_'Název prostoru'}</label>
            </div>
         </div>
      </div>
      <div class="row d-flex gap-1 py-4">
         <div class="col-md-5">
            <div class="form-floating">
               <select name="typ_mistnosti" id="typ_mistnosti" class="form-select" required>
                  <option value="{$mistnost->getTypMistnosti()->value}">{$mistnost->getTypMistnosti()->getTranslatedTitle()}</option>
                  {foreach $typyMistnosti as $typ}
                      {if $typ->value != $mistnost->typ_mistnosti}<option value="{$typ->value}">{$typ->getTranslatedTitle()}</option>{/if}
                  {/foreach}
               </select>
               <label>{_'Typ prostoru'}</label>
            </div>
         </div>

         <div class="col-md-3">
            <div class="form-floating">
               <input type="number" class="form-control" name="kapacita" id="kapacita" value="{$mistnost->kapacita}" required min="0">
               <label>{_'Maximální kapacita'}</label>
            </div>
         </div>

         <div class="col-auto form-group">
            <label>{_'Použitelný prostor'}</label>
            <div class="input-group">
               <div class="btn-group" data-radioswitch="is_aktivni">
                  <a class="btn btn-primary btn-sm notActive" data-value="false">{_'Ne'}</a>
                  <input type="hidden" name="is_aktivni" value="{$mistnost->isAktivniStr()}">
                  <a class="btn btn-primary btn-sm notActive" data-value="true">{_'Ano'}</a>
               </div>
            </div>
         </div>
      </div>

      <div class="row">
         <label for="vlastnostiProstory">{_'Vlastnosti prostoru'}</label>
         <div class="input-group mb-3 d-flex gap-2">
            <div class="input-group-prepend col-12 col-lg">
               <select id="vlastnostiProstory" class="form-select js-select-vlastnost-prostor" name="vlastnosti[]" multiple="multiple">
                  {foreach $vlastnosti as $id_vlastnosti => $vlastnost}
                     <option value="{$id_vlastnosti}" n:attr="selected: isset($pouzivaneVlastnostiID[$id_vlastnosti])">
                        {$vlastnost}
                     </option>
                  {/foreach}
               </select>
            </div>
         </div>
      </div>

      <div class="row">
         <div class="form-group my-3">
            {$tinyEditor}
         </div>
      </div>

      <div class="row">
         {if !empty($photoGalery)}
            <table class="table">
               <thead>
               <tr>
                  <td style="width: 5%">{_'Náhled'}</td>
                  <td>{_'Pojmenování fotky'}</td>
                  <td class="text-end" style="width: 15%">{_'Vytvořeno'}</td>
                  <td style="width: 5%"></td>
               </tr>
               </thead>
               <tbody>
               <tr n:foreach="$photoGalery as $image">
                  <td>
                     <div class="d-flex">
                        <div class="img-cont">
                           <img class="img-fluid" src="{$image->getPhotoFullSrc()}">
                        </div>
                     </div>
                  </td>
                  <td><input type="text" value="{$image->nazev ?: ''}" class="form-control" name="galerie_nazev[{$image->id}]" maxlength="255" required></td>
                  <td class="text-end">{$image->created|date: 'j.n.Y H:i'}</td>
                  <td class="table-action">
                     <div class="d-flex gap-1 justify-content-end row">
                        <div class="col col-auto p-0">
                           <button type="submit" name="btnDeleteRoomPhoto" value="{$image->id}" class="form-control btn btn-outline-danger"
                                   data-confirm="{_'Opravdu chcete fotku smazat?'}"><i class="bi bi-trash"></i></button>
                        </div>
                     </div>
                  </td>
               </tr>
               </tbody>
            </table>

         {else}
            <p>{_'Žádné fotky tady nejsou'}</p>
         {/if}
      </div>

      <button n:if="empty($photoGalery)" type="button" class="btn btn-sm btn-outline-secondary" data-add-chat-attachment>
         <i class="bi bi-paperclip"></i>{_'Přidat fotografii'}
      </button>

      <div class="row pb-2">
         <div class="d-flex gap-2" data-attachment-chat-container></div>
      </div>

      <input type="hidden" name="id_misto" value="{$mistnost->id_misto}">
      <input type="hidden" name="id_mistnost" value="{$mistnost->id_mistnost}">
   </div>

   <div class="row mt-4">
      <div id="dimensionInputs" class="col">
         <div class="form-group col">
            <label for="length">{_'Délka prostoru'}: <span>{$mistnost->getRoomDimensions()->length ?? 0}</span> m</label>
            <input type="range" class="form-range" id="length" min="0" max="100" value="{$mistnost->getRoomDimensions()->length ?? 0}"
                   oninput="updateValue(this)" name="room_length">
         </div>
         <div class="form-group col">
            <label for="width">{_'Šířka prostoru'}: <span>{$mistnost->getRoomDimensions()->width ?? 0}</span> m</label>
            <input type="range" class="form-range" id="width" min="0" max="100"
                   value="{$mistnost->getRoomDimensions()->width ?? 0}"
                   oninput="updateValue(this)" name="room_width">
         </div>
         <div class="form-group col">
            <label for="ceilingHeight">{_'Výška prostoru'} (m): <span>{$mistnost->getRoomDimensions()->ceiling_height ?? 0}</span> m</label>
            <input type="range" class="form-range" id="ceilingHeight" name="room_ceiling_height" min="0" max="10" value="{$mistnost->getRoomDimensions()->ceiling_height ?? 0}" step="0.1"
                   oninput="updateValue(this)">
         </div>
         <div class="form-group">
            <label for="unusableArea">{_'Neupotřebitelná plocha'}: <span>{$mistnost->getRoomDimensions()->unusable_area ?? 0}</span> m²</label>
            <input type="range" class="form-range" id="unusableArea" min="0" max="1000" value="{$mistnost->getRoomDimensions()->unusable_area ?? 0}"
                   oninput="updateValue(this)" name="room_unusable_area">
            <small class="text-black-50">{_'Například pro podium'}, {_'raut'}, {_'taneční parket apod'}.</small>
         </div>
      </div>
      <div class="col align-content-center">
         <div class="col-12 border border-light-gray radius-card p-4 text-center">
            <p id="totalAreaText">{_'Celková plocha prostoru je'}:</p>
            <p class="lead"><span id="totalAreaValue">0</span> m²</p>
         </div>
      </div>
   </div>

   <div class="row mt-4 mb-2">
      <span class="lead mt-2 d-block">{_'Návrh kapacity pro různé typy uspořádání'}:</span>
      <small>{_'Návrh je orientační'}, {_'jednotlivé kapacity si můžete manuálně upravit'}.</small>
   </div>
   <div class="row mt-2 gap-2 gap-lg-0">
      {foreach $typyUsporadani as $usporadani}
         <div class="form-group col">
            <img src="{$usporadani->getIconPath()}" class="w-50">
            <label for="classroomCapacity[{$usporadani->value}]">{$usporadani->getTranslatedTitle()}<br>({$usporadani->getRecomendedSpace()})</label>
            <input type="number" class="form-control" id="classroomCapacity[{$usporadani->value}]" min="0" name="arrangement_capacities[{$usporadani->value}]"
                 n:attr="value: isset($mistnost->getRoomCapacities()[$usporadani->value]) ? $mistnost->getRoomCapacities()[$usporadani->value]->kapacita">
         </div>
      {/foreach}
   </div>

   <div class="row mt-4 d-flex justify-content-center">
      <div class="col-auto">
         <input type="submit" name="btnEditMistnost" class="btn btn-primary w-auto shaddow-hover" value="{_'Uložit změny'}">
      </div>
   </div>
</form>

<script>
   $(function () {
      const form = $('#editMistnostiModalForm');
      const btnAddAttachment = form.find('[data-add-chat-attachment]');
      const fileInputContainer = form.find('[data-attachment-chat-container]');
      const maxFilesContainerSize = 20 * 1024 * 1024;
      const maxFileSize = 2 * 1024 * 1024;
      const allowedExtensions = ['jpg', 'jpeg', 'png'];
      let filesContainerSize = 0;

      btnAddAttachment.on('click', function(e) {
         e.preventDefault();

         const tempFileInput = document.createElement('input');
         tempFileInput.type = 'file';
         tempFileInput.name = 'attachments[]';
         tempFileInput.style.display = 'none';
         tempFileInput.setAttribute('accept', allowedExtensions.map(ext => `.${ ext }`).join(','));

         tempFileInput.click();

         tempFileInput.addEventListener('change', function() {
            if(
               tempFileInput.files.length === 0
            ){
               toastr['error']("Příloha neobsahuje žádné soubory", '', {
                  positionClass: 'toast-bottom-left',
                  progressBar: true,
               });
               tempFileInput.remove();
               return;
            }

            const file = tempFileInput.files[0];

            if(!(file instanceof File)){
               toastr['error']("Příloha je poškozena zkuste to znovu za chvíli, nebo kontaktujte podporu", '', {
                  positionClass: 'toast-bottom-left',
                  progressBar: true,
               });
               tempFileInput.remove();
               return;
            }

            const fileExtension = file.name.split('.').pop().toLowerCase();

            if(!allowedExtensions.includes(fileExtension)){
               toastr['error']("Omlouváme se, ale soubor, který jste nahráli, není ve formátu, který podporujeme.", '', {
                  positionClass: 'toast-bottom-left',
                  progressBar: true,
               });
               tempFileInput.remove();
               return;
            }

            const humanFileSizeMiB = (file.size / (1024 * 1024)).toFixed(2);
            const humanFileSize = (file.size / (1000 * 1000)).toFixed(2);

            if(file.size > maxFileSize){
               const humanMaxFileSize = (maxFileSize / (1024 * 1024)).toFixed(2);

               toastr['error'](`Omlouváme se, ale soubor, který jste nahráli je větší jak ${ humanMaxFileSize } MB (${ humanFileSize } MB)`, '', {
                  positionClass: 'toast-bottom-left',
                  progressBar: true,
               });
               tempFileInput.remove();
               return;
            }

            if((file.size + filesContainerSize) > maxFilesContainerSize){
               toastr['error'](`Omlouváme se, ale další soubor již nelze nahrát`, '', {
                  positionClass: 'toast-bottom-left',
                  progressBar: true,
               });
               tempFileInput.remove();
               return;
            }

            filesContainerSize += file.size;
            const attachmentCard = document.createElement('div');

            attachmentCard.classList.add('border', 'border-radius', 'col-lg-auto', 'col-6', 'text-center', 'px-2', 'py-1', 'shaddow-hover');
            attachmentCard.dataset.attachmentFile = '';

            const cardHtml = `
               <div><i class="bi bi-paperclip"></i> ${ file.name } (${ humanFileSize } MB) (${ humanFileSizeMiB } MiB)<a class="cursor-pointer text-danger" data-attachment-delete><i class="bi bi-trash"></i></a></div>
               `;

            const cardHtmlEl = (new DOMParser()).parseFromString(cardHtml, 'text/html').body.firstElementChild;

            attachmentCard.appendChild(cardHtmlEl);
            attachmentCard.appendChild(tempFileInput);

            fileInputContainer[0].appendChild(attachmentCard);

            btnAddAttachment.hide();
         });

         $('body').on('click', '[data-attachment-delete]', function(e) {
            const fileCard = e.target.closest('[data-attachment-file]');

            if(fileCard === undefined)
               return;

            fileCard.remove();
            btnAddAttachment.show();
         });

      })
   });
</script>
<script>
   $(function() {
      $('.js-select-vlastnost-prostor').select2({
         dropdownParent: $('#editMistnostiModalForm')
      });
      const form = $('form#editMistnostiModalForm');
      const inputRoomLength = form.find('input[name="room_length"]');
      const inputRoomWidth = form.find('input[name="room_width"]');
      const inputRoomHeight = form.find('input[name="room_ceiling_height"]');

      form.validate({
         rules: {
            'room_length': {
               min: getMinimalRange
            },
            'room_width': {
               min: getMinimalRange,
            },
            'room_ceiling_height': {
               min: getMinimalRange,
            },
         },
      });

      function getMinimalRange() {
         if(Number(inputRoomLength.val()) === 0
            && Number(inputRoomWidth.val()) === 0
            && Number(inputRoomHeight.val()) === 0
         )
            return 0;

         return 1;
      }

      calculate(false);
   });

   function updateValue(element) {
      const valueRoom = element.value;
      const label = element.parentElement.querySelector(`label[for="${ element.id }"]`).querySelector('span');

      label.innerText = valueRoom;
      calculate();
   }

   function calculate(calculateCapacities = true) {
      const length = parseFloat(document.getElementById('length').value);
      const width = parseFloat(document.getElementById('width').value);
      const unusableAreaElement = document.getElementById('unusableArea');
      const unusableArea = parseFloat(unusableAreaElement.value);

      const totalArea = length * width;
      const usableArea = totalArea - unusableArea;

      document.getElementById('totalAreaValue').innerText = totalArea.toFixed(2);

      unusableAreaElement.max = totalArea.toFixed(2);

      // Dočastně vypnuto u#86c5d65xw
      {*if(calculateCapacities){*}
      {*   let result;*}
      {*   {foreach $typyUsporadani as $usporadani}*}
      {*      result = (Math.floor(usableArea / Number({$usporadani->getSpaceKoeficient()})) > 0) ? Math.floor(usableArea / Number({$usporadani->getSpaceKoeficient()})) : 0;*}
      {*      document.getElementById('classroomCapacity[{$usporadani->value}]').value = result;*}
      {*   {/foreach}*}
      {*}*}
   }
</script>