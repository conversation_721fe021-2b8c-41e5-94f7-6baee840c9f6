<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\personal\modal\personal\edit;

use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\modul\detail\uzivatele\zalozky\personal\modal\personal\edit\prava\PravaPersonaluComponent;
use app\front\tisk\misto\smeny\MistoSmenyPersonaluTisk;
use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\model\organizace\personal\eventy\UpravaPersonaluPostEvent;
use app\system\model\organizace\personal\eventy\VytvoritUcetPersonalEvent;
use app\system\model\organizace\personal\Personal;
use app\system\model\organizace\personal\PersonalRow;
use app\system\model\uzivatele\docasny\DocasnyUzivatel;
use app\system\model\uzivatele\Uzivatel;
use app\system\model\uzivatele\UzivatelRow;
use app\system\modul\modal\AjaxModal;
use app\system\modul\modal\styles\ModalScaleEnum;
use DateInterval;
use Dibi\DateTime;

class EditPersonalModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Úprava personálu';
   }

   public function prepareAjaxData() :void {
      $this->personal = Personal::get($_POST['slug']);
   }

   public function prepareModal(Templater $templater) {
      $data = [];

      $data['personal'] = $this->personal;
      $this->appendUzivatel($data);
      $this->appendCekajiciPozvanka($data);
      $this->appendTiskDropdown($data);

      $templater->addData($data);
   }

   public ?PersonalRow $personal;
   public ?UzivatelRow $uzivatel = null;

   protected function preparePostListeners() :void {
      $this->isset('btnUpravitPersonal', function ($post) {
         if(
            !($personal = Personal::get($post['id_personal']))
            || $personal->id_misto !== FrontApplicationEnvironment::get()->id_organizace
         ){
            throw FlashException::create('Tento personál nyní nelze uložit, zkontrolujte svou aktivní organizace');
         }

         unset($post['id_personal']);

         (new UpravaPersonaluPostEvent($personal, $post))
            ->call();

         FlashMessages::setSuccess('Personál uložen');
      });

      $this->isset('btnVytvoritUcet', function ($post) {
         (new VytvoritUcetPersonalEvent())
            ->setPostForm($post)
            ->call();
      });

      $this->isset('btnZneaktivnitPersonal', function ($post) {
         Personal::changeActivity($post['id_personal'], false);
         FlashMessages::setSuccess('Personál byl zneaktivněný');
      });

      $this->isset('btnSmazatPersonal', function ($post) {
         Personal::delete($post['id_personal']);
         FlashMessages::setSuccess('Personál byl smazán');
      });

      $this->isset('btnOdebratPristup', function ($post) {
         Personal::deleteAccess($post['id_personal']);
         FlashMessages::setSuccess('Přístup do systému byl odebrán');
      });

      $this->isset('btnAktivovatPersonal', function($post) {
         Personal::changeActivity($post['id_personal'], true);
         FlashMessages::setSuccess('Personál byl aktivován');
      });
   }

   protected function appendUzivatel(array &$data) {
      $uzivatel = $data['uzivatel'] = $this->personal->id_uzivatel
            ? Uzivatel::get($this->personal->id_uzivatel)
            : null;

      $misto = Organizace::getMisto($this->personal->id_misto);
      $data['isMajitel'] = $misto->id_uzivatel === $uzivatel?->id_uzivatele;

      if($uzivatel)
         $data['pravaTable'] = (string)PravaPersonaluComponent::getComponent()
            ->setUzivatel($uzivatel)
            ->setPersonal($this->personal)
            ->setMisto($misto);
   }

   protected function appendCekajiciPozvanka(array &$data) {
      if(!$this->personal->isNeaktivni && !$this->personal->id_uzivatel)
         $data['pozvanka'] = DocasnyUzivatel::getByPersonal($this->personal->id_personal);
   }

   protected function appendTiskDropdown(array &$data) {
      $data['dropdown'] = Templater::prepare(__DIR__ . '/dropdown-calendar.latte', [
         'personal' => $this->personal,
         'attr' => MistoSmenyPersonaluTisk::getHtmlAttr(['id_mista' => $this->personal->id_misto]),
         'od' => $now = new DateTime(),
         'do' => (clone $now)->add(new DateInterval('P1M'))
      ])->render();
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::LARGE;

}