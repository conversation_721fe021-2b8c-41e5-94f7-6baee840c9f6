<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\personal\modal\personal\novy;

use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\model\organizace\personal\eventy\UpravaPersonaluPostEvent;
use app\system\modul\modal\Modal;
use app\system\modul\modal\styles\ModalScaleEnum;

class NovyPersonalModal extends Modal
{

   public function getTitleName() :string {
      return 'Přidat zaměstnance';
   }

   public function setIdMista(mixed $id_mista) :static {
      $this->id_mista = $id_mista;
      return $this;
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'id_mista' => $this->id_mista,
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnVytvoritPersonal', function($post) {
         if(!($email = trim($post['email'])))
            throw FlashException::create('Email je povinný');

         unset($post['email']);
         unset($post['id_misto']);

         UpravaPersonaluPostEvent::create(
            $email,
            FrontApplicationEnvironment::get()->id_organizace,
            $post
         )->call();

         FlashMessages::setSuccess('Personál byl vytvořen');
      });
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::SMALL;

   protected int $id_mista;
}