{varType int $id_mista}

<form method="post" id="organizationAddPersonal">
   <div class="row d-flex justify-content-center">
   <div>
        <div class="row g-2">
            <div class="col-md col-12 form-group">
                <label for="jmeno-modal">{_'Jméno'}</label>
                <input type="text" name="jmeno" id="jmeno-modal" class="form-control" maxlength="40" required>
            </div>
            <div class="col-md col-12 form-group">
                <label for="prijmeni-modal">{_'Přijmení'}</label>
                <input type="text" name="prijmeni" id="prijmeni-modal" class="form-control" maxlength="40" required>
            </div>
        </div>
        <div class="row">
            <div class="col-md col-12 form-group">
                <label for="email-check">{_'Email'}</label>
                <input type="text" name="email" id="email-check" class="form-control" maxlength="90" required>
            </div>
            <div class="col-md col-12 form-group">
                <label for="telefon-modal">{_'Telefon'}</label>
                <input type="text" name="telefon" id="telefon-modal" class="form-control" maxlength="20" required>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 col-12 form-group">
                <label for="hodinova_sazba">{_'Hodinová sazba'}</label>
                <input type="text" name="hodinova_sazba" id="hodinova_sazba" class="form-control js-price-mask" maxlength="11">
            </div>
        </div>
   </div>
   <div class="row mt-4 d-flex justify-content-center">
      <div class="col-auto">
         <input type="hidden" name="id_misto" value="{$id_mista}">
         <input type="submit" name="btnVytvoritPersonal" class="btn btn-primary w-auto shaddow-hover" value="{_'Uložit'}">
      </div>
   </div>
   </div>
</form>

<script>
    $(function() {
        const form = $("form#organizationAddPersonal");

        form.validate({
            rules: {
                "email": {
                    email: true,
                },
                "telefon": {
                    telefon: true,
                },
            }
        })
    })
</script>