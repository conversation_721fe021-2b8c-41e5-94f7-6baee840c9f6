{varType app\system\model\organizace\personal\PersonalRow $personal}
{varType ?app\system\model\uzivatele\UzivatelRow $uzivatel}
{varType ?app\system\model\uzivatele\docasny\DocasnyUzivatelRow $pozvanka}
{varType ?app\front\organizace\modul\detail\uzivatele\zalozky\personal\modal\personal\edit\prava\PravaPersonaluComponent $pravaTable}

{varType string $dropdown}
{varType bool $isMajitel}

<form method="post" id="organizationEditPersonal">
   <div class="container gap-2">
      <div class="row d-flex justify-content-center">
       <div class="p-3">
          <div class="row">
          <div class="col-md-8 col-12">
                <h2 class="lead">{$personal->getFullName()}</h2>
                <small n:if="!$uzivatel">{_'Personál bez přístupu do QVAMP'}</small>
          </div>
          <div class="col-md col-12 px-3">
             {if !$personal->isNeaktivni}
             <div class="row mt-2">
                   <input type="submit" name="btnZneaktivnitPersonal"
                          data-confirm="{_'Personál se stane neaktivním, nebude se mu dát přiřadit nový úkol a stávající přiřazené úkoly v jiném stavu jako je hotový budou nastaveny jako nepřiřazené'}"
                          class="btn btn-sm btn-outline-secondary shaddow-hover no-validate" value="{_'Zneaktivnit'}" formnovalidate>
             </div>
             {else}
                <div class="row mt-2">
                   <input type="submit" name="btnSmazatPersonal"
                          data-confirm="{_'Dojde k úplnému smazání personálu, úkoly co byly historicky přiřazené, budou teď nepřiřazené, opravdu chcete pokračovat?'}"
                          class="btn btn-sm btn-outline-secondary shaddow-hover no-validate" value="{_'Odstranit'}" formnovalidate>
                </div>
                <div class="row">
                   <input type="submit" name="btnAktivovatPersonal"
                          data-confirm="{_'Chcete znovu aktivovat personál $1?', $personal->getFullName()}"
                          class="btn btn-sm btn-outline-primary shaddow-hover" value="{_'Aktivovat'}">
                </div>
             {/if}
             <div n:if="!$isMajitel" class="row mt-2">
                {if $uzivatel}
                   <input type="submit" name="btnOdebratPristup"
                          data-confirm="{_'Opravdu chcete odebrat přístup uživateli $1?', $personal->getFullName()}"
                          class="btn btn-sm btn-outline-secondary shaddow-hover" value="{_'Odebrat přístup'}">
                {elseif !$personal->isNeaktivni}
{*                  @TODO pending pozvánka - možnost poslat znovu *}

                   <input type="submit" name="btnVytvoritUcet"
                      {if $pozvanka}
                        class="btn btn-outline-primary shaddow-hover" value="{_'Obnovit pozvánku'}"
                        data-confirm="{_'Pozvánka již byla odeslána, pokud budete pokračovat vytvoří se nová a stará se zneaktivní'}"
                      {else}
                        class="btn btn-sm btn-outline-primary shaddow-hover" value="{_'Vytvořit účet'}"
                      {/if}
                   >
                {/if}
             </div>
          </div>
          </div>
       </div>
      </div>
       <div class="row d-flex justify-content-center">
          <div class="border border-light-gray radius-card p-3">
             <div class="row mb-2 gap-2">
                <div class="col-md col-12 form-group">
                   <label for="jmeno">{_'Jméno'}</label>
                   <input type="text" name="jmeno" id="jmeno" class="form-control" value="{$personal->jmeno}"
                          maxlength="40" {if $personal->isNeaktivni === 1}disabled{else}required{/if}>
                </div>
                <div class="col-md col-12 form-group">
                   <label for="prijmeni">{_'Přijmení'}</label>
                   <input type="text" name="prijmeni" id="prijmeni" class="form-control" value="{$personal->prijmeni}"
                          maxlength="40" {if $personal->isNeaktivni === 1}disabled{else}required{/if}>
                </div>
             </div>

             <div class="row mb-2">
                <div class="col-md col-12 form-group">
                   <label for="email">{_'Email'}</label>
                   <input type="text" name="email" id="email" class="form-control" value="{$personal->email}"
                          {if $uzivatel || $personal->isNeaktivni === 1}disabled{else}required{/if} maxlength="90">
                </div>
                <div class="col-md col-12 form-group">
                   <label for="telefon">{_'Telefon'}</label>
                   <input type="text" name="telefon" id="telefon" class="form-control"
                          value="{$personal->telefon}" maxlength="20" {if $personal->isNeaktivni === 1}disabled{else}required{/if}>
                </div>
             </div>

             <div class="row">
                <div class="col-md-3 col-12 form-group">
                   <label for="hodinova_sazba">{_'Hodinová sazba'}</label>
                   <input type="text" name="hodinova_sazba" id="hodinova_sazba" class="form-control js-price-mask"
                          value="{$personal->hodinova_sazba}" {if $personal->isNeaktivni === 1}disabled{/if}>
                </div>
             </div>
          </div>
       </div>
      <div class="row d-flex justify-content-center mt-3">
         <div n:if="$uzivatel" class="border border-light-gray radius-card p-3">
            {$pravaTable|noescape}
          </div>
      </div>
      <input type="hidden" name="id_personal" value="{$personal->id_personal}">
      <input type="hidden" name="id_mista" value="{$personal->id_misto}">
   </div>
   <div class="row mt-4 d-flex justify-content-center" n:if="$personal->isNeaktivni === 0">
      <div class="col-auto" n:ifset="$dropdown">
         {$dropdown|noescape}
      </div>
      <div class="col-auto">
         <input type="submit" name="btnUpravitPersonal" class="btn btn-primary w-auto shaddow-hover" value="{_'Uložit změny'}">
      </div>
   </div>
</form>
<script>
   $(function() {
      const form = $("form#organizationEditPersonal");

      form.validate({
         ignore: ".no-validate",
         rules: {
            "telefon": {
               telefon: true,
            },
         },
      });
   })
</script>