<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\personal;

use app\front\organizace\modul\detail\uzivatele\zalozky\personal\modal\personal\novy\NovyPersonalModal;
use app\front\organizace\modul\tables\SeznamPrirazenychUzivateluTable;
use app\system\component\Templater;
use app\system\Environment;
use app\system\model\organizace\Misto;
use app\system\modul\zalozky\Zalozka;

/** Created by <PERSON><PERSON><PERSON>. Date: 08.12.2022 */
class PersonalZalozka extends Zalozka
{

   public function getTitle() :string {
      return 'Personál';
   }

   public function setMisto(Misto $misto) :static {
      $this->misto = $misto;
      return $this;
   }

   protected function prepareTemplater(Templater $templater) :void {
      $templater->addData([
         'misto' => $this->misto->mistoRow,
         'tableUzivatelu' => (string)(new SeznamPrirazenychUzivateluTable())->setIdMista($this->misto->getMistoRow()->id_organizace),
         'rootUrl' => Environment::getRealUrl(),
         'novyPersonalAttr' => NovyPersonalModal::init()->btnToggleAttributes(),
      ]);
   }

   protected Misto $misto;
}