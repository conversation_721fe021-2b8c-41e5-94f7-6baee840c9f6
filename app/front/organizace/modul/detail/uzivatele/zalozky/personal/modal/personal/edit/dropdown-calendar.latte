{varType app\system\model\organizace\personal\PersonalRow $personal}
{varType Dibi\DateTime $od}
{varType Dibi\DateTime $do}
{varType array $attr}

<div class="dropdown text-end">
    <a class="form-control btn btn-outline-secondary dropdown-toggle" id="tisk-smeny-personal" data-bs-auto-close="outside"
       aria-expanded="false" data-bs-toggle="dropdown"><i class="align-middle me-2 bi bi-printer"></i>{_'Tisk směn'}</a>

    <div class="dropdown-menu dropdown-menu-lg p-2" aria-labelledby="tisk-smeny-personal">
        <form method="post" class="px-4 py-3">
            <div class="col">
                <div class="form-group">
                    <label>{_'Od'}</label>
                    <div class="input-group date js-datepicker" id="tisk-smeny-personal-od" data-target-input="nearest">
                        <input type="text" class="form-control datetimepicker-input" data-target="#tisk-smeny-personal-od" name="tisk-smeny-personal-od" value="{$od|date: 'j.n.Y'}">
                        <div class="input-group-text" data-target="#tisk-smeny-personal-od" data-toggle="datetimepicker"><i class="bi bi-calendar2-event"></i></div>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="form-group">
                    <label>{_'Do'}</label>
                    <div class="input-group date js-datepicker" id="tisk-smeny-personal-do" data-target-input="nearest">
                        <input type="text" class="form-control datetimepicker-input" data-target="#tisk-smeny-personal-do" name="tisk-smeny-personal-do" value="{$do|date: 'j.n.Y'}">
                        <div class="input-group-text" data-target="#tisk-smeny-personal-do" data-toggle="datetimepicker"><i class="bi bi-calendar2-event"></i></div>
                    </div>
                </div>
            </div>
            <input type="hidden" name="id_personal_tisk" value="{$personal->id_personal}">
           <button class="my-3 btn btn-outline-primary js-smeny-tisk" n:attr="$attr">
              <div class="spinner-border spinner-border-sm text-secondary me-2 js-tisk-loader" role="status" style="display: none">
                 <span class="visually-hidden">{_'Načítání'}...</span>
              </div>
               <i class="align-middle me-2 bi bi-printer"></i>{_'Tisk'}
           </button>
        </form>
    </div>
</div>

<script>
    $(function() {
        $('.js-smeny-tisk').on('qvamp::printer.call', function(e, btn) {
            const bt = $(btn);
            e.data = bt.closest('form').serializeArray();
        });
    });
</script>