<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\personal\modal\personal\edit\prava;

use app\front\organizace\model\organizace\OrganizaceRow;
use app\system\component\Component;
use app\system\model\organizace\personal\PersonalRow;
use app\system\model\organizace\personal\prava\data\PersonalAccessGroup;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\model\organizace\personal\prava\OrganizacePersonalPrava;
use app\system\model\uzivatele\UzivatelRow;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 31.01.2023 */
class PravaPersonaluComponent extends Component
{

   function setData() :array {
      if($this->misto->id_uzivatel === $this->uzivatel->id_uzivatele){
         $this->setTemplateName('majitel');
         return [];
      }

      $this->data['aktivni_prava'] = $prava = OrganizacePersonalPrava::getValuesByPersonal($this->personal->id_personal);

      $pravaGroups = [];
      $ret = [
         1 => null,
      ];

      foreach(PersonalAccessGroup::cases() as $case){
         $pravaGroups[$case->name] = $case;
         $ret[$case->name] = [];
      }

      $this->data['skupinyPrava'] = $pravaGroups;

      $aktivniSkupiny = [];

      foreach(PersonalAccessRightsEnum::cases() as $rightsEnum){
         if($rightsEnum->getGroup()){
            $ret[$rightsEnum->getGroup()->name][$rightsEnum->value] = $rightsEnum;

            if(in_array($rightsEnum->value, array_keys($prava)))
               $aktivniSkupiny[$rightsEnum->getGroup()->name] = true;

            continue;
         }

         $ret[$rightsEnum->value] = $rightsEnum;
      }

      $this->data['dostupnePrava'] = $ret;
      $this->data['aktivniSkupiny'] = $aktivniSkupiny;

      $this->data['isAdmin'] = in_array(PersonalAccessRightsEnum::ADMIN->value, array_keys($prava));

      return $this->data;
   }

   public function setUzivatel(UzivatelRow $uzivatel) :PravaPersonaluComponent {
      $this->uzivatel = $uzivatel;
      return $this;
   }

   public function setMisto(OrganizaceRow $misto) :PravaPersonaluComponent {
      $this->misto = $misto;
      return $this;
   }

   public function setPersonal(PersonalRow $personal) :PravaPersonaluComponent {
      $this->personal = $personal;
      return $this;
   }

   protected UzivatelRow $uzivatel;
   protected PersonalRow $personal;
   protected int $id_misto;
   protected OrganizaceRow $misto;
}