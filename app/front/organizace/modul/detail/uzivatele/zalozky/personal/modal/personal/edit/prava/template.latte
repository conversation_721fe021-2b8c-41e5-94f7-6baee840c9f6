{varType app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum[]|app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum[][] $dostupnePrava}
{varType array $aktivni_prava}

{varType app\system\model\organizace\personal\prava\data\PersonalAccessGroup[] $skupinyPrava}
{varType array $aktivniSkupiny}

{varType bool $isAdmin}

<div class="row">
   <h3>{_'Práva uživatele'}</h3>
</div>
<div class="row mt-2 js-prava-container">
   {foreach $dostupnePrava as $key => $pravo}
      {if is_array($pravo)}
         {var $showContainer = isset($aktivniSkupiny[$key])}
         <div class="row">
            <div class="row">
               <h5 class="col-auto">{$skupinyPrava[$key]->getTranslatedTitle()}</h5>
               <div class="col form-check form-switch">
                  <input class="form-check-input js-admin-included js-group-permission" type="checkbox" role="switch" data-bs-toggle="collapse" data-bs-target="#{$key}Div" n:attr="checked: $showContainer ? true, disabled: $isAdmin">
               </div>
            </div>
            <div id="{$key}Div" n:class="js-prava-group-container, row, mx-2, $showContainer ? show : collapse">
               {foreach $pravo as $pr}
                  {include subPravoInput, pravo: $pr, groupShow: $showContainer}
               {/foreach}
            </div>
         </div>
      {else}
         <div class="row">
            <h5 class="col-auto">{$pravo->getTitle()}</h5>
            <div class="col form-check form-switch">
               <input n:class="form-check-input, $pravo->isAdmin() ? js-admin-check : js-admin-included"
                       n:attr="checked: isset($aktivni_prava[$pravo->value]), name: sprintf('ids_prav[%d]', $pravo->value), disabled: $isAdmin && !$pravo->isAdmin()"
                       type="checkbox" role="switch" id="pravoCheckbox{$pravo->value}">
            </div>
         </div>
      {/if}

      {sep}<hr>{/sep}
   {/foreach}
</div>

<script>
   $(function() {
      const body = $('body');

      body.on('change', 'input.js-admin-check', function() {
         const checkbox = $(this);
         const container = checkbox.closest('.js-prava-container');
         const isChecked = checkbox.is(':checked');

         container.find('.js-admin-included').each(function(i, el){
            const check = $(el);
            const groupCheck = check.closest('.js-prava-group-container');

            if(isChecked === true || groupCheck.length === 0){
               check
                  .prop('disabled', isChecked);
               return;
            }

            const groupCheckInput = container.find('input[data-bs-target="#' + groupCheck[0].id + '"]');

            if(!groupCheckInput.is(':checked'))
               return;

            check
               .prop('disabled', false);
         });
      }).on('click', 'input.js-radio-uncheck', function(e) {
         const checkbox = $(this);

         if(this.dataset.checked === undefined) {
            const cont = checkbox.closest('.js-prava-group-container');
            const inputs = cont.find('input[name="' + this.name + '"]');

            inputs.each(function(i, el){
               if(el.dataset.checked)
                  delete el.dataset.checked;
            });

            this.dataset.checked = true;
            return;
         }

         this.checked = false;
         delete this.dataset.checked;
      });

      $('div.js-prava-group-container')
         .on('hide.bs.collapse show.bs.collapse', function(e) {
            const container = $(this);

            container.find('.js-admin-included').each(function(i, el){
               const check = $(el);
               check
                  .prop('disabled', e.type === 'hide')
            });
         });
   });
</script>

{define subPravoInput, app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum $pravo, bool $groupShow = false}
   {if $pravo->getMultiValue()}
      {foreach $pravo->getMultiValue() as $value => $valueTitle}
         <div class="form-check form-switch">
            <input n:class="form-check-input, js-admin-included, $pravo->isVoluntary() ? js-radio-uncheck"
                   n:attr="checked: isset($aktivni_prava[$pravo->value]) && $aktivni_prava[$pravo->value] === $value, name: sprintf('ids_prav[%d]', $pravo->value), disabled: $isAdmin || !$groupShow, value: $value, required: !$pravo->isVoluntary()"
                   type="radio" role="switch" id="pravoCheckbox{$pravo->value}-{$iterator->counter}">
            <label class="form-check-label" for="pravoCheckbox{$pravo->value}-{$iterator->counter}">{_$valueTitle}</label>
         </div>
      {/foreach}
   {else}
      <div class="form-check form-switch">
         <input class="form-check-input js-admin-included"
                n:attr="checked: isset($aktivni_prava[$pravo->value]), name: sprintf('ids_prav[%d]', $pravo->value), disabled: $isAdmin || !$groupShow, required: !$pravo->isVoluntary()"
                type="checkbox" role="switch" id="pravoCheckbox{$pravo->value}">
         <label class="form-check-label" for="pravoCheckbox{$pravo->value}">{$pravo->getTitle()}</label>
      </div>
   {/if}
{/define}