<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\table;

use app\system\component\Templater;
use app\system\table\DynamicTable;
use dibi;
use Dibi\Fluent;

/** Created by <PERSON><PERSON>. Date: 30.01.2023 */
class SeznamPrirazenychAktivitTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addText('nazev', 'Název');
      $this->addText('termin', 'Počet dnů od eventu');
      $this->addText('id.id_aktivita', '-')
         ->setOrderable(false)
         ->setSearchable(false)
         ->setFormatter(function($value, $item) {
            return Templater::prepare(__DIR__ . '/DeletePrirazenaAktivitaButtonTable.latte', [
               'id_sada' => $item['id_sada'],
               'id_aktivita' => $item['id_aktivita'],
            ]);
         });
   }

   public function setIdSady(int $id_sady) :static {
      $this->addParametrQuery('id_sady', $id_sady);
      return $this;
   }

   function getQuery() :Fluent {
      return dibi::select('a.*, sa.id_sada')
         ->from('organizace_aktivity as a')
         ->join('organizace_sady_aktivity as sa')->on('a.id_aktivita = sa.id_aktivita')
         ->where('sa.id_sada = %i', $_GET['id_sady']);
   }
}