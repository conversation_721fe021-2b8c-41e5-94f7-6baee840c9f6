<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\modal\sada\edit;

use app\front\controllers\organizace\DetailMistaActionController;
use app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\table\SeznamPrirazenychAktivitTable;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\organizace\aktivity\sada\OrganizaceSadaAktivitRow;
use app\system\model\organizace\aktivity\sada\OrganizaceSadaAktivit;
use app\system\model\organizace\aktivity\sada\OrganizaceSadaAktivity;
use app\system\model\organizace\aktivity\aktivita\OrganizaceAktivity;
use app\system\modul\modal\AjaxModal;
use app\system\modul\modal\styles\ModalScaleEnum;

/** Created by <PERSON><PERSON>. Date: 26.01.2023 */
class EditMistaSadaAktivitModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Úprava sady';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnEditSada', function($post) {
         if($sada = OrganizaceSadaAktivit::get($post['id_sada'])) {
            $this->editSada($sada, $post['nazev_sady'], $post['popis']);
            FlashMessages::setSuccess('Seznam byl upraven');
         }
      });

      $this->isset('btnOdstarnitAktivitu', function($post) {
         OrganizaceSadaAktivity::delete($post['odstranit_id_sada'], $post['odstranit_id_aktivita']);
         FlashMessages::setSuccess('Aktivita odstraněna ze seznamu');
      });
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'sada' => $this->sada,
         'aktivityTable' => (string)(new SeznamPrirazenychAktivitTable())->setIdSady($this->sada->id_sada),
         'id_modal' => $this->getIdModal(),
         'baseUrl' => DetailMistaActionController::getUrl($this->sada->id_mista),
         'id_mista' => $this->sada->id_mista,
         'ukolyDostupne' => Templater::prepare(__DIR__ . '/dropdown-dostupne-ukoly.latte', [
            'ukoly' => OrganizaceAktivity::getNeprirazene($this->sada->id_sada, $this->sada->id_mista),
            'id_sada' => $this->sada->id_sada,
            'id_mista' => $this->sada->id_mista,
            'id_modal' => $this->getIdModal(),
            'baseUrl' => DetailMistaActionController::getUrl($this->sada->id_mista)
         ]),
      ]);
   }

   public function prepareAjaxData() :void {
      $this->sada = OrganizaceSadaAktivit::get($_POST['slug']);
   }

   private function editSada(OrganizaceSadaAktivitRow $sada, string $nazev, string $popis) :void {
      if($sada->nazev !== $nazev)
         $sada->nazev = $nazev;

      if($sada->popis !== $popis)
         $sada->popis = $popis;

      $sada->save();
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::LARGE;


   protected OrganizaceSadaAktivitRow $sada;
}