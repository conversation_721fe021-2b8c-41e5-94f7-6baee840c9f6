<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\table;

use app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\modal\sada\edit\EditMistaSadaAktivitModal;
use app\System;
use app\system\helpers\HtmlBuilder;
use app\system\model\organizace\aktivity\sada\OrganizaceSadaAktivit;
use app\system\table\DynamicTable;
use Dibi\Fluent;

/** Created by <PERSON><PERSON>. Date: 30.01.2023 */
class SeznamMistaSadaAktivitTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addText('nazev', 'Název');
      $this->addText('id.id_sada', '-')
         ->setOrderable(false)
         ->setSearchable(false)
         ->setFormatter(function($value) {
            return HtmlBuilder::buildElement('a', array_merge([
               'href' => '#'
            ], EditMistaSadaAktivitModal::getShowAttributes($value)))
               ->setHtml(System::getTranslator()->translate('Detail'))->get();
         });
   }

   public function setIdMista(int $id_mista) :static {
      $this->addParametrQuery('misto', $id_mista);
      return $this;
   }

   function getQuery() :Fluent {
      return OrganizaceSadaAktivit::findByMisto($_GET['misto']);
   }
}