{varType int $id_sada}
{varType int $id_mista}
{varType string $id_modal}

<style>
    .select2-default {
        color: #999 !important;
        width: auto !important;
    }
</style>

<div class="dropdown text-end mx-2">
   <a class="form-control btn btn-primary btn-sm dropdown-toggle" id="dostupne-aktivity" data-bs-auto-close="outside"
      aria-expanded="false" data-bs-toggle="dropdown">{_'Vyberte aktivity'}</a>

   <div class="dropdown-menu dropdown-menu-lg" aria-labelledby="tisk-eventy-od-dropdown">
      <div class="m-2">
            <div class="input-group">
               <select class="form-select js-novy-ukol-id" id="novy-aktivity" style="min-width: 100px;"></select>
            </div>

            <div class="row mt-2">
               <div class="col-auto">
                  <button class="btn btn-primary" id="js-pridat-do-sady">{_'Přidat do seznamu'}</button>
               </div>
            </div>
      </div>
   </div>
</div>

<script>
    $(function () {
        initSelect2();
        $('#js-pridat-do-sady').prop('disabled', true);

        $('body').on('click', '#js-pridat-do-sady', function (e) {
            e.preventDefault();
            let ids_aktivity = $('#novy-aktivity').val();
            let id_sada = {$id_sada};

            ajaxHandler.post({$baseUrl} +'/add_sada', { ids_aktivity, id_sada}, function (response) {
                $('#novy-aktivity').select2().val(null).trigger("change");
                initSelect2();
                $('#seznam-aktivit').html(response['aktivityTable'])

            });
        }).on('change', '#novy-aktivity', function () {
           let ids = $('#novy-aktivity').find(':selected').length;
           $('#js-pridat-do-sady').prop('disabled', !(ids > 0));
        });

        function initSelect2() {
            let aktivity = $('#novy-aktivity');
            aktivity.select2({
                dropdownParent: $('#' + {$id_modal}),
                placeholder: "{_'Výběr aktivit'}",
                delay: '350ms',
                width: '300px',
                multiple: true,
                language: {
                    noResults: function (params) {
                        $('#js-pridat-do-sady').prop('disabled', true);
                        return "{_'Žádné další aktivity.'}";
                    }
                },
                ajax: {
                    url: {$baseUrl} +'/findaktivity',
                    dataType: 'json',
                    type: 'POST',
                    data: function (params) {
                        return {
                            id_sada: {$id_sada},
                            id_mista: {$id_mista},
                            search: params['term'],
                        };
                    },
                    processResults: function (data) {
                        return {
                            results: data,
                        };
                    },
                },
            });
        }
    });
</script>