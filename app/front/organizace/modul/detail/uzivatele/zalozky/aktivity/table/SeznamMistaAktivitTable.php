<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\table;

use app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\modal\aktivita\edit\EditMistaAktivityModal;
use app\System;
use app\system\helpers\HtmlBuilder;
use app\system\model\organizace\aktivity\aktivita\OrganizaceAktivity;
use app\system\table\DynamicTable;
use Dibi\Fluent;

/** Created by <PERSON><PERSON>. Date: 26.01.2023 */
class SeznamMistaAktivitTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addText('nazev', 'Název');
      $this->addText('termin', 'Počet dnů od eventu');
      $this->addText('id.id_aktivita', '-')
         ->setOrderable(false)
         ->setSearchable(false)
         ->setFormatter(function($value) {
            return HtmlBuilder::buildElement('a', array_merge([
               'href' => '#'
            ], EditMistaAktivityModal::getShowAttributes($value)))->setHtml(System::getTranslator()->translate('Detail'))->get();
         });
   }

   public function setIdMista(int $id_mista) :static {
      $this->addParametrQuery('misto', $id_mista);
      return $this;
   }

   function getQuery() :Fluent {
      return OrganizaceAktivity::findByMisto($_GET['misto']);
   }
}