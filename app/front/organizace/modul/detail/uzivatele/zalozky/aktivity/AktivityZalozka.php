<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\aktivity;

use app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\modal\sada\novy\NovaMistaSadaAktivitModal;
use app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\modal\aktivita\novy\NovaMistaAktivitaModal;
use app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\table\SeznamMistaSadaAktivitTable;
use app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\table\SeznamMistaAktivitTable;
use app\system\component\Templater;
use app\system\Environment;
use app\system\model\organizace\Misto;
use app\system\modul\zalozky\Zalozka;

/** Created by Filip Pavlas. Date: 26.01.2023 */
class AktivityZalozka extends Zalozka
{

   public function getTitle() :string {
      return 'Aktivity';
   }

   public function setMisto(Misto $misto) :static {
      $this->misto = $misto;
      return $this;
   }

   protected function prepareTemplater(Templater $templater) :void {
      $templater->addData([
         'misto' => $this->misto->mistoRow,
         'tableAktivity' => (string)(new SeznamMistaAktivitTable())->setIdMista($this->misto->mistoRow->id_organizace),
         'tableSady' => (string)(new SeznamMistaSadaAktivitTable())->setIdMista($this->misto->mistoRow->id_organizace),
         'rootUrl' => Environment::getRealUrl(),
         'novySadyAttr' => NovaMistaSadaAktivitModal::init()->setIdMista($this->misto->mistoRow->id_organizace)->btnToggleAttributes(),
         'novaAktivitaAttr' => NovaMistaAktivitaModal::init()->setIdMista($this->misto->mistoRow->id_organizace)->btnToggleAttributes(),
      ]);
   }

   protected Misto $misto;
}