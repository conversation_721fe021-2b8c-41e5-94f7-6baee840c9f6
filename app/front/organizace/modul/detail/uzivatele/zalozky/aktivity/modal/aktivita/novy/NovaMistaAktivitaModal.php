<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\modal\aktivita\novy;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\organizace\aktivity\aktivita\OrganizaceAktivity;
use app\system\modul\modal\Modal;
use app\system\modul\modal\styles\ModalScaleEnum;

/** Created by <PERSON><PERSON>. Date: 26.01.2023 */
class NovaMistaAktivitaModal extends Modal
{

   public function getTitleName() :string {
      return 'Nová aktivita';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnVytvorAktivitu', function($post) {
         OrganizaceAktivity::create($post['id_mista'], $post['termin'], $post['nazev'], $post['popis'] );
         FlashMessages::setSuccess('Ukol byl vytvořen');
      });
   }

   public function setIdMista(int $id_mista) :static {
      $this->id_mista = $id_mista;
      return $this;
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'id_mista' => $this->id_mista,
      ]);
   }
   protected ModalScaleEnum $scale = ModalScaleEnum::SMALL;

   protected int $id_mista;
}