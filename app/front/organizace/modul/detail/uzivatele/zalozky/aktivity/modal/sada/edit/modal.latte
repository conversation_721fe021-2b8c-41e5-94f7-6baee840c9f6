{varType app\system\model\organizace\aktivity\sada\OrganizaceSadaAktivitRow $sada}
{varType string $aktivityTable}
{varType string $ukolyDostupne}

<form method="post">
   <div class="container">
      <div class="col-12">
            <div class="form-floating">
               <input type="text" class="form-control" name="nazev_sady" value="{$sada->nazev}" maxlength="100">
               <label>{_'Název seznamu aktivit'}</label>
            </div>
      </div>
   </div>
   <div class="container mt-2">
      <div class="col-12">
            <div class="form-floating">
               <textarea type="text" class="form-control" name="popis" maxlength="255" style="height: 100px">{$sada->popis?? ''}</textarea>
               <label>{_'Poznámka'}</label>
            </div>
      </div>
   </div>

   <div class="row mt-4">
      <div class="col-auto">
         <h4 class="lead">{_'Aktivity v seznamu'}</h4>
      </div>
      <div class="col d-flex justify-content-end">
         {$ukolyDostupne|noescape}
      </div>
   </div>
   <div class="row mt-2">
      <div id="seznam-aktivit">
         {$aktivityTable|noescape}
      </div>
   </div>
   <div class="row d-flex justify-content-center mt-4">
      <div class="col-auto">
         <input type="hidden" name="id_sada" value="{$sada->id_sada}">
         <input type="hidden" name="id_mista" value="{$sada->id_mista}">
         <input type="submit" name="btnEditSada" class="btn btn-primary w-auto shaddow-hover" value="{_'Uložit změny'}">
      </div>
   </div>
</form>
