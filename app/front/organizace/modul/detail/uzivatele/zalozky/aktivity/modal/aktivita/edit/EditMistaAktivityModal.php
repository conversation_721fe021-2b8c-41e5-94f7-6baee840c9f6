<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\modal\aktivita\edit;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\organizace\aktivity\aktivita\OrganizaceAktivity;
use app\system\model\organizace\aktivity\aktivita\OrganizaceAktivityRow;
use app\system\modul\modal\AjaxModal;
use app\system\modul\modal\styles\ModalScaleEnum;

/** Created by <PERSON><PERSON>. Date: 26.01.2023 */
class EditMistaAktivityModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Úprava aktivity';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUlozitAktivita', function($post) {
         OrganizaceAktivity::edit($post['id_aktivita'], $post['id_mista'], $post['termin'], $post['nazev'], $post['popis']);
         FlashMessages::setSuccess('Aktivita byla upravena');
      });
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'aktivita' => $this->aktivita,
         'id_mista' => $this->aktivita->id_mista,
      ]);
   }

   public function prepareAjaxData() :void {
      $this->aktivita = OrganizaceAktivity::get($_POST['slug']);
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::SMALL;

   protected OrganizaceAktivityRow $aktivita;
}