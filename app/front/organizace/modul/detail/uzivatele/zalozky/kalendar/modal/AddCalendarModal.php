<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\kalendar\modal;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\organizace\kalendar\eventy\SaveOrganizaceKalendarEvent;
use app\system\model\organizace\personal\Personal;
use app\system\modul\modal\Modal;
use Nette\Utils\Random;

class AddCalendarModal extends Modal
{

   public function getTitleName() :string {
      return 'Nový iKalendář';
   }

   public function prepareModal(Templater $templater) :void {
      $templater->addData([
         'kalendarRow' => null,
         'selectedPersonalIDS' => [],
         'randomID' => Random::generate(),
         'personal' => Personal::getByMisto($this->organizaceID),
      ]);
   }
   public function preparePostListeners() :void {
      $this->isset('btnAddNewCalendar', function($post) {
         if(SaveOrganizaceKalendarEvent::create($post, $this->organizaceID)->call()->getSuccess())
            FlashMessages::setSuccess('Kalendář byl přid<PERSON>');
      });
   }

   public function setOrganizaceID(int $organizaceID) :static {
      $this->organizaceID = $organizaceID;
      return $this;
   }

   private int $organizaceID;
}