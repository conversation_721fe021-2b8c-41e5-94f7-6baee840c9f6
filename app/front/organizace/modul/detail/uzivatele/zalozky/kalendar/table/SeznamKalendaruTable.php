<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\kalendar\table;

use app\front\organizace\modul\detail\uzivatele\zalozky\kalendar\modal\EditCalendarModal;
use app\System;
use app\system\helpers\HtmlBuilder;
use app\system\model\organizace\kalendar\OrganizaceKalendar;
use app\system\model\organizace\kalendar\OrganizaceKalendarPersonal;
use app\system\model\organizace\kalendar\OrganizaceKalendarPersonalRow;
use app\system\model\organizace\kalendar\OrganizaceKalendarRow;
use app\system\model\organizace\personal\Personal;
use app\system\table\DynamicTable;
use app\system\table\DynamicTableColumn;
use Dibi\Fluent;

class SeznamKalendaruTable extends DynamicTable
{

   public function prepareTable() :void {
      $this->addText('nazev', 'Název');

      $this
         ->appendTypExportu()
         ->setWidth(DynamicTableColumn::WIDTH_10);

      $this
         ->appendAssignedPersonal()
         ->setWidth(DynamicTableColumn::WIDTH_15);

      $this
         ->addDate('created', 'Datum vytvoření')
         ->setWidth(DynamicTableColumn::WIDTH_10);

      $this
         ->appendButtons()
         ->setWidth(DynamicTableColumn::WIDTH_10);
   }

   public function setOrganizaceID(int $id_mista) :static {
      $this->addParametrQuery('organizaceID', $id_mista);
      $this->id_organizace = $id_mista;
      return $this;
   }

   public function getOrganizaceID() :int {
      return $this->id_organizace ??= $_GET['organizaceID'];
   }

   public function getQuery() :Fluent {
      return OrganizaceKalendar::findByMisto($this->getOrganizaceID());
   }

   private function appendTypExportu() :DynamicTableColumn {
      return $this->addSelect(
         'nastaveni.id',
         'Typ exportu',
         [
            1 => System::getTranslator()->translate('Eventy'),
            2 => System::getTranslator()->translate('Směny'),
            3 => System::getTranslator()->translate('Aktivity'),
         ]
      )->setFormatter(
         function(int $value, OrganizaceKalendarRow $row) {
            $rStrings = [];

            if($row->eventy)
               $rStrings[] = System::getTranslator()->translate('Eventy');

            if($row->smeny)
               $rStrings[] = System::getTranslator()->translate('Směny');

            if($row->aktivity)
               $rStrings[] = System::getTranslator()->translate('Aktivity');

            return implode('</br>', $rStrings);
         }
      )->setSearchCallback(
         function(Fluent $query, int $search) {
            if($search === 1)
               $query->where('ok.eventy = 1');

            elseif($search === 2)
               $query->where('ok.smeny = 1');

            elseif($search === 3)
               $query->where('ok.aktivity = 1');
         }
      )->setOrderable(false);
   }

   private function appendButtons() :DynamicTableColumn {
      return $this->addText('buttons.id', ' ')
         ->setFormatter(
            function(int $value, OrganizaceKalendarRow $row) {
               return sprintf(
                  '%s&nbsp;%s',

                  HtmlBuilder::buildElement('a', array_merge([
                     'href' => '#'
                  ], EditCalendarModal::getShowAttributes($value)))->setHtml('Detail')->get(),

                  HtmlBuilder::buildElement('button', [
                     'type' => 'button',
                     'class' => 'btn btn-sm btn-outline-primary d-flex align-items-center gap-1',
                     'data-copy-to-clipboard' => $row->getUrlLink(),
                  ])->setHtml(sprintf(
                     '<i class="bi bi-clipboard"></i><span>%s</span>',
                     System::getTranslator()->translate('Odkaz'),
                  ))->get()
               );
            }
         )->setSearchable(false)->setOrderable(false);
   }

   private function appendAssignedPersonal() :DynamicTableColumn {
      $this->addDataPreparator(
         function(array $rows) {
            $kalendareID = array_map(
               function(OrganizaceKalendarRow $row) {
                  return $row->id;
               },
               $rows
            );

            if(empty($kalendareID))
               return;

            $this->kalendarePersonal = OrganizaceKalendarPersonal::getForKalendare($kalendareID);
         }
      );

      $personal = [];

      foreach(Personal::getByMisto($this->getOrganizaceID()) as $personalRow)
         $personal[$personalRow->id_personal] = $personalRow->getFullName();

      return $this->addSelect(
         'personal.id',
         'Personál',
         [
            0 => System::getTranslator()->translate('Bez omezení'),
         ] + $personal
      )->setFormatter(
         function(int $value, OrganizaceKalendarRow $row) use($personal) {
            if(!isset($this->kalendarePersonal[$value]))
               return System::getTranslator()->translate('Bez omezení');

            $rStrings = [];

            foreach($this->kalendarePersonal[$value] as $kalendarPersonalRow)
               $rStrings[] = $personal[$kalendarPersonalRow->id_personal] ?? System::getTranslator()->translate('Neznámy personál');

            return implode('</br>', $rStrings);
         }
      )->setSearchCallback(
         function(Fluent $query, int $search) {
            if($search === 0){
               $query
                  ->leftJoin(OrganizaceKalendarPersonal::TABLE, 'okp')
                  ->on('ok.id = okp.%n', OrganizaceKalendarPersonal::PK_KALENDAR)
                  ->where('okp.%n IS NULL', OrganizaceKalendarPersonal::PK_PERSONAL);

               return;
            }

            $query
               ->join(OrganizaceKalendarPersonal::TABLE, 'okp')
               ->on('ok.id = okp.%n', OrganizaceKalendarPersonal::PK_KALENDAR)
               ->where('okp.%n = %i', OrganizaceKalendarPersonal::PK_PERSONAL, $search);
         }
      )->setOrderable(false);
   }

   protected int $id_organizace;

   /** @var OrganizaceKalendarPersonalRow[][] */
   protected array $kalendarePersonal;
}