<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\kalendar\modal;

use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\model\organizace\kalendar\eventy\DeleteOrganizaceKalendarEvent;
use app\system\model\organizace\kalendar\eventy\SaveOrganizaceKalendarEvent;
use app\system\model\organizace\kalendar\OrganizaceKalendar;
use app\system\model\organizace\kalendar\OrganizaceKalendarPersonal;
use app\system\model\organizace\kalendar\OrganizaceKalendarPersonalRow;
use app\system\model\organizace\kalendar\OrganizaceKalendarRow;
use app\system\model\organizace\personal\Personal;
use app\system\modul\modal\AjaxModal;
use Nette\Utils\Random;

class EditCalendarModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Editace iKalendáře';
   }

   public function preparePostListeners() :void {
     $this->isset('btnDeleteCal', function($post) {
        $id_kalendar = (int)$post['id_kalendar'];
        $kaldenarRow = OrganizaceKalendar::getById($id_kalendar);

        if (
           !$kaldenarRow ||
           FrontApplicationEnvironment::get()->id_organizace !== $kaldenarRow->id_organizace
        ) {
           throw FlashException::create('Došlo k chybě');
        }

        if((new DeleteOrganizaceKalendarEvent($kaldenarRow))->call()->getSuccess())
           FlashMessages::setSuccess('Kalendář byl smazán');
      });

     $this->isset('btnEditCal', function($post) {
         if(($kalendarRow = OrganizaceKalendar::getById((int)$post['id_kalendar'])) === null || $kalendarRow->id_organizace !== FrontApplicationEnvironment::get()->id_organizace)
            throw FlashException::create('Došlo k chybě');

         if((new SaveOrganizaceKalendarEvent($kalendarRow, $post))->call()->getSuccess())
            FlashMessages::setSuccess('Kalendář byl upraven');
      });
   }

   public function prepareModal(Templater $templater) :void {
      $templater->addData([
         'kalendarRow' => $this->kalendarRow,
         'randomID' => Random::generate(),
         'selectedPersonalIDS' => array_map(
            fn(OrganizaceKalendarPersonalRow $row) => $row->id_personal,
            OrganizaceKalendarPersonal::getByKalendarID($this->kalendarRow->id)
         ),
         'personal' => Personal::getByMisto(FrontApplicationEnvironment::get()->id_organizace),
      ]);
   }

   public function prepareAjaxData() :void {
      $this->kalendarRow = OrganizaceKalendar::getById((int)$_POST['slug']);
   }

   private OrganizaceKalendarRow $kalendarRow;
}