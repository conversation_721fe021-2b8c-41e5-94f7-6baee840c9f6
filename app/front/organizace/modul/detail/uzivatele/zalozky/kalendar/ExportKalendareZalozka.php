<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\kalendar;

use app\front\organizace\model\organizace\OrganizaceRow;
use app\front\organizace\modul\detail\uzivatele\zalozky\kalendar\modal\AddCalendarModal;
use app\front\organizace\modul\detail\uzivatele\zalozky\kalendar\table\SeznamKalendaruTable;
use app\system\component\Templater;
use app\system\modul\zalozky\Zalozka;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>zy<PERSON>. Date: 04.08.2025 */
class ExportKalendareZalozka extends Zalozka
{

   public function getTitle() :string {
      return 'iKalendář';
   }

   public function setOrganizace(OrganizaceRow $organizaceRow) :static {
      $this->organizace = $organizaceRow;
      return $this;
   }

   protected function prepareTemplater(Templater $templater) :void {
      $templater->addData([
         'addCalendarModal' => AddCalendarModal::init()->btnToggleAttributes(),
         'calendarTable' => new Html((new SeznamKalendaruTable())->setOrganizaceID($this->organizace->id_organizace)),
      ]);
   }

   private OrganizaceRow $organizace;
}