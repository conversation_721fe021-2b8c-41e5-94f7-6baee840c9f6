{varType app\system\model\organizace\personal\PersonalRow[] $personal}
{varType int $randomID}
{varType ?app\system\model\organizace\kalendar\OrganizaceKalendarRow $kalendarRow}
{varType array $selectedPersonalIDS}

<div class="container mt-4">
   <form method="post" id="form-{$randomID}">
      {if $kalendarRow}
         <input type="hidden" name="id_kalendar" value="{$kalendarRow->id}">
         <div class="row">
            <span class="col-auto">{_'URL odkaz kalendáře:'} </span>
            <span class="col-auto text-primary">{$kalendarRow->getUrlLink(true)}</span>
            <button type="button" class="col-auto btn btn-outline-secondary btn-sm" data-copy-to-clipboard="{$kalendarRow->getUrlLink(true)}">
               <i class="bi bi-copy me-1"></i>{_'Kopírovat'}
            </button>
         </div>
      {/if}
      <div class="row mb-3">
         <div class="col-md-6 mb-3">
            <label for="nazev" class="form-label fw-bold">{_'Zadej název'}</label>
            <input
                    type="text"
                    class="form-control"
                    id="nazev"
                    name="nazev"
                    placeholder="{_'Např. Osobní kalendář'}"
                    n:attr="value => $kalendarRow?->nazev"
            >
         </div>

         <div class="col-md-6 mb-3">
            <label for="selectPersonal-{$randomID}" class="form-label fw-bold">{_'Vyberte personál'}</label>
            <select
                    class="form-select"
                    name="personal[]"
                    id="selectPersonal-{$randomID}"
                    multiple
                    data-placeholder="{_'Vyberte personál'}"
            >
               {foreach $personal as $osoba}
                  <option
                     value="{$osoba->id_personal}"
                     n:attr="selected => in_array($osoba->id_personal, $selectedPersonalIDS)"
                  >
                     {$osoba->jmeno} {$osoba->prijmeni}
                  </option>
               {/foreach}
            </select>
         </div>
      </div>

      <div class="row mb-3">
         <div class="col-md-4">
            <div>
               <div class="form-check form-switch">
                  <input class="form-check-input switch-eventy js-eventy-input"
                         type="checkbox"
                         name="eventy"
                         id="eventy-{$randomID}"
                          n:attr="checked => $kalendarRow?->eventy == 1 ? 'checked' : null">
                  <label class="form-check-label" for="eventy">{_'Zobrazit eventy'}</label>
               </div>
               <div class="form-check form-switch">
                  <i class="bi bi-arrow-return-right mx-2 text-primary"></i>
                  <input class="form-check-input switch-eventy js-anonym-input"
                         type="checkbox"
                         name="anonymni"
                         id="anonym-{$randomID}"
                         n:attr="checked => $kalendarRow?->anonymni == 1 ? 'checked' : null">
                  <label class="form-check-label" for="anonym">{_'Zobrazit bez detailů eventu'}</label>
               </div>
            </div>
         </div>

         <div class="col-md-4">
            <div class="form-check form-switch">
               <input class="form-check-input switch-smeny"
                      type="checkbox"
                      name="smeny"
                      id="smeny"
                       n:attr="checked => $kalendarRow?->smeny == 1 ? 'checked' : null">
               <label class="form-check-label" for="smeny">{_'Zobrazit směny'}</label>
            </div>
         </div>

         <div class="col-md-4">
            <div class="form-check form-switch">
               <input class="form-check-input switch-aktivity"
                      type="checkbox"
                      name="aktivity"
                      id="aktivity"
                       n:attr="checked => $kalendarRow?->aktivity == 1 ? 'checked' : null">
               <label class="form-check-label" for="aktivity">{_'Zobrazit aktivity'}</label>
            </div>
         </div>
      </div>

      <div class="d-flex justify-content-center gap-3">
         <button type="submit"
                 name="btnDeleteCal"
                 class="btn btn-outline-secondary px-4 no-validate"
                 data-confirm="{_'Opravdu chcete smazat iKalendář?'}"
                 formnovalidate
                 n:if="$kalendarRow">
            {_'Smazat'}
         </button>

         <button type="submit"
                 n:attr="name => $kalendarRow ? 'btnEditCal' : 'btnAddNewCalendar'"
                 class="btn btn-primary px-4">
            {$kalendarRow ? 'Uložit' : 'Vytvořit'}
         </button>
      </div>
   </form>

</div>

<script>
   $(function() {
      const form = $('#form-' + {$randomID});
      const select = form.find('select[name="personal[]"]');

      const anonym = $('#anonym-' + {$randomID});
      const eventy = $('#eventy-' + {$randomID});

      anonym.prop('disabled', !eventy.prop('checked'));

      eventy.on('change', function() {
         anonym.prop('disabled', !$(this).prop('checked'));

         if(!eventy.prop('checked'))
            anonym.prop('checked', false);
      });

      select.select2({
         width: '100%',
         dropdownParent: form.closest('.modal'),
         multiple: true,
      });

      form.validate({
         ignore: '.no-validate',
         rules: {
            nazev: {
               required: true
            },
         },
         submitHandler: function (formEl, event) {
            const checkedCount = form.find('.switch-eventy:checked, .switch-smeny:checked, .switch-aktivity:checked').length;

            if (checkedCount === 0) {
               const $switchRow = form.find('.row.mb-3').last();

               const errorText = `{_'Zaškrtněte alespoň jednu možnost (eventy, směny nebo aktivity).'}`;
               const errorEl = $('<label class="is-invalid text-danger error">' + errorText + '</label>');

               $switchRow.find('label.error').remove();
               $switchRow.append(errorEl);
               return;
            }

            formEl.submit();
         }
      });
   });
</script>