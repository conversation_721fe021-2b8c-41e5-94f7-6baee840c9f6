<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\pozice\modal\edit;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\organizace\pozice\PozicePersonal;
use app\system\model\organizace\pozice\PozicePersonalRow;
use app\system\modul\modal\AjaxModal;
use app\system\modul\modal\styles\ModalScaleEnum;

class EditPozicePersonalModal extends AjaxModal
{

   protected function preparePostListeners() :void {
      $this->isset('btnSavePozice', function($post) {
         PozicePersonal::saveFromPost($post);
         FlashMessages::setSuccess('Pozice personálu byla změněna');
      });

      $this->isset('btnDeletePozice', function($post) {
         PozicePersonal::delete((int)$post['id_pozice']);
         FlashMessages::setSuccess('Pozice personálu byla smazána');
      });
   }

   public function getTitleName() :string {
      return 'Úprava pozice';
   }

   public function prepareAjaxData() :void {
      $this->pozice = PozicePersonal::get($_POST['slug']);
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'pozice' => $this->pozice,
      ]);
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::SMALL;

   protected PozicePersonalRow $pozice;
}