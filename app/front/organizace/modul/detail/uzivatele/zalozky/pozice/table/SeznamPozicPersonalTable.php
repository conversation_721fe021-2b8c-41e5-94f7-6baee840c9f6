<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\pozice\table;

use app\front\organizace\modul\detail\uzivatele\zalozky\pozice\modal\edit\EditPozicePersonalModal;
use app\System;
use app\system\helpers\HtmlBuilder;
use app\system\model\organizace\pozice\PozicePersonal;
use app\system\table\DynamicTable;
use Dibi\Fluent;

class SeznamPozicPersonalTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addText('nazev', 'Název');
      $this->addText('id.id_pozice', '-')
         ->setOrderable(false)
         ->setSearchable(false)
         ->setFormatter(function($value) {
               return HtmlBuilder::buildElement('a', array_merge([
                  'href' => '#'
               ], EditPozicePersonalModal::getShowAttributes($value)))
                  ->setHtml(System::getTranslator()->translate('Detail'))->get();
         });
   }

   public function setIdMista(int $id_mista) :static {
      $this->addParametrQuery('misto', $id_mista);
      return $this;
   }

   function getQuery() :Fluent {
      return PozicePersonal::findByMisto($_GET['misto']);
   }
}