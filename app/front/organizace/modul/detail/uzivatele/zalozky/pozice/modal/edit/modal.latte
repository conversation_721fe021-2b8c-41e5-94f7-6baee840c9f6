{varType app\system\model\organizace\pozice\PozicePersonalRow $pozice}

<form method="post" id="organizationEditPosition">
   <div class="container">
      <div class="col-12">
         <div class="form-floating">
            <input type="text" id="nazev_pozice" name="nazev" class="form-control" value="{$pozice->nazev}" maxlength="50" required>
            <label for="nazev_pozice">{_'Název'}</label>
         </div>
      </div>
   </div>
   <div class="row d-flex justify-content-center mt-4">
      <input type="hidden" name="id_mista" value="{$pozice->id_mista}">
      <input type="hidden" name="id_pozice" value="{$pozice->id_pozice}">
      <div class="col-auto">
         <input type="submit" name="btnDeletePozice" class="btn btn-outline-secondary w-auto shaddow-hover no-validate"
                data-confirm="{_'Opravdu chcete pozici smazat?'}" value="{_'Smazat pozici'}" formnovalidate>
      </div>
      <div class="col-auto">
         <input type="submit" name="btnSavePozice" class="btn btn-primary w-auto shaddow-hover"
                data-confirm="{_'Opravdu chcete změnit pozici?'}" value="{_'Uložit změny'}">
      </div>
   </div>
</form>
<script>
   $(function() {
      const form = $("form#organizationEditPosition");

      form.validate({
         ignore: ".no-validate",
      });
   })
</script>