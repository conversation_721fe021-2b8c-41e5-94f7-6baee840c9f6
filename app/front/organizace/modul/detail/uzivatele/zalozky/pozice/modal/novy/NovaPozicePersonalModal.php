<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\pozice\modal\novy;

use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\organizace\pozice\PozicePersonal;
use app\system\modul\modal\Modal;
use app\system\modul\modal\styles\ModalScaleEnum;

class NovaPozicePersonalModal extends Modal
{

   public function getTitleName() :string {
      return 'Nová pozice';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnCreatePozice', function($post) {
         PozicePersonal::saveFromPost($post);
         FlashMessages::setSuccess('Typ akce byl uložen');
      });
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'organizaceID' => FrontApplicationEnvironment::get()->id_organizace
      ]);
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::SMALL;

}