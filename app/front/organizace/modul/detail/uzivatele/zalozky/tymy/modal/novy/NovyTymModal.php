<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\tymy\modal\novy;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\tymy\TymyRow;
use app\system\modul\modal\Modal;
use app\system\modul\modal\styles\ModalScaleEnum;

/** Created by <PERSON>. Date: 14.01.2023 */
class NovyTymModal extends Modal
{

   public function getTitleName() :string {
      return 'Přídat tým';
   }

   public function setMisto(int $id_mista) :static {
      $this->id_mista = $id_mista;
      return $this;
   }

   protected function preparePostListeners() :void {
      $this->isset('btnPridatTym', function($post) {
         $rowTym = (new TymyRow())->createFromPost($post);
         $rowTym->save();
         FlashMessages::setSuccess('Tým byl vytvořen');
      });
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'id_misto' => $this->id_mista,
      ]);
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::SMALL;

   protected int $id_mista;
}