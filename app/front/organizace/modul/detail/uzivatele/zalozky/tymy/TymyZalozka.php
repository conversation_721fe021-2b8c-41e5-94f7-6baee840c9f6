<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\tymy;

use app\front\organizace\modul\detail\uzivatele\zalozky\tymy\modal\novy\NovyTymModal;
use app\front\organizace\modul\tables\SeznamTymuTable;
use app\system\component\Templater;
use app\system\modul\zalozky\Zalozka;

/** Created by <PERSON>. Date: 12.01.2023 */
class TymyZalozka extends Zalozka
{

   public function getTitle() :string {
      return 'Týmy';
   }

   public function setMisto(int $id_mista) :static {
      $this->id_mista = $id_mista;
      return $this;
   }

   protected function prepareTemplater(Templater $templater) :void {
      $templater->addData([
         'tableTymy' => (new SeznamTymuTable())->setMisto($this->id_mista),
         'novyTym' => NovyTymModal::init()->btnToggleAttributes(),
      ]);
   }

   protected int $id_mista;
}