{varType app\system\model\tymy\TymyRow $tym}
{varType string $action_addPersonal}
{varType string $action_removePersonal}
{varType string $action_removeTeam}

<form method="post" id="organizationEditTeam">
   <div class="row p-3">
      <div class="col-12">
         <div class="form-floating">
            <input type="text" id="name" name="nazev" class="form-control" value="{$tym->nazev}" required
                   maxlength="120">
            <label for="name">{_'Název týmu'}</label>
         </div>
      </div>
   </div>
   <div class="row d-flex justify-content-center mt-3 p-3">
      <div class="border border-light-gray radius-card p-3">
         <div class="input-group">
            <label class="input-group-text" for="tymSelect">{_'Vyberte personál'}</label>
            <select id="tymSelect" class="form-select">
               <option></option>
               {foreach $potencialniPersonal as $personal}
                  <option value="{$personal->id_personal}">{$personal->jmeno} {$personal->prijmeni}</option>
               {/foreach}
            </select>
            <button id="btnPridatClenaTymu" class="btn btn-primary"><i
                          class="align-middle bi bi-plus-lg me-1-circle"></i>{_'Přidat do týmu'}</button>
         </div>
         <div class="row mt-5">
            <h3 class="lead">{_'Přehled personálu v týmu'}</h3>
         </div>
         <div class="row">
            {$clenoveTymu|noescape}
         </div>
      </div>
   </div>
   <div class="row d-flex justify-content-center mt-4">
       <div class="col-auto">
            <input type="submit" value="{_'Smazat tým'}" data-confirm="{_'Opravdu chcete smazat tým?'}"
                   name="btnOrganizaceSmazatTym" class="btn btn-outline-secondary w-auto shaddow-hover">
       </div>
       <div class="col-auto">
          <input type="hidden" id="id_tym" name="id_tym" value="{$tym->id_tym}">
          <input type="hidden" id="id_mista" name="id_mista" value="{$tym->id_mista}">
          <input type="submit" name="btnUpravitTym" class="btn btn-primary w-auto shaddow-hover"
                value="{_'Uložit změny'}">
       </div>
   </div>
</form>

<script>
    checkSelect();
    const form = $("form#organizationEditTeam");
    $('#tymSelect').select2({
        placeholder: "{_'Vyber personál'}",
        width: '300px',
        dropdownParent: $('#' + {$id_modal}),
    });

    $('body').on('change', '#tymSelect', function () {
        checkSelect();
    });

    function checkSelect(){
        if($('#tymSelect').val() === '')
            $('#btnPridatClenaTymu').prop('disabled', true);
        else
            $('#btnPridatClenaTymu').prop('disabled', false);
    }

    $('body')
        .on('click', '#btnPridatClenaTymu', function() {
            $('#btnPridatClenaTymu').prop('disabled', true);
            let id_personal = $('#tymSelect').val();
            let id_tym = $('#id_tym').val();
            ajaxHandler.post({$action_addPersonal}, {
                id_personalu: id_personal,
                id_tymu: id_tym,
            }, function() {
                $('#SeznamClenuTymuTable').DataTable().draw();
                $('#btnPridatClenaTymu').prop('disabled', false);
                $('#tymSelect').val(null).trigger('change');
            });
        })
        .on('click', '#btnOdebratPersonal', function() {
            let id_personal = $(this).attr('id_personal_action');
            let id_tym = $('#id_tym').val();
            ajaxHandler.post({$action_removePersonal}, {
                id_personalu: id_personal,
                id_tymu: id_tym,
            }, function() {
                $('#SeznamClenuTymuTable').DataTable().draw();
            });
        })

        form.validate();
</script>