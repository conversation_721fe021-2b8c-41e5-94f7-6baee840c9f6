<?php namespace app\front\organizace\modul\detail\uzivatele\zalozky\tymy\modal\edit;

use app\front\controllers\tymy\TymyActionController;
use app\front\organizace\modul\tables\SeznamClenuTymuTable;
use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\model\organizace\personal\Personal;
use app\system\model\tymy\event\DeleteOrganizaceTymEvent;
use app\system\model\tymy\event\EditOrganizaceTymEvent;
use app\system\model\tymy\Tymy;
use app\system\model\tymy\TymyRow;
use app\system\modul\modal\AjaxModal;
use app\system\modul\modal\styles\ModalScaleEnum;

/** Created by <PERSON>. Date: 14.01.2023 */
class EditTymModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Úprava týmu';
   }

   public function prepareModal(Templater $templater) {
      $id_tym = $_POST['slug'];

      $templater->addData([
         'tym' => $tym = Tymy::get($id_tym),
         'clenoveTymu' => (new SeznamClenuTymuTable())->setTym($id_tym),
         'potencialniPersonal' => Personal::getByMisto($tym->id_mista),
         'id_modal' => $this->getIdModal(),
         'action_addPersonal' => TymyActionController::getUrl('addpersonal'),
         'action_removePersonal' => TymyActionController::getUrl('removePersonal'),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUpravitTym', function($post) {
         $tym = $this->getAndCheckTym($post['id_tym']);

         if((new EditOrganizaceTymEvent($tym, $post['nazev']))->call()->getSuccess()){
            FlashMessages::setSuccess('Tým byl upraven');
         }
      });

      $this->isset('btnOrganizaceSmazatTym', function($post) {
         $tym = $this->getAndCheckTym($post['id_tym']);

         if((new DeleteOrganizaceTymEvent($tym))->call()->getSuccess()){
            FlashMessages::setSuccess('Tým byl smazán');
         }
      });
   }

   private function getAndCheckTym(int $tymID) :TymyRow {
      if(
         !($tym = Tymy::get($tymID))
         || FrontApplicationEnvironment::get()->id_organizace !== $tym->id_mista
      )
         throw FlashException::create('Tým nelze upravit.');

      return $tym;
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::LARGE;

}