<?php namespace app\front\organizace\modul\detail\uzivatele;

use app\front\controllers\organizace\DetailMistaController;
use app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\AktivityZalozka;
use app\front\organizace\modul\detail\uzivatele\zalozky\kalendar\ExportKalendareZalozka;
use app\front\organizace\modul\detail\uzivatele\zalozky\personal\PersonalZalozka;
use app\front\organizace\modul\detail\uzivatele\zalozky\pozice\PoziceZalozka;
use app\front\organizace\modul\detail\uzivatele\zalozky\pozice\table\SeznamPozicPersonalTable;
use app\front\organizace\modul\detail\uzivatele\zalozky\tymy\TymyZalozka;
use app\front\organizace\modul\tables\SeznamPrirazenychUzivateluTable;
use app\system\component\Templater;
use app\system\model\organizace\<PERSON>sto;
use app\system\modul\panels\Panel;

/** Created by <PERSON><PERSON>. Date: 30.03.2022 */

class PrirazeniUzivatelePanel extends Panel
{

   function getMenuName() :string {
      return 'Personál a uživatelé';
   }

   public function preparePanel(Templater $templater) :void {
      $templater->addData([
         'tableUzivatelu' => (string)(new SeznamPrirazenychUzivateluTable())->setIdMista($this->getParameter('id_mista')),
         'tablePozice' => (string)(new SeznamPozicPersonalTable())->setIdMista($this->getParameter('id_mista')),
         'baseUrl' => DetailMistaController::getUrl($this->getParameter('id_mista')),
      ]);
   }

   public function returnString(): ?string {
      $misto = Misto::get($this->getParameter('id_mista'));

      $this
         ->addZalozka(PersonalZalozka::init()
            ->setMisto($misto))
         ->addZalozka(PoziceZalozka::init()
            ->setMisto($misto))
         ->addZalozka(TymyZalozka::init()
            ->setMisto($this->getParameter('id_mista')))
         ->addZalozka(AktivityZalozka::init()
            ->setMisto($misto))
         ->addZalozka(ExportKalendareZalozka::init()
            ->setOrganizace($misto->mistoRow));

      return '';
   }


   protected int $id_mista;
}