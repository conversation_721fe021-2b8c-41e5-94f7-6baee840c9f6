<?php namespace app\front\organizace\modul\detail\nastaveni;

use app\system\model\organizace\localtax\OrganizaceLocalTax;
use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceNastaveni;
use app\front\controllers\organizace\DetailMistaActionController;
use app\front\organizace\modul\detail\nastaveni\modal\localtax\NastaveniLocalTaxModal;
use app\front\organizace\modul\detail\nastaveni\modal\NovaOrganizaceKalkulacePrilohaModal;
use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\model\nabidka\prilohy\PrilohyTypyEnum;
use app\system\model\organizace\meny\OrganizaceMeny;
use app\system\model\organizace\Misto;
use app\system\flash\FlashMessages;
use app\system\model\mista\images\ImageUploadHandler;
use app\system\model\mista\images\OrganizaceImages;
use app\system\model\mista\images\OrganizaceImagesRow;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\model\organizace\prilohy\kalkulace\data\OrganizacePrilohy;
use app\system\model\organizace\prilohy\kalkulace\event\EditOrganizacePrilohaEvent;
use app\system\model\organizace\personal\Personal;
use app\system\model\organizace\prilohy\OrganizaceFileUploadHandler;
use app\system\model\organizace\zdroje\OrganizaceZdroje;
use app\system\modul\panels\Panel;
use Nette\Http\FileUpload;

class NastaveniPanel extends Panel
{

   function getMenuName() :string {
      return 'Další nastavení';
   }

   public function preparePanel(Templater $templater) :void {
      $templater->addData([
         'organizace' => ($misto = Misto::get(FrontApplicationEnvironment::get()->id_organizace))->mistoRow,
         'nastaveni' => $misto->getNastaveni(),
         'jazyky' => $misto->getActiveJazyky(),
         'meny' => OrganizaceMeny::getAll($misto->mistoRow->id_organizace),
         'images' => OrganizaceImages::getForMisto($misto->mistoRow->id_organizace),
         'changeNameFileUrl' => DetailMistaActionController::getUrl(
            $misto->mistoRow->id_organizace,
            DetailMistaActionController::ACTION_SAVE_IMAGE_NAME
         ),
         'zdrojeMista' => OrganizaceZdroje::getByMisto($misto->mistoRow->id_organizace),
         'pridatPrilohuModalAttr' => NovaOrganizaceKalkulacePrilohaModal::init()->btnToggleAttributes(),
         'personal' => Personal::getWithRights(
            $misto->mistoRow->id_organizace,
            [PersonalAccessRightsEnum::ADMIN, PersonalAccessRightsEnum::POPTAVKY]
         ),
         'prilohy' => OrganizacePrilohy::getByOrganizace($misto->mistoRow->id_organizace),
         'editLocalTaxModalAttr' => NastaveniLocalTaxModal::init()->btnToggleAttributes(),
         'localTaxArray' => OrganizaceLocalTax::getAllForOrganizace($misto->mistoRow->id_organizace),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUlozitNastaveni', function($post) {
         OrganizaceNastaveni::insertNastaveni([
            'globalni_konec' => $post['globalni_konec'] ?: null,
            'id_mista' => $this->getParameter('id_mista'),
            'schvalovaci_proces' => ($post['schvalovaci_proces_nastaveni'] === 'true')? 1 : 0,
            'schvaleni_smlouvy' => ($post['schvalovaci_proces_smlouva'] === 'true')? 1 : 0,
//            'autosign_smlouva' => ($post['autosign_smlouva'] === 'true')? 1 : 0,
            'platnost_nabidky' => (int)$post['platnost_nabidky'] ?: null,
            'id_poptavkovy_zdroj' => (int)$post['id_zdroj_poptavky'],
            'platnost_smlouva' => intval($post['platnost_smlouva']) ?: null,
            'id_prirazovany_personal' => (int)$post['id_prirazovany_personal'],
         ]);
      });

//      @TODO smazat - vypnuto z důvodu předělání obrázků v tinyEditoru u#86c0x4yy1
      $this->isset('addNewImgAction', function($post) {
         FlashMessages::setError('Nelze přidat nové logo');
         return;

         $misto = Organizace::getMisto($this->getParameter('id_mista'));

         $fileName = (new ImageUploadHandler())
            ->setPostFile($_FILES['new_logo'])
            ->setMistoDirectory($misto->getImagesDirectory())
            ->upload()
            ->getFileName();

         $row = new OrganizaceImagesRow();
         $row->id_organizace = $misto->id_organizace;
         $row->file = $fileName;
         $row->save();
         FlashMessages::setSuccess('Fotka byla přidána');
      });

      $this->isset('btnDeleteImg', function($post) {
         OrganizaceImages::delete($post['btnDeleteImg']);
         FlashMessages::setWarning('Fotka smazána');
      });

      $this->isset('btnPridatPrilohu', function($post) {
         $organizace = Organizace::getMisto($this->getParameter('id_mista'));
         $typ = PrilohyTypyEnum::tryFrom($post['id_typ']);

         if(!$typ || !$organizace){
            FlashMessages::setError('Při ukládání se vyskytla chyba, zkuste to prosím za chvíli');
            return;
         }

         if($typ === PrilohyTypyEnum::ODKAZ){
            $nazev = trim($post['nazev']);
            $odkaz = trim($post['url']);

            if(!$odkaz || !$nazev){
               FlashMessages::setError('Název a odkaz musí být vyplněny');
               return;
            }

//            @TODO kontrola dostupnosti odkazu

         } elseif($typ === PrilohyTypyEnum::SOUBOR) {
            $nazev = trim($post['nazev']);

            if(!isset($_FILES['attachment'])){
               FlashMessages::setError('Nebyl vybrán žádný soubor');
               return;
            }

            $file = new FileUpload($_FILES['attachment']);

            if(!$file->isOk()){
               FlashMessages::setError('Soubor se nepodařilo nahrát, zkuste to později, nebo kontaktujte podporu');
               return;
            }

            $odkaz = (new OrganizaceFileUploadHandler($organizace))->saveFile($file);
         } else return;

         (new EditOrganizacePrilohaEvent(null))
            ->setInitData($organizace->id_organizace, $typ)
            ->setNazev($nazev)
            ->setData($odkaz)
            ->call();

         FlashMessages::setSuccess('Příloha byla vytvořena');
      });

      $this->isset('btnUpravitPrilohu', function($post) {
         if(
            !($priloha = OrganizacePrilohy::get($post['id_priloha']))
            || $priloha->id_organizace !== intval($this->getParameter('id_mista'))
         ){
            return;
         }

         $nazev = trim($post['nazev']);

         if(!$nazev){
            FlashMessages::setError('Název musí být vyplněný');
            return;
         }

         $event = (new EditOrganizacePrilohaEvent($priloha))
            ->setNazev($nazev);

         if(!$priloha->getTyp()->isFile()){
            if(!($odkaz = trim($post['url']))){
               FlashMessages::setError('Odkaz musí být vyplněný');
               return;
            }

//            @TODO kontrola dostupnosti odkazu
            $event
               ->setData($odkaz);
         }

         $event->call();

         FlashMessages::setSuccess('Příloha byla upravena');
      });
   }
}
