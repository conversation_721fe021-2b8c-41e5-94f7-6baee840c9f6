{varType app\system\model\organizace\jazyk\OrganizaceJazykRow[] $jazykyOrganizace}
{varType int $id_organizace}
{varType int $randomID}
{varType app\system\model\akce\typ\AkceTypNazevRow[] $typyAkci}
{varType app\system\model\organizace\sluzby\OrganizaceSluzbaNazevRow[] $systemSluzby}
{varType ?app\system\model\organizace\sluzby\OrganizaceSluzbyRow $sluzba}
{varType ?app\system\model\organizace\sluzby\OrganizaceSluzbaFotkyRow[] $photoGallery}

<form method="post" id="organizaceSluzba{$randomID}" enctype="multipart/form-data">
   <div class="row">
      {foreach $jazykyOrganizace as $jazyk}
         {var $id_jazyk = $jazyk->id_jazyk}
         {var $text = $sluzba?->getTexty($jazyk->getJazyk())}

         <div class="card">
            <div class="card-body">
               <div class="row">
                  <h4>
                     <img src="{$jazyk->getJazyk()->getIcon()}" width="25px" alt="{$jazyk->getJazyk()->getTitle()} flag">
                     {$jazyk->getJazyk()->getTitle()}
                  </h4>
               </div>
               <div class="row mb-3">
                  <label for="title_{$id_jazyk}-{$randomID}" class="form-label">
                     {_'Název služby'}&nbsp;-&nbsp;{$jazyk->getJazyk()->getTitle()}
                     <span n:if="$jazyk->isPrimary()" class="text-danger">&nbsp;*</span>
                  </label>
                  <input type="text"
                         class="form-control"
                         maxlength="60"
                         id="title_{$id_jazyk}-{$randomID}"
                         name="titles[{$id_jazyk}]"
                         value="{$text?->nazev ?? ''}"
                          n:attr="required: $jazyk->isPrimary()" />
                  <div class="form-text">
                     {_'Max. 60 znaků.'}
                  </div>
               </div>

               <div class="row mb-3">
                  {var $textName = sprintf('descriptions[%d]', $id_jazyk)}
                  {var $editor = app\system\tiny\TinyEditor::init()->setInputName($textName)->setContent($text?->popis)->setHeightPX(200)}

                  <label for="{$editor->getElementID()}" class="form-label">{_'Popis služby'}&nbsp;-&nbsp;{$jazyk->getJazyk()->getTitle()}</label>

                  {$editor->render()|noescape}
               </div>
            </div>
         </div>
      {/foreach}
   </div>

   <div class="row mt-4">
      <label for="systemSluzby-{$randomID}">{_'Vyberte jednu kategorii, do které chcete službu zařadit'}</label>
      <div class="input-group mb-3 d-flex gap-2">
         <div class="input-group-prepend col-12 col-lg">
            <select id="systemSluzby-{$randomID}" class="form-select js-select-system-sluzby" name="systemSluzba" required>
               <option n:if="!$sluzba"></option>
               {foreach $systemSluzby as $systemSluzba}
                  <option value="{$systemSluzba->id_sluzba}"
                          data-icon="{$systemSluzba->icon}"
                          {if $sluzba!= null && $sluzba->getSystemSluzba() != null}
                            {if $systemSluzba->id_sluzba === $sluzba->getSystemSluzba()->id_sluzba}selected{/if}
                          {/if}>
                     {$systemSluzba->nazev}
                  </option>
               {/foreach}
            </select>
         </div>
      </div>
   </div>

   <div class="row mt-2">
      <label for="typyAkci-{$randomID}">{_'Vyberte jeden a více typů akce, u kterých službu poskytujete'}</label>
      <div class="input-group mb-3 d-flex gap-2">
         <div class="input-group-prepend col-12 col-lg">
            <select id="typyAkci-{$randomID}" class="form-select js-select-typy-akci" name="typyAkci[]" multiple required>
               {var $akceTypy = $sluzba?->getAkceTypy()}

               {foreach $typyAkci as $akce}
                  <option value="{$akce->id_typ}" n:attr="selected: $akceTypy && isset($akceTypy[$akce->id_typ])">
                     {$akce->nazev}
                  </option>
               {/foreach}
            </select>
         </div>
      </div>
   </div>

   <div class="row mt-4">
      <h4>{_'Fotky'}</h4>
   </div>

   <div class="row mb-2">
      <div class="d-flex gap-2 flex-wrap" data-attachment-chat-container>
         <table class="table table-sm align-middle text-center w-100">
            <thead>
            <tr>
               <th style="width: 40px;"></th>
               <th></th>
               <th></th>
            </tr>
            </thead>
            <tbody class="sortable-photo-table-{$randomID}" data-attachment-chat-container>
            {if $photoGallery}
               {foreach $photoGallery as $index => $photo}
                  <tr data-attachment-file data-photo-id="{$photo->id}" data-position="{$photo->poradi ?? 0}" class="{if $index === 0}table-thumbnail-row{/if}">
                     <td class="drag-handle align-middle text-muted" style="cursor: move;">
                        <i class="bi bi-grip-vertical fs-3 sortable-placeholder"></i>
                     </td>
                     <td>
                        <div class="photo-cell-wrapper">
                           <img src="{$photo->getFullSrc($id_organizace)}"
                                alt="photo"
                                class="img-thumbnail photo-preview"
                                style="max-height: 70px;">
                           <input type="hidden" name="existing_photos[]" value="{$photo->id}">
                        </div>
                     </td>
                     <td>
                        <a class="btn btn-sm btn-outline-danger" data-attachment-delete title="Smazat">
                           <i class="bi bi-trash"></i>
                        </a>
                     </td>
                  </tr>
               {/foreach}
            {/if}
            </tbody>
         </table>
      </div>
   </div>

   <div class="row justify-content-end">
      <div class="col-auto">
         <button type="button" class="btn btn-sm btn-outline-secondary" data-add-chat-attachment>
            <i class="bi bi-paperclip"></i>{_'Přidat fotografii'}
         </button>
      </div>
   </div>

   <div class="fixed-bottom pe-none d-flex justify-content-center">
      <div class="d-inline-flex flex-column flex-lg-row flex-wrap align-items-center bg-white border border-light-gray radius-card shaddow-light shaddow-hover my-2 p-2 pe-auto gap-2">
         <input n:if="$sluzba" type="hidden" name="id_sluzba" value="{$sluzba->id}">

         <div n:if="$sluzba" class="col-auto">
            <button type="submit"
                    class="btn btn-outline-secondary no-validate w-auto shaddow-hover"
                    name="btnDeleteSluzba"
                    data-confirm="{_'Smazání této služby je nevratné. Opravdu ji chcete odstranit'}"
            >{_'Smazat'}</button>
         </div>

         <div class="col-auto">
            <button type="submit" class="btn btn-secondary w-auto shaddow-hover" n:attr="name: $sluzba ? 'btnUpravitSluzbu' : 'btnVytvoritSluzbu'">{_'Uložit'}</button>
         </div>
      </div>
   </div>
</form>


<script >
   $(function () {
      const form = $('form#organizaceSluzba' + {$randomID});

      form.find('.js-select-typy-akci').select2({
         placeholder: "{_'Typy akcí'}",
         dropdownParent: form.closest('.modal')
      });

      form.find('.js-select-system-sluzby').select2({
         placeholder: "{_'Vyberte kategorii služby'}",
         dropdownParent: form.closest('.modal'),
         templateResult: function(data) {
            if (!data.id) { return data.text; }
            let icon = $(data.element).data('icon');
            return $('<span><i class="' + icon + '"></i> ' + data.text + '</span>');
         },
         templateSelection: function(data) {
            let icon = $(data.element).data('icon');
            return $('<span><i class="' + icon + '"></i> ' + data.text + '</span>');
         }
      });

      form.find('button[name="btnDeleteSluzba"]').on('click', function(e) {
         form.find('[required]').addClass('no-validate');
      });

      form.validate({
         ignore: '.no-validate',
      });

      // Dočasně vypnuto, musí se vyřešit napojení jquery validatoru na tinyMCE
      {*$.validator.addMethod('descRequiresTitle', function (value, element) {*}
      {*   const lang = $(element).data('lang');*}
      {*   const title = form.find('input[name="titles[' + lang + ']"]').val();*}
      {*   return value.trim() === '' || title.trim() !== '';*}
      {*}, "{_'Nejdřív vyplňte název služby v daném jazyce'}");*}
   });

   $(function () {
      updatePhotoTableUI();
      const form = $('form#organizaceSluzba' + {$randomID});
      const btnAddAttachment = form.find('[data-add-chat-attachment]');
      const fileInputContainer = form.find('[data-attachment-chat-container]');
      const maxFilesContainerSize = 20 * 1024 * 1024;
      const maxFileSize = 5 * 1024 * 1024;
      const allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];
      let filesContainerSize = 0;

      form.on('submit', function () {
         const tableSelector = 'tbody.sortable-photo-table-'+{$randomID};
         const orderedRows = fileInputContainer.find(tableSelector + " [data-attachment-file]");

         orderedRows.each(function (index) {
            const $row = $(this);
            const position = index + 1;
            let photoId = null;

            const existingInput = $row.find('input[name="existing_photos[]"]');
            if (existingInput.length) {
               photoId = existingInput.val();
            } else {
               const fileInput = $row.find('input[type="file"]');
               if (fileInput.length && fileInput[0].files.length > 0) {
                  photoId = fileInput.attr('name').match(/\[([^\]]+)\]/)?.[1] || null;
               }
            }

            if (photoId) {
               const orderInput = document.createElement('input');
               orderInput.type = 'hidden';
               orderInput.name = 'photo_order[' + photoId + ']';
               orderInput.value = position;
               $row.append(orderInput);
            }
         });
      });

      fileInputContainer.find('tbody.sortable-photo-table-'+{$randomID}).sortable({
         items: '[data-attachment-file]',
         handle: '.drag-handle',
         placeholder: 'sortable-placeholder',
         helper: function (e, tr) {
            const $originals = tr.children();
            const $helper = tr.clone();
            $helper.children().each(function (index) {
               $(this).width($originals.eq(index).width());
            });
            $helper.css('background-color', tr.css('background-color'));
            return $helper;
         },
         update: function () {
            fileInputContainer.find('[data-attachment-file]').each(function (index) {
               $(this).attr('data-position', index + 1);
            });
            updatePhotoTableUI();
         }
      });


      btnAddAttachment.on('click', function(e) {
         e.preventDefault();

         const tempFileInput = document.createElement('input');
         tempFileInput.type = 'file';
         tempFileInput.name = 'attachments[]';
         tempFileInput.style.display = 'none';
         tempFileInput.setAttribute('accept', allowedExtensions.map(ext => `.${ ext }`).join(','));

         tempFileInput.click();

         tempFileInput.addEventListener('change', function() {
            if(
               tempFileInput.files.length === 0
            ){
               toastr['error']("Příloha neobsahuje žádné soubory", '', {
                  positionClass: 'toast-bottom-left',
                  progressBar: true,
               });
               tempFileInput.remove();
               return;
            }

            const file = tempFileInput.files[0];

            if(!(file instanceof File)){
               toastr['error']("Příloha je poškozena zkuste to znovu za chvíli, nebo kontaktujte podporu", '', {
                  positionClass: 'toast-bottom-left',
                  progressBar: true,
               });
               tempFileInput.remove();
               return;
            }

            const fileExtension = file.name.split('.').pop().toLowerCase();

            if(!allowedExtensions.includes(fileExtension)){
               toastr['error']("Omlouváme se, ale soubor, který jste nahráli, není ve formátu, který podporujeme.", '', {
                  positionClass: 'toast-bottom-left',
                  progressBar: true,
               });
               tempFileInput.remove();
               return;
            }

            const humanFileSizeMiB = (file.size / (1024 * 1024)).toFixed(2);
            const humanFileSize = (file.size / (1000 * 1000)).toFixed(2);

            if(file.size > maxFileSize){
               const humanMaxFileSize = (maxFileSize / (1024 * 1024)).toFixed(2);

               toastr['error'](`Omlouváme se, ale soubor, který jste nahráli je větší jak ${ humanMaxFileSize } MB (${ humanFileSize } MB)`, '', {
                  positionClass: 'toast-bottom-left',
                  progressBar: true,
               });
               tempFileInput.remove();
               return;
            }

            if((file.size + filesContainerSize) > maxFilesContainerSize){
               toastr['error'](`Omlouváme se, ale další soubor již nelze nahrát`, '', {
                  positionClass: 'toast-bottom-left',
                  progressBar: true,
               });
               tempFileInput.remove();
               return;
            }

            filesContainerSize += file.size;

            const tableBody = form.find('.sortable-photo-table-'+{$randomID})[0];
            console.log('.sortable-photo-table-'+{$randomID});
            console.log(tableBody);
            if (!tableBody) return;

            const row = document.createElement('tr');
            row.setAttribute('data-attachment-file', '');
            row.setAttribute('data-position', tableBody.children.length + 1);

            const fileURL = URL.createObjectURL(file);

            const handleCell = document.createElement('td');
            handleCell.className = 'drag-handle align-middle text-muted';
            handleCell.style.cursor = 'move';
            handleCell.innerHTML = '<i class="bi bi-grip-vertical fs-3"></i>';

            const photoCell = document.createElement('td');

            const photoWrapper = document.createElement('div');
            photoWrapper.className = 'photo-cell-wrapper d-flex align-items-center gap-2';
            photoWrapper.style.marginLeft = '50px';

            const img = document.createElement('img');
            img.src = URL.createObjectURL(file);
            img.alt = file.name;
            img.className = 'img-thumbnail photo-preview';
            img.style.maxHeight = '70px';

            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'new_photo_ids[]';
            hiddenInput.value = file.name;

            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            let count = fileInputContainer.children('[data-attachment-file]').length +1;
            fileInput.name = 'attachments[add_'+count+']';
            fileInput.style.display = 'none';
            fileInput.files = tempFileInput.files;

            const orderInput = document.createElement('input');
            orderInput.type = 'hidden';
            orderInput.name = 'photo_order[' + file.name + ']';
            orderInput.className = 'photo-order-input';
            orderInput.value = tableBody.children.length + 1;

            photoWrapper.appendChild(img);
            photoWrapper.appendChild(hiddenInput);
            photoWrapper.appendChild(fileInput);

            photoCell.appendChild(photoWrapper);
            photoCell.appendChild(orderInput);


            const actionCell = document.createElement('td');
            const deleteBtn = document.createElement('a');
            deleteBtn.className = 'btn btn-sm btn-outline-danger';
            deleteBtn.setAttribute('data-attachment-delete', '');
            deleteBtn.title = 'Smazat';
            deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';

            actionCell.appendChild(deleteBtn);

            row.appendChild(handleCell);
            row.appendChild(photoCell);
            row.appendChild(actionCell);
            row.appendChild(orderInput);

            tableBody.appendChild(row);

            updatePhotoTableUI();
         });
      });

      function updatePhotoTableUI() {
         const rows = $('[data-attachment-file]');
         rows.removeClass('table-thumbnail-row');
         rows.each(function(index) {
            $(this).find('.photo-order-input').val(index + 1);
            $(this).attr('data-position', index + 1);

            if (index === 0) {
               $(this).addClass('table-thumbnail-row');
            }
         });
      }

      form.on('click', '[data-attachment-delete]', function(e) {
         const fileCard = e.target.closest('[data-attachment-file]');

         if(fileCard === undefined)
            return;
         fileCard.remove();
         btnAddAttachment.show();
         updatePhotoTableUI();
      });
   });
</script>

<style>
    .table-thumbnail-row {
        background-color: rgba(165, 74, 211, 0.18);
    }
    .sortable-placeholder {
        height: 70px;
        background-color: transparent;
        border: none;
    }
    .drag-handle i {
        transition: color 0.2s ease-in-out;
        color: #999;
    }

    .drag-handle:hover i {
        color: #d50ccc;
    }
    .photo-cell-wrapper {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-left: 50px;
    }
</style>