<?php namespace app\front\organizace\modul\detail\zakladni\zalozky\portal\otazky;

use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\model\katalog\otazky\event\SaveOtazkaOdpovedEvent;
use app\system\model\katalog\otazky\Otazky;
use app\system\model\katalog\otazky\OtazkyOrganizace;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\modul\modal\Modal;
use app\system\modul\modal\styles\ModalScaleEnum;
use Nette\Utils\Random;

/** Created by <PERSON><PERSON><PERSON>. Date: 13.02.2025 */
class NovaOdpovedOtazkaModal extends Modal
{
   
   public function getTitleName() :string {
      return 'Nová otázka';
   }

   public function prepareModal(Templater $templater) :void {
      $templater->addData([
         'id_otazka' => null,
         'id_organizace' =>  $this->id_organizace,
         'nepouzivaneOtazky' => Otazky::getNepouzivaneOtazkyOrganizace(OtazkyOrganizace::getSystemOtazkyForOrganizaceIds($this->id_organizace)),
         'odpovedi' =>  null,
         'jazyky' => OrganizaceJazyky::getPairs($this->id_organizace),
         'primarniJazykOrganizace' => OrganizaceJazyky::getPrimary($this->id_organizace),
         'randomID' =>  Random::generate(),
         'modalID' => $this->getIdModal(),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnVytvoritOdpovedi', function($post) {
         if (!isset($post['id_otazka']))
            throw FlashException::create('Otázka je povinná');

         if(
            SaveOtazkaOdpovedEvent::create(FrontApplicationEnvironment::get()->id_organizace, $post)
               ->call()
               ->getSuccess()
         ) {
            FlashMessages::setSuccess('Odpovědi byly vytvořeny');
         }
      });
   }

   public function setIdOrganizace(int $id_organizace) :static {
      $this->id_organizace = $id_organizace;
      $this->setOrganizace($this->id_organizace);
      return $this;
   }

   public function setOrganizace(int $id_organizace) :static {
      $this->organizace = Organizace::getMisto($id_organizace);
      return $this;
   }

   private int $id_organizace;
   private OrganizaceRow $organizace;

}