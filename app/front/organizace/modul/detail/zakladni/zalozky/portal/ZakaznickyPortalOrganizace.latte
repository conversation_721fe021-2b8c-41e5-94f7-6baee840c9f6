{varType app\front\organizace\modul\detail\zakladni\zalozky\portal\ZakaznickyPortalOrganizace $_zalozka}
{varType string $klientskyPortalOdkazFull}
{varType string $katalogOdkazFull}
{varType string $klientskyPortalOdkazPoptavka}
{varType Latte\Runtime\Html[] $tinyEditors}
{varType app\system\model\organizace\portal\OrganizacePortalRow $portalInfo}
{varType app\system\model\organizace\portal\content\OrganizacePortalContentRow[] $portalContent}
{varType app\front\organizace\modul\detail\zakladni\zalozky\portal\OrganizaceUsedSpaceItem $usedSpace}
{varType app\system\model\organizace\portal\galerie\OrganizacePortalGalerieRow[] $portalGalerie}
{varType app\system\model\tagy\SystemTagyNazvyRow[] $tagy}
{varType app\system\model\tagy\SystemTagyNazvyRow[] $vybraneTagy}
{varType app\system\model\organizace\sluzby\OrganizaceSluzbyRow[] $sluzby}
{varType app\system\model\highlighty\OrganizaceHighlightRow[] $highlights}
{varType bool $canEnableKatalogView}
{varType string $poptavkovyFormularUrl}
{varType app\system\model\katalog\otazky\OtazkyOrganizaceRow[] $otazky}
{varType app\system\model\translator\TextVariablesFactory[] $jazyky}
{varType array $vlastnosti}
{varType array $pouzivaneVlastnosti}
{varType array $typyAkci}
{varType array $pouzivaneTypyAkci}
{varType string[] $modalNovaSluzbaAttr}
{varType array $vytvoreniHighlightuModal}
{varType int $systemLanguage}
{varType int $organizacePrimaryLanguage}
{varType string $modalAttrNovaOdpoved}
{varType app\front\organizace\model\organizace\OrganizaceRow $organizace}

<div class="row border-bottom toolbar-section gap-3 bg-white rohy-2-2 px-1 py-3">
   <div class="col-lg col-12 align-content-center">
      <h4 class="card-title">{_'Nastavení Booking Portal'}</h4>
   </div>
</div>

<!--Fotky-->
<div class="col-12 card p-3 border border-light-gray">
   <h3 class="mb-2 card-title">{_'Fotky v profilu'}</h3>
   <div class="row card-body">

      <div class="col-12 my-2">
         <div class="row d-inline-block mb-1">
            <p>{_'Využití uložiště'}:<small class="ms-2">{$usedSpace->getConvertedUsedSpace()}&nbsp;/&nbsp;{$usedSpace->getConvertedTotalSpace()}</small></p>
         </div>
         <div class="progress" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100">
            <div class="progress-bar bg-{$usedSpace->getColor()}" style="width: {$usedSpace->percentualUsage > 100 ? 100 :$usedSpace->percentualUsage}%">{$usedSpace->percentualUsage}%</div>
         </div>
      </div>


      <div class="row my-4">
         <h4>{_'Profilová fotka'}
            <a data-bs-toggle="offcanvas" href="#offcanvasProfilSupport" role="button" aria-controls="offcanvasProfilSupport">
               <i class="bi bi-info-circle ms-1"></i>
            </a>
         </h4>
         <div class="row">
            {if $portalInfo->profile_photo_src}
               <div class="col col-4">
                  <img src="/{$portalInfo->getProfilPhotoFullSrc() ?: 'files/system/img/chybi-cover.jpg?2'}" class="img-fluid radius-card-img radius-card" alt="Název prostoru">
               </div>
            {else}
               <p>{_'Není nahraná žádná fotka'}</p>
            {/if}
         </div>
         <div class="row mt-1">
            <div class="col-auto">
               <form method="post" enctype="multipart/form-data">
                  <input type="hidden" name="actionOrganizationProfilePhoto" value="true">
                  <input type="file" accept="image/jpeg, image/png, image/jpg, image/webp" name="profile_photo" data-file-input style="display: none">
                  <button type="button" n:class="btn, w-auto, shaddow-hover, !$portalInfo->profile_photo_src ? btn-secondary : btn-outline-secondary" data-file-btn>
                     {if $portalInfo->profile_photo_src === null}
                        <i class="bi bi-plus me-1"></i>{_'Přidat fotku'}
                     {else}
                        <i class="bi bi-arrow-left-right"></i>
                     {/if}
                  </button>
               </form>
            </div>
            <div n:if="$portalInfo->profile_photo_src !== null" class="col col-auto p-0">
               <form method="post">
                  <button type="submit" name="btnDeleteProfilImg" value="true" class="btn btn-outline-secondary"
                          data-confirm="{_'Opravdu chcete profilovou fotku odebrat?'}"><i class="bi bi-trash"></i></button>
               </form>
            </div>
         </div>
      </div>


      <div class="row mt-4">
         <form method="post">
            <h4>{_'Další fotky'}</h4>

            {if !empty($portalGalerie)}
               <table class="table">
                  <thead>
                  <tr>
                     <td style="width: 5%">{_'Náhled'}</td>
                     <td>{_'Pojmenování fotky'}<i class="bi bi-info-circle ms-1" data-bs-toggle="tooltip" data-bs-title="{_'Fotky se v galerii řadí podle názvu. Pokud chcete upravit jeich pořadí, upravte názvy.'}"></i></td>
                     <td class="text-end" style="width: 15%">{_'Vytvořeno'}</td>
                     <td style="width: 5%"></td>
                  </tr>
                  </thead>
                  <tbody>
                  <tr n:foreach="$portalGalerie as $image">
                     <td>
                        <div class="d-flex">
                           <div class="img-cont">
                              <img class="img-fluid" src="{$image->getPhotoFullSrc()}">
                           </div>
                        </div>
                     </td>
                     <td><input type="text" value="{$image->name ?: ''}" class="form-control" name="galerie_nazev[{$image->id}]" maxlength="255" required></td>
                     <td class="text-end">{$image->created|date: 'j.n.Y H:i'}</td>
                     <td class="table-action">
                        <div class="d-flex gap-1 justify-content-end row">
                           <div class="col col-auto p-0">
                              <button type="submit" name="btnDeleteGalleryPhoto" value="{$image->id}" class="form-control btn btn-outline-danger"
                                      data-confirm="{_'Opravdu chcete fotku smazat?'}"><i class="bi bi-trash"></i></button>
                           </div>
                        </div>
                     </td>
                  </tr>
                  </tbody>
               </table>

               <div class="row mb-2 justify-content-end">
                  <div class="col col-auto">
                     <button type="submit" name="btnUlozitNazvyGalerie" class="btn btn-outline-secondary w-auto shaddow-hover w-100"><i class="bi bi-save me-1"></i>{_'Uložit názvy'}</button>
                  </div>
               </div>
            {else}
               <p>{_'Žádné fotky tady nejsou'}</p>
            {/if}

         </form>
         <form n:if="$usedSpace->percentualUsage < 100" id="uploadPhoto" method="post" enctype="multipart/form-data">
            <div class="row">
               <div class="col-auto">
                  <input type="hidden" name="actionOrganizationAddPhoto" value="true">
                  <input type="file" accept="image/jpeg, image/png, image/jpg, image/webp" name="new_photo_galery" data-file-input style="display: none">
                  <button type="button" class="btn btn-secondary w-auto shaddow-hover" data-file-btn><i class="bi bi-plus me-1"></i>{_'Přidat fotku'}</button>
               </div>
            </div>
         </form>
      </div>
   </div>
</div>

<form method="post">
   <!--Info-->
   <div class="col-12 card p-3 border border-light-gray">
      <div class="row">
         <h3 class="card-title">{_'Základní informace'}
            <a data-bs-toggle="offcanvas" href="#offcanvasZakladniInfoSupport" role="button" aria-controls="offcanvasZakladniInfoSupport">
               <i class="bi bi-info-circle ms-1"></i>
            </a>
         </h3>
      </div>
      <div class="row mt-2">
         <div class="col-12">
            <div class="form-check form-switch">
               {if $canEnableKatalogView}
                  <input class="form-check-input" type="checkbox" role="switch" name="is_hidden_catalog"
                         id="is_hidden_catalog" n:attr="checked: $portalInfo->is_hidden_catalog === 0">
               {else}
                  <input class="form-check-input" type="checkbox" role="switch"
                         id="is_hidden_catalog" disabled>
               {/if}

               <label class="form-check-label text-secondary" for="is_hidden_catalog">
                  {_'Zobrazovat profil v katalogu'}
            {if !$canEnableKatalogView}
                     <i class="bi bi-info-circle ms-1 text-primary" data-bs-toggle="tooltip" data-bs-title="{_'Profili bude možné zveřejnit po zkušební době'}"></i>
                  {/if}
               </label>
            </div>
         </div>
      </div>
      <div class="form-group mb-3">
         <label for="instagram_link">Instagram</label>
         <div class="input-group">
            <span class="input-group-text input-levy-round"><i class="bi bi-instagram"></i></span>
            <input type="text" name="instagram_link" id="instagram_link" class="form-control col input-pravy-round"
                   value="{$portalInfo->instagram_link}" maxlength="255" placeholder="{_'Vložte prosím kompletní adresu profilu na Instagramu'}">
         </div>
      </div>
      <div class="form-group mb-3">
         <label for="fb_link">Facebook</label>
         <div class="input-group">
            <span class="input-group-text input-levy-round"><i class="bi bi-facebook"></i></span>
            <input type="text" name="fb_link" id="fb_link" class="form-control col input-pravy-round"
                   value="{$portalInfo->fb_link}" maxlength="255" placeholder="{_'Vložte prosím kompletní adresu profilu na Facebooku'}">
         </div>
      </div>
      <div class="form-group mb-3">
         <label for="fb_link">{_'Web'}</label>{*@TODO mít ho i tady ať je to komplet.*}
         <div class="input-group">
            <span class="input-group-text input-levy-round"><i class="bi bi-globe"></i></span>
            <input type="text" name="web_link" id="web_link" class="form-control col input-pravy-round"
                   value="{$organizace->getKontakt()?->website}" maxlength="255" placeholder="{_'Vložte prosím kompletní webovou adresu'}">
         </div>
      </div>

      <div class="row">
         <div class="tab tab-vertical">
            <ul class="nav nav-tabs" role="tablist">
               {foreach $tinyEditors as $id_jazyk => $editor}
                  {var $jazyk = app\system\model\translator\Jazyky::from($id_jazyk)}
                  <li class="nav-item">
                     <a class="nav-link {if $iterator->first}active{/if}" href="#vertical-icon-tab-{$id_jazyk}" data-bs-toggle="tab" role="tab" {if $iterator->first}aria-selected="true"{/if}>
                        <img src="{$jazyk->getIcon()}" width="25px" alt="{$jazyk->getTitle()} flag">
                     </a>
                  </li>
               {/foreach}
            </ul>
            <div class="tab-content">
               {foreach $tinyEditors as $id_jazyk => $editor}
                  {var $jazyk = app\system\model\translator\Jazyky::from($id_jazyk)}
                  <div class="tab-pane {if $iterator->first}active{/if}" id="vertical-icon-tab-{$id_jazyk}" role="tabpanel">
                     <h4 class="tab-title">{$jazyk->getTitle()}</h4>
                     <div class="row mb-4">
                        <label for="portal_kratky_popis{$jazyk->value}">{_'Krátký popis'}</label>
                        <textarea class="form-control" id="portal_kratky_popis{$jazyk->value}" name="portal_kratky_popis[{$jazyk->value}]" placeholder="{_'Krátký popis, který bude zobrazen na kartě vaší organizace v katalogu.'}">{($portalContent[$jazyk->value] ?? null)?->popis ?: ''}</textarea>
                     </div>
                     <div class="row">
                        <label for="portal_detail_popis_{$jazyk->value}">{_'Detailní popis do profilu'}</label>
                        {$editor}
                     </div>
                  </div>
               {/foreach}
            </div>
         </div>
      </div>
      <div class="row d-flex justify-content-center">
         <button class="btn btn-secondary w-auto shaddow-hover" type="submit" name="btnSavePortalInfo"><i class="bi bi-save me-1"></i>{_'Uložit změny '}</button>
      </div>
   </div>

   <!--Služby-->
   <div class="card border-0 shadow-sm radius-card">
      <div class="card-header bg-white d-flex justify-content-between align-items-center">
         <h3 class="card-title">{_'Služby'}
            <a data-bs-toggle="offcanvas" href="#offcanvasSluzbySupport" role="button" aria-controls="offcanvasSluzbySupport">
               <i class="bi bi-info-circle ms-1"></i>
            </a>
         </h3>
         <button n:attr="$modalNovaSluzbaAttr" type="button" class="btn btn-primary btn-sm">
            <i class="bi bi-plus-lg me-1"></i>{_'Přidat novou službu'}
         </button>
      </div>

      {if !empty($sluzby)}
         <div class="list-group list-group-flush">
            {foreach $sluzby as $sluzba}
               {var $thumb = $sluzba->getThumbnail()}
               {var $texty = $sluzba->getTexty(app\system\model\translator\Jazyky::from($systemLanguage))}
               {var $fallback = $sluzba->getTexty(app\system\model\translator\Jazyky::from($organizacePrimaryLanguage))}
               {var $altText = $texty?->nazev ?? $fallback?->nazev ?? 'Neznámý název'}

               <div class="list-group-item d-flex align-items-center">
                  <!-- Ikona / obrázek -->
                  <div class="me-3">
                     {if $thumb !== null}
                        <img src="{$thumb->getFullSrc($sluzba->id_organizace)}"
                             alt="{$altText}"
                             class="rounded"
                             style="width: 48px; height: 48px; object-fit: cover;">
                     {else}
                        <div class="placeholder-img d-flex align-items-center justify-content-center text-white text-bg-info rounded"
                             style="width: 48px; height: 48px;">
                           <i class="bi bi-image"></i>
                        </div>
                     {/if}
                  </div>

                  <!-- Obsah -->
                  <div class="flex-grow-1">
                     <div class="fw-semibold">
                        {if !empty($texty?->nazev)}
                           {$texty->nazev}
                        {elseif !empty($fallback?->nazev)}
                           {$fallback->nazev}
                        {else}
                           {_ 'Neznámý název'}
                        {/if}
                     </div>
                     <div class="small text-muted">
                        <div>
                           <i class="bi bi-music-note-beamed me-1"></i>
                           {_'Typ služby'}: {$sluzba->getSystemSluzba()->nazev ?? 'Neznámý typ'}
                        </div>
                        <div>
                           <i class="bi bi-calendar-event me-1"></i>
                           {_'Typy akcí'}:
                           {foreach $sluzba->getAkceTypy() as $typ}
                              {$typ->nazev}{sep}, {/sep}
                           {/foreach}
                        </div>
                     </div>
                  </div>

                  <!-- Akce -->
                  <div>
                     <button type="button" class="btn btn-outline-secondary btn-sm"
                             n:attr="$_zalozka->getSluzbaEditModalAttr($sluzba->id)"
                             title="{_'Upravit službu'}">
                        <i class="bi bi-pencil"></i>
                     </button>
                  </div>
               </div>
            {/foreach}
         </div>
      {else}
         <div class="card-body text-center text-muted py-4">
            <i class="bi bi-info-circle me-1"></i>{_'Žádné služby zatím nebyly přidány.'}
         </div>
      {/if}
   </div>

   <div class="bg-white border border-light-gray shadow-sm radius-card p-3 mb-4">
      <div class="card-header bg-white d-flex justify-content-between align-items-center">
         <h3 class="card-title">{_'Podklady pro filtraci'}
            <a data-bs-toggle="offcanvas" href="#offcanvasFiltraceSupport" role="button" aria-controls="offcanvasFiltraceSupport">
               <i class="bi bi-info-circle ms-1"></i>
            </a>
         </h3>
      </div>
      <div class="row mt-3">
         <label for="vlastnosti">{_'Vlastnosti'}</label>
         <div class="input-group mb-3 d-flex gap-2">
            <div class="input-group-prepend col-12 col-lg">
               <select id="vlastnosti" class="form-select  js-example-basic-multiple" name="vlastnosti[]" multiple="multiple">
                  {foreach $vlastnosti as $id_vlastnosti => $vlastnost}
                     <option value="{$id_vlastnosti}" n:attr="selected: isset($pouzivaneVlastnosti[$id_vlastnosti])">
                        {$vlastnost}
                     </option>
                  {/foreach}
               </select>
            </div>
         </div>
      </div>

      {foreach $pouzivaneVlastnosti as $vlastnostID}
         <p class="badge bg-primary rounded-pill text-white" id="stav"> {$vlastnosti[$vlastnostID]}</p>
      {/foreach}

      <div class="row">
         <label for="tagy">{_'Tagy'}</label>
         <div class="input-group mb-3 d-flex gap-2">
            <div class="input-group-prepend col-12 col-lg">
               <select id="tagy" class="form-select  js-example-basic-multiple" name="tagy[]" multiple="multiple">
                  {foreach $tagy as $tag}
                     <option value="{$tag->id_tag}" n:attr="selected: isset($vybraneTagy[$tag->id_tag])">
                        {$tag->nazev}
                     </option>
                  {/foreach}
               </select>
            </div>
         </div>
       </div>

      <div class="row">
         <label for="typyAkci">{_'Vaše typy akcí'}</label>
         <div class="input-group mb-3 d-flex gap-2">
            <div class="input-group-prepend col-12 col-lg">
               <select id="typyAkci" class="form-select js-select-typ-akce" name="typy_akci[]" multiple="multiple">
                  {foreach $typyAkci as $id_typAkce => $typAkce}
                     <option value="{$id_typAkce}" n:attr="selected: isset($pouzivaneTypyAkci[$id_typAkce])">
                        {$typAkce}
                     </option>
                  {/foreach}
               </select>
            </div>
         </div>
      </div>
      {foreach $pouzivaneTypyAkci as $pouzivanyTypAkce}
         <p class="badge bg-primary rounded-pill ms-2 my-1 text-white" id="stav">{$pouzivanyTypAkce}</p>
      {/foreach}
      <div class="d-flex justify-content-center">
         <button class="btn btn-secondary w-auto shaddow-hover" type="submit" name="btnSavePortalInfo"><i class="bi bi-save me-1"></i>{_'Uložit změny'}</button>
      </div>
   </div>
</form>

<!--Highlighty-->
<div class="card border-0 shadow-sm radius-card">
   <div class="card-header bg-white d-flex justify-content-between align-items-center">
      <h3 class="card-title">{_'Highlighty'}
         <a data-bs-toggle="offcanvas" href="#offcanvasHighlightsSupport" role="button" aria-controls="offcanvasHighlightsSupport">
            <i class="bi bi-info-circle ms-1"></i>
         </a>
      </h3>
      <a href="#"
         class="btn btn-primary btn-sm"
              n:attr="$vytvoreniHighlightuModal">
         <i class="bi bi-plus-lg me-1"></i>{_'Přidat highlight'}
      </a>
   </div>

   <div class="list-group list-group-flush">
      {foreach $highlights as $highlight}
         <div class="list-group-item d-flex justify-content-between align-items-center">
            <div class="flex-grow-1">
               <div class="fw-semibold"><i class="{$highlight->icon} me-1"></i>{$highlight->title}</div>
               <span class="text-muted">{$highlight->text}</span>
            </div>
            <button type="button" class="btn btn-outline-secondary btn-sm"
                    n:attr="$_zalozka->getHighlightModalAttr($highlight->id)"
                    title="{_'Upravit highlight'}">
               <i class="bi bi-pencil"></i>
            </button>
         </div>
      {/foreach}
   </div>
</div>

<!--Otázky-->
<div class="card border-0 shadow-sm radius-card">
   <div class="card-header bg-white d-flex justify-content-between align-items-center">
      <h3 class="card-title">{_'Otázky a odpovědi'}
         <a data-bs-toggle="offcanvas" href="#offcanvasFAQSupport" role="button" aria-controls="offcanvasFAQSupport">
            <i class="bi bi-info-circle ms-1"></i>
         </a>
      </h3>
      <button n:attr="$modalAttrNovaOdpoved" type="button" class="btn btn-primary btn-sm">
         <i class="bi bi-plus-lg me-1"></i>{_'Přidat otázku'}
      </button>
   </div>

   {if !empty($otazky)}
      <div class="list-group list-group-flush">
         {foreach $otazky as $otazka}
            <div class="list-group-item d-flex justify-content-between align-items-start">
               <div class="me-3 flex-grow-1">
                  <div class="fw-semibold mb-1">
                     <i class="bi bi-question-circle text-primary me-2"></i>
                     {$otazka->getName($systemLanguage)}
                  </div>
                  <div class="small">
                     {foreach $otazka->getOdpovedi() as $id_jazyk => $odpoved}
                        <div>
                           <span class="text-muted">{_'Odpověď v'} {$jazyky[$id_jazyk]}:</span>
                           <span class="fw-semibold text-primary">{$odpoved}</span>
                        </div>
                     {/foreach}
                  </div>
               </div>
               <div>
                  <button type="button" class="btn btn-outline-secondary btn-sm"
                          n:attr="$_zalozka->getOtazkaDetailModalAttr($otazka->id_otazka)"
                          title="{_'Upravit otázku'}">
                     <i class="bi bi-pencil"></i>
                  </button>
               </div>
            </div>
         {/foreach}
      </div>
   {else}
      <div class="card-body text-center text-muted py-4">
         <i class="bi bi-info-circle me-1"></i>{_'Žádné otázky zatím nebyly přidány.'}
      </div>
   {/if}
</div>

<div class="row">
   <h3 class="mb-2 card-title">{_'Odkazy'}</h3>
   <label for="basic-url">{_'Katalog'}</label>
   <div class="input-group mb-3">
      <div class="input-group-prepend col">
         <span class="input-group-text">{$katalogOdkazFull}</span>
      </div>
      <div class="form-group col-auto d-flex align-items-center ms-2">
         <a href="{$katalogOdkazFull}" target="_blank" class="btn btn-outline-secondary btn-sm"><i class="bi bi-window-plus"></i>&nbsp;{_'Přejít'}</a>
      </div>
   </div>
</div>

<div class="row">
   <label for="basic-url">{_'Váš zákaznický portál'}</label>
   <div class="input-group mb-3 d-flex gap-2">
      <div class="input-group-prepend col-12 col-lg">
         <span class="input-group-text">{$klientskyPortalOdkazFull}</span>
      </div>
      <div class="form-group col-auto d-flex align-items-center ms-2">
         <a class="btn btn-outline-secondary btn-sm" data-copy-to-clipboard="{$klientskyPortalOdkazFull}"><i class="bi bi-copy me-1"></i>{_'Kopírovat'}</a>
      </div>
      <div class="form-group col-auto d-flex align-items-center ms-2">
         <a href="{$klientskyPortalOdkazFull}" target="_blank" class="btn btn-outline-secondary btn-sm"><i class="bi bi-window-plus"></i>&nbsp;{_'Přejít'}</a>
      </div>
   </div>
</div>

<div class="row mb-5">
   <label for="basic-url">{_'Váš poptávkový formulář'}</label>
   <div class="input-group mb-3 d-flex gap-2">
      <div class="input-group-prepend col-12 col-lg">
         <span class="input-group-text">{$poptavkovyFormularUrl}</span>
      </div>
      <div class="form-group col-auto d-flex align-items-center ms-2">
         <a class="btn btn-outline-secondary btn-sm" data-copy-to-clipboard="{$poptavkovyFormularUrl}"><i class="bi bi-copy me-1"></i>{_'Kopírovat'}</a>
      </div>
      <div class="form-group col-auto d-flex align-items-center ms-2">
         <a href="{$poptavkovyFormularUrl}" target="_blank" class="btn btn-outline-secondary btn-sm"><i class="bi bi-window-plus"></i>&nbsp;{_'Přejít'}</a>
      </div>
   </div>
</div>


<!--Onboarding panely-->

<!--Hlavní fotka-->
<div class="offcanvas offcanvas-end" data-bs-scroll="true" data-bs-backdrop="false" tabindex="-1" id="offcanvasProfilSupport" aria-labelledby="offcanvasProfilSupportLabel">
   <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="offcanvasProfilSupportLabel">{_'Fotky – první dojem prodává'}</h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="{_'Zavřít'}"></button>
   </div>
   <div class="offcanvas-body">
      <article class="card mb-4">
         <div class="card-body">
            <h2 class="h3 mb-3">{_'Fotky – první dojem prodává'}</h2>
            <p><strong>{_'Proč:'}</strong> {_'Lidé se rozhodují během několika sekund. Silná úvodní fotka a konzistentní galerie dramaticky zvyšují šanci, že uživatel zůstane a pošle poptávku.'}</p>

            <h3 class="h5 mt-4">{_'Co vyplnit'}</h3>
            <ul class="mb-3">
               <li><strong>{_'Profilová fotka:'}</strong> {_'nejreprezentativnější snímek (celkový záběr prostoru / hero moment z akce).'}</li>
               <li><strong>{_'Galerie 8–15 fotek:'}</strong> {_'prázdný prostor, reálné akce (svatba, firemní event), detaily (dekor, catering, světlo).'}</li>
               <li><strong>{_'Pořadí:'}</strong> {_'první tři fotky = to nejlepší, co máte.'}</li>
            </ul>

            <h3 class="h5">{_'Důležitá data'}</h3>
            <ul class="mb-3">
               <li>{_'67 % zákazníků říká, že kvalita fotek je pro jejich rozhodnutí klíčová (zdroj: MDG Advertising).'}</li>
               <li>{_'Airbnb zjistilo, že profily s profesionálními fotkami mají o 40 % vyšší konverze než ty s amatérskými.'}</li>
               <li>{_'Uživatel se rozhoduje během 2,6 sekundy – první tři fotky rozhodují, zda zůstane. I proto vidíte ve svém profilu hned 3 fotky.'}</li>
            </ul>

            <h3 class="h5">{_'Best practices'}</h3>
            <ul class="mb-3">
               <li>{_'Preferujte světlé, čisté snímky; vyhněte se fotobankám a AI generování.'}</li>
               <li>{_'Ukažte škálu využití (konference, gala, svatba) a měřítko (lidé v prostoru).'} </li>
               <li>{_'Dodržte vizuální konzistenci (stejná úprava, poměr stran -> ideálně 16:9 ).'} </li>
            </ul>

            <div class="p-3 radius-card border border-light-gray">
               <strong>{_'Checklist'}</strong>
               <div class="form-check">
                  <input class="form-check-input" type="checkbox"> {_'Profilová fotka nastavená'}
               </div>
               <div class="form-check">
                  <input class="form-check-input"
                         type="checkbox"> {_'8–15 fotek v galerii (mix prázdno/akce/detaily)'}
               </div>
               <div class="form-check">
                  <input class="form-check-input" type="checkbox"> {_'Top 3 fotky vybrány jako první'}
               </div>
            </div>
         </div>
      </article>
   </div>
</div>

<!-- 2. Základní informace -->
<div class="offcanvas offcanvas-end" data-bs-scroll="true" data-bs-backdrop="false" tabindex="-1" id="offcanvasZakladniInfoSupport" aria-labelledby="offcanvasZakladniInfoSupportLabel">
   <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="offcanvasZakladniInfoSupportLabel">{_'Základní informace – váš příběh'}</h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="{_'Zavřít'}"></button>
   </div>
   <div class="offcanvas-body">
      <article class="card mb-4">
         <div class="card-body">
            <article class="card mb-4">
               <div class="card-body">
                  <h2 class="h4 mb-3">{_'Základní informace – váš příběh'}</h2>
                  <p><strong>{_'Proč:'}</strong> {_'Text prodává jako obchodník. Uživatelé většinou nesrollují celé, proto rozhoduje struktura a jasnost.'}</p>

                  <h3 class="h5 mt-4">{_'Co vyplnit'}</h3>
                  <ul class="mb-3">
                     <li><strong>{_'Odkazy:'}</strong> {_'Web, Instagram, Facebook (ověření a sociální důkaz).'} </li>
                     <li><strong>{_'Krátký popis:'}</strong> {_'2 věty, ty vidí uživatelé jako první.'}</li>
                     <li><strong>{_'Detailní popis do profilu:'}</strong> {_'Tady patří to nejlepší, co o sobě můžete napsat. Prostoru na to je dost.'}</li>
                  </ul>

                  <h3 class="h5">{_'Důležitá data'}</h3>
                  <ul class="mb-3">
                     <li>{_'Lidé čtou v průměru jen 20 % textu na webu, zbytek skenují (zdroj: Nielsen Norman Group).'}</li>
                     <li>{_'56 % eventových klientů hledá prostory i v cizím jazyce → anglická verze popisu výrazně zvyšuje šanci na zahraniční klientelu (zdroj: EventMB).'}</li>
                     <li>{_'Profily s jasným USP (unikátní nabídkou) mají 2× vyšší konverzní míru než obecné popisy. Chce s tím pomoct? Dejte nám vědět.'}</li>
                  </ul>

                  <h3 class="h5">{_'Best practices'}</h3>
                  <ul class="mb-3">
                     <li>{_'Úvodní 2 věty = elevator pitch („Co nabízíme a pro koho jsme ideální“).'}</li>
                     <li>{_'Strukturovaný text → odrážky místo bloků textu.'}</li>
                     <li>{_'Překlad do EN → více zahraničních poptávek.'} </li>
                  </ul>

                  <div class="p-3 radius-card border border-light-gray">
                     <strong>{_'Checklist'}</strong>
                     <div class="form-check">
                        <input class="form-check-input" type="checkbox"> {_'Vyplněny odkazy (Web, Instagram, Facebook)'}
                     </div>
                     <div class="form-check">
                        <input class="form-check-input" type="checkbox"> {_'Popis v češtině stručný a strukturovaný'}
                     </div>
                     <div class="form-check">
                        <input class="form-check-input" type="checkbox"> {_'Přidán popis v angličtině'}
                     </div>
                  </div>
               </div>
            </article>
         </div>
      </article>
   </div>
</div>

<!--Služby-->
<div class="offcanvas offcanvas-end" data-bs-scroll="true" data-bs-backdrop="false" tabindex="-1" id="offcanvasSluzbySupport" aria-labelledby="offcanvasSluzbySupportLabel">
   <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="offcanvasSluzbySupportLabel">{_'Služby – kvalitní nabídka prodává'}</h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="{_'Zavřít'}"></button>
   </div>
   <div class="offcanvas-body">
      <article class="card mb-4">
         <div class="card-body">
            <h2 class="h4 mb-3">{_'Služby – kvalitní nabídka prodává'}</h2>
            <p><strong>{_'Proč:'}</strong> {_'Klienti preferují jasné informace s benefity. Čím lépe popsané služby, tím vyšší šance na rezervaci i vyšší obrat akce.'}</p>

            <h3 class="h5 mt-4">{_'Co vyplnit'}</h3>
            <ul class="mb-3">
               <li><strong>{_'Vlastní služby:'}</strong> {_'catering, technika, ubytování, koordinace…'} </li>
               <li><strong>{_'Prodejní motivační popis:'}</strong> {_'Dejte uživatelům jasné argumenty proč si vybrat vaše služby'} </li>
               <li><strong>{_'Skvělé fotky:'}</strong> {_'Kvalitní fotky prodávají, to už víme. Vyberte takové, které ukazují z každé služby to nejlepší. Opět se vyhněte fotobankám a generování AI.'}</li>
            </ul>

            <h3 class="h5">{_'Důležitá data'}</h3>
            <ul class="mb-3">
               <li>{_'Prostory a dodavatelé s více službami mají až o 45 % více poptávek. (zdroj: WeddingWire 2023)'}</li>
               <li>{_'Cross-sell (doplnění služeb) zvyšuje průměrnou hodnotu objednávky až o 30 % (zdroj: McKinsey)'}</li>
            </ul>

            <h3 class="h5">{_'Best practices'}</h3>
            <ul class="mb-3">
               <li>{_'U každé služby uveďte krátký benefit (např. „Moderní A/V set pro hybridní eventy“).'} </li>
               <li>{_'Nabídněte balíčky (pronájem + technika + koordinace).'} </li>
               <li>{_'Transparentně popište volitelnost (vlastní dodavatel? za jakých podmínek?).'}</li>
            </ul>

            <div class="p-3 radius-card border border-light-gray">
               <strong>{_'Checklist'}</strong>
               <div class="form-check">
                  <input class="form-check-input" type="checkbox"> {_'Přidány všechny vlastní služby'}
               </div>
               <div class="form-check">
                  <input class="form-check-input" type="checkbox"> {_'Uvedeni ověření partneři'}
               </div>
               <div class="form-check">
                  <input class="form-check-input" type="checkbox"> {_'Definované balíčky a volitelné varianty'}
               </div>
            </div>
         </div>
      </article>
   </div>
</div>


<!--Podklady pro filtraci-->
<div class="offcanvas offcanvas-end" data-bs-scroll="true" data-bs-backdrop="false" tabindex="-1" id="offcanvasFiltraceSupport" aria-labelledby="offcanvasFiltraceSupportLabel">
   <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="offcanvasFiltraceSupportLabel">{_'Filtry a tagy – aby vás našli ti správní'}</h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="{_'Zavřít'}"></button>
   </div>
   <div class="offcanvas-body">
      <article class="card mb-4">
         <div class="card-body">
            <h2 class="h4 mb-3">{_'Filtry a tagy – aby vás našli ti správní'}</h2>
            <p><strong>{_'Proč:'}</strong> {_'Filtry fungují jako klíčová slova. Kompletní a přesné vyplnění zásadně zvyšuje dohledatelnost v portálu i mimo něj.'}</p>

            <h3 class="h5 mt-4">{_'Co vyplnit'}</h3>
            <ul class="mb-3">
               <li><strong>{_'Speciální vlastnosti:'}</strong> {_'terasa, zahrada, parkování, wellness, ubytování, bezbariérovost…'}</li>
               <li><strong>{_'Typy akcí a styl prostoru:'}</strong> {_'svatba, konference, firemní večírek; historický, industriální, moderní…'}</li>
               <li><strong>{_'Kapacity a limity:'}</strong> {_'počet osob, hluk/čas, parkovací místa.'}</li>
            </ul>

            <h3 class="h5">{_'Důležitá data'}</h3>
            <ul class="mb-3">
               <li>{_'75 % uživatelů klikne pouze na výsledky na první stránce vyhledávání (zdroj: HubSpot).'}</li>
               <li>{_'Profily s kompletními filtry se zobrazují 2,3× častěji než nekompletní.'}</li>
               <li>{_'Tagy fungují jako klíčová slova → čím relevantnější, tím vyšší „match rate“.'}</li>
            </ul>

            <h3 class="h5">{_'Best practices'}</h3>
            <ul class="mb-3">
               <li>{_'Myslete jako klient: co by hledal? (např. „místo na horách“).'}</li>
               <li>{_'Vybírejte jen relevantní tagy.'} </li>
               <li>{_'Zvolte jen typy akcí, které reálně chcete dělat. Né všechny..'}</li>
               <li>{_'Průběžně aktualizujte dle sezóny a novinek.'}</li>
            </ul>

            <div class="p-3 radius-card border border-light-gray">
               <strong>{_'Checklist'}</strong>
               <div class="form-check">
                  <input class="form-check-input" type="checkbox"> {_'Vyplněny všechny relevantní vlastnosti'}
               </div>
               <div class="form-check">
                  <input class="form-check-input" type="checkbox"> {_'Přidány konkrétní tagy/typy akcí'}
               </div>
               <div class="form-check">
                  <input class="form-check-input" type="checkbox"> {_'Uvedeny typy akcí'}
               </div>
            </div>
         </div>
      </article>
   </div>
</div>

<!--Highlighty-->
<div class="offcanvas offcanvas-end" data-bs-scroll="true" data-bs-backdrop="false" tabindex="-1" id="offcanvasHighlightsSupport" aria-labelledby="offcanvasHighlightsSupportLabel">
   <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="offcanvasHighlightsSupportLabel">{_'Filtry a tagy – aby vás našli ti správní'}</h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="{_'Zavřít'}"></button>
   </div>
   <div class="offcanvas-body">
      <article class="card mb-4">
         <div class="card-body">
            <h2 class="h4 mb-3">{_'Highlights – rychlé háčky na klienta'}</h2>
            <p><strong>{_'Proč:'}</strong> {_'Lidé čtou body více než odstavce. Krátké a úderné benefity rozhodují rychle.'}</p>

            <h3 class="h5 mt-4">{_'Co vybrat'}</h3>
            <ul class="mb-3">
               <li>{_'3 nejsilnější výhody (USP), např. „Kapacita 300 hostů“, „Soukromí uprostřed přírody“, „Historický prostor s nečekaným komfortem“.'}</li>
            </ul>

            <h3 class="h5">{_'Důležitá data'}</h3>
            <ul class="mb-3">
               <li>{_'Texty v odrážkách mají až o 30 % vyšší čtenost než blokový text (zdroj: Nielsen Norman Group).'} </li>
               <li>{_'Krátké USP zvyšuje CTR o 22 % oproti obecným popisům (zdroj: HubSpot).'}</li>
            </ul>

            <h3 class="h5">{_'Best practices'}</h3>
            <ul class="mb-3">
               <li>{_'Pište benefity, ne jen popisy („Soukromí v přírodě“ místo „velký pozemek“).'} </li>
               <li>{_'Používejte čísla a fakta („Ubytování pro 120 hostů“).'} </li>
               <li>{_'Začněte tím nejdůležitějším benefitem.'}</li>
            </ul>

            <div class="p-3 radius-card border border-light-gray">
               <strong>{_'Checklist'}</strong>
               <div class="form-check">
                  <input class="form-check-input" type="checkbox"> {_'Přidány 3 highlights'}
               </div>
               <div class="form-check">
                  <input class="form-check-input" type="checkbox"> {_'Použita konkrétní čísla a benefity'}
               </div>
               <div class="form-check">
                  <input class="form-check-input" type="checkbox"> {_'Správné pořadí (nejdříve to nejdůležitější)'}
               </div>
            </div>
         </div>
      </article>
   </div>
</div>

<!--FAQ-->
<div class="offcanvas offcanvas-end" data-bs-scroll="true" data-bs-backdrop="false" tabindex="-1" id="offcanvasFAQSupport" aria-labelledby="offcanvasFAQSupportLabel">
   <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="offcanvasFAQSupportLabel">{_'Otázky a odpovědi'} ({_'FAQ'})</h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="{_'Zavřít'}"></button>
   </div>
   <div class="offcanvas-body">
      <article class="card mb-4">
         <div class="card-body">
            <h2 class="h4 mb-3">{_'FAQ – méně e-mailů, rychlejší poptávky a lepší dohledatelnost'}</h2>
            <p><strong>{_'Proč:'}</strong> {_'Opakující se otázky zdržují. FAQ šetří čas a urychluje cestu k rezervaci. Zároveň pomáhá SEO a AI vyhledávání, protože dobře strukturované otázky/odpovědi se dostávají do výsledků Google i AI nástrojů.'}</p>

            <h3 class="h5">{_'Důležitá data'}</h3>
            <ul class="mb-3">
               <li>{_'80 % dotazů klientů se opakuje (zdroj: Salesforce, State of Service Report 2022).'}</li>
               <li>{_'FAQ snižuje odchod z profilu o 25 % (zdroj: Nielsen Norman Group, UX Research 2021).'}</li>
               <li>{_'Google zobrazuje FAQ v sekci „People Also Ask“ – správně vyplněné FAQ může zvýšit prokliky až o 30 % (zdroj: Backlinko, 2023).'}</li>
               <li>{_'Strukturované FAQ (schema.org/FAQPage) - je automatickou součástí profilu - pomáhá dohledatelnosti v AI nástrojích, protože AI využívá přímo jasné otázky/odpovědi (zdroj: Google Search Central, 2023).'}</li>
            </ul>

            <h3 class="h5">{_'Best practices'}</h3>
            <ul class="mb-3">
               <li>{_'Přidejte minimálně 5–7 nejčastějších otázek.'}</li>
               <li>{_'Odpovídejte stručně, jasně, bez složitého jazyka.'}</li>
               <li>{_'Používejte celé odpovědi v přirozeném jazyce – pomáhá to SEO i AI dohledatelnosti.'}</li>
               <li>{_'U složitých témat odkažte na podmínky.'}</li>
            </ul>

            <div class="p-3 radius-card border border-light-gray">
               <strong>{_'Checklist'}</strong>
               <div class="form-check">
                  <input class="form-check-input" type="checkbox"> {_'Přidáno min. 5 otázek'}
               </div>
               <div class="form-check">
                  <input class="form-check-input" type="checkbox"> {_'Jasné a stručné odpovědi'}
               </div>
               <div class="form-check">
                  <input class="form-check-input" type="checkbox"> {_'Použité celé odpovwdi (přirozený jazyk) pro SEO/AI'}
               </div>
            </div>
         </div>
      </article>
   </div>
</div>




<script>
   $(function() {
      $('.js-example-basic-multiple').select2();
      const body = $('body');
      $('.js-select-typ-akce').select2();
      body.on('click', 'button[data-file-btn]', function() {
         const input = $(this).siblings('input[data-file-input]');
         input.click();
      }).on('change', 'input[data-file-input]', function() {
         if($(this).val()){
            const form = $(this).closest('form');
            form.submit();
            form.find('.js-add-img').attr('disabled', true);
         }
      });

   });
</script>