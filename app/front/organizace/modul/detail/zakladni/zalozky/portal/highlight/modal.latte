{varType array $icons}
{varType ?app\system\model\highlighty\OrganizaceHighlightRow $highlight}
{varType int $randomId}
{varType app\system\model\translator\Jazyky[] $organizaceLanguages}

<div class="container">
    <form method="post" id="highlight_form-{$randomId}">
        <div class="row">
           <label for="js-select-icon-{$randomId}" class="form-label">{_'Vyberte ikonu'}</label>
           <select name="icon" id="js-select-icon-{$randomId}" class="form-select">
              {foreach $icons as $icon}
                 <option value="{$icon}" {if $highlight?->icon === $icon}selected{/if} data-icon="fa {$icon}"></option>
              {/foreach}
           </select>
        </div>

       <div n:if="!$highlight" class="row mt-2">
          {if count($organizaceLanguages) > 1}
             <label for="heading_lang{$randomId}">{_'Jazyk'}</label>
             <select name="id_jazyk" id="heading_lang{$randomId}" class="form-select">
                {foreach $organizaceLanguages as $language}
                   <option value="{$language->value}">{$language->getTitle()}</option>
                {/foreach}
             </select>
          {elseif !empty($organizaceLanguages)}
             {var $lang = first($organizaceLanguages)}
             <label class="form-label">{_'Jazyk'}</label>
             <h6>{$lang->getTitle()}</h6>
             <input type="hidden" name="id_jazyk" n:attr="value: $lang->value">
          {/if}
       </div>
       <div n:else class="row mt-4">
          <label class="form-label">{_'Jazyk'}</label>
          <h6>{$highlight->getJazyk()->getTitle()}</h6>
       </div>

       <hr class="my-4">

        <div class="row">
           <div class="col-12 mb-3">
              {var $idSuffix = $highlight?->id_jazyk ?: $randomId}
              <label for="heading_{$idSuffix}" class="form-label">{_'Nadpis'}</label>
              <input maxlength="60" type="text" class="form-control mb-2 js-title-input" id="heading_{$idSuffix}" name="title"
                      n:attr="value: $highlight?->title" required>
              <div class="form-text">
                 {_'Max. 60 znaků.'}
              </div>

              <label for="description_{$idSuffix}" class="form-label">{_'Popis'}</label>
              <textarea maxlength="120" class="form-control" id="description_{$idSuffix}"
                        name="text"
              >{$highlight?->text}</textarea>
              <div class="form-text">
                 {_'Max. 120 znaků.'}
              </div>
           </div>
        </div>

        <div class="fixed-bottom pe-none d-flex justify-content-center">
            <div class="d-inline-flex flex-column flex-lg-row flex-wrap align-items-center bg-white border border-light-gray radius-card shaddow-light shaddow-hover my-2 p-2 pe-auto gap-2">
                <div class="d-flex gap-2" n:if="$highlight">
                    <div class="col-auto">
                        <button type="submit"
                                class="btn btn-outline-secondary no-validate w-auto shaddow-hover"
                                name="btnDeleteHighlight"
                                data-confirm="{_'Opravdu chcete smazat highlight?'}"
                                formnovalidate
                        >
                           {_'Smazat'}
                        </button>
                    </div>
                    <div class="col-auto">
                        <input type="hidden" name="id_highlight" value="{$highlight->id}">
                        <button id="editButton" type="submit" class="btn btn-secondary w-auto shaddow-hover" name="btnEditHighlight">{_'Upravit'}</button>
                    </div>
                </div>
                <div n:else>
                    <div class="col-auto">
                        {if !empty($organizaceLanguages)}
                           <button id="addButton" type="submit" class="btn btn-secondary shaddow-hover w-auto" name="btnAddHighlight">{_'Vytvořit highlight'}</button>
                        {else}
                           <p>{_'Nelze vytvořit další highligth'}</p>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
    $(function(){
        const form = $("form#highlight_form-" + {$randomId});
        form.validate({
            ignore: '.no-validate',
        });

        const select2input = form.find("#js-select-icon-" + {$randomId});
        select2input.select2({
            minimumResultsForSearch: Infinity,
            dropdownParent: form,
            templateResult: format,
            templateSelection: format,
            escapeMarkup: function(m) { return m; }
        });

        function format(state) {
            if (!state.id) return state.text;
            return $('<span><i class="' + state.id + ' fa-lg" style="margin-right:5px;"></i> ' + state.text + '</span>');
        }
    });
</script>