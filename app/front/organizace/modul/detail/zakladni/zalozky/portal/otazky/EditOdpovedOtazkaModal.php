<?php namespace app\front\organizace\modul\detail\zakladni\zalozky\portal\otazky;

use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\model\katalog\otazky\event\DeleteOtazkaOrganizaceEvent;
use app\system\model\katalog\otazky\event\SaveOtazkaOdpovedEvent;
use app\system\model\katalog\otazky\OtazkyOrganizace;
use app\system\model\katalog\otazky\OtazkyOrganizaceOdpoved;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\modul\modal\AjaxModal;
use Nette\Utils\Random;

/** Created by <PERSON><PERSON><PERSON>. Date: 13.02.2025 */
class EditOdpovedOtazkaModal extends AjaxModal
{
   
   public function getTitleName() :string {
      return 'Upravit odpověd';
   }

   public function prepareAjaxData() :void {
      $decodedData = json_decode($_POST['slug'], true);

      if (!isset($decodedData['id_otazka'], $decodedData['id_organizace'])) {
         throw FlashException::create('Došlo k chybě');
      }

      $this->id_organizace = $decodedData['id_organizace'];
      $this->id_otazka = $decodedData['id_otazka'];
      $this->odpovedi = OtazkyOrganizaceOdpoved::getForSpecificOrganizace($this->id_organizace, $this->id_otazka);
      $this->primarniJazykOrganizace = OrganizaceJazyky::getPrimary($this->id_organizace);
      $this->jazykyOrganizace = OrganizaceJazyky::getPairs($this->id_organizace);
   }

   public function prepareModal(Templater $templater) :void {
      $templater->addData([
         'id_otazka' => $this->id_otazka,
         'id_organizace' => $this->id_organizace,
         'nepouzivaneOtazky' =>  null,
         'odpovedi' =>  $this->odpovedi,
         'jazyky' => $this->jazykyOrganizace,
         'primarniJazykOrganizace' => $this->primarniJazykOrganizace,
         'randomID' => Random::generate(),
         'modalID' => $this->getIdModal(),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnSmazatOdpovedi', function($post) {
         if (
            !isset($post['id_otazka'], $post['id_organizace']) ||
            !($row = OtazkyOrganizace::get($post['id_otazka'], $post['id_organizace']))
         ) {
            throw FlashException::create('Chyba při mazání otázky');
         }

         (new DeleteOtazkaOrganizaceEvent($row))->call();
         FlashMessages::setSuccess('Otázka byla smazána');
      });

      $this->isset('btnUpravitOdpovedi', function($post) {
         if (
            !isset($post['id_otazka'], $post['id_organizace']) ||
            !($row = OtazkyOrganizace::get($post['id_otazka'], $post['id_organizace']))
         ) {
            throw FlashException::create('Došlo k chybě');
         }

         if((new SaveOtazkaOdpovedEvent($row, $post))->call()->getSuccess())
            FlashMessages::setSuccess('Odpovědi byly upraveny');
      });
   }

   /** @var OtazkyOrganizaceOdpoved[] $odpovedi*/
   private array $odpovedi;

   /** @var array<int,string> $jazykyOrganizace*/
   private array $jazykyOrganizace;

   /** @var int $primarniJazykOrganizace*/
   private int $primarniJazykOrganizace;

   /** @var int $id_organizace*/
   private int $id_organizace;

   /** @var int $id_otazka */
   private int $id_otazka;
}