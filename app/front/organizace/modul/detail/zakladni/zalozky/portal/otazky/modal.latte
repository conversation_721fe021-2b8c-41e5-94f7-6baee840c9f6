{varType ?app\system\model\katalog\otazky\OtazkyRow[] $nepouzivaneOtazky}
{varType app\system\model\translator\Jazyky[] $jazyky}
{varType int $primarniJazykOrganizace}
{varType int $id_organizace}
{varType ?app\system\model\katalog\otazky\OtazkyOrganizaceOdpovedRow $odpovedi}
{varType ?int $id_otazka}
{varType int $randomID}
{varType ?string $modalID}

<form method="post" id="odpovedi-form-{$randomID}">
   {if $id_otazka == null}
      <div class="row">
         <label for="otazky">{_'Vyberte otázku'}</label>
         <div class="input-group mb-3 d-flex gap-2">
            <div class="input-group-prepend col-12 col-lg">
               <select id="otazky" class="form-select js-select-otazka " name="id_otazka" required>
                  <option></option>
                  {foreach $nepouzivaneOtazky as $otazka}
                     <option value="{$otazka->id}">
                        {$otazka->getName($primarniJazykOrganizace)}
                     </option>
                  {/foreach}
               </select>
            </div>
         </div>
      </div>
   {/if}
   <div class="row">
      {*@TODO sem přidat znění otázky prosím*}
{*      <p>Znění otázky</p>*}
      {foreach $jazyky as $id_jazyk => $nazev_jazyk}
         <div class="col-12">
            <div class="mb-3">
               <label for="nazev_{$id_jazyk}" class="form-label">
                  {$nazev_jazyk}
               </label>
               <textarea
                      class="form-control odpovedi-input"
                      id="nazev_{$id_jazyk}"
                      rows="3"
                      name="odpovedi[{$id_jazyk}]"
                       n:attr="required: $id_jazyk === $primarniJazykOrganizace,
                       value: (isset($odpovedi) && array_key_exists($id_jazyk, $odpovedi)) ? $odpovedi[$id_jazyk]->odpoved : ''">

               </textarea>
            </div>
         </div>
      {/foreach}

      {if !empty($odpovedi)}
         <input type="hidden" name="organizace_odpoved_id" value="{current($odpovedi)->id_organizace_otazka}">
      {/if}
   </div>

   <div class="fixed-bottom pe-none d-flex justify-content-center">
      <div class="d-inline-flex flex-column flex-lg-row flex-wrap align-items-center bg-white border border-light-gray radius-card shaddow-light shaddow-hover my-2 p-2 pe-auto gap-2">
         <input type="hidden" name="id_organizace" value="{$id_organizace}">

         {if !$id_otazka}
            <button type="submit" class="btn btn-secondary w-auto shaddow-hover" name="btnVytvoritOdpovedi">{_'Uložit'}</button>
         {else}
            <input type="hidden" name="id_otazka" value="{$id_otazka}">

            <button id="deleteButton"
                    type="submit"
                    class="form-control btn btn-outline-secondary w-auto shaddow-hover no-validate"
                    formnovalidate
                    name="btnSmazatOdpovedi">{_'Smazat'}
            </button>

            <button type="submit" class="btn btn-secondary w-auto shaddow-hover" name="btnUpravitOdpovedi">{_'Uložit'}</button>
         {/if}
      </div>
   </div>
</form>

<script>
   $(function () {
      const form = $('form#odpovedi-form-' + {$randomID});

      form.find('.js-select-otazka').select2({
         placeholder: "{_'Vyberte otázku'}",
         dropdownParent: $('#' + {$modalID})
      });

      form.validate({
         ignore: '.no-validate',
      });

      form.find('input.odpovedi-input').each(function () {
         $(this).rules("add", {
            noSpaceOnly: true
         });
      });
   });
</script>