<?php namespace app\front\organizace\modul\detail;

use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\modul\detail\texty\zalozky\kalkulace\modal\EditKalkulaceTextSablonaModal;
use app\front\organizace\modul\detail\texty\zalozky\kalkulace\modal\NovyKalkulaceTextSablonaModal;
use app\front\organizace\modul\detail\zakladni\zalozky\portal\sluzby\EditOrganizaceSluzbaModal;
use app\front\formular\modals\formular\kopie\NovaKopieFormulareModal;
use app\front\formular\modals\formular\novy\NovyCistyFormularModal;
use app\front\formular\modals\respondents\show\DisplayFormularRespondentValues;
use app\front\organizace\modul\detail\ciselniky\CiselnikyPanel;
use app\front\organizace\modul\detail\ciselniky\zalozky\ceny\modal\EditCenoveSkupinyModal;
use app\front\organizace\modul\detail\ciselniky\zalozky\ceny\modal\NovaCenovaSkupinaModal;
use app\front\organizace\modul\detail\ciselniky\zalozky\priznaky\modal\edit\DetailPriznakModal;
use app\front\organizace\modul\detail\ciselniky\zalozky\priznaky\modal\novy\NovyPriznakModal;
use app\front\organizace\modul\detail\ciselniky\zalozky\prostory\modal\detail\DetailMistnostiModal;
use app\front\organizace\modul\detail\ciselniky\zalozky\prostory\modal\novy\NovaMistnostModal;
use app\front\organizace\modul\detail\ciselniky\zalozky\tagy\modal\novy\EditZakaznikTagModal;
use app\front\organizace\modul\detail\ciselniky\zalozky\tagy\modal\novy\NovyZakaznikTagModal;
use app\front\organizace\modul\detail\ciselniky\zalozky\typy\modal\edit\EditTypAkceModal;
use app\front\organizace\modul\detail\ciselniky\zalozky\typy\modal\novy\NovyTypAkceModal;
use app\front\organizace\modul\detail\ciselniky\zalozky\zdroje\modal\edit\EditMistaZdrojeModal;
use app\front\organizace\modul\detail\ciselniky\zalozky\zdroje\modal\novy\NovyMistaZdrojModal;
use app\front\organizace\modul\detail\inventar\InventarPanel;
use app\front\organizace\modul\detail\inventar\modal\edit\EditPolozkyInvetareModal;
use app\front\organizace\modul\detail\inventar\modal\novy\NovaPolozkaInventareModal;
use app\front\organizace\modul\detail\nastaveni\modal\edit\EditOrganizaceKalkulacePrilohaModal;
use app\front\organizace\modul\detail\nastaveni\modal\localtax\NastaveniLocalTaxModal;
use app\front\organizace\modul\detail\nastaveni\modal\NovaOrganizaceKalkulacePrilohaModal;
use app\front\organizace\modul\detail\nastaveni\NastaveniPanel;
use app\front\organizace\modul\detail\polozky\baliky\modal\edit\EditBalikModal;
use app\front\organizace\modul\detail\polozky\baliky\modal\novy\NovyBalikModal;
use app\front\organizace\modul\detail\polozky\kategorie\edit\EditKategorieModal;
use app\front\organizace\modul\detail\polozky\kategorie\novy\NovaKategorieModal;
use app\front\organizace\modul\detail\polozky\modal\EditPolozkyModal;
use app\front\organizace\modul\detail\polozky\modal\NovaPolozkaModal;
use app\front\organizace\modul\detail\polozky\PolozkyPanel;
use app\front\organizace\modul\detail\texty\TextyPanel;
use app\front\organizace\modul\detail\texty\zalozky\bloky\modals\NovyObsahBlokModal;
use app\front\organizace\modul\detail\texty\zalozky\emaily\log\modal\DetailEmailOrganizaceModal;
use app\front\organizace\modul\detail\texty\zalozky\emaily\modals\novy\NovyMailMistaModal;
use app\front\organizace\modul\detail\texty\zalozky\smlouvy\modals\nova\NovaSmlouvaModal;
use app\front\organizace\modul\detail\uzivatele\PrirazeniUzivatelePanel;
use app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\modal\aktivita\edit\EditMistaAktivityModal;
use app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\modal\aktivita\novy\NovaMistaAktivitaModal;
use app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\modal\sada\edit\EditMistaSadaAktivitModal;
use app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\modal\sada\novy\NovaMistaSadaAktivitModal;
use app\front\organizace\modul\detail\uzivatele\zalozky\kalendar\modal\AddCalendarModal;
use app\front\organizace\modul\detail\uzivatele\zalozky\kalendar\modal\EditCalendarModal;
use app\front\organizace\modul\detail\uzivatele\zalozky\personal\modal\personal\edit\EditPersonalModal;
use app\front\organizace\modul\detail\uzivatele\zalozky\personal\modal\personal\novy\NovyPersonalModal;
use app\front\organizace\modul\detail\uzivatele\zalozky\pozice\modal\edit\EditPozicePersonalModal;
use app\front\organizace\modul\detail\uzivatele\zalozky\pozice\modal\novy\NovaPozicePersonalModal;
use app\front\organizace\modul\detail\uzivatele\zalozky\tymy\modal\edit\EditTymModal;
use app\front\organizace\modul\detail\uzivatele\zalozky\tymy\modal\novy\NovyTymModal;
use app\front\organizace\modul\detail\zakladni\ZakladniUdajePanel;
use app\front\organizace\modul\detail\zakladni\zalozky\api\modal\edit\EditApiKliceModal;
use app\front\organizace\modul\detail\zakladni\zalozky\api\modal\form\NahledApiPoptavkaFormModal;
use app\front\organizace\modul\detail\zakladni\zalozky\api\modal\novy\NovyMistaApiKlicModal;
use app\front\organizace\modul\detail\zakladni\zalozky\portal\highlight\EditHighlightModal;
use app\front\organizace\modul\detail\zakladni\zalozky\portal\highlight\PridatHighlightModal;
use app\front\organizace\modul\detail\zakladni\zalozky\portal\sluzby\NovaOrganizaceSluzbaModal;
use app\front\organizace\modul\detail\zakladni\zalozky\portal\otazky\EditOdpovedOtazkaModal;
use app\front\organizace\modul\detail\zakladni\zalozky\portal\otazky\NovaOdpovedOtazkaModal;
use app\front\platby\scenare\prehled\modals\NovyPlatebniScenarModal;
use app\front\platby\scenare\prehled\panely\SeznamPlatebnichScenaruPanel;
use app\system\application\FrontApplicationEnvironment;
use app\system\lay\panels\PanelsLayout;
use app\system\model\formular\enumy\FormulareTypyEnum;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\organizace\subscription\OrganizaceLicenceTyp;

/** Created by Kryštof Czyź. Date: 17.04.2022 */
class DetailMistaPage extends PanelsLayout
{

   function getPageName() :string {
      return 'Nastavení organizace';
   }

   function preparePanely() :void {
      $id_mista = $this->id_mista;
      $env = FrontApplicationEnvironment::get();

      $this->appendZakladniUdaje($env);

      if(!$env->licenceTyp->hasEventCRM())
         return;

      $this->subscribePanel(
         PrirazeniUzivatelePanel::init()
            ->addParameter('id_mista', $id_mista)
            ->addModal(NovyPersonalModal::init()
               ->setIdMista($id_mista))
            ->addModal(EditPersonalModal::init())
            ->addModal(NovaPozicePersonalModal::init())
            ->addModal(NovaMistaAktivitaModal::init()
               ->setIdMista($id_mista))
            ->addModal(NovaPozicePersonalModal::init())
            ->addModal(EditPozicePersonalModal::init())
            ->addModal(EditTymModal::init())
            ->addModal(NovyTymModal::init()
               ->setMisto($this->id_mista))
            ->addModal(NovaMistaSadaAktivitModal::init()
               ->setIdMista($id_mista))
            ->addModal(EditMistaSadaAktivitModal::init())
            ->addModal(EditMistaAktivityModal::init())
            ->addModal(EditPozicePersonalModal::init())
            ->addModal(AddCalendarModal::init()->setOrganizaceID($id_mista))
            ->addModal(EditCalendarModal::init())
      );


      $this->subscribePanel(CiselnikyPanel::init()
         ->addParameter('id_mista', $id_mista)
         ->addModal(EditTypAkceModal::init())
         ->addModal(NovyTypAkceModal::init()->setIdMista($id_mista))
         ->addModal(NovaCenovaSkupinaModal::init()->setMisto($id_mista))
         ->addModal(EditCenoveSkupinyModal::init())
         ->addModal(EditMistaZdrojeModal::init())
         ->addModal(NovyMistaZdrojModal::init()->setIdMista($id_mista))

         ->addModal(NovyPriznakModal::init()->setIdOrganizace($id_mista))
         ->addModal(NovyZakaznikTagModal::init()->setIdOrganizace($id_mista))
         ->addModal(EditZakaznikTagModal::init()->setIdOrganizace($id_mista))
         ->addModal(DetailPriznakModal::init()->setIdOrganizace($id_mista)));

      if($this->environment->versionType->isVenue())
         $this->addModal(NovaMistnostModal::init())->addModal(DetailMistnostiModal::init());

      $this->subscribePanel(InventarPanel::init()
         ->addParameter('id_mista', $id_mista)
         ->addModal(EditPolozkyInvetareModal::init())
         ->addModal(NovaPolozkaInventareModal::init()));

      $this->subscribePanel(TextyPanel::init()
         ->addParameter('id_mista', $id_mista)
         ->addModal(DisplayFormularRespondentValues::init())
         ->addModal(NovyCistyFormularModal::init()
            ->setIdOrganizace($id_mista)
            ->setEntity($id_mista, FormulareTypyEnum::TEMPLATE)
            ->setJazykoveMutace(OrganizaceJazyky::getAll($id_mista)))
         ->addModal(NovaKopieFormulareModal::init()
            ->setEntity($id_mista, FormulareTypyEnum::TEMPLATE)
            ->setIdOrganizace($id_mista)
            ->setJazykoveMutace(OrganizaceJazyky::getAll($id_mista)))
         ->addModal(NovyMailMistaModal::init()
            ->setIdMista($id_mista))
         ->addModal(NovyObsahBlokModal::init()
            ->setIdMista($id_mista))
         ->addModal(NovaSmlouvaModal::init()
            ->setIdMista($id_mista))
         ->addModal(DetailEmailOrganizaceModal::init())
         ->addModal(NovyKalkulaceTextSablonaModal::init()
            ->setOrganizaceID($id_mista))
         ->addModal(EditKalkulaceTextSablonaModal::init())
      );

      $this->subscribePanel(PolozkyPanel::init()
         ->addParameter('id_mista', $id_mista)
         ->addModal(EditPolozkyModal::init())
         ->addModal(NovaPolozkaModal::init()->setMisto($id_mista))
         ->addModal(EditBalikModal::init())
         ->addModal(NovyBalikModal::init()->setMisto($id_mista))
         ->addModal(EditKategorieModal::init())
         ->addModal(NovaKategorieModal::init()->setMisto($id_mista))
      );

      $this->subscribePanel(SeznamPlatebnichScenaruPanel::init()
         ->addModal(NovyPlatebniScenarModal::init()
            ->setIdOrganizace($id_mista)));

      $this->subscribePanel(NastaveniPanel::init()
         ->addParameter('id_mista', $id_mista)
         ->addModal(EditOrganizaceKalkulacePrilohaModal::init())
         ->addModal(NovaOrganizaceKalkulacePrilohaModal::init()
            ->setIdOrganizace($id_mista))
         ->addModal(NastaveniLocalTaxModal::init()
            ->setIdOrganizace($id_mista))
      );
   }

   public function setIdMista(int $id_mista) :DetailMistaPage {
      $this->id_mista = $id_mista;
      return $this;
   }

   private function appendZakladniUdaje(FrontApplicationEnvironment $env) :void {
      $panel = ZakladniUdajePanel::init()
         ->addParameters(['id_mista' => $this->id_mista])
         ->addModal(NovaOrganizaceSluzbaModal::init()
            ->setIdOrganizace($this->id_mista))
         ->addModal(EditOrganizaceSluzbaModal::init())
         ->addModal(NovaOdpovedOtazkaModal::init()
            ->setIdOrganizace($this->id_mista))
         ->addModal(EditOdpovedOtazkaModal::init())
         ->addModal(PridatHighlightModal::init()
            ->setIdOrganizace($this->id_mista))
         ->addModal(EditHighlightModal::init());

      if($env->licenceTyp->hasEventCRM())
         $panel
            ->addModal(EditApiKliceModal::init())
            ->addModal(NovyMistaApiKlicModal::init()
               ->setIdMista($this->id_mista))
            ->addModal(NahledApiPoptavkaFormModal::init()
               ->setIdMista($this->id_mista));

      if(
         FrontApplicationEnvironment::get()->licenceTyp === OrganizaceLicenceTyp::ONLY_KATALOG
         && Organizace::getMisto($this->id_mista)->getVersion()->isVenue()
      ){
         $panel
            ->addModal(NovaMistnostModal::init())
            ->addModal(DetailMistnostiModal::init());
      }

      $this->subscribePanel($panel);
   }

   protected int $id_mista;
}