<?php namespace app\front\organizace\model\organizace;

use app\front\controllers\organizace\DetailMistaController;
use app\front\organizace\model\organizace\soubory\OrganizaceSoubory;
use app\katalog\organizace\DetailOrganizaceController;
use app\system\application\ApplicationVersion;
use app\system\helpers\row\EntryRow;
use app\system\model\akce\typ\AkceTypOrganizace;
use app\system\model\formular\enumy\FormulareTypyEnum;
use app\system\model\formular\IEntityFormular;
use app\system\model\highlighty\OrganizaceHighlight;
use app\system\model\highlighty\OrganizaceHighlightRow;
use app\system\model\katalog\KatalogOrganizaceRegion;
use app\system\model\katalog\otazky\OtazkyOrganizace;
use app\system\model\organizace\akce\OrganizaceAkce;
use app\system\model\organizace\fakturace\FakturacniAdresa;
use app\system\model\organizace\fakturace\FakturacniAdresaRow;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\organizace\kontakt\OrganizaceKontakt;
use app\system\model\organizace\kontakt\OrganizaceKontaktRow;
use app\system\model\organizace\portal\OrganizacePortal;
use app\system\model\organizace\portal\OrganizacePortalRow;
use app\system\model\organizace\subscription\OrganizaceLicenceTyp;
use app\system\model\translator\Jazyky;
use app\system\model\vlastnosti\IVlastnosti;
use app\system\model\vlastnosti\VlastnostiEntityType;

/** Created by Filip Pavlas. Date: 24.03.2022 */
class OrganizaceRow extends EntryRow
   implements IEntityFormular,IVlastnosti
{

   public int $id_organizace, $id_uzivatel, $typ;

   /**
    * @var int Slouží pro uložení informace, zda organizace Používá celý systém, nebo jen Katalog a základní příjem poptávek
    * @link OrganizaceLicenceTyp
    */
   public int $licence_typ = 1;
   public string $nazev, $portal_odkaz;
   private array $otazkyOrganizaceIds;

   /** @return array<int,int> */
   public function getCasteOtazkyIds() :array {
      return $this->otazkyOrganizaceIds ??= OtazkyOrganizace::getOtazkyForOrganizaceIds($this->id_organizace);
   }
   /** @var OrganizaceHighlightRow[]  */
   private array $highlights;
   /** @var OrganizaceHighlightRow[]  */
   private ?array $allHighlights = null;

   public function getHighlightsForLanguage(Jazyky $jazyk) :array {
      $languageKey = $jazyk->value;

      if (!isset($this->highlights))
         $this->highlights = [];

      if (isset($this->highlights[$languageKey]))
         return $this->highlights[$languageKey];

      $availableLanguages = OrganizaceJazyky::getAllJazyky($this->id_organizace);

      if (!in_array($jazyk, $availableLanguages, true)) {
         $primaryLanguage = OrganizaceJazyky::getPrimary($this->id_organizace);

         return $this->highlights[$primaryLanguage] ??= OrganizaceHighlight::getHighlightsForOrganizaceByIds(
            OrganizaceHighlight::getHighlightsIdsForOrganizaceInLanguage($this->id_organizace, $primaryLanguage)
         );
      }

      return $this->highlights[$languageKey] ??= OrganizaceHighlight::getHighlightsForOrganizaceByIds(
         OrganizaceHighlight::getHighlightsIdsForOrganizaceInLanguage($this->id_organizace, $languageKey)
      );
   }

   /** @return OrganizaceHighlightRow[] */
   public function getAllHighlightsForOrganizace() :array {
      return $this->allHighlights ??= OrganizaceHighlight::getForOrganizace($this->id_organizace);
   }

   public function getID() :int {
      return $this->id_organizace;
   }

   public function getVlastnostiEntityType() :VlastnostiEntityType {
      return VlastnostiEntityType::ORGANIZACE;
   }

   /** @return array<int,int> */
   public function getPouzivaneTypyAkciIds() :array {
      return $this->pouzivanetypyAkciIDs ??= AkceTypOrganizace::getIDsForOrganizace($this->id_organizace);
   }

   /** @return int[] */
   public function getVlasnostiIDs() :array {
      return $this->vlasnostiIDs ??= $this->getVlastnostiEntityType()->getIdsVlastnosti($this->getID());
   }

   /** @return array<int, string> */
   public function getAkceTypySelectData() :array {
      return OrganizaceAkce::getSelectData($this->id_organizace);
   }

   /** @return array<int, string> */
   public function getSystemAkceTypySelectData() :array {
      return AkceTypOrganizace::getOrganizacePairs($this->id_organizace);
   }

   public function isDodavatel() :bool {
      return $this->getTypOrganizace() === OrganizaceTypy::VENDOR;
   }

   public function getImagesDirectory() :string {
      return sprintf('directory_%04d_images', $this->id_organizace);
   }

   public function getImagesDirectoryCrc32() :string {
      return crc32($this->getImagesDirectory());
   }

   public function getAttachmentsDirectory() :string {
      return sprintf('directory_%04d_attachments', $this->id_organizace);
   }

   public function getTypOrganizace() :OrganizaceTypy {
      return $this->type ??= OrganizaceTypy::from($this->typ);
   }

   public function getKontakt() :OrganizaceKontaktRow {
      return $this->kontakt ??=
         (OrganizaceKontakt::get($this->id_organizace)
            ?: (new OrganizaceKontaktRow())->setIdOrganizace($this->id_organizace));
   }

   public function getFakturacniAdresa() :?FakturacniAdresaRow {
      return $this->fakturacniAdresa ??= FakturacniAdresa::get($this->id_organizace);
   }

   public function getEntityId() :int {
      return $this->id_organizace;
   }

   public function getFormularEntityType() :FormulareTypyEnum {
      return FormulareTypyEnum::TEMPLATE;
   }

   public function getClientDetailUrl() :string {
      return DetailOrganizaceController::getUrlOrganizace($this);
   }

   public function getDetailUrl() :string {
      return DetailMistaController::getUrl($this->id_organizace);
   }

   public function getVersion() :ApplicationVersion {
      return ApplicationVersion::from($this->typ);
   }

   public function getSoubory() :OrganizaceSoubory {
      return $this->soubory ??= new OrganizaceSoubory($this);
   }

   public function getCapacity() :?OrganizaceKapacitaRow {
      return $this->capacity ??= OrganizaceKapacita::get($this->id_organizace);
   }

   public function getPortalInfo() :?OrganizacePortalRow {
      return $this->portalInfo ??= OrganizacePortal::get($this->id_organizace);
   }

   public function getPrimaryJazyk() :Jazyky {
      return $this->primaryJazyk ??= Jazyky::from(OrganizaceJazyky::getPrimary($this->id_organizace));
   }

   public function getKatalogRegion() :KatalogOrganizaceRegion {
      return KatalogOrganizaceRegion::CZ;
   }

//   @TODO připravit pro různé jazyky
   public function getKatalogUrl() :string {
      return DetailOrganizaceController::getUrl('cz', $this->portal_odkaz, Jazyky::CZ->getISO6391());
   }

   public function getLicenceTyp() :OrganizaceLicenceTyp {
      return OrganizaceLicenceTyp::from($this->licence_typ);
   }

   private OrganizaceTypy $type;
   private OrganizaceKontaktRow $kontakt;
   private ?FakturacniAdresaRow $fakturacniAdresa;
   private OrganizaceSoubory $soubory;
   private ?OrganizaceKapacitaRow $capacity;
   private array $pouzivanetypyAkciIDs;

   /** @return int[] */
   private array $vlasnostiIDs;
   private Jazyky $primaryJazyk;
}