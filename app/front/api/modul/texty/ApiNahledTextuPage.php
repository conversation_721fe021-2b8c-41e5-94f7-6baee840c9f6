<?php namespace app\front\api\modul\texty;

use app\system\component\Templater;
use app\system\helpers\text\blok\TextBlokyComponent;
use app\system\helpers\text\text\TextViewerComponent;
use app\system\lay\nolog\NologLayout;

class ApiNahledTextuPage extends NologLayout
{

   function getPageName(): string {
      return 'Náhled textu';
   }

   protected function prepareTemplate(Templater $templater) {
      if(str_starts_with($this->id_text, 's')){
         $id = substr($this->id_text, 1);

         $templater->addData([
            'text' => TextViewerComponent::getComponent()->setIdText((int)$id)->setType(TextBlokyComponent::SABLONA),
         ]);
      }

      else
         $templater->addData([
            'text' => TextViewerComponent::getComponent()->setIdText((int)$this->id_text)->setType(TextBlokyComponent::TEXT),
         ]);
   }

   public function setIdText(string $id_text) :ApiNahledTextuPage {
      $this->id_text = $id_text;
      return $this;
   }

   protected string $id_text;
}