<?php namespace app\front\api;

use app\system\component\Templater;
use app\system\lay\nolog\NologLayout;

/** Created by <PERSON><PERSON>. Date: 20.03.2023 */
class InfoPage extends NologLayout
{

   function getPageName() :string {
     return 'Lead wasn\'t save';
   }

   protected function prepareTemplate(Templater $templater) {
      $templater->addData([
         'header' => $this->headerText,
         'content' => $this->contentText,
         'backUrl' => $this->originalPage,
      ]);
   }

   public function setContent(string $header, ?string $page = null, ?string $content = null) :self {
      $this->headerText = $header;
      $this->contentText = $content;
      $this->originalPage = $page;
      return $this;
   }

   public string $headerText;
   public ?string $contentText, $originalPage;

}