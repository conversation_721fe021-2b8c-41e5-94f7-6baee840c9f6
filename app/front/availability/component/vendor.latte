{varType app\front\availability\vendor\VendorAvailabilityContainer $checker}

<div n:class="card, p-3, border, $checker->state->getBorderColorCss()">
   <div class="row gap-2">
      <div class="col-auto d-flex align-items-center">
         {if $checker->state->isEmpty()}
            <p class="h3">
               <i class="align-middle me-2 bi bi-calendar-check text-success"></i>{_'<PERSON><PERSON><PERSON> je volný'}
            </p>
         {elseif $checker->state->isFull()}
            <p class="h3">
               <i class="align-middle me-2 bi bi-calendar-x text-danger"></i>{_'Termín je obsazen'}
            </p>
         {else}
            <p class="h3">
               <i class="align-middle me-2 bi bi-calendar-week text-secondary"></i>{_'Termín je částečně obsazen'}
            </p>
         {/if}
      </div>
   </div>
   {if !empty($checker->conflicts)}
      <hr class="my-2">
      <div class="row mt-2">
         <div class="col-md-3 col-xl-2 col-12 d-flex align-items-center">
            <label>{_'Tento den'}</label>
         </div>
         <div class="vr p-0 ms-3 me-3 d-md-inline-block d-none"></div>
         <div class="col-md col-12">
            {foreach $checker->conflicts as $conflictEvent}
               <div class="my-1">
                  <div class="col-12">
                     <span class="lead"><a n:tag-if="$conflictEvent->detailUrl" n:attr="href: $conflictEvent->detailUrl" class="text-dark">{$conflictEvent->nazevEventu}</a></span>
                  </div>
                  <div class="col-12">
                     <span n:class="text-sm, me-1, badge, $conflictEvent->typ->getTitleCssClass()">{_$conflictEvent->typ->getTitle()}</span>
                     <span n:if="$conflictEvent->mistoPoradani" class="text-sm me-1"><i class="me-1 bi bi-geo"></i>{$conflictEvent->mistoPoradani->nazev}</span>{*@TODO adresa místa konání*}

                     {if $conflictEvent->start && $conflictEvent->end}
                        <span class="text-sm mx-1">
                           <i class="mx-1 bi bi-clock-history"></i>
                           {if $conflictEvent->start->diff($conflictEvent->end)->days > 0}
                              {$conflictEvent->start|date: 'j.n. H:i'}&nbsp;-&nbsp;{$conflictEvent->end|date: 'j.n. H:i'}
                           {else}
                              {$conflictEvent->start|date: 'j.n. H:i'}&nbsp;-&nbsp;{$conflictEvent->end|date: 'H:i'}
                           {/if}
                        </span>
                     {elseif $conflictEvent->start}
                        <span class="text-sm mx-1">
                           <i class="mx-1 bi bi-clock-history"></i>
                           {_'od'}:&nbsp;{$conflictEvent->start|date: 'H:i'}
                        </span>
                     {elseif $conflictEvent->end}
                        <span class="text-sm mx-1">
                           <i class="mx-1 bi bi-clock-history"></i>
                           {_'do'}:&nbsp;{$conflictEvent->end|date: 'H:i'}
                        </span>
                     {/if}

                  </div>
                  <div class="col-12">
                     {foreach $conflictEvent->personalEventu as $personal}
                        <span class="text-sm mx-1"><i class="bi bi-person-lock me-1"></i>{$personal->getFullName()}</span>
                     {/foreach}
                  </div>
               </div>
            {/foreach}
         </div>
      </div>
   {/if}
   {if !empty($checker->freePersonal)}
      <hr class="my-2">
      <div class="row mt-2">
         <div class="col-md-3 col-xl-2 col-12 d-flex align-items-center">
            <label>{_'Volný personál'}</label>
         </div>
         <div class="vr p-0 ms-3 me-3 d-md-inline-block d-none"></div>
         <div class="col-md col-12">

            <div class="my-1">
               <div class="col-12">
                  {foreach $checker->freePersonal as $freePersonal}
                     <p class="me-2"><i class="me-1 bi bi-person-check"></i>{$freePersonal->getFullName()}</p>
                  {/foreach}
               </div>
            </div>

         </div>
      </div>
   {/if}
</div>