<?php namespace app\front\availability\component;

use app\front\availability\vendor\VendorAvailabilityContainer;
use app\front\availability\VenueAvailabilityCheckContainer;
use app\system\component\Component;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 25.03.2024 */
class AvailabilityContainerComponent extends Component
{

   public const DEFAULT = self::DEFAULT_TEMPLATE_NAME;

   function setData() :array {
      return [
         'checker' => $this->checkerContainer,
      ];
   }

   public function setContainer(VenueAvailabilityCheckContainer|VendorAvailabilityContainer $container) :static {
      $this->checkerContainer = $container;
      return $this;
   }

   private VenueAvailabilityCheckContainer|VendorAvailabilityContainer $checkerContainer;
}