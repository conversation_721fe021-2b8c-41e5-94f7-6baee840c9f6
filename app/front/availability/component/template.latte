{varType app\front\availability\VenueAvailabilityCheckContainer $checker}

<div n:class="card, p-3, border, $checker->state->getBorderColorCss()">
   <div class="row gap-2">
      <div class="col-auto d-flex align-items-center">
         {if $checker->state->isEmpty()}
            <p class="h3">
               <i class="align-middle me-2 bi bi-calendar-check text-success"></i>{_'<PERSON>rm<PERSON> je volný'}
            </p>
         {elseif $checker->state->isFull()}
            <p class="h3">
               <i class="align-middle me-2 bi bi-calendar-x text-danger"></i>{_'Termín je obsazen'}
            </p>
         {else}
            <p class="h3">
               <i class="align-middle me-2 bi bi-calendar-week text-secondary"></i>{_'Termín je částečně obsazen'}
            </p>
         {/if}
      </div>
   </div>
   {if !empty($checker->conflicts)}
      <hr class="my-2">
      <div class="row mt-2">
         <div class="col-md-3 col-xl-2 col-12 d-flex align-items-center">
            <label>{_'Tento den'}</label>
         </div>
         <div class="vr p-0 ms-3 me-3 d-md-inline-block d-none"></div>
         <div class="col-md col-12">
            {foreach $checker->conflicts as $conflictEvent}
               <div class="my-1">
                  <div class="col-12">
                     <span class="lead"><a n:tag-if="$conflictEvent->detailUrl" n:attr="href: $conflictEvent->detailUrl" class="text-dark">{$conflictEvent->nazevEventu}</a></span>
                  </div>
                  <div class="col-12">
                     <span n:class="text-sm, me-1, badge, $conflictEvent->typ->getTitleCssClass()">{_$conflictEvent->typ->getTitle()}</span>
                     <span n:if="$conflictEvent->nazevMistnosti" class="text-sm me-1"><i class="me-1 bi bi-geo"></i>{$conflictEvent->nazevMistnosti}</span>

                     <span n:if="$conflictEvent->pocetHostu" n:class="text-sm, mx-1, $conflictEvent->pocetHostu > ($conflictEvent->kapacitaMistnosti ?: 0) ? text-secondary : text-success">
                        <i class="mx-1 bi bi-people"></i>{$conflictEvent->pocetHostu}{if $conflictEvent->kapacitaMistnosti}/{$conflictEvent->kapacitaMistnosti}{/if}
                     </span>

                     {if $conflictEvent->start && $conflictEvent->end}
                        <span class="text-sm mx-1">
                           <i class="mx-1 bi bi-clock-history"></i>
                           {if $conflictEvent->start->diff($conflictEvent->end)->days > 0}
                              {$conflictEvent->start|date: 'j.n. H:i'}&nbsp;-&nbsp;{$conflictEvent->end|date: 'j.n. H:i'}
                           {else}
                              {$conflictEvent->start|date: 'j.n. H:i'}&nbsp;-&nbsp;{$conflictEvent->end|date: 'H:i'}
                           {/if}
                        </span>
                     {elseif $conflictEvent->start}
                        <span class="text-sm mx-1">
                           <i class="mx-1 bi bi-clock-history"></i>
                           {_'od'}:&nbsp;{$conflictEvent->start|date: 'H:i'}
                        </span>
                     {elseif $conflictEvent->end}
                        <span class="text-sm mx-1">
                           <i class="mx-1 bi bi-clock-history"></i>
                           {_'do'}:&nbsp;{$conflictEvent->end|date: 'H:i'}
                        </span>
                     {/if}

                  </div>
               </div>
            {/foreach}
         </div>
      </div>


      {if !empty($checker->freeRooms)}
         <hr class="my-2">
         <div class="row mt-2">
            <div class="col-md-3 col-xl-2 col-12 d-flex align-items-center">
               <label>{_'Volné prostory'}</label>
            </div>
            <div class="vr p-0 ms-3 me-3 d-md-inline-block d-none"></div>
            <div class="col-md col-12">
               {foreach $checker->freeRooms as $freeRoom}
                  <div class="my-1">
                     <div class="col-12">
                        <span n:class="text-sm, me-1, badge, text-bg-light">{$freeRoom->getTypMistnosti()->getTranslatedTitle()}</span>
                        <span class="text-sm me-1"><i class="me-1 bi bi-geo"></i>{$freeRoom->nazev}</span>
                        <span n:class="text-sm, mx-1, text-success">
                        <i class="mx-1 bi bi-people"></i>{_'Kapacita'}: {$freeRoom->kapacita}
                     </span>
                     </div>
                  </div>
               {/foreach}
            </div>
         </div>
      {/if}

      {if !empty($checker->overflowRooms)}
         <hr class="my-2">
         <div class="row mt-2">
            <div class="col-md-auto col-12 d-flex align-items-center">
               <label>{_'Místnosti s nedostačující kapacitou'}</label>
            </div>
            <div class="vr p-0 ms-3 me-3 d-md-inline-block d-none"></div>
            <div class="col-md col-12">
               {foreach $checker->overflowRooms as $freeRoom}
                  <div class="my-1">
                     <div class="col-12">
                        <span n:class="text-sm, me-1, badge, text-bg-light">{$freeRoom->getTypMistnosti()->getTranslatedTitle()}</span>
                        <span class="text-sm me-1"><i class="me-1 bi bi-geo"></i>{$freeRoom->nazev}</span>
                        <span n:class="text-sm, mx-1, text-danger">
                        <i class="mx-1 bi bi-people"></i>{_'Kapacita'}: {$freeRoom->kapacita}
                     </span>
                     </div>
                  </div>
               {/foreach}
            </div>
         </div>
      {/if}
   {/if}
</div>