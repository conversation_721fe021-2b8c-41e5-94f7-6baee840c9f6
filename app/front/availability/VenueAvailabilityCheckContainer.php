<?php namespace app\front\availability;

use app\front\availability\component\AvailabilityContainerComponent;
use app\system\model\organizace\mistnosti\MistnostRow;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 19.03.2024 */
class VenueAvailabilityCheckContainer
   implements IAvailabilityCheckContainer
{


   public function __construct(
      public readonly AvailabilityState $state,
      /** @var AvailabilityConflictEntity[] */
      public readonly array $conflicts = [],
      /** @var array<int, MistnostRow> */
      public readonly array $freeRooms = [],
      /** @var array<int, MistnostRow> */
      public readonly array $overflowRooms = [],
      public readonly ?int $entityCapacity = null,
   ) { }

   public function renderComponent(string $template = AvailabilityContainerComponent::DEFAULT) :Html {
      return new Html(AvailabilityContainerComponent::getComponent()
         ->setContainer($this)
         ->prepareTemplater($template)
         ->render());
   }
}