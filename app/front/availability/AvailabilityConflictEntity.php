<?php namespace app\front\availability;

use DateTimeImmutable;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 19.03.2024 */
class AvailabilityConflictEntity
{

   public function __construct(
      public readonly ConflictEntityType $typ,
      public readonly string             $nazevEventu,
      public readonly ?string            $nazevMistnosti = null,
      public readonly ?int               $pocetHostu = null,
      public readonly ?int               $kapacitaMistnosti = null,
      public readonly ?DateTimeImmutable $start = null,
      public readonly ?DateTimeImmutable $end = null,
      public readonly ?string $detailUrl = null,
   ) {
   }
}