<?php namespace app\front\availability;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 25.03.2024 */
enum ConflictEntityType
{

   case POPTAVKA;
   case KALKULACE;
   case EVENT;
   case HARMONOGRAM_BLOK;

   public function getTitle() :string {
      return match ($this) {
         self::POPTAVKA => 'Poptávka',
         self::KALKULACE => 'Kalkulace',
         self::EVENT => 'Event',
         self::HARMONOGRAM_BLOK => 'Blok harmonogramu',
      };
   }

   public function getTitleCssClass() :string {
      return match ($this) {
         self::EVENT => 'text-bg-success',
         self::HARMONOGRAM_BLOK => 'text-bg-tertiary',
         default => 'text-bg-light',
      };
   }
}