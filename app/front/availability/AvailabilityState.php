<?php namespace app\front\availability;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 19.03.2024 */
enum AvailabilityState :string
{

   case FULL = 'full';
   case PARTIALLY_FILLED = 'partially';
   case OVERFLOW_ROOMS = 'overflow';
   case EMPTY = 'empty';

   public function isEmpty() :bool {
      return $this === self::EMPTY;
   }

   public function isFull() :bool {
      return $this === self::FULL;
   }

   public function getColorCss() :string {
      return match ($this) {
         self::FULL => 'danger',
         self::PARTIALLY_FILLED, self::OVERFLOW_ROOMS => 'secondary',
         self::EMPTY => 'success',
      };
   }

   public function getBorderColorCss() :string {
      return sprintf('border-%s', $this->getColorCss());
   }

   public function getTextColorCss() :string {
      return sprintf('text-%s', $this->getColorCss());
   }
}