<?php namespace app\front\availability\vendor;

use app\front\availability\AvailabilityConflictEntity;
use app\front\availability\AvailabilityState;
use app\front\availability\component\AvailabilityContainerComponent;
use app\front\availability\IAvailabilityCheckContainer;
use app\system\model\organizace\personal\PersonalRow;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 06.02.2025 */
class VendorAvailabilityContainer
   implements IAvailabilityCheckContainer
{


   public function __construct(
      public readonly AvailabilityState $state,
      /** @var VendorConflictEntity[] */
      public readonly array $conflicts = [],
      /** @var array<int, PersonalRow> */
      public readonly array $freePersonal = [],
   ) {
   }

   public function renderComponent(string $template = 'vendor') :Html {
      return new Html(AvailabilityContainerComponent::getComponent()
         ->setContainer($this)
         ->prepareTemplater($template)
         ->render());
   }
}