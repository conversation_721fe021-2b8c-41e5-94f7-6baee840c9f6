<?php namespace app\front\availability\vendor;

use app\front\availability\AvailabilityState;
use app\front\availability\ConflictEntityType;
use app\front\availability\IAvailabilityCheckContainer;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\system\application\ApplicationVersion;
use app\system\model\event\EventStavyEnum;
use app\system\model\event\personal\EventPersonal;
use app\system\model\event\VendorEventRow;
use app\system\model\event\VendorEventy;
use app\system\model\harmonogram\HarmonogramBlokRow;
use app\system\model\nabidka\VendorNabidkaRow;
use app\system\model\organizace\personal\Personal;
use app\system\model\organizace\personal\PersonalRow;
use app\system\model\poptavky\VendorPoptavkaRow;
use Dibi\DateTime;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 05.02.2025 */
class VendorAvailability
{

   public function __construct(protected readonly OrganizaceRow $organizace, protected readonly DateTime $datum, private ?DateTime $datumEnd = null) {
      $this->personal = Personal::getByMisto($this->organizace->id_organizace);

      if($this->datumEnd !== null && ((($diff = $this->datum->diff($this->datumEnd))->invert === 1 || $diff->days === 0)))
         $this->datumEnd = null;
   }

   public function setActualEntity(VendorPoptavkaRow|VendorEventRow|VendorNabidkaRow|HarmonogramBlokRow| null $actualEntity) :VendorAvailability {
      if($actualEntity !== null)
         $this->actualEntity = $actualEntity;

      return $this;
   }

   public function getAvailability() :IAvailabilityCheckContainer {
      $this->handleEventy();

      $freePersonal = [];

      foreach($this->personal as $personal){
         if(!isset($this->usedPersonal[$personal->id_personal]))
            $freePersonal[$personal->id_personal] = $personal;
      }

      if(empty($this->conflictEvents))
         return new VendorAvailabilityContainer(
            AvailabilityState::EMPTY,
            $this->conflictEvents,
            $freePersonal
         );

      if(empty($freePersonal))
         return new VendorAvailabilityContainer(
            AvailabilityState::FULL,
            $this->conflictEvents,
            $freePersonal
         );

      return new VendorAvailabilityContainer(
         AvailabilityState::PARTIALLY_FILLED,
         $this->conflictEvents,
         $freePersonal
      );
   }

   private function handleEventy() {
      $query = VendorEventy::find()
         ->where('e.id_organizace = %i', $this->organizace->id_organizace)
         ->where('e.id_stav NOT IN %in', EventStavyEnum::getValues([EventStavyEnum::ZRUSENY]));

      if($this->datumEnd === null) {
         $query
            ->where(
               '(DATE(e.date_start) = %d OR (e.date_end IS NOT NULL AND e.date_start <= %d AND e.date_end >= %d))',
               $this->datum, $this->datum, $this->datum
            );
      } else {
         $query
            ->where(
               '(
                  (DATE(e.date_start) BETWEEN %d AND %d)
                  OR
                  (e.date_end IS NOT NULL AND DATE(e.date_end) BETWEEN %d AND %d)
                  OR
                  (e.date_end IS NOT NULL AND DATE(e.date_start) <= %d AND DATE(e.date_end) >= %d)
                  OR
                  (e.date_end IS NULL AND DATE(e.date_start) BETWEEN %d AND %d)
              )',
               $this->datum, $this->datumEnd,
               $this->datum, $this->datumEnd,
               $this->datumEnd, $this->datum,
               $this->datum, $this->datumEnd,
            );
      }

      if(isset($this->actualEntity) && $this->actualEntity instanceof VendorEventRow)
         $query->where('e.id != %i', $this->actualEntity->getID());

      /** @var VendorEventRow[] $eventy */
      $eventy = $query->fetchAll();

      if(empty($eventy))
         return;

      $prirazenyPersonal = EventPersonal::getByMultipleEventID(array_column($eventy, 'id'), ApplicationVersion::VENDOR);

      foreach($eventy as $eventRow){
         if(!isset($prirazenyPersonal[$eventRow->getID()]))
            continue;

         $eventPersonal = [];

         foreach($prirazenyPersonal[$eventRow->getID()] as $personalRow){
            if(!($p = $this->personal[$personalRow->id_prirazeny_uzivatel] ?? null))
               continue;

            $eventPersonal[$p->id_personal] = $p;

            if(!isset($this->usedPersonal[$p->id_personal]))
               $this->usedPersonal[$p->id_personal] = $p;
         }

         $this->conflictEvents[] = new VendorConflictEntity(
            ConflictEntityType::EVENT,
            $eventRow->nazev ?: $eventRow->getDefaultNazev(),
            $eventRow->getMistoPoradani(),
            $eventPersonal,
            $eventRow->date_start,
            $eventRow->date_end,
            $eventRow->getDetailUrl(),
         );
      }
   }

   /** @var PersonalRow[]  */
   private array $personal;

   /** @var VendorConflictEntity[] */
   private array $conflictEvents = [];

   private VendorPoptavkaRow|VendorEventRow|VendorNabidkaRow|HarmonogramBlokRow $actualEntity;

   /** @var array<int, PersonalRow>  */
   private array $usedPersonal;
}