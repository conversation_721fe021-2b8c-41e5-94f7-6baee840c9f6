<?php namespace app\front\availability\vendor;

use app\front\availability\ConflictEntityType;
use app\system\model\mista\OrganizaceMistaRow;
use app\system\model\organizace\personal\PersonalRow;
use DateTimeImmutable;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 06.02.2025 */
readonly class VendorConflictEntity
{

   public function __construct(
      public ConflictEntityType  $typ,
      public string              $nazevEventu,
      public ?OrganizaceMistaRow $mistoPoradani = null,
      /** @var array<int, PersonalRow> */
      public array               $personalEventu = [],
      public ?DateTimeImmutable  $start = null,
      public ?DateTimeImmutable  $end = null,
      public ?string             $detailUrl = null,
   ) {
   }
}