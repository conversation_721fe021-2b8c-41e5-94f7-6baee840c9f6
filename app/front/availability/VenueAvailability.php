<?php namespace app\front\availability;

use app\front\organizace\model\organizace\OrganizaceRow;
use app\System;
use app\system\application\ApplicationEntityType;
use app\system\application\ApplicationVersion;
use app\system\model\event\EventStavyEnum;
use app\system\model\event\mistnosti\VenueEventMistnosti;
use app\system\model\event\predchozi\EventPredchoziEntita;
use app\system\model\event\VenueEventRow;
use app\system\model\event\VenueEventy;
use app\system\model\harmonogram\HarmonogramBlokRow;
use app\system\model\harmonogram\HarmonogramBloky;
use app\system\model\nabidka\mistnosti\VenueNabidkaMistnosti;
use app\system\model\nabidka\NabidkaStatus;
use app\system\model\nabidka\VenueNabidkaRow;
use app\system\model\nabidka\VenueNabidky;
use app\system\model\organizace\cenove\IEntityHoste;
use app\system\model\organizace\mistnosti\Mistnosti;
use app\system\model\organizace\mistnosti\MistnostRow;
use app\system\model\organizace\zakaznici\OrganizaceZakaznici;
use app\system\model\poptavky\VenuePoptavkaRow;
use app\system\model\poptavky\VenuePoptavky;
use Dibi\DateTime;

/** Created by Kryštof Czyź. Date: 20.03.2024 */
class VenueAvailability
{

   public function __construct(protected readonly OrganizaceRow $organizace, protected readonly DateTime $datum, private ?DateTime $datumEnd = null) {
      $this->prostory = Mistnosti::getByMisto($this->organizace->id_organizace);

      if($this->datumEnd !== null && ((($diff = $this->datum->diff($this->datumEnd))->invert === 1 || $diff->days === 0)))
         $this->datumEnd = null;
   }

   public function setActualEntity(VenuePoptavkaRow|VenueEventRow|VenueNabidkaRow|HarmonogramBlokRow| null $actualEntity) :VenueAvailability {
      if($actualEntity !== null)
         $this->actualEntity = $actualEntity;

      return $this;
   }

   public function getAvailability() :VenueAvailabilityCheckContainer {
      $this->handleEventy();
      $this->handleKalkulace();
      $this->handlePoptavky();
      $this->handleHarmonogram();

      if(empty($this->usedProstory)){
         return new VenueAvailabilityCheckContainer(
            AvailabilityState::EMPTY,
            $this->conflictEvents,
            entityCapacity: isset($this->actualEntity)
               ? ($this->actualEntity instanceof IEntityHoste ? $this->actualEntity->getPocetHostu() : $this->actualEntity->pocet_hostu)
               : null,
         );
      }

      foreach($this->prostory as $mistnostRow){
         if(isset($this->usedProstory[$mistnostRow->id_mistnost]))
            continue;

         $this->freeProstory[$mistnostRow->id_mistnost] = $mistnostRow;
      }

      if(!empty($this->freeProstory) && isset($this->actualEntity)){
         match (true) {
            $this->actualEntity instanceof IEntityHoste => $this->handleIEntityHoste(),
            ($this->actualEntity instanceof VenuePoptavkaRow || $this->actualEntity instanceof HarmonogramBlokRow) && $this->actualEntity->pocet_hostu !== null => $this->handleRest(),
            default => null,
         };
      }

      if(empty($this->freeProstory) && empty($this->overflowProstory))
         $state = AvailabilityState::FULL;
      elseif(empty($this->freeProstory) && !empty($this->overflowProstory))
         $state = AvailabilityState::OVERFLOW_ROOMS;
      else
         $state = AvailabilityState::PARTIALLY_FILLED;

      return new VenueAvailabilityCheckContainer(
         $state,
         $this->conflictEvents,
         $this->freeProstory,
         $this->overflowProstory,
         isset($this->actualEntity)
            ? ($this->actualEntity instanceof IEntityHoste ? $this->actualEntity->getPocetHostu() : $this->actualEntity->pocet_hostu)
            : null,
      );
   }

   private function handleHarmonogram() :void {
      $query = HarmonogramBloky::find()
         ->join(VenueEventy::TABLE, 'e')->on('hb.id_event = e.id_event')
         ->where('e.id_misto = %i', $this->organizace->id_organizace)
         ->where('e.id_stav NOT IN %in', EventStavyEnum::getValues([EventStavyEnum::ZRUSENY]));

      if($this->datumEnd === null)
         $query->where('DATE(hb.date_start) = %d', $this->datum);
      else
         $query->where('(DATE(hb.date_start) BETWEEN %d AND %d)', $this->datum, $this->datumEnd);

      if(isset($this->actualEntity) && $this->actualEntity instanceof HarmonogramBlokRow)
         $query->where('hb.id != %i', $this->actualEntity->getID());

      /** @var HarmonogramBlokRow[] $bloky */
      $bloky = $query->fetchAll();

      if(empty($bloky))
         return;

      foreach($bloky as $blokRow){
         $room = $blokRow->id_mistnost ? ($this->prostory[$blokRow->id_mistnost] ?? null) : null;
         $this->usedProstory[$blokRow->id_mistnost] = $blokRow->id_mistnost;

         $this->conflictEvents[] = new AvailabilityConflictEntity(
            ConflictEntityType::HARMONOGRAM_BLOK,
            $blokRow->nazev,
            $room?->nazev ?: System::getTranslator()->translate('Neaktivní prostor / místnost'),
            $blokRow?->pocet_hostu,
            $room?->kapacita,
            $blokRow->date_start,
            $blokRow->date_end,
         );
      }
   }

   private function handleEventy() :void {
      $query = VenueEventy::find()
         ->where('e.id_misto = %i', $this->organizace->id_organizace)
         ->where('e.id_stav NOT IN %in', EventStavyEnum::getValues([EventStavyEnum::ZRUSENY]));

      if($this->datumEnd === null) {
         $query
            ->where(
               '(DATE(e.date_start) = %d OR (e.date_end IS NOT NULL AND e.date_start <= %d AND e.date_end >= %d))',
               $this->datum, $this->datum, $this->datum
            );
      } else {
         $query
            ->where(
               '(
                  (DATE(e.date_start) BETWEEN %d AND %d)
                  OR
                  (e.date_end IS NOT NULL AND DATE(e.date_end) BETWEEN %d AND %d)
                  OR
                  (e.date_end IS NOT NULL AND DATE(e.date_start) <= %d AND DATE(e.date_end) >= %d)
                  OR
                  (e.date_end IS NULL AND DATE(e.date_start) BETWEEN %d AND %d)
              )',
               $this->datum, $this->datumEnd,
               $this->datum, $this->datumEnd,
               $this->datumEnd, $this->datum,
               $this->datum, $this->datumEnd,
            );
      }

      if(isset($this->actualEntity) && $this->actualEntity instanceof VenueEventRow)
         $query->where('e.id_event != %i', $this->actualEntity->getID());

      /** @var VenueEventRow[] $eventy */
      $eventy = $query->fetchAll();

      if(empty($eventy))
         return;

      $mistnostiAll = VenueEventMistnosti::getForEventy(...array_column($eventy, 'id_event'));

      foreach($eventy as $eventRow){
         if($eventRow->isRezervaceVsechMistnosti()){
            $this->usedProstory = array_combine($mKeys = array_keys($this->prostory), $mKeys);
            $celkovaKapacita = 0;

            foreach($this->prostory as $mistnostRow)
               $celkovaKapacita += $mistnostRow->kapacita;

            $this->conflictEvents[] = new AvailabilityConflictEntity(
               ConflictEntityType::EVENT,
               $eventRow->nazev ?: $eventRow->getDefaultNazev(),
               System::getTranslator()->translate('Všechny prostory'),
               $eventRow->getPocetHostu(),
               $celkovaKapacita,
               $eventRow->date_start,
               $eventRow->date_end,
               $eventRow->getDetailUrl(),
            );
            continue;
         }

         $mistnosti = $mistnostiAll[$eventRow->id_event] ?? [];
         $roomsNames = [];
         $kapacita = 0;

         foreach($mistnosti as $nabidkaMistnostRow){
            if(!isset($this->prostory[$nabidkaMistnostRow->id_mistnost]))
               continue;

            $room = $this->prostory[$nabidkaMistnostRow->id_mistnost];
            $roomsNames[$room->id_mistnost] = $room->nazev;
            $kapacita += $room->kapacita;

            if(!isset($this->usedProstory[$room->id_mistnost]))
               $this->usedProstory[$room->id_mistnost] = $room->id_mistnost;
         }

         $this->conflictEvents[] = new AvailabilityConflictEntity(
            ConflictEntityType::EVENT,
            $eventRow->nazev ?: $eventRow->getDefaultNazev(),
            !empty($roomsNames)
               ? implode(', ', $roomsNames)
               : System::getTranslator()->translate('Bez místnosti'),
            $eventRow->getPocetHostu(),
            $kapacita,
            $eventRow->date_start,
            $eventRow->date_end,
            $eventRow->getDetailUrl(),
         );
      }
   }

   private function handleKalkulace() :void {
      $query = VenueNabidky::find()
         ->join(VenuePoptavky::TABLE, 'p')->on('p.id_lead = n.id_lead AND p.id_organizace = %i', $this->organizace->id_organizace)
         ->leftJoin(EventPredchoziEntita::getTable(ApplicationVersion::VENUE), 'epe')
         ->on(
            'epe.entity_type = %s AND epe.%n = n.%n',
            ApplicationEntityType::NABIDKA->value,
            EventPredchoziEntita::FK_ENTITY_ID, VenueNabidky::PK
         )
         ->where('n.stav IN %in', NabidkaStatus::getCalendarVisible())
         ->where('epe.id_event IS NULL');

      if($this->datumEnd === null){
        $query
           ->where(
              '(DATE(n.date_start) = %d OR (n.date_end IS NOT NULL AND n.date_start <= %d AND n.date_end >= %d))',
              $this->datum, $this->datum, $this->datum
           );
      } else {
         $query
            ->where(
               '(
                  (n.date_start BETWEEN %d AND %d)
                  OR
                  (n.date_end IS NOT NULL AND n.date_end BETWEEN %d AND %d)
                  OR
                  (n.date_end IS NOT NULL AND n.date_start <= %d AND n.date_end >= %d)
                  OR
                  (n.date_end IS NULL AND n.date_start BETWEEN %d AND %d)
              )',
               $this->datum, $this->datumEnd,
               $this->datum, $this->datumEnd,
               $this->datumEnd, $this->datum,
               $this->datum, $this->datumEnd,
            );
      }

      if(isset($this->actualEntity) && $this->actualEntity instanceof VenueNabidkaRow)
         $query->where('n.id_nabidka != %i', $this->actualEntity->getID());

      /** @var VenueNabidkaRow[] $nabidky */
      $nabidky = $query->fetchAll();

      if(empty($nabidky))
         return;

      $mistnostiAll = VenueNabidkaMistnosti::getForNabidky(...array_column($nabidky, 'id_nabidka'));

      foreach($nabidky as $nabidkaRow){
         if(!!$nabidkaRow->is_vsechny_mistnosti){
            $this->usedProstory = array_merge($this->usedProstory, array_combine($mKeys = array_keys($this->prostory), $mKeys));
            $celkovaKapacita = 0;

            foreach($this->prostory as $mistnostRow)
               $celkovaKapacita += $mistnostRow->kapacita;

            $this->conflictEvents[] = new AvailabilityConflictEntity(
               ConflictEntityType::KALKULACE,
               $nabidkaRow->nazev ?: System::getTranslator()->translate('Kalkulace: $1', $nabidkaRow->getID()),
               System::getTranslator()->translate('Všechny prostory'),
               $nabidkaRow->getPocetHostu(),
               $celkovaKapacita,
               $nabidkaRow->dtStart,
               $nabidkaRow->dtEnd,
               $nabidkaRow->getDetailUrl(),
            );
            continue;
         }

         $mistnosti = $mistnostiAll[$nabidkaRow->id_nabidka] ?? [];
         $roomsNames = [];
         $kapacita = 0;

         foreach($mistnosti as $nabidkaMistnostRow){
            if(!isset($this->prostory[$nabidkaMistnostRow->id_mistnost]))
               continue;

            $room = $this->prostory[$nabidkaMistnostRow->id_mistnost];
            $roomsNames[$room->id_mistnost] = $room->nazev;
            $kapacita += $room->kapacita;

            if(!isset($this->usedProstory[$room->id_mistnost]))
               $this->usedProstory[$room->id_mistnost] = $room->id_mistnost;
         }

         $this->conflictEvents[] = new AvailabilityConflictEntity(
            ConflictEntityType::KALKULACE,
            $nabidkaRow->nazev ?: System::getTranslator()->translate('Kalkulace: $1', $nabidkaRow->getID()),
            !empty($roomsNames)
               ? implode(', ', $roomsNames)
               : System::getTranslator()->translate('Bez místnosti'),
            $nabidkaRow->getPocetHostu(),
            $kapacita,
            $nabidkaRow->dtStart,
            $nabidkaRow->dtEnd,
            $nabidkaRow->getDetailUrl(),
         );
      }
   }

   private function handlePoptavky() :void {
      $query = VenuePoptavky::find()
         ->leftJoin(VenueNabidky::TABLE, 'n')->on('l.id_lead = n.id_lead')
         ->leftJoin(EventPredchoziEntita::getTable(ApplicationVersion::VENUE), 'epe')
         ->on(
            'epe.entity_type = %s AND epe.%n = n.%n',
            ApplicationEntityType::POPTAVKA->value,
            EventPredchoziEntita::FK_ENTITY_ID, VenuePoptavky::COLUMN_ID
         )
         ->where('l.id_organizace = %i', $this->organizace->id_organizace)
         ->where('(n.id_nabidka IS NULL OR epe.id_event IS NULL)');

      if($this->datumEnd === null){
         $query
            ->where(
               '(l.date_start = %d OR (l.date_end IS NOT NULL AND l.date_start <= %d AND l.date_end >= %d))',
               $this->datum, $this->datum, $this->datum
            );
      } else {
        $query
           ->where(
              '(
                  (l.date_start BETWEEN %d AND %d)
                  OR
                  (l.date_end IS NOT NULL AND l.date_end BETWEEN %d AND %d)
                  OR
                  (l.date_end IS NOT NULL AND l.date_start <= %d AND l.date_end >= %d)
                  OR
                  (l.date_end IS NULL AND l.date_start BETWEEN %d AND %d)
              )',
              $this->datum, $this->datumEnd,
              $this->datum, $this->datumEnd,
              $this->datumEnd, $this->datum,
              $this->datum, $this->datumEnd,
           );
      }

//      @TODO only potvrzené

      if(isset($this->actualEntity) && $this->actualEntity instanceof VenuePoptavkaRow)
         $query->where('l.id_lead != %i', $this->actualEntity->getID());

      /** @var VenuePoptavkaRow[] $poptavky */
      $poptavky = $query->fetchAll();

      if(empty($poptavky))
         return;

      foreach($poptavky as $poptavkaRow){
         $this->conflictEvents[] = new AvailabilityConflictEntity(
            ConflictEntityType::POPTAVKA,
            System::getTranslator()->translate('Poptávka - $1', OrganizaceZakaznici::get($poptavkaRow->id_zakaznika)->full_name),
            pocetHostu: $poptavkaRow->pocet_hostu ?: null,
            detailUrl: $poptavkaRow->getDetailUrl()
         );
      }
   }

   private function handleIEntityHoste() :void {
      $this->freeProstory = array_filter($this->freeProstory, function(MistnostRow $room) {
         if($this->actualEntity->getPocetHostu() <= $room->kapacita)
            return true;

         $this->overflowProstory[$room->id_mistnost] = $room;
         return false;
      });
   }

   private function handleRest() :void {
      $this->freeProstory = array_filter($this->freeProstory, function(MistnostRow $room) {
         if($this->actualEntity->pocet_hostu <= $room->kapacita)
            return true;

         $this->overflowProstory[$room->id_mistnost] = $room;
         return false;
      });
   }

   /** @var MistnostRow[] */
   private array $prostory;

   /** @var array<int, MistnostRow> */
   private array $freeProstory = [];

   /** @var array<int, int> */
   private array $usedProstory = [];

   /** @var AvailabilityConflictEntity[] */
   private array $conflictEvents = [];

   /** @var array<int, MistnostRow> */
   private array $overflowProstory = [];

   private VenuePoptavkaRow|VenueNabidkaRow|VenueEventRow|HarmonogramBlokRow $actualEntity;
}