<?php namespace app\front\availability;

use app\front\availability\vendor\VendorAvailability;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\system\application\ApplicationVersion;
use app\system\model\event\BaseEventRow;
use app\system\model\harmonogram\HarmonogramBlokRow;
use app\system\model\nabidka\BaseNabidkaRow;
use app\system\model\poptavky\BasePoptavkaRow;
use Dibi\DateTime;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 18.03.2024 */
class AvailabilityChecker
{

   public function __construct(protected readonly OrganizaceRow $organizace) { }

   public function setDatum(DateTime $datumStart, ?DateTime $datumEnd = null) :static {
      $this->datum = $datumStart;
      $this->datumEnd = $datumEnd;
      return $this;
   }

   public function setActualEntity(BasePoptavkaRow|BaseNabidkaRow|BaseEventRow|HarmonogramBlokRow|null $entity) :static {
      if($entity !== null)
         $this->actualEntity = $entity;

      return $this;
   }

   public function processCheck() :IAvailabilityCheckContainer {
      if(!isset($this->datum)){
         bdump('WARNING: availabilityChecker nemá nastavené datum');
         $this->datum = new DateTime();
      }

      if($this->organizace->getVersion() === ApplicationVersion::VENUE){
         return $this->checkContainer ??= (new VenueAvailability($this->organizace, $this->datum, $this->datumEnd))
            ->setActualEntity($this->actualEntity ?? null)
            ->getAvailability();
      }

//      @TODO pro VENDOR
      return $this->checkContainer = (new VendorAvailability($this->organizace, $this->datum, $this->datumEnd))
         ->setActualEntity($this->actualEntity ?? null)
         ->getAvailability();
   }

   protected DateTime $datum;
   private BasePoptavkaRow|BaseNabidkaRow|BaseEventRow|HarmonogramBlokRow $actualEntity;

   private IAvailabilityCheckContainer $checkContainer;
   private ?DateTime $datumEnd;
}