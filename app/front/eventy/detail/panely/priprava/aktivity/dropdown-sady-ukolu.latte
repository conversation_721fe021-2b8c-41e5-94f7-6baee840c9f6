<div class="dropdown">
   <a class="btn btn-sm btn-primary dropdown-toggle shaddow-hover" id="pridat-ukoly" data-bs-auto-close="outside"
      aria-expanded="false" data-bs-toggle="dropdown"><i class="me-1 bi bi-card-checklist"></i>{_'Přidat šablonu'}</a>

   <div class="dropdown-menu" aria-labelledby="tisk-eventy-od-dropdown">
      <div class="px-4 py-3">
         <form method="post">
            <div class="row">
               <div class="input-group">
                  <select id="js-sady-select" class="form-select js-select-sady" name="ids_sady[]" multiple></select>
               </div>
            </div>
            <div class="row mt-1">
               <div class="col">
                  <button type="submit" id="submit-sady" class="btn btn-sm btn-outline-secondary w-auto" name="btnPridatSady" disabled>{_'Přidat aktivity'}</button>
               </div>
            </div>
         </form>
      </div>
   </div>
</div>

<script>
    $(function() {
       const select2 = $('#js-sady-select');
       const submitBtn = $('#submit-sady');
       submitBtn.prop('disabled', !(select2.find(':selected').length > 0));

       select2
          .select2({
             placeholder: "{_'Vyber aktivity'}",
             delay: '350ms',
             width: '400px',
             multiple: true,
             ajax: {
                url: {$baseUrl},
                dataType: 'json',
                type: 'POST',
                data: function(params) {
                   return {
                      search: params['term'],
                   };
                },
                processResults: function(data) {
                   return {
                      results: data,
                   };
                },
             },
          });

        $('body').on('change', '#js-sady-select', function () {
           submitBtn.prop('disabled', !(select2.find(':selected').length > 0));
        });
    });
</script>