<?php namespace app\front\eventy\detail\panely\priprava\aktivity;

use app\front\controllers\eventy\DetailEventuActionsController;
use app\front\controllers\eventy\DetailEventuActionsWHController;
use app\front\eventy\detail\modaly\aktivity\NovaAktivitaModal;
use app\front\tisk\event\aktivity\NedokonceneAktivityEventTisk;
use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\model\event\BaseEventRow;
use app\system\model\event\data\aktivity\EventAktivityStavy;
use app\system\model\event\EventAktivity;
use app\system\modul\zalozky\Zalozka;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 20.01.2023 */
class AktivityEventuZalozka extends Zalozka
{

   protected static array $stavy_id_col = [
      EventAktivity::STAV_UDELAT => 'tasks-todo',
      EventAktivity::STAV_RESENI => 'tasks-progress',
      EventAktivity::STAV_HOTOVO => 'tasks-completed',
   ];

   public function getTitle() :string {
      return 'Aktivity';
   }

   public function setEvent(BaseEventRow $baseEvent) :AktivityEventuZalozka {
      $this->event = $baseEvent;
      return $this;
   }

   public function setEnviroment(FrontApplicationEnvironment $environment) :static{
      $this->environment = $environment;
      return $this;
   }

   protected function prepareTemplater(Templater $templater) :void {
      $templater->addData([
         'aktivity' => EventAktivity::getForEventAssocStav($this->event->getID(), $this->environment->versionType),
         'stavyNazvy' => EventAktivityStavy::getTranslatedPairs(),
         'stavyColId' => self::$stavy_id_col,
         'novaAktivitaBtnAttr' => NovaAktivitaModal::init()->getBtnAttributes(),
         'attrTisk' => NedokonceneAktivityEventTisk::getHtmlAttr(['id_event' => $this->event->getID()]),
         'baseUrl' => DetailEventuActionsController::getUrl($this->event->getID(), DetailEventuActionsController::ACTION_SAVE_UKOL),
         'dostupne_sady' => Templater::prepare(__DIR__ . '/dropdown-sady-ukolu.latte', [
            'id_event' => $this->getId(),
            'baseUrl' => DetailEventuActionsWHController::getUrl($this->event->getID(), DetailEventuActionsWHController::ACTION_FINDAKTIVITY)
         ]),
      ]);
   }

   public BaseEventRow $event;
   protected FrontApplicationEnvironment $environment;
}