{varType app\system\model\event\entity\aktivity\EventAktivita[][] $aktivity}
{varType string $dostupne_sady}
{varType array $stavyNazvy}
{varType array $stavyColId}
{varType array $novaAktivitaBtnAttr}
{varType array $attrTisk}

<div class="row">
   <div class="card bg-body border border-light-gray">
      <div class="row border-bottom toolbar-section gap-3 bg-white rohy-2-2 px-1 py-3">
         <div class="col-xl col-12 align-content-center">
            <h4><i class="align-middle px-2 py-1 me-2 bi-list-task bg-primary radius-icon icon-shadow text-white"></i>{_'Aktivity pro tento event'}</h4>
         </div>
         <div class="col-auto d-flex justify-content-end gap-2">
            <div class="col-auto">
               <a href="#" class="btn btn-sm btn-primary shaddow-hover" n:attr="$novaAktivitaBtnAttr"><i
                          class="bi bi-plus-lg me-1"></i>{_'Přidat aktivitu'}</a>
            </div>
            <div class="col-auto">
               {$dostupne_sady|noescape}
            </div>
            <div class="col-auto">
               <button class="form-control btn btn-sm btn-outline-secondary shaddow-hover" n:attr="$attrTisk">
                  <div class="spinner-border spinner-border-sm text-secondary me-2 js-tisk-loader" role="status"
                       style="display: none">
                     <span class="visually-hidden">{_'Načítání'}...</span>
                  </div>
                  <i class="bi bi-printer me-1"></i>{_'Tisk'}
               </button>
            </div>
         </div>
      </div>

      <div class="row my-2 p-3 d-flex justify-content-center">
         {foreach $aktivity as $id_stav => $items}
            <div class="col-12 col-md">
               <div data-ukol-status="{$id_stav}">
                  <div>
                     <h5 class="lead">{$stavyNazvy[$id_stav]}</h5>
                     <hr>
                  </div>
                  <div class="">
                     <div id="{$stavyColId[$id_stav]}" style="min-height: 10vh">
                        {foreach $items as $aktivita}
                           <div class="card mb-3 cursor-grab shaddow-hover" data-actual-stav="{$id_stav}"
                                data-id-ukol="{$aktivita->id_aktivita}">
                              <div class="card-body p-3">
                                 <div class="float-end me-n2">
                                    {$aktivita->getBtnEdit()|noescape}
                                 </div>
                                 <strong class="ukol-nazev mb-3">{$aktivita->nazev}</strong>
                                 <hr class="my-2">
                                 <div class="row">
                                    <div class="col-auto align-content-center">
                                       <div class="profile-pic-bg">{$aktivita->inicialyPrirazeny}</div>
                                    </div>
                                    <div class="col mt-1">
                                       <small class="d-block ukol-prirazeny">
                                          <span>{$aktivita->jmeno_prirazeny}</span>
                                       </small>

                                       <small class="py-1">
                                          <i class="bi bi-calendar2-check me-1"></i>
                                          {if $aktivita->dtStart}
                                             {$aktivita->dtStart|date: 'j.n.Y H:i'}&nbsp;-&nbsp;{$aktivita->dtEnd|date: 'H:i'}
                                          {else}
                                             {$aktivita->termin|date: 'j.n.Y'}
                                          {/if}
                                       </small>

                                       <small class="d-block">
                                          <span>{$aktivita->typ->getTranslatedTitle()}</span>
                                       </small>
                                    </div>
                                 </div>
                              </div>
                           </div>
                        {/foreach}
                     </div>
                  </div>
               </div>
            </div>
            {if !$iterator->last}
               <div class="vr p-0 mx-1 d-none d-md-block"></div>
            {/if}
         {/foreach}
      </div>
   </div>
</div>


<script>
   $(function() {
      dragula([
         document.querySelector('#tasks-todo'),
         document.querySelector('#tasks-progress'),
         document.querySelector('#tasks-completed'),
      ]).on('drop', function(el) {
         let aktivitaBlok = $(el);
         let sloupec = aktivitaBlok.closest('div[data-ukol-status]');

         let actualStatus = aktivitaBlok.attr('data-actual-stav');
         let newStatus = sloupec.attr('data-ukol-status');
         let id_aktivita = aktivitaBlok.attr('data-id-ukol');

         if(newStatus === actualStatus)
            return false;

         ajaxHandler.post({$baseUrl}, { id_aktivita, newStatus }, function(response) {
            aktivitaBlok.attr('data-actual-stav', newStatus);
         });
      });
   });
</script>