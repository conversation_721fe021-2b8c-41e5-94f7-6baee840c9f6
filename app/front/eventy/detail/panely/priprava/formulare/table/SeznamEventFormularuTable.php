<?php namespace app\front\eventy\detail\panely\priprava\formulare\table;

use app\front\controllers\formular\FormulareActionController;
use app\front\controllers\formular\FormulareController;
use app\front\formular\modals\respondents\show\DisplayFormularRespondentValues;
use app\System;
use app\system\application\FrontApplicationEnvironment;
use app\system\helpers\HtmlBuilder;
use app\system\model\event\BaseEventRow;
use app\system\model\formular\data\FormularRawRow;
use app\system\model\formular\enumy\FormulareTypyEnum;
use app\system\model\formular\enumy\FormularStavy;
use app\system\model\formular\enumy\FormularViewerModeEnum;
use app\system\model\formular\FormulareModel;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\table\DynamicTable;
use Dibi\Fluent;

/** Created by <PERSON><PERSON>. Date: 13.02.2023 */
class SeznamEventFormularuTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addText('title', 'Název');
      $this->addSelect('id_stav', 'Stav', FormularStavy::getTranslatedPairs());

      //$this->addSelect('is_interni', 'Interní', [
      //   0 => System::getTranslator()->translate('Ne'),
      //   1 => System::getTranslator()->translate('Ano')
      //])->setWidth(DynamicTableColumn::WIDTH_10);

      $this->addText('d.id', 'Odpovědi')
         ->setOrderable(false)
         ->setSearchable(false)
         ->setFormatter(function($value, $row) {
            /** @var FormularRawRow $row */
            return FormularStavy::from($row->id_stav)->isVyplneny()
               ? $this->makeResultButton($value)
               : System::getTranslator()->translate('Bez výsledků');
         });

      $enviroment = FrontApplicationEnvironment::get();
      if(
         $enviroment->personal->hasAccessRight(PersonalAccessRightsEnum::EVENTY)
         || $enviroment->personal->hasAccessRight(PersonalAccessRightsEnum::ADMIN)
         || $enviroment->isMajitel
      ) {
         $this->addText('i.id', ' ')
            ->setOrderable(false)
            ->setSearchable(false)
            ->setFormatter(function ($value, $row) {
               /** @var FormularRawRow $row */
               return FormularStavy::from($row->id_stav)->isVyplnitelny()
                  ? $this->makeLinkButton($value, FormularViewerModeEnum::FILL->value, System::getTranslator()->translate('Vyplnit'))
                  : $this->prepareResultColumn($row);
            });

         $this->addText('id.id', ' ')
            ->setOrderable(false)
            ->setSearchable(false)
            ->setFormatter(
               function ($value, $row) {
                  /** @var FormularRawRow $row */
                  return !FormularStavy::from($row->id_stav)->isLocked()
                     ? $this->makeLinkButton($value, FormularViewerModeEnum::EDIT->value, System::getTranslator()->translate('Upravit'))
                     : System::getTranslator()->translate('Nelze upravit');
               }
            );
      }
   }

   public function setEvent(BaseEventRow $event) :static {
      $this->event = $event;
      $this->enum = FormulareTypyEnum::getTypeByEvent($event);

      $this->addParametrQuery('id_event', $event->getID());
      $this->addParametrQuery('type_entity', $this->enum->value);

      return $this;
   }

   function getQuery(): Fluent {
      $enum = FormulareTypyEnum::tryFrom($_GET['type_entity']);
      return FormulareModel::findByEntityRaw($enum->value, $_GET['id_event']);
   }

   private function makeResultButton(int $value) :string {
      return HtmlBuilder::buildElement('a', array_merge([
         'href' => '#'
      ], DisplayFormularRespondentValues::getShowAttributes($value)))
         ->setHtml(System::getTranslator()->translate('Zobrazit odpovědi'))
         ->get();
   }

   private function makeLinkButton(int $value, string $action, string $buttonTitle) :string {
      return HtmlBuilder::buildElement('a', array_merge([
         'href' => FormulareController::getUrl($value, $action),
         ]))->setHtml($buttonTitle)->get();
   }

   private function prepareResultColumn(FormularRawRow $formular) :string {
      return (FormularStavy::from($formular->id_stav)->isStavVytvoreny())
         ? HtmlBuilder::buildElement('a', array_merge([
            'href' => FormulareActionController::getUrl($formular->id, FormulareActionController::ACTION_SENDFORM),
            'data-confirm' =>  System::getTranslator()->translate('Opravdu chtete formulář odeslat?'),
         ]))->setHtml(System::getTranslator()->translate('Odeslat'))->get()
         : System::getTranslator()->translate('Nelze vyplnit');
   }

   protected BaseEventRow $event;
   protected FormulareTypyEnum $enum;
}