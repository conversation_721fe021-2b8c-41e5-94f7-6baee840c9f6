{varType ?app\system\model\mista\OrganizaceMistaRow $misto}
{varType string $mistoForm}

<form method="post">
   <div class="misto-poradani pt-4">
      {if $misto}
         <div class="row">
            <div class="col-md-3 col-xl-2">
               <div class="list-group list-group-flush" role="tablist">
                  <a class="list-group-item list-group-item-action active" data-bs-toggle="list" href="#kontakt" role="tab"
                     aria-selected="false">
                     {_'Kontakt'}
                  </a>
                  <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#detail" role="tab"
                     aria-selected="false">
                     {_'Detail'}
                  </a>
               </div>
            </div>

         <div class="col-md-9 col-xl-10 mt-3 mt-md-0">
            <div class="tab-content">
               <div class="tab-pane fade active show card p-3" id="kontakt" role="tabpanel">
                  <div class="row border-bottom toolbar-section">
                     <div class="col-auto">
                        <h4 class="card-title ">{_'Kontakty a adresa místa'}</h4>
                     </div>
                  </div>
                  <div class="row">
                     <div class="col-md">
                        <div class="row">
                           <div class="form-floating">
                              <p class="form-control bg-gray-100 bg-opacity-50 border-0">{$misto?->nazev}</p>
                              <label class="ms-2">{_'Název'}</label>
                           </div>
                        </div>
                        <div class="row js-adresa-mista">
                           <div class="form-floating">
                              <p class="form-control bg-gray-100 bg-opacity-50 border-0">{$misto?->ulice}</p>
                              <label class="ms-2">{_'Ulice'}</label>
                           </div>
                           <div class="col-12 d-flex gap-2">
                              <div class="form-floating col-auto">
                                 <p class="form-control bg-gray-100 bg-opacity-50 border-0">{$misto?->psc}</p>
                                 <label class="ms-2">{_'PSČ'}</label>
                              </div>
                              <div class="form-floating col">
                                 <p class="form-control bg-gray-100 bg-opacity-50 border-0">{$misto?->mesto}</p>
                                 <label class="ms-2">{_'Město'}</label>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="col-md">
                        {*@TODO tady bude mapa*}
                     </div>
                  </div>
               </div>
               <div class="tab-pane fade card p-3" id="detail" role="tabpanel">
                  <div class="row border-bottom toolbar-section">
                     <div class="col-auto">
                        <h4 class="card-title ">{_'Další informace k místu'}</h4>
                     </div>
                  </div>
                  <div class="row">
                     <label>{_'Poznámka k místu'}</label>
                     <p>{$misto?->popis}</p>
                  </div>
               </div>
            </div>
         </div>
         </div>
      {else}
         <div class="row col-md-8 card p-3">
            <div class="row">
               <h4 class="card-title ">{_'Přidat místo konání'}</h4>
            </div>
            <div class="row">
               {$mistoForm|noescape}
            </div>
         </div>
      {/if}

      <div class="row">
         <div class="col-sm-3">
            <div class="row">
               {if $misto}
               <div class="col-auto">
                  <input type="submit" class="btn-sm btn btn-outline-secondary w-auto shaddow-hover" name="btnRemoveMisto"
                         value="{_'Odebrat místo z eventu'}">
               </div>
               {else}
                  <div class="col-auto">
                     <input type="submit" class="form-control btn btn-primary" name="btnAddMisto"
                            value="{_'Přidat místo'}">
                  </div>
               {/if}
            </div>
         </div>
      </div>
   </div>
</form>