<?php namespace app\front\eventy\detail\panely\misto;

use app\front\eventy\detail\panely\IEventPanel;
use app\system\component\Templater;
use app\system\model\event\adresy\EventDodavateleAdresy;
use app\system\model\event\BaseEventRow;
use app\system\model\event\VendorEventRow;
use app\system\model\event\VendorEventy;
use app\system\model\mista\event\CreateOrganizaceMistoEvent;
use app\system\model\mista\form\OrganizaceMistaFormularComponent;
use app\system\model\mista\OrganizaceMista;
use app\system\modul\panels\Panel;

/** Created by <PERSON><PERSON>. Date: 14.08.2023 */
class MistoPoradaniEventPanel extends Panel
   implements IEventPanel
{

   public function getEvent() :BaseEventRow {
      return $this->event ??= VendorEventy::get($this->getParameter('id_eventu'));
   }

   public function preparePanel(Templater $templater) :void {
      /** @var $event VendorEventRow */
      $event = $this->getEvent();
      $templater->addData([
         'misto' => $event->getMistoPoradani(),
         'event' => $event,
         'mistoForm' => OrganizaceMistaFormularComponent::getComponent()->setIdOrganizace($event->getIdOrganizace()),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnRemoveMisto', function() {
         /** @var $event VendorEventRow */
         $event = $this->getEvent();

         EventDodavateleAdresy::delete($event->getMistoPoradani()->id_mista, $event->getID());
      });

//      Duplicita CreatePlainEvents
      $this->isset('btnAddMisto', function($post) {
         $misto = OrganizaceMista::getByName($this->getEvent()->getIdOrganizace(), trim($post['misto-nazev']));

         if(!$misto){
            $psc = trim($post['misto-psc']);
            $ulice = trim($post['misto-ulice']);

            if(
               !$psc
               || !$ulice
               || !($misto = OrganizaceMista::getByAddress($this->getEvent()->getIdOrganizace(), $post['misto-ulice'], $post['misto-psc']))
            ){
               $misto = (new CreateOrganizaceMistoEvent($this->getEvent()->getIdOrganizace(), [
                  'nazev' => trim($post['misto-nazev']),
                  'id_stat' => intval($post['misto-stat']),
                  'mesto' => trim($post['misto-mesto']),
                  'ulice' => $post['misto-ulice'],
                  'psc' => $post['misto-psc'],
                  'popis' => null,
               ]))->call()->getMisto();
            }
         }

         if($misto->id_mista ?? null)
            EventDodavateleAdresy::createMultiple([$misto], $this->getEvent()->getID());
      });
   }

   function getMenuName() :string {
      return 'Místo pořádání';
   }

   private ?VendorEventRow $event;
}