<?php namespace app\front\eventy\detail\panely\finance;

use app\front\controllers\eventy\DetailEventuActionsController;
use app\front\controllers\eventy\DetailEventuActionsWHController;
use app\front\controllers\eventy\DetailEventuController;
use app\front\eventy\detail\modaly\finance\naklad\NovyNakladEventModal;
use app\front\eventy\detail\modaly\finance\novy\NovaPlatbaModal;
use app\front\eventy\detail\modaly\finance\novy\vlastni\VlastniNovaPlatbaModal;
use app\front\eventy\detail\panely\finance\component\PrehledFinanciComponent;
use app\front\eventy\detail\panely\IEventPanel;
use app\front\eventy\detail\panely\uzavreni\UzavreniEventuPanel;
use app\System;
use app\system\application\ApplicationVersion;
use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\event\BaseEventRow;
use app\system\model\naklady\EventNaklady;
use app\system\model\naklady\EventNakladyRow;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\model\organizace\zakaznici\OrganizaceZakaznikRow;
use app\system\model\platby\event\uhrazene\EventUhrazenePlatby;
use app\system\modul\panels\Panel;
use Dibi\DateTime;
use Nette\Utils\Html;

/** Created by Filip Pavlas. Date: 10.08.2023 */
abstract class BaseFinanceEventuPanel extends Panel
   implements IEventPanel
{

   function getMenuName() :string {
      return 'Finance';
   }

   public function preparePanel(Templater $templater) :void {
      $this->appendEvent();

      if($this->event->canUzavrit())
         $this->data['panelsRefresh'] = json_encode([
            sprintf('#%s', UzavreniEventuPanel::init()->getHtmlId()),
         ]);

      $templater->addData([
         '_panel' => $this,
         'platbaModal' => VlastniNovaPlatbaModal::init()->btnToggleAttributes(),
         'platbaModalID' => VlastniNovaPlatbaModal::init()->getIdModal(),
         'baseUrl' => DetailEventuController::getUrl($this->event->getID()),
         'financeComponent' => new \Latte\Runtime\Html(PrehledFinanciComponent::getComponent()->setEvent($this->event)),
         'odstranitPlatbuUrl' => DetailEventuActionsController::getUrl($this->event->getID(), DetailEventuActionsController::ACTION_ODSTRANIT_PLATBU),
         'vygenerovatDoplatekUrl' => DetailEventuActionsWHController::getUrl($this->event->getID(), DetailEventuActionsWHController::ACTION_GENERATE_DOPLATEK),
         'addNakladModalAttr' => NovyNakladEventModal::init()->btnToggleAttributes(),
      ] + $this->data);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUlozitNaklad', function($post) {
         $event = $this->getEvent();
         $version = ApplicationVersion::getFromEvent($event);

         $naklad = isset($post['id_naklad'])
            ? EventNaklady::get($post['id_naklad'], $version)
            : new EventNakladyRow(['id_event' => $event->getID()]);

         $naklad->nazev = trim($post['nazev']);

         if(!$naklad->nazev){
            FlashMessages::setError('Název musí být vyplněn');
            return;
         }

         $naklad->datum = new DateTime($post['datum_naklad']);
         $naklad->popis = strip_tags(trim($post['popis'] ?? '')) ?: null;
         $naklad->suma = bcmul(str_replace(',', '.', $post['suma']), '100');

         EventNaklady::save($naklad, $version);
      });

      $this->isset('btnSmazatNaklad', function($post) {
         $event = $this->getEvent();
         $version = ApplicationVersion::getFromEvent($event);

         if(
            !($id = intval($post['id_naklad'] ?? ''))
            || !($naklad = EventNaklady::get($id, $version))
            || $naklad->id_event !== $event->getID()
         ){
            FlashMessages::setError('Chyba při mazání');
            return;
         }

         EventNaklady::delete($naklad, $version);
         FlashMessages::setSuccess('Náklad byl smazán');
      });

      $this->isset('btnDeletePayment', function($post) {
         $id_platba = intval($post['btnDeletePayment']);
         $env = FrontApplicationEnvironment::get();

         if(!($env->isMajitel || $env->personal?->hasAccessRight(PersonalAccessRightsEnum::ADMIN))){
            FlashMessages::setError('Nemáte právo smazat platbu!');
            return;
         }

         $platba = EventUhrazenePlatby::get($id_platba, $env->versionType);

         if(!$platba || $platba->id_event !== $this->getEvent()->getID()){
            FlashMessages::setError('Chyba při ukládání');
            return;
         }

         EventUhrazenePlatby::delete($platba, FrontApplicationEnvironment::get()->versionType);
         FlashMessages::setSuccess('Uhrazená platba byla smazáná');
      });
   }

   public function createNovaPlatbaModal() :Html {
      $icon = Html::el('i')
         ->addAttributes(['class' => 'me-1 bi bi-plus-lg']);

      $text = Html::el('span')
         ->setText(System::getTranslator()->translate('Přidat úhradu'));

      $link = Html::el('a')
         ->addAttributes([
            'class' => 'btn btn-sm btn-outline-primary shaddow-hover',
            'href' => '#',
         ])
         ->addAttributes(NovaPlatbaModal::getShowAttributes($this->event->getID()))
         ->addHtml($icon)
         ->addHtml($text);

      return Html::el('div')
         ->addAttributes(['class' => 'col-auto form-group'])
         ->addHtml($link);
   }

   protected function appendEvent() :void {
      $this->event = $this->getEvent();
      $this->data['event'] = $this->event;
   }

   public BaseEventRow $event;
   public OrganizaceZakaznikRow $zakaznik;
   public array $data = [];
}