{varType app\front\eventy\detail\panely\finance\BaseFinanceEventuPanel $_panel}
{varType app\system\model\event\BaseEventRow $event}
{varType Latte\Runtime\Html $financeComponent}
{varType string[] $platbaModal}
{varType string $platbaModalID}
{varType string $odstranitPlatbuUrl}
{varType string $panelsRefresh}
{varType array $addNakladModalAttr}
{varType string $vygenerovatDoplatekUrl}

{capture $zrusenoText}
   <br><small class="text-danger">{_'Zrušeno'}</small>
{/capture}

<div class="container">
   <div class="my-2 js-finance-component">
      {$financeComponent}
   </div>
   <div class="row mt-2 gap-3">
      <div class="col-md">
         <div class="row mt-2 pt-2 p-3">
            <div class="card-header row mb-3 mb-md-0">
               <div class="col">
                  <h3 class="mb-0 d-inline"><i class="align-middle px-2 py-1 me-2 bi bi-graph-up-arrow bg-primary radius-icon icon-shadow text-white"></i>{_'Přehled úhrad'}</h3>
               </div>
               <div class="col-auto d-flex justify-content-end js-nova-platba" n:if="$event->getFinance()->hasNezaplacene()">
                  {$_panel->createNovaPlatbaModal()}
               </div>
            </div>
            <div class="p-0 p-md-3">
               {foreach $event->getFinance()->uhrazene as $uhrazena}
                  <div class="card mb-1 px-3 py-1 shaddow-hover">
                     <div class="row d-flex align-items-center">
                        <div class="col">
                           <span><i class="bi bi-calendar-week me-2 d-none d-md-inline"></i>{$uhrazena->paid|date:'j.n.Y'}</span>
                        </div>
                        <div class="col">
                           {$uhrazena->popis}
                        </div>
                        <div class="col">
                           <span>{$uhrazena->getSumaCurrency()|price}</span>
                        </div>
                        <div class="col text-end">
                           <a href="#" n:attr="$uhrazena->getPlatbaModalAttrs()" class="lead text-secondary"><i class="bi bi-arrow-up-right-circle hover-icon-arrow"></i></a>
                        </div>
                     </div>
                  </div>
               {/foreach}
            </div>
         </div>
      </div>

      <div class="vr p-0 d-none d-sm-inline-block"></div>

      <div class="col-md">
         <div class="row mt-2 pt-2 p-3">
            <div class="card-header row mb-3 mb-md-0">
               <div class="col">
                  <h3 class="mb-0 d-inline"><i class="align-middle px-2 py-1 me-2 bi bi-graph-down-arrow bg-primary radius-icon icon-shadow text-white"></i>{_'Přehled nákladů'}</h3>
               </div>
               <div class="col-auto">
                  <button class="btn btn-sm btn-outline-primary shaddow-hover" type="button" n:attr="$addNakladModalAttr">
                     <i class="me-1 bi bi-plus-lg"></i>{_'Přidat náklad'}</button>
               </div>
            </div>
            <div class="p-0 p-md-3">
            {foreach $event->getFinance()->naklady as $naklad}
                  <div class="card mb-1 px-3 py-1 shaddow-hover">
                     <div class="row d-flex align-items-center">
                        <div class="col">
                           <span><i class="bi bi-calendar-week me-2 d-none d-md-inline"></i>{$naklad->datum|date:'j.n.Y'}</span>
                        </div>
                        <div class="col">
                           <span>{$naklad->nazev}</span>
                        </div>
                        <div class="col">
                           <span>{$naklad->getSuma()|price}</span>
                        </div>
                        <div class="col text-end">
                           <a href="#" n:attr="$naklad->getModalBtnAttr()" class="lead text-secondary"><i class="bi bi-arrow-up-right-circle hover-icon-arrow"></i></a>
                        </div>
                     </div>
                  </div>
               {/foreach}
            </div>
         </div>
      </div>
   </div>
   <div class="row mt-2">
      <div class="col">
         <div class="row mt-2 p-3">
            <div class="row toolbar-section">
               <div class="col-md-auto col-12 mb-3 mb-md-0 d-flex align-content-center">
                  <h3><i class="align-middle px-2 py-1 me-2 bi bi-credit-card bg-primary radius-icon icon-shadow text-white"></i>{_'Nejbližší plánované platby'}</h3>
               </div>
               <div class="d-flex gap-2 col-md-auto col-12">
                  <div class="col-auto">
                     <a href="#" n:attr="$platbaModal" class="btn btn-sm btn-outline-secondary w-auto shaddow-hover"><i class="me-1 bi bi-plus-lg"></i>{_'Přidat plánovanou platbu'}</i>
                     </a>
                  </div>

                  <div n:if="!$event->getFinance()->platbyDoplatek->isZero()"
                          class="col">
                     <button class="btn btn-sm btn-outline-secondary w-auto js-create-doplatek">
                        <i>{_'Vytvořit doplatek'}</i></button>
                  </div>
               </div>
               <div class="col">
                  <hr>
               </div>
            </div>
            <div>
               <div class="row gap-3">
                  {if !empty($event->getFinance()->platby)}
                     {foreach $event->getFinance()->platby as $platba}
                        <div n:if="!$platba->zaplaceno"
                                class="border border-light-gray radius-card col-md-auto col-12 px-lg-4 py-3 js-nasledujici-platba shaddow-hover">
                           <div class="d-flex gap-2">
                              <div class="col-auto">
                                 <img src="/files/system/img/icons/plat-karta.png" width="30px">
                              </div>
                              <div class="col">
                                 <div class="row">
                                    <div class="col">
                                       <p class="lead">{$platba->nazev}<span
                                                  class="js-declined-platba">{if $platba->is_zrusena}{$zrusenoText}{/if}</span>
                                       </p>
                                    </div>
                                    <div class="col-auto" n:if="!$platba->uhrazeno">
                                       <a href="#" class="js-nejblizsi-platba text-secondary"
                                          data-platba="{$platba->id_platby}"
                                          data-action="delete"
                                          data-confirm="{_'Opravdu chcete naplánovanou platbu smazat?'}">
                                          <i class="hover-icon-arrow bi bi-x-circle"></i>
                                       </a>
                                    </div>
                                    <div class="col-auto js-decline-col" n:if="$platba->uhrazeno !== null && !$platba->is_zrusena">
                                       <a href="#" class="js-nejblizsi-platba"
                                          data-platba="{$platba->id_platby}"
                                          data-action="decline"
                                          data-confirm="{_'Opravdu chcete platbu zrušit?'}">
                                          <i class="bi bi-ban"></i>
                                       </a>
                                    </div>
                                 </div>
                                 <div class="row">
                                    <div class="col col-md-12">
                                       <small>{$platba->termin|date:'j.n.Y'}</small>
                                    </div>
                                    <div class="vr p-0 d-md-none"></div>
                                    <div class="col col-md-12">
                                       <small>{$platba->suma|price}</small>
                                    </div>
                                 </div>
                                 <p>
                             <span class="ms-2" n:if="$platba->uhrazeno">
                                <small>{_'Uhrazeno'}:</small> {$platba->uhrazeno|price}
                             </span>
                                 </p>
                              </div>
                           </div>
                        </div>
                        {if !$iterator->first && $iterator->nextValue}
                           <div class="col-md-auto col-12 d-flex align-items-center justify-content-center px-0">
                              <i class="bi bi-arrow-right lead text-gray-light d-none d-md-block"></i>
                              <i class="bi bi-arrow-down lead text-gray-light d-md-none"></i>
                           </div>
                        {/if}
                     {/foreach}
                  {else}
                     <p>{_'Žádná další platba'}</p>
                  {/if}
               </div>
            </div>
         </div>
      </div>
   </div>
</div>


<script>
    $(function () {
       const idModalNovaPlatba = {$platbaModalID};

        $('body').on('click', '.js-nejblizsi-platba', function (e) {
            const btn = $(this);
            let id_platba = $(this).attr('data-platba');
            let action = $(this).attr('data-action');
            const financeComponent = $('div.js-finance-component');

            if (e.isDefaultPrevented() === false)
                ajaxHandler.post({$odstranitPlatbuUrl}, {
                   id_platba,
                   action,
                   isDetail: financeComponent.find('input#zobrazitDetailFinance').is(':checked')
                }, function (response) {
                   const card = btn.closest('.js-nasledujici-platba');
                    $('div.js-finance-component').html(response['financeComponent']);
                    refreshPanels();

                    if(!response['moreActivePayments']){
                       const btnNovaPlatba = $('.js-nova-platba');

                       if(btnNovaPlatba.length > 0)
                          btnNovaPlatba.hide();
                    }

                    if(action === 'decline'){
                       card.find('.js-declined-platba').html({$zrusenoText});
                       card.find('.js-decline-col').remove();
                       return;
                    }

                   card.remove();
                });
        }).on('click', 'button.js-create-doplatek', function() {
           const btn = $(this);
           btn.prop('disabled', true);

           ajaxHandler.post({$vygenerovatDoplatekUrl}, { }, function(response) {
              const modal = bootstrap.Modal.getOrCreateInstance('#' + idModalNovaPlatba);
              const modalContent = $(modal._element);

              const sumaInput = modalContent.find('input[name="suma_vlastni_platba"]');
              const nazevInput = modalContent.find('input[name="nazev_platby"]');
              const switchVratka = modalContent.find('input[name="is_negative"]');

              let sumaNumber = Number(response['suma'].replace(',', '.'));

              switchVratka.prop("checked", sumaNumber < 0).trigger('change');

              if(sumaNumber < 0)
                 sumaNumber = sumaNumber * (-1);

              sumaInput.val(String(sumaNumber).replace('.', ','));

              $.applyDataMask(sumaInput);

              nazevInput.val("{_'Doplatek'}");

              modal.show();
              btn.prop('disabled', null);
           });
        })


       const refreshPanelsJson = {ifset $panelsRefresh}{$panelsRefresh|noescape}{else}null{/ifset};

       function refreshPanels() {
          if(refreshPanelsJson === null)
             return;

          refreshPanelsJson.forEach(function(value) {
             const panel = $(value);

             if(panel.length === 0)
                return;

             const loader = panel.find('.lazyLoader');
             const container = panel.find('.lazyloader-container');

             if(loader.attr('data-lazy-show') === 'false'){
                container.remove();
                loader.attr('data-lazy-show', true);
                loader.show();
             }
          });
       }
    });
</script>