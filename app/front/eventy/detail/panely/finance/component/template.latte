{varType app\system\model\event\EventFinance $finance}
{varType ?Money\Money $localtax}
{varType bool $isDetail}
{varType bool $canToggleDetail}

<div class="row p-3">
   <div n:if="$canToggleDetail" class="row">
      <div class="form-check form-switch">
         <input class="form-check-input" type="checkbox" role="switch" id="zobrazitDetailFinance" data-bs-toggle="collapse" data-bs-target=".jsDetailFinanceHiddenBlock" n:attr="checked: $isDetail">
         <label class="form-check-label" for="zobrazitDetailFinance">{_'Zobrazit detaily'}</label>
      </div>
   </div>
   <div class="d-md-flex gap-3 gap-md-2 mt-2">
      <div class="card p-3 col-md-auto col-lg d-md-inline form-group">
         <div>
            <label>{_'Celková cena'}:</label>
            <p class="lead">{$finance->obratBezDPH|price}&nbsp;<small>{_'bez DPH'}</small></p>
            <div n:class="$canToggleDetail ? jsDetailFinanceHiddenBlock, !$isDetail ? collapse">
               <small class="text-secondary"><span class="me-1">{_'Včetně DPH'}:</span>{$finance->obrat|price}</small><br>
               <small class="text-secondary" n:if="$localtax"><span class="me-1">+ {_'Poplatek za ubytování'}:</span>{$localtax|price}</small><br>
               <small n:if="!$finance->odhad->isZero()" class="text-secondary"><span class="me-1">{_'Odhad'}:</span>{$finance->odhad|price}</small>
            </div>
         </div>
      </div>

      <div class="card p-3 col-md-auto col-lg d-md-inline form-group">
         <div>
            <label>{_'Zaplaceno'}:</label>
            <p class="lead">{$finance->zaplaceno|price}</p>
            <div n:class="$canToggleDetail ? jsDetailFinanceHiddenBlock, !$isDetail ? collapse">
               <small n:if="$finance->doplatit" class="text-secondary">
                  <span class="me-1">{_'Zbývá doplatit'}:</span>{$finance->doplatit|price}
               </small><br>
               <small class="text-secondary">
                  <span class="me-1">{_'Naplánováno'}:</span>
                  <span>{$finance->platbyCelkem|price}</span>
               </small><br>
               <small n:if="!$finance->platbyDoplatek->isZero()" class="text-danger">
                  <span class="me-1">{_'Doplatek'}:</span>
                  <span>{$finance->platbyDoplatek|price}</span>
               </small>
            </div>
         </div>
      </div>
      <div class="card p-3 col-md-auto col-lg d-md-inline form-group">
         <div>
            <label>{_'Celkové náklady'}:</label>
            <p class="lead">{$finance->nakladyCelkove|price}</p>
            <div n:class="$canToggleDetail ? jsDetailFinanceHiddenBlock, !$isDetail ? collapse">
               <small class="text-secondary"><span class="me-1">{_'Personál'}:</span>{$finance->nakladyPersonal|price}</small><br>
               <small class="text-secondary"><span class="me-1">{_'Balíčky a položky'}:</span>{$finance->nakladyPolozky|price}</small><br>
               <small n:if="!$finance->nakladyOstatni->isZero()" class="text-secondary"><span class="me-1">{_'Další'}:</span>{$finance->nakladyOstatni|price}</small>
            </div>
         </div>
      </div>

      <div class="card p-3 col-md-auto col-lg d-md-inline form-group">
         <label>{_'Odhadovaný zisk'}:</label>
         <p n:class="lead, $finance->ziskOdhad->isPositive() ? text-success : text-danger">{$finance->ziskOdhad|price}&nbsp;({$finance->getOdhadZiskProcenta()}&nbsp;%)<i class="bi bi-question-circle ms-1 text-info" data-bs-toggle="tooltip" data-bs-title="{_'Souhrn očekávaných přijmů bez DPH a nákladů bez DPH.'}"></i></p>
         <div n:class="$canToggleDetail ? jsDetailFinanceHiddenBlock, !$isDetail ? collapse">
            <small class="text-secondary">
               <span class="me-1">{_'Zisk na hosta'}:</span>
               <span n:class="$finance->ziskHost->isPositive() ? text-success : text-danger">{$finance->ziskHost|price}</span>
            </small><br>

            <small class="text-secondary">
               <span class="me-1">{_'Zisk'}:</span>
               <span n:class="$finance->zisk->isPositive() ? text-success : text-danger">{$finance->zisk|price}&nbsp;({$finance->getZiskProcenta()}&nbsp;%)</span>
               <span><i class="bi bi-question-circle ms-1 text-info" data-bs-toggle="tooltip" data-bs-title="{_'Souhrn aktuálních nákladů bez DPH a očekávaných příjmů bez DPH.'}"></i></span>
            </small>
         </div>
      </div>
   </div>
</div>