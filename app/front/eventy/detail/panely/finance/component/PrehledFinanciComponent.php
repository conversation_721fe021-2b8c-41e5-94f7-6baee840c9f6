<?php namespace app\front\eventy\detail\panely\finance\component;

use app\system\component\Component;
use app\system\model\event\BaseEventRow;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 08.12.2023 */
class PrehledFinanciComponent extends Component
{

   public function setEvent(BaseEventRow $eventRow) :static {
      $this->event = $eventRow;
      return $this;
   }

   function setData() :array {
      return [
         'finance' => $this->event->getFinance(),
         'isDetail' => $this->isDetail,
         'canToggleDetail' => $this->canToggleDetail,
         'localtax' => $this->event->localtax
            ? $this->event->getLocaltaxSuma()
            : null,
      ];
   }

   public function setIsDetail(bool $isDetail = true) :PrehledFinanciComponent {
      $this->isDetail = $isDetail;
      return $this;
   }

   public function setCanToggleDetail(bool $canToggleDetail) :PrehledFinanciComponent {
      $this->canToggleDetail = $canToggleDetail;
      return $this;
   }

   private BaseEventRow $event;
   private bool $isDetail = false;
   private bool $canToggleDetail = true;
}