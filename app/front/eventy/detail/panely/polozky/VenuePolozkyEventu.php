<?php namespace app\front\eventy\detail\panely\polozky;

use app\front\eventy\detail\panely\finance\VenueFinanceEventu;
use app\front\eventy\detail\panely\prehled\VenuePrehledEventu;
use app\system\model\event\BaseEventRow;
use app\system\model\event\VenueEventRow;
use app\system\model\event\VenueEventy;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 02.08.2023 */
class VenuePolozkyEventu extends BasePolozkyEventuPanel
{

   public function getEvent() :BaseEventRow {
      return $this->event ??= VenueEventy::get($this->getParameter('id_eventu'));
   }

   protected function getRefreshPanelsJSON() :string {
      return json_encode([
         sprintf('#%s', VenueFinanceEventu::init()->getHtmlId()),
         sprintf('#%s', VenuePrehledEventu::init()->getHtmlId()),
      ]);
   }

   private ?VenueEventRow $event;
}