<?php namespace app\front\eventy\detail\panely\polozky;

use app\front\eventy\detail\panely\IEventPanel;
use app\system\component\Templater;
use app\system\model\entity\polozky\component\BasePolozkyComponent;
use app\system\modul\panels\Panel;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 02.08.2023 */
abstract class BasePolozkyEventuPanel extends Panel
   implements IEventPanel
{

   function getMenuName() :string {
      return 'Balíčky a položky';
   }

   public function preparePanel(Templater $templater) :void {
      $templater->addData([
         'event' => $event = $this->getEvent(),
         'polozkyComponent' => BasePolozkyComponent::getComponent()
            ->setEntity($event)
            ->setRefreshJson($this->getRefreshPanelsJSON())
            ->renderHtml(),
      ]);
   }

   abstract protected function getRefreshPanelsJSON() :string;
}