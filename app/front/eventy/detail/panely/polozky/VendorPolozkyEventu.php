<?php namespace app\front\eventy\detail\panely\polozky;

use app\front\eventy\detail\panely\finance\VendorFinanceEventu;
use app\front\eventy\detail\panely\prehled\VendorPrehledEventu;
use app\system\model\event\BaseEventRow;
use app\system\model\event\VendorEventRow;
use app\system\model\event\VendorEventy;

/** Created by <PERSON><PERSON>. Date: 10.08.2023 */
class VendorPolozkyEventu extends BasePolozkyEventuPanel
{

   public function getEvent() :BaseEventRow {
      return $this->event ??= VendorEventy::get($this->getParameter('id_eventu'));
   }

   protected function getRefreshPanelsJSON() :string {
      return json_encode([
         sprintf('#%s', VendorFinanceEventu::init()->getHtmlId()),
         sprintf('#%s', VendorPrehledEventu::init()->getHtmlId()),
      ]);
   }

   private ?VendorEventRow $event;
}