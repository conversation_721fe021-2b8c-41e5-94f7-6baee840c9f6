<?php namespace app\front\eventy\detail\panely\personal\component;

use app\front\eventy\detail\modaly\personal\DetailPersonalModal;
use app\system\application\FrontApplicationEnvironment;
use app\system\component\Component;
use app\system\model\event\BaseEventRow;
use app\system\model\event\personal\EventPrirazenyPersonal;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\model\organizace\pozice\PozicePersonal;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 03.04.2024 */
class SeznamSmenComponent extends Component
{

   function setData() :array {
      $personal = FrontApplicationEnvironment::get()->personal;

      $zobrazovatCeny = FrontApplicationEnvironment::get()->isMajitel
         || ($personal !== null && (
               $personal->hasAccessRight(PersonalAccessRightsEnum::ADMIN)
               || ($personal->hasAccessRight(PersonalAccessRightsEnum::EVENTY) && $personal->hasAccessRight(PersonalAccessRightsEnum::ZOBRAZUJE_FINANCE_EVENT))
            ));

      return [
         'smeny' => EventPrirazenyPersonal::getByIdEvent($this->event),
         'pracovniPozice' => PozicePersonal::getByMistoAssoc($this->event->getIdOrganizace()),
         'canViewPrices' => $zobrazovatCeny,
         'uzivatelPersonal' => $personal,
         'event' => $this->event,
      ];
   }

   public function detailPersonalModalAttr(int $id) :array {
      return DetailPersonalModal::getShowAttributes($id);
   }

   public function setEvent(BaseEventRow $event) :static {
      $this->event = $event;
      return $this;
   }

   private BaseEventRow $event;
}