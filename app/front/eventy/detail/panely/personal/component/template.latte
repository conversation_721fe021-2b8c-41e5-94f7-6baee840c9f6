{varType app\front\eventy\detail\panely\personal\component\SeznamSmenComponent $_comp}
{varType app\system\model\event\personal\EventSmenyContainer $smeny}
{varType app\system\model\organizace\pozice\PozicePersonalRow[] $pracovniPozice}
{varType ?app\system\model\organizace\personal\PersonalRow $uzivatelPersonal}
{varType bool $canViewPrices}
{varType app\system\model\event\BaseEventRow $event}


   {foreach $smeny->getSmenaItems() as $personal}
      <div class="card mb-2 px-3 py-2 shaddow-hover">
         <div class="row">
            <div class="col-10">
               <div class="row">
                  <div class="col-auto">
                     <span class="lead my-1 me-3">{$personal->personal->getFullName()}</span>
                     <small class="text-gray">{if isset($personal->smena->id_pozice)}{($pracovniPozice[$personal->smena->id_pozice])->nazev}{else}{_'Bez pozice'}{/if}</small>
                  </div>
               </div>
               <div class="row gap-2">
                  <div class="col-auto">
                     <small class="text-gray">
                        <i class="bi bi-clock-history me-1"></i>
                        {$personal->smena->dtStart|date:'j.n. H:i'}&nbsp;-&nbsp;{$personal->smena->dtEnd|date:'H:i'}
                     </small>
                  </div>
                  {if $canViewPrices || ($uzivatelPersonal->id_personal == $personal->personal->id_personal)}
                     <div class="col-auto">
                        <small class="text-gray"><i class="bi bi-cash-coin me-1"></i>{$personal->smena->getMzda()|price}</small>
                     </div>
                  {/if}
               </div>
            </div>
            <div class="col text-end align-content-center">
               <a class="display-5 text-secondary" href="#" n:attr="$_comp->detailPersonalModalAttr($personal->smena->id)"><i class="bi bi-arrow-up-right-circle hover-icon-arrow"></i></a>
            </div>
         </div>
      </div>
   {/foreach}
