{varType app\front\eventy\detail\panely\personal\PersonalEventuPanel $_panel}
{varType app\system\model\event\personal\EventSmenyContainer $smeny}
{varType app\system\model\tymy\TymyRow[]|null $tymy}
{varType Latte\Runtime\Html $smenyComponent}
{varType Latte\Runtime\Html $tinyEditor}
{varType ?app\system\model\organizace\personal\PersonalRow $uzivatelPersonal}
{varType app\system\model\event\BaseEventRow $event}

{varType bool $canViewPrices}

{varType array $pridatPersonalModalAttr}
{varType string $pridatTymUrl}

{varType array $attrTisk}

<div class="container">
   <div class="row mt-2 gap-3">
      <div class="col-md-9 border border-light-gray radius-card">
         <div class="row border-bottom toolbar-section gap-3 bg-white rohy-2-2 px-1 py-3">
            <div class="col-lg col-12 align-content-center">
               <h4><i class="align-middle px-2 py-1 me-2 bi bi-people bg-primary radius-icon icon-shadow text-white"></i>{_'Personál'}</h4>
            </div>
            <div class="col-auto d-flex align-items-center gap-2">
               <div n:if="$canViewPrices" class="col-auto">
                  <div class="col-auto">
                     <a href="#" n:attr="$pridatPersonalModalAttr" class="form-control btn btn-sm btn-primary shaddow-hover"><i
                                class="bi bi-plus-lg me-2"></i>{_'Přidat'}</a>
                  </div>
               </div>
               <div class="col-sm-auto">
                  <button class="form-control btn btn-sm btn-outline-secondary shaddow-hover" n:attr="$attrTisk">
                     <div class="spinner-border spinner-border-sm text-secondary me-2 js-tisk-loader"
                          role="status" style="display: none">
                        <span class="visually-hidden">{_'Načítání'}...</span>
                     </div>
                     <i class="align-middle me-2 bi bi-printer"></i>{_'Tisk'}
                  </button>
               </div>
               <div n:if="$canViewPrices && !empty($tymy)" class="text-end">
                  <a class="form-control btn btn-sm btn-outline-secondary shaddow-hover dropdown-toggle"
                     data-bs-auto-close="false" data-bs-toggle="dropdown"
                     aria-expanded="false">
                     <i class="align-middle me-2 fas fa-fw fa-clipboard-list"></i>{_'Přidat tým'}
                  </a>

                  <div class="dropdown-menu dropdown-menu-lg" id="dropdown-tymy">
                     <form method="post" class="px-4 py-3">
                        <div class="row">
                           <div class="col">
                              <div class="form-group">
                                 <label for="tymSelect">{_'Název týmu'}</label>
                                 <div class="input-group" data-target-input="nearest">
                                    <select id="tymSelect" class="form-select">
                                       <option></option>
                                       {foreach $tymy as $tym}
                                          <option value="{$tym->id_tym}">{$tym->nazev}</option>
                                       {/foreach}
                                    </select>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <div n:if="$event->isVicedenni()" class="row">
                           <div class="form-group col-md col-6">
                              <label for="addTymDatum">{_'Datum směny'}<span class="text-danger">&nbsp;*</span></label>
                              <div class="input-group date js-datepicker" id="addTymmodal_datum"
                                   data-datepicker-min="{$event->date_start->sub(new \DateInterval('P2D'))|date: 'm/d/Y'}" data-datepicker-max="{$event->date_end->add(new \DateInterval('P2D'))|date: 'm/d/Y'}"
                                   data-target-input="nearest">
                                 <input type="text" class="form-control datetimepicker-input" name="datum"
                                        data-target="#addTymmodal_datum" id="addTymDatum"
                                        value="{$event->date_start|date:'j.n.Y'}" required>
                                 <div n:if="$event->isVicedenni()" class="input-group-text" data-target="#addTymmodal_datum" data-toggle="datetimepicker"><i class="bi bi-clock"></i></div>
                              </div>
                           </div>
                        </div>

                        {var $formID = 'addTym'}
                        <div class="row">
                           <div class="form-group col-md col-6">
                              <label for="{$formID}CasStart">{_'Začátek'}<span class="text-danger">&nbsp;*</span></label>
                              <div class="input-group date js-timepicker" id="{$formID}modal_start" data-target-input="nearest">
                                 <input type="text" class="form-control datetimepicker-input" name="timeStart"
                                        data-target="#{$formID}modal_start" id="{$formID}CasStart" value="{$event->date_start|date:'H:i'}" required>
                                 <div class="input-group-text" data-target="#{$formID}modal_start" data-toggle="datetimepicker"><i class="bi bi-clock"></i></div>
                              </div>
                           </div>
                           <div class="form-group col-md col-6">
                              <label for="{$formID}CasEnd">{_'Konec'}<span class="text-danger">&nbsp;*</span></label>
                              <div class="input-group date js-timepicker" id="{$formID}modal_end" data-target-input="nearest">
                                 <input type="text" class="form-control datetimepicker-input" name="timeEnd"
                                        data-target="#{$formID}modal_end" id="{$formID}CasEnd" value="{$event->date_end|date:'H:i'}" required>
                                 <div class="input-group-text" data-target="#{$formID}modal_end" data-toggle="datetimepicker"><i class="bi bi-clock"></i></div>
                              </div>
                           </div>
                        </div>
                        <div class="row">
                           <div class="col-auto">
                              <button type="button" class="my-3 btn btn-outline-primary js-event-pridat-tym" id="btnPridatTym">
                                 <i class="align-middle bi bi-plus-lg me-1-circle"></i>{_'Přidat tým'}
                              </button>
                           </div>
                        </div>
                     </form>
                  </div>
               </div>
            </div>
         </div>
         <div class="row js-smeny-container p-md-3">
            {$smenyComponent}
         </div>
         </div>
      <div class="col-md ms-md-2">
         <div class="">
            <div n:if="$uzivatelPersonal && $smeny->getNakladOsoby($uzivatelPersonal->id_personal)" style="font-size:0.8rem" class="row card p-4 bg-secondary text-white border-0" class="row">
               <small class="text-gray-light">{_'Můj výdělek'}:</small>
               <p class="text-white lead">{$smeny->getNakladOsoby($uzivatelPersonal->id_personal)|price}</p>
            </div>
            <div style="font-size:0.8rem" class="row card p-4 bg-secondary text-white border-0">
               <small class="text-gray-light">{_'Celkový počet personálu'}:</small>
               <p class="text-white lead">{$smeny->getPocetPersonalu()}</p>
            </div>
            <div style="font-size:0.8rem" class="row card p-4 bg-secondary text-white border-0">
               <small class="text-gray-light">{_'Personál/hosté'}:</small>
               <p class="text-white lead">{$smeny->getPocetPersonalu()} / {$_panel->event->getPocetHostu()}</p>
            </div>

            <div n:if="$canViewPrices && $smeny->getNakladPersonalu()" style="font-size:0.8rem" class="row card p-4 bg-secondary text-white border-0" class="row">
               <small class="text-gray-light">{_'Celkové náklady'}:</small>
               <p class="text-white lead">{$smeny->getNakladPersonalu()|price}</p>
            </div>
         </div>
      </div>
   </div>
   <form method="post">
      <div class="row">
         <div class="mb-4">
            <div class="row">
               <h3 class="lead my-5 text-center">{_'Další informace k personálu'}</h3>
            </div>
            {$tinyEditor}
            <div class="row d-flex justify-content-center">
               <button type="submit" name="btnUlozitPoznamkuPersonalu" class="btn btn-outline-primary w-auto mt-3">{_'Uložit poznámku'}</button>
            </div>
         </div>
      </div>
   </form>
</div>

<script>
   $(function() {
      const body = $('body');
      const pridatButton = $('.js-event-pridat-tym');
      pridatButton.prop('disabled', true);

   {if $canViewPrices && !empty($tymy)}
      const selectTym = $('#tymSelect');
      const dropdownTymy = $('#dropdown-tymy');

      selectTym.select2({
         placeholder: "{_'Vyber tým'}",
         width: '200px',
         dropdownParent: dropdownTymy,
      });

      body.on('click', 'button#btnPridatTym', function() {
         const id_tym = selectTym.val();
         pridatButton.prop('disabled', true);

         const datum = dropdownTymy.find('input[name=datum]').val();
         const timeStart = dropdownTymy.find('input[name=timeStart]').val();
         const timeEnd = dropdownTymy.find('input[name=timeEnd]').val();

         ajaxHandler.post({$pridatTymUrl}, {
            id_tym,
            datum,
            timeStart,
            timeEnd,
         }, function(response) {
            if(response['reload']){
               location.reload();
               return;
            }

            pridatButton.prop('disabled', false);
         });
      })
         .on('change', '#tymSelect', function () {
            pridatButton.prop('disabled', $(this).val() === 0)
         });
   {/if}
   });
</script>
