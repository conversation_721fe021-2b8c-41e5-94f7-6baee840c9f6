<?php namespace app\front\eventy\detail\panely\personal;

use app\front\controllers\eventy\DetailEventuActionsWHController;
use app\front\eventy\detail\modaly\personal\EventNovyPersonalModal;
use app\front\eventy\detail\panely\personal\component\SeznamSmenComponent;
use app\front\tisk\event\personal\PersonalEventuTisk;
use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\event\BaseEventRow;
use app\system\model\event\personal\EventPrirazenyPersonal;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\model\tymy\Tymy;
use app\system\modul\panels\Panel;
use app\system\tiny\TinyEditor;
use app\system\tiny\TinyEditorVersion;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 16.06.2022 */
class PersonalEventuPanel extends Panel
{

   function getMenuName() :string {
      return 'Personál';
   }

   public function preparePanel(Templater $templater) :void {
      $this->event = $this->getEvent();

      $data = [
         '_panel' => $this,
         'smeny' => EventPrirazenyPersonal::getByIdEvent($this->event, FrontApplicationEnvironment::get()->versionType),
         'attrTisk' => PersonalEventuTisk::getHtmlAttr(['id_event' => $this->event->getID()]),
         'smenyComponent' => new Html(SeznamSmenComponent::getComponent()->setEvent($this->event)->prepareTemplater()->render()),
         'tinyEditor' => new Html(TinyEditor::init()
            ->setVersion(TinyEditorVersion::CHAT)
            ->setContent($this->event->poznamka_personalu)
            ->setInputName('poznamka_personal')
            ->setId('poznamka_personal')
            ->setPlaceholder('Jakékoli poznámky k personálu')
            ->render()),
         'uzivatelPersonal' => FrontApplicationEnvironment::get()->personal,
         'event' => $this->event,
      ];

      $this->appendZobrazovatCeny($data);

      $templater->addData($data);
   }

   public BaseEventRow $event;

   protected function appendZobrazovatCeny(array &$data) :void {
      $zobrazovatCeny = FrontApplicationEnvironment::get()->isMajitel
         || (($personal = FrontApplicationEnvironment::get()->personal) !== null && (
               $personal->hasAccessRight(PersonalAccessRightsEnum::ADMIN)
               || ($personal->hasAccessRight(PersonalAccessRightsEnum::EVENTY) && $personal->hasAccessRight(PersonalAccessRightsEnum::ZOBRAZUJE_FINANCE_EVENT))
            ));

      $data['canViewPrices'] = $zobrazovatCeny;

      if(!$zobrazovatCeny)
         return;

      $data['pridatPersonalModalAttr'] = EventNovyPersonalModal::init()->btnToggleAttributes();
      $data['tymy'] = Tymy::getTeamArrayByMisto($this->event->getIdOrganizace());
      $data['pridatTymUrl'] = DetailEventuActionsWHController::getUrl($this->event->getID(), DetailEventuActionsWHController::ACTION_PRIDAT_TYM);
   }

   protected function getEvent() :BaseEventRow {
      return FrontApplicationEnvironment::get()->versionType->getEvent($this->getParameter('id_eventu'));
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUlozitPoznamkuPersonalu', function($post) {
         $event = $this->getEvent();

         $event->poznamka_personalu = trim($post['poznamka_personal']) ?: null;
         $event->save();

         FlashMessages::setSuccess('Poznámka k personálu byla uložena');
      });
   }
}
