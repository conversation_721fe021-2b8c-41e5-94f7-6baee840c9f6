<?php namespace app\front\eventy\detail\panely\prehled;

use app\system\model\event\BaseEventRow;
use app\system\model\event\VenueEventy;
use app\system\model\organizace\mistnosti\Mistnosti;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 02.08.2023 */
class VenuePrehledEventu extends BasePrehledEventuPanel
{

   public function getEvent() :BaseEventRow {
      return $this->event ??= VenueEventy::get($this->getParameter('id_eventu'));
   }

   public function appendTemplateData() :array {
      return [
         'mistnosti' => Mistnosti::getByMisto($this->getEvent()->getIdOrganizace()),
      ];
   }
}