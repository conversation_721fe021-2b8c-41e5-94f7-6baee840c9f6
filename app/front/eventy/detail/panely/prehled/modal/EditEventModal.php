<?php namespace app\front\eventy\detail\panely\prehled\modal;

use app\front\availability\AvailabilityChecker;
use app\front\controllers\availability\AvailabilityCheckerType;
use app\front\organizace\model\organizace\Organizace;
use app\System;
use app\system\component\Templater;
use app\system\model\event\BaseEventRow;
use app\system\model\event\VendorEventRow;
use app\system\model\event\VenueEventRow;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\organizace\localtax\component\EntityLocalTaxComponent;
use app\system\model\organizace\meny\OrganizaceMeny;
use app\system\model\organizace\mistnosti\Mistnosti;
use app\system\model\organizace\priznaky\OrganizacePriznaky;
use app\system\model\organizace\zdroje\OrganizaceZdroje;
use app\system\modul\modal\Modal;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 20.09.2023 */
class EditEventModal extends Modal
{

   public function getTitleName() :string {
      return 'Úprava eventu';
   }

   public function setEvent(BaseEventRow $event) :static {
      $this->event = $event;
      return $this;
   }

   public function prepareModal(Templater $templater) {
      $data['event'] = $this->event;
      $data['zdrojeMista'] = OrganizaceZdroje::getByMisto($this->event->getIdOrganizace());
      $data['skupiny'] = $this->event->getHosteContainer()->getVisiableSkupinyHostu();
      $data['jazyky'] = OrganizaceJazyky::getAll($this->event->getIdOrganizace());
      $data['meny'] = OrganizaceMeny::getAll($this->event->getIdOrganizace());
      $data['_modal'] = $this;

      $data['checkerMode'] = AvailabilityCheckerType::EVENT;
      $data['isMobil'] = System::get()->detect->isMobile();
      $data['localTaxComponent'] = (new EntityLocalTaxComponent($this->event))->render();
      $data['organizacePriznaky'] = OrganizacePriznaky::getForOrganizace($this->event->getIdOrganizace());

      if($this->event->getStatus()->isNovy() && !$this->event->getNabidka()){
         $data['terminChecker'] = (new AvailabilityChecker(Organizace::getMisto($this->event->getIdOrganizace())))
            ->setDatum($this->event->date_start, $this->event->isVicedenni() ? $this->event->date_end : null)
            ->setActualEntity($this->event)
            ->processCheck()
            ->renderComponent();
      }

      $templater->addData($data);
   }

   /** @noinspection PhpUnused */
   public function getLocationComponent() :Html|string {
      if($this->event instanceof VenueEventRow)
         return new Html(Templater::prepare(__DIR__ . '/mistnost.latte', [
            'event' => $this->event,
            'mistnosti' => Mistnosti::getByMisto($this->event->getIdOrganizace()),
         ]));
      elseif($this->event instanceof VendorEventRow)
         return '';

      return '';
   }

   protected BaseEventRow $event;
}