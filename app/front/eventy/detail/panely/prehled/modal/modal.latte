{varType bool $isMobil}
{varType app\system\model\event\BaseEventRow $event}
{varType app\system\model\organizace\zdroje\OrganizaceZdrojRow[] $zdrojeMista}
{varType app\system\model\organizace\cenove\HosteSkupina[] $skupiny}
{varType app\front\eventy\detail\panely\prehled\modal\EditEventModal $_modal}

{varType app\system\model\organizace\jazyk\OrganizaceJazykRow[] $jazyky}
{varType app\system\model\organizace\meny\OrganizaceMenaRow[] $meny}

{varType Latte\Runtime\Html $terminChecker}
{varType app\front\controllers\availability\AvailabilityCheckerType $checkerMode}

{varType Latte\Runtime\Html $localTaxComponent}
{varType app\system\model\organizace\priznaky\OrganizacePriznakyRow[] $organizacePriznaky}

<form method="post" id="editEventModal">
   <div class="js-termin-checker-block">{$terminChecker ?? ''}</div>
   <div class="container p-3 gap-3">
      <div class="row mb-2">
         <div class="col-md">
            <div class="form-floating">
               <input type="text" class="form-control" name="nazev" value="{$event->nazev?? ''}" maxlength="160" required>
               <label>{_'Název eventu'}</label>
            </div>
         </div>
      </div>

      <div class="row gap-3 pb-3">
         <div class="col-12 col-md">
            {$_modal->getLocationComponent()}
         </div>
      </div>

      <div class="row gap-3">
         <div class="col-12 col-md">
            <div class="form-floating">
               <select name="id_zdroj" class="form-select">
                  <option value="">{_'Žádný'}</option>
                  {foreach $zdrojeMista as $zdroj}
                     <option value="{$zdroj->id}"{if $event->id_zdroj === $zdroj->id} selected{/if}>{$zdroj->nazev}</option>
                  {/foreach}
               </select>
               <label for="id_zdroj">{_'Zdroj eventu'}</label>
            </div>
         </div>
      </div>

      <div class="row mt-3 gap-2">
         <div class="col-lg col">
            <div class="form-group">
               <div class="input-group">
                  <span class="input-group-text text-uppercase">{_'Začátek'}</span>
                  {if !$isMobil}
                     <input id="eventDetailDateStart" type="text" class="form-control datetimepicker-input date js-datepicker valid"
                            data-target-input="nearest" name="datumStart" autocomplete="off"
                            aria-invalid="false" value="{$event->date_start|date: 'j.n.Y'}" data-toggle="datetimepicker" required n:attr="disabled: !$event->canChangeDatum()">

                     <input type="text" class="form-control datetimepicker-input date js-timepicker"
                            data-target-input="nearest" autocomplete="off"
                            name="timeStart" id="eventDetailTimeStart" value="{$event->date_start|date: 'H:i'}" data-toggle="datetimepicker" required>
                  {else}
                     <input id="eventDetailDateStart" type="date" class="form-control datetimepicker-input"
                            name="datumStart" autocomplete="off" value="{$event->date_start|date: 'Y-m-d'}" required n:attr="disabled: !$event->canChangeDatum()">

                     <input id="eventDetailTimeStart" type="time" class="form-control text-end" autocomplete="off"
                            name="timeStart" value="{$event->date_start|date: 'H:i'}" required>
                  {/if}
               </div>
            </div>
         </div>
         <div class="col-md-auto col-12 align-content-center text-center">
            <i class="lead bi bi-arrow-right d-none d-md-block"></i>
            <i class="lead bi bi-arrow-down d-block d-md-none"></i>
         </div>
         <div class="col-lg col">
            <div class="form-group">
               <div class="input-group">
                  <span class="input-group-text text-uppercase">{_'Konec'}</span>
                  {if !$isMobil}
                     <input id="eventDetailDateEnd" type="text" class="form-control datetimepicker-input date js-datepicker valid"
                            data-target-input="nearest" name="datumEnd" autocomplete="off"
                            aria-invalid="false" value="{$event->date_end|date: 'j.n.Y'}" data-toggle="datetimepicker" required n:attr="disabled: !$event->canChangeDatum()">

                     <input type="text" class="form-control datetimepicker-input date js-timepicker"
                            id="eventDetailTimeEnd" data-target-input="nearest" autocomplete="off"
                            name="timeEnd" value="{$event->date_end|date: 'H:i'}" data-toggle="datetimepicker" required>
                  {else}
                     <input id="eventDetailDateEnd" type="date" class="form-control datetimepicker-input"
                            name="datumEnd" autocomplete="off" value="{$event->date_end|date: 'Y-m-d'}" required n:attr="disabled: !$event->canChangeDatum()">

                     <input type="time" class="form-control datetimepicker-input text-end"
                            id="eventDetailTimeEnd" autocomplete="off"
                            name="timeEnd" value="{$event->date_end|date: 'H:i'}" required>
                  {/if}
               </div>
            </div>
         </div>
      </div>

      <div class="row mt-2">
         <div class="col-md-6 col-xl-4" n:if="count($jazyky) > 1">
            <div class="form-group">
               <label for="id_jazyk">{_'Jazyk'}</label>
               <select class="form-select" id="id_jazyk" name="id_jazyk" n:attr="disabled: !$event->getStatus()->isEditovatelny()">
                  {foreach $jazyky as $jazyk}
                     <option value="{$jazyk->id_jazyk}" n:attr="selected: $jazyk->id_jazyk === $event->id_jazyk">{$jazyk->getJazykTitle()}</option>
                  {/foreach}
               </select>
            </div>
         </div>

         <div class="col-md-6 col-xl-4" n:if="count($meny) > 1">
            <div class="row">
               <div class="form-group">
                  <label for="id_mena">{_'Měna'}</label>
                  <select class="form-select" id="id_mena" name="id_mena" n:attr="disabled: !$event->getStatus()->isEditovatelny() || !$event->getProductsContainer()->isEmpty()">
                     {foreach $meny as $mena}
                        <option value="{$mena->id_mena}" n:attr="selected: $mena->id_mena === $event->id_mena_jazyk">{$mena->getMena()->getTitle()}</option>
                     {/foreach}
                  </select>
               </div>
            </div>
         </div>

         <div class="align-items-end col-md-6 col-xl-4 d-flex mt-2">
            <div class="form-check form-switch form-group">
               <input type="checkbox" name="bez_dph" id="bez_dph" class="form-check-input" n:attr="checked: $event->zobrazovat_bez_dph === 1">
               <label for="bez_dph" class="form-check-label">{_'Zobrazovat ceny bez DPH'}</label>
            </div>
         </div>
      </div>

      <div class="row mt-2">
         <div class="col-md-6 col-xl-4">
            {$localTaxComponent}
         </div>
      </div>

      <div n:if="!empty($organizacePriznaky)" class="row mt-2">
         <div class="col-md-6 col-xl-4">
            <label class="form-label" for="eventDetailPriznakEdit">{_'Příznak eventu'}</label>
            <select class="form-select" id="eventDetailPriznakEdit" name="id_priznak" autocomplete="off">
               <option value="0">{_'Bez příznaku'}</option>
               {foreach $organizacePriznaky as $priznak}
                  <option n:attr="value: $priznak->id, selected: $priznak->id === $event->priznak">{$priznak->nazev}</option>
               {/foreach}
            </select>
         </div>
      </div>
   </div>

      <div class="container border border-gray radius-card p-3 p-3">
         <div class="row mb-2">
            <h4 class="card-title ">{_'Úprava počtu hostů'}</h4>
         </div>
         <div class="col-md col-12 row">
            <div class="col form-group">
               <label>{_'Celkem hostů:'}</label>
               <input class="form-control form-control-sm my-1 js-pocet-hostu" type="number" name="pocet_hostu_celkem" value="{$event->getPocetHostu()}" disabled>
            </div>
            <div class="vr p-0 d-none d-sm-inline"></div>
            <div class="col-md-10 col-12 form-group">
            <label class="ms-2">{_'Jednotlivé skupiny hostů:'}</label>
            <div class="row d-flex">
               {foreach $skupiny as $skupina}
               <div class="col my-1 d-inline-flex">
                  <div class="d-flex mx-1 form-group col">
                     <div class="col-auto mx-1 text-end">
                     <label n:class="'col-form-label-sm px-2', $skupina->isNepocitatelna ? 'is-invalid text-danger'"
                           n:attr="title: strlen($skupina->nazev_skup) > 18? $skupina->nazev_skup">
                        <span n:if="$skupina->isNepocitatelna">
                           <i class="bi bi-question-circle ms-1 text-danger"
                              data-bs-toggle="tooltip"
                              data-bs-title="{_'Kategorie je nastavena jako nepočítatelná skupina hostů v nastavení'}">
                           </i>&nbsp;
                        </span>
                        <span>{$skupina->nazev_skup|truncate: 18}</span>:
                     </label>
                     </div>
                     <div class="col-7 p-0">
                     <input type="number" pattern="[0-9]*" inputmode="numeric" min="0"
                            n:class="'form-control form-control-sm js-pocet-skupiny', $skupina->isNepocitatelna ? 'border-danger bg-gray-100'"
                            name="pocet_hostu[{$skupina->id_skupiny}]" value="{$skupina->getPocet()}"
                                n:attr="disabled: !$event->getStatus()->isEditovatelny()">
                     </div>
                     </div>
                  </div>
               {/foreach}
            </div>
         </div>
      </div>
   </div>
   <div class="row d-flex justify-content-center m-4">
      <div class="col-md-4 col-sm-6 col-12">
         <button name="btnSaveEventChanges" type="submit"
                 class="btn btn-primary w-100 shaddow-hover"><i class="bi bi-save me-1"></i>{_'Uložit změny'}</button>
      </div>
   </div>
</form>

<script>
   $(function() {
      const form = $('form#editEventModal');

      {if $event->canChangeDatum()}
      const inputDateEnd = form.find('input[name=datumEnd]');
      form.validate({
         rules: {
            'datumStart': {
               date: false,
               dateBefore: inputDateEnd[0],
            },
            'datumEnd': {
               date: false,
            },
            'timeStart': {
               date: false
            },
            'timeEnd': {
               date: false
            },
         }
      });
      {/if}

      const checkerContainer = form.find('div.js-termin-checker-block');

      window.qvampSystem.availabilityChecker.initContainer({
         container: checkerContainer[0],
         dateStart: $('input#eventDetailDateStart')[0],
         dateEnd: $('input#eventDetailDateEnd')[0],
         entity: {
            entityID: Number({$event->getID()}),
            mode: String({$checkerMode->value}),
         },
      });

      form.on('change', 'input.js-pocet-skupiny', function() {
         const arrInputs = $(this).closest('form').find('input.js-pocet-skupiny');
         let pocet = 0;

         arrInputs.each((i, el) => {
            pocet = pocet + Number(el.value);
         });

         $('input.js-pocet-hostu').val(pocet);
      });
   });
</script>