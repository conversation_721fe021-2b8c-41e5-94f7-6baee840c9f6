{varType app\system\model\event\VenueEventRow $event}
{varType app\system\model\organizace\mistnosti\MistnostRow[] $mistnosti}

<div class="row">
   <div class="col-auto">
      <div class="form-group">
         <label for="select-mistnosti-event-edit">{_'Prostor'}/{_'Místnost'}</label>
         <p class="d-inline-block ml-1 mb-1"></p>
         <div class="input-group" data-target-input="nearest">
            <select id="select-mistnosti-event-edit" class="form-select-sm js-form-change" multiple="multiple"
                    name="mistnost[]"
                    n:attr="disabled: $event->is_vsechny_mistnosti === 1,
                    required: $event->is_vsechny_mistnosti === 0">
               <option></option>
               {foreach $mistnosti as $mistnost}
                  <option value="{$mistnost->id_mistnost}"
                          n:attr="selected: array_key_exists($mistnost->id_mistnost, $event->getMistnosti())">
                     {$mistnost->nazev}
                  </option>
               {/foreach}
            </select>
         </div>
      </div>
   </div>

   <div class="col-2 d-flex align-items-end">
      <div class="form-check form-switch form-group">
         <input type="checkbox" name="mistnosti_all" id="mistnosti-event-edit"
                class="form-check-input js-form-change" n:attr="checked: $event->is_vsechny_mistnosti === 1">
         <label for="mistnosti-event-edit" class="form-check-label">{_'Celý prostor'}</label>
      </div>
   </div>
</div>

<script>
   $(function(){
      let mySelect = $('#select-mistnosti-event-edit');
      let myModal = mySelect.closest('.js-beatmodal');
      let options = {
         placeholder: "{_'Vyber místnost'}",
         closeOnSelect: true,
         delay: '350ms',
         width: '500px',
      };

      if(myModal.length > 0)
         options['dropdownParent'] = myModal;

      mySelect.select2(options);

      $('#mistnosti-event-edit').change(function() {
         $('#select-mistnosti-event-edit')
            .prop('disabled', this.checked)
            .prop('required', !this.checked);
      });
   })
</script>