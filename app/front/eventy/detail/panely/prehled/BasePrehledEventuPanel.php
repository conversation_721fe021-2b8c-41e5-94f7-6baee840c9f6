<?php namespace app\front\eventy\detail\panely\prehled;

use app\front\controllers\eventy\PrehledEventuController;
use app\front\eventy\detail\modaly\aktivity\EditAktivityModal;
use app\front\eventy\detail\modaly\aktivity\NovaAktivitaModal;
use app\front\eventy\detail\modaly\tisk\VariabilniTiskovaSadaModal;
use app\front\eventy\detail\panely\IEventPanel;
use app\front\eventy\detail\panely\prehled\modal\EditEventModal;
use app\front\eventy\detail\panely\uzavreni\UzavreniEventuPanel;
use app\front\organizace\model\organizace\Organizace;
use app\front\zakaznici\modal\info\ZakaznikInfoModal;
use app\system\application\ApplicationVersion;
use app\system\application\FrontApplicationEnvironment;
use app\system\chat\ChatUser;
use app\system\chat\component\ChatWindowComponent;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\event\BaseEventRow;
use app\system\model\event\data\aktivity\EventAktivityRow;
use app\system\model\event\data\aktivity\EventAktivityStavy;
use app\system\model\event\EventAktivity;
use app\system\model\event\EventStavyEnum;
use app\system\model\event\eventy\ChangeEventStavEvent;
use app\system\model\event\eventy\SaveEventDetailPostEvent;
use app\system\model\event\personal\EventPrirazenyPersonal;
use app\system\model\formular\enumy\FormulareTypyEnum;
use app\system\model\formular\FormulareModel;
use app\system\model\organizace\akce\OrganizaceAkce;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\organizace\meny\OrganizaceMeny;
use app\system\model\organizace\personal\prirazeni\component\PersonalAssignmentComponent;
use app\system\model\organizace\personal\prirazeni\component\PersonalPrirazeniSaveRules;
use app\system\model\organizace\personal\prirazeni\PersonalAssignmentType;
use app\system\model\organizace\zakaznici\OrganizaceZakaznici;
use app\system\model\organizace\zakaznici\OrganizaceZakaznikRow;
use app\system\modul\panels\Panel;
use Dibi\DateTime;
use Latte\Runtime\Html;

/** Created by Kryštof Czyź. Date: 02.08.2023 */
abstract class BasePrehledEventuPanel extends Panel
   implements IEventPanel
{

   function getMenuName() :string {
      return 'Přehled';
   }

   public function preparePanel(Templater $templater) :void {
      $this->data['_panel'] = $this;

      $this->appendEvent();
      $this->appendMistoInfo();
      $this->appendNabidkaPoptavka();
      $this->appendNejblizsiAktivity();
      $this->appendNedokonceneUkoly();
      $this->appendOdhad();
      $this->appendChat();
      $this->appendForms();
      $this->appendUzavritPanel();
      $this->appendTiskSada();

      $this->data['isOwnerOrganization'] = FrontApplicationEnvironment::get()->isMajitel;
      $this->data['canViewPrices'] = FrontApplicationEnvironment::get()->canViewPrices();

      $templater->addData($this->data + $this->appendTemplateData());
   }

   public BaseEventRow $event;
   public OrganizaceZakaznikRow $zakaznik;
   public array $data = [];

   protected function appendTemplateData() :array { return []; }

   protected function preparePostListeners() :void {
      $this->isset('btnSaveEventChanges', function($post) {
         (new SaveEventDetailPostEvent($this->getEvent(), $post))->call();
         FlashMessages::setSuccess('Změny uloženy');
      });

      $this->isset('btnZamitnoutEvent', function($post) {
         (new ChangeEventStavEvent($event = $this->getEvent(), EventStavyEnum::ZRUSENY))->call();
         FlashMessages::setInfo('Event $1 je zamítnutý', $event->nazev ?: $event->getID());
      });

      $this->isset('btnSmazatEvent', function($post) {
         $event = $this->getEvent();

         if(
            !$event->getStatus()->isNovy()
            && (!FrontApplicationEnvironment::get()->isMajitel || $event->getIdOrganizace() !== FrontApplicationEnvironment::get()->id_organizace)
         ){
            FlashMessages::setError('Event $1 nelze smazat', $event->nazev ?: $event->getID());
            return;
         }

         $event->delete();
         FlashMessages::setInfo('Event $1 byl smazaný', $event->nazev ?: $event->getID());
      }, PrehledEventuController::getUrl());

      $this->isset('btnPotvrditEvent', function($post) {
         (new ChangeEventStavEvent($event = $this->getEvent(), EventStavyEnum::POTVRZENY))
            ->setSendZakaznikEmail(!(trim($post['btnPotvrditEvent']) === 'false'))
            ->call();

         FlashMessages::setInfo('Event $1 byl potvrzený', $event->nazev ?: $event->getID());
      });

      $this->isset('btnNastavitDatum', function($post) {
         $event = $this->getEvent();
         if(isset($post['datum_konani']) && $event->getStatus()->isEditovatelny()) {
            $event->date_start = new DateTime(sprintf('%s %s', $post['datum_konani'], $post['cas_konani']));
            $event->save();
            FlashMessages::setSuccess('Datum bylo nastaveno');
         }
      });
   }

   protected function appendEvent() :void {
      $this->event = $this->getEvent();
      $this->data['event'] = $this->event;
      $this->data['skupiny'] = $this->event->getHosteContainer()->getVisiableSkupinyHostu();
      $this->data['upravaModalBtn'] = EditEventModal::init()->btnToggleAttributes();

      $this->data['assignmentIcons'] = PersonalAssignmentComponent::getForEntity(
         $this->event,
         FrontApplicationEnvironment::get()->personal,
      )->setSaveRules(
         PersonalPrirazeniSaveRules::MANUAL_SAVE,
         $this->event->getIdOrganizace(),
         $this->event->getID(),
         PersonalAssignmentType::EVENT
      )->render();

      $this->data['vydelek'] = EventPrirazenyPersonal::getByIdEvent(
         $this->event, FrontApplicationEnvironment::get()->versionType
      )->getNakladOsoby(FrontApplicationEnvironment::get()->personal->id_personal);
   }

   protected function appendMistoInfo() :void {
      $this->zakaznik = OrganizaceZakaznici::get($this->event->id_zakaznik);
      $this->data['zakaznik'] = $this->zakaznik;
      $this->data['zakaznikModalAttrs'] = ZakaznikInfoModal::getShowAttributes($this->zakaznik->id);
      $this->data['eventType'] = OrganizaceAkce::get($this->event->id_event_type);
      $this->data['misto'] = Organizace::getMisto($this->event->getIdOrganizace());

      $this->data['jazyky'] = OrganizaceJazyky::getAll($this->event->getIdOrganizace());
      $this->data['meny'] = OrganizaceMeny::getAll($this->event->getIdOrganizace());
   }

   protected function appendNabidkaPoptavka() :void {
      if($this->event->getNabidka()) {
         $this->data['nabidka'] = $this->event->getNabidka();
         $this->data['poptavka'] = $this->event->getNabidka()->getPoptavka();
      }
   }

   protected function appendNejblizsiAktivity() :void {
      /** @var ?EventAktivityRow $aktivita */
      $this->data['nejblizsiAktivita'] = EventAktivity::find(FrontApplicationEnvironment::get()->versionType)
         ->where('id_mista = %i AND id_event = %i AND stav != %i',
            $this->event->getIdOrganizace(), $this->event->getID(), EventAktivityStavy::HOTOVO->value)
         ->orderBy('termin')
         ->fetch();

      $this->data['novyUkolBtnAttr'] = NovaAktivitaModal::init()->getBtnAttributes();
   }

   protected function appendNedokonceneUkoly() :void {
      /** @var EventAktivityRow[] $nedokoncene */
      $nedokoncene = EventAktivity::find(ApplicationVersion::getFromEvent($this->event))
         ->where('id_mista = %i AND id_event = %i AND stav != %i',
            $this->event->getIdOrganizace(), $this->event->getID(), EventAktivityStavy::HOTOVO->value)
         ->orderBy('termin')
         ->offset(1)
         ->limit(4)
         ->fetchAll();

      $this->data['nedokonceneAktivity'] = $nedokoncene;
   }

   public function getEditAktivityModalAttr(int $aktivitaID) :array {
      return EditAktivityModal::getShowAttributes($aktivitaID);
   }

   protected function appendOdhad() {
      if(!$this->event->getNabidka() && $this->event->odhad_cena > 0)
         $this->data['odhadCena'] = $this->event->getOdhad();
   }

   private function appendChat() {
      $this->data['chatComponent'] = new Html(ChatWindowComponent::getComponent()
         ->setEntity($this->event)
         ->setZobrazuje(new ChatUser(FrontApplicationEnvironment::get()->personal)));
   }

   private function appendForms() {
      $this->data['formsNedokoncene'] = FormulareModel::getAllByEntity(
         FormulareTypyEnum::getTypeByEvent($this->getEvent())->value, $this->getEvent()->getID());
   }

   private function appendUzavritPanel() {
      $this->data['panelUzavreniId'] = UzavreniEventuPanel::init()->getHtmlId();
   }

   protected function appendTiskSada() {
      $this->data['tiskSadaModalAttr'] = VariabilniTiskovaSadaModal::init()->btnToggleAttributes();
   }
}