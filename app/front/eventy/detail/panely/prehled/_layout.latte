{varType app\front\eventy\detail\panely\prehled\BasePrehledEventuPanel $_panel}

{varType app\system\model\event\BaseEventRow $event}
{varType app\system\model\organizace\zakaznici\OrganizaceZakaznikRow $zakaznik}
{varType app\system\model\organizace\akce\OrganizaceAkceRow $eventType}
{varType app\system\model\organizace\cenove\HosteSkupina[] $skupiny}
{varType array $novyUkolBtnAttr}
{varType ?app\system\model\event\data\aktivity\EventAktivityRow $nejblizsiAktivita}
{varType array $nedokonceneAktivity}
{varType array $zakaznikModalAttrs}
{varType array $upravaModalBtn}
{varType array $tiskSadaModalAttr}
{varType Latte\Runtime\Html $chatComponent}
{varType string $panelUzavreniId}
{varType bool $isOwnerOrganization}
{varType bool $canViewPrices}
{varType Latte\Runtime\Html $assignmentIcons}

{varType app\system\model\organizace\jazyk\OrganizaceJazykRow[] $jazyky}
{varType app\system\model\organizace\meny\OrganizaceMenaRow[] $meny}
{varType Money\Money $vydelek}

{varType app\system\model\formular\data\FormularData[] $formsNedokoncene}

<div class="container mb-7 mb-lg-4">
   <form method="post">
      <div class="row gap-3">
         <div class="col-lg-9 col-12">
            <div class="row card bg-secondary mb-0 p-3">
               <div class="row d-flex align-content-center mt-2">
                  <h1 class="h1 text-white col-md-auto col-10">{$event->nazev ?: $event->getDefaultNazev()}</h1>
                  <div class="col d-flex justify-content-end my-md-0 my-2">
                     <a class="lead text-white" href="#" n:attr="$upravaModalBtn">
                        <i class="bi bi-pencil-square hover-icon-arrow"></i></a>
                  </div>
               </div>
               <hr>
               <div class="row">
                  <div class="col-12 col-md-auto d-flex bg-white-30 px-2 py-1 radius-card">
                     <div class="col-md-auto col text-center">
                        <span class="text-white"><i class="bi bi-calendar2-range me-1"></i>{$event->date_start|date:'j.n.Y'}<br class="d-flex d-md-none"><i class="bi bi-clock-history ms-2 me-1"></i>{$event->date_start|date:'H:i'}</span>
                     </div>
                     <div class="col-auto text-center">
                        <i class="bi bi-arrow-right text-white mx-2"></i>
                     </div>
                     <div class="col-md-auto col text-center">
                        <span class="text-white"><i class="bi bi-calendar2-range me-1"></i>{$event->date_end|date:'j.n.Y'}<br class="d-flex d-md-none"><i class="bi bi-clock-history ms-2 me-1"></i>{$event->date_end|date:'H:i'}</span>
                     </div>
                  </div>
                  <div class="col-md-auto col-6 align-content-center mt-2 mt-md-0">
                     <span class="text-white"><i class="bi bi-pin-map-fill me-1"></i>{$event->getLocationName()}</span>
                  </div>
                  <div class="col-md-auto col-6 align-content-center mt-2 mt-md-0">
                     <span class="text-white"><i class="bi bi-people me-1"></i>{$event->getPocetHostu()}
                        <a data-bs-toggle="collapse"
                           href="#collapseGuest" role="button"
                           aria-expanded="false"
                           aria-controls="collapseGuest">
                           <i class="text-white align-middle ms-1 bi bi-chevron-double-down"></i>
                        </a>
                     </span>
                  </div>
                  {if count($jazyky) > 1}
                     <div class="col-md-auto col-6 align-content-center mt-2 mt-md-0">
                        <span class="text-white"><i class="bi bi-translate me-1"></i>{$event->getJazyk()->getTitle()}</span>
                     </div>
                  {/if}

                  {if count($meny) > 1}
                     <div class="col-md-auto col-6 align-content-center mt-2 mt-md-0">
                        <span class="text-white"><i class="bi bi-currency-exchange me-1"></i>{$event->getMena()->name}</span>
                     </div>
                  {/if}
               </div>
            </div>

            <div class="collapse" id="collapseGuest">
               <div class="row mt-2">
                  <p class="col-md-auto">{_'Jednotlivé skupiny hostů'}</p>
                  <div class="row d-flex justify-content-center gap-2">
                     {foreach $skupiny as $skupina}
                        <div n:class="'card px-4 py-md-3 py-2 col-4 col-md d-inline form-group', $skupina->isNepocitatelna ? 'border border-danger'">
                           <label n:attr="title: strlen($skupina->nazev_skup) > 18? $skupina->nazev_skup"
                                  n:class="$skupina->isNepocitatelna ? 'is-invalid text-danger'">
                              <span n:if="$skupina->isNepocitatelna">
                                          <i class="bi bi-question-circle ms-1 text-danger"
                                             data-bs-toggle="tooltip"
                                             data-bs-title="{_'Kategorie je nastavena jako nepočítatelná skupina hostů v nastavení'}">
                                          </i>&nbsp;
                                       </span>
                              <span>{$skupina->nazev_skup|truncate: 18}</span>:
                           </label>
                           <p>{$skupina->getPocet()}</p>
                        </div>
                     {/foreach}
                  </div>
               </div>
            </div>

            <div class="row mt-3">
               <div class="col-md col-xl col-12 p-3">
                  <div class="row align-content-center">
                     <h3 class="col"><i class="align-middle px-2 py-1 me-2 bi bi-list-task bg-primary radius-icon icon-shadow text-white"></i>{_'Aktivity'}</h3>
                     <div class="col-auto align-items-end pe-0">
                        <a class="btn btn-sm btn-outline-secondary shaddow-hover" href="#" n:attr="$novyUkolBtnAttr"><i
                                   class="bi bi-plus-lg me-1"></i>{_'Přidat aktivitu'}</a>
                     </div>
                  </div>

                  <div class="mt-3" n:if="$nejblizsiAktivita">
                     {if $nejblizsiAktivita}
                        {include aktivita, aktivita => $nejblizsiAktivita}
                     {else}
                        <p>{_'Event nemá žádnou aktivitu'}</p>
                     {/if}
                  </div>

                  <div n:if="!empty($nedokonceneAktivity)" class="mt-2">
                     {foreach $nedokonceneAktivity as $aktivita}
                        {include aktivita, aktivita => $aktivita}
                     {/foreach}
                  </div>
               </div>

               <div class="vr p-0 mx-4 d-none d-md-block"></div>

               <div class="col-md col-xl col-12 p-3">
                  <div class="row align-content-center">
                     <h3><i class="align-middle px-2 py-1 me-2 bi bi-ui-checks-grid bg-primary radius-icon icon-shadow text-white"></i>{_'Nedokončené formuláře'}</h3>
                  </div>
                  <div class="mt-md-3 mt-2" n:if="$formsNedokoncene">
                     {foreach $formsNedokoncene as $formular}
                        <div class="card py-3 ps-3 row mb-2 shaddow-hover">
                           <div class="row p-0">
                              <div class="col">
                                 <div class="col-12 d-flex align-items-center">
                                    <a href="{$formular->getDetailUrl()}" class="pe-0 text-truncate d-block text-secondary text-strong">{$formular->title}</a>
                                 </div>
                                 <div class="col-12 mt-2 gap-1">
                                    <span class="btn btn-sm btn-outline-info w-auto text-secondary cursor-text"><i class="bi bi-calendar2-check me-1"></i>{$formular->datum_pred_akci|date: 'j.n.Y'}</span>
                                    <span class="btn btn-sm btn-outline-info w-auto text-secondary cursor-text"><i class="bi bi-input-cursor-text me-1"></i>{$formular->getStav()->getTranslatedTitle()}</span>
                                 </div>
                              </div>
                              <div class="col-auto p-0 text-end">
                                 <a href="{$formular->getDetailUrl()}" class="text-secondary display-6"><i
                                            class="i bi-arrow-right-circle hover-icon-arrow"></i></a>
                              </div>
                           </div>
                        </div>
                     {/foreach}
                  </div>
               </div>
            </div>

         </div>
         <div class="col">
            <div class="row">
               {$assignmentIcons}
            </div>

            <div class="row card p-2 bg-secondary form-group align-content-between mb-2">
               <span class="text-white"><i class="bi bi-person-circle me-2"></i><strong>{$zakaznik->full_name}</strong>
                  <a href="#" n:attr="$zakaznikModalAttrs" class="ms-2 text-white text-end lead"><i
                             class="bi bi-arrow-up-right-circle hover-icon-arrow"></i></a></span>
            </div>

            <div class="row d-flex align-content-center justify-content-center my-3">
               <div class="col-auto align-content-center">
                  <i n:class="'bi bi-sliders', $event->isPred() ? 'text-primary' : 'text-secondary'" data-bs-toggle="tooltip" data-bs-title="{_'V této fázi můžete skvěle připravit event a to díky nástrojům jako Harmonogram, Aktivity, Formuláře či Inventář. Po celou dobu můžete také průběžné upravovat Balíčky a položky přímo zde v eventu. Kalkulace i poptávka jsou již zamčeny.'}"></i>
               </div>
               <div class="col-auto align-content-center px-0">
                  <i class="bi bi-arrow-right lead text-gray-light"></i>
               </div>
               <div class="col-auto align-content-center">
                  <i n:class="'bi bi-calendar2-event', $event->isDnes() ? 'text-primary' : 'text-secondary'" data-bs-toggle="tooltip" data-bs-title="{_'Tento event právě probíhá, ať se vše podaří :-)'}"></i>
               </div>
               <div class="col-auto align-content-center px-0">
                  <i class="bi bi-arrow-right lead text-gray-light"></i>
               </div>
               <div class="col-auto align-content-center">
                  <i n:class="'bi bi-flag', $event->isProbehly() ? 'text-primary' : 'text-secondary'" data-bs-toggle="tooltip" data-bs-title="{_'Je po eventu, bude potřeba jej uzavřít. K uzavření využíjte záložku Uzavřít. Doporučujeme zejmena doplnit Balíčky a položky, následně zkontrolovat všechny platby a až poté event uzavřít.'}"></i>
               </div>
            </div>

            <div n:if="$canViewPrices || $event->isUzavreny()" class="row px-2">
               <div class="form-group">
                  {if $event->isUzavreny()}
                     <p>{_'Event je uzavřený'}</p>
                  {elseif $canViewPrices}
                     {if $event->isProbehly() || $event->getStatus()->isZamitnuty()}
                        <div class="row">
                           <small class="badge bg-secondary rounded-pill text-white">{_'Event je potřeba uzavřít'}</small>
                        </div>
                     {/if}
                     {if $event->getStatus()->isNovy()}
                        <div class="row mt-1">
                           <small >{_'Event máte teď nastavený jako "Nový", takže ho zákazník zatím nevidí ve svém portálu a také se nepočítá do statistik. Jakmile ho potvrdíte, zákazník ho uvidí a vám se začne započítávat do statistik.'}</small>
                        </div>
                     {/if}

                     <div class="row mt-lg-3 gap-2 gap-lg-4">
                        <div class="col-12 d-flex justify-content-center">
                           <button type="button" class="btn btn-lg btn-outline-secondary w-auto shaddow-hover" n:attr="$tiskSadaModalAttr">
                              <i class="bi bi-printer me-2"></i>{_'Tisk'}/Export
                           </button>
                        </div>

                     </div>
                  {/if}
               </div>
            </div>

            <div class="row border border-gray radius-card p-3 my-3">
               <h3 class="d-inline-flex justify-content-between lead mb-0">
                  {_'Další informace'}
                  <a data-bs-toggle="collapse" href="#detailInfo" role="button" aria-expanded="false" aria-controls="detailInfo">
                     <i class="bi bi-chevron-double-down"></i>
                  </a>
               </h3>
               <div class="collapse multi-collapse" id="detailInfo">
                  <div class="row mt-2">
                     {if $event->getStatus()->canViewKlient()}
                        {if $event->client_first_visit}
                           <div class="row mt-3">
                              <div class="col-md form-group">
                                 <label>Poprvé zobrazeno</label>
                                 <p>{$event->client_first_visit|date:'j.n.Y H:i'}</p>
                              </div>
                              <div n:if="$event->client_first_visit->getTimestamp() !== $event->client_last_visit->getTimestamp()" class="col-md form-group">
                                 <label>Naposledy zobrazeno</label>
                                 <p>{$event->client_last_visit|date:'j.n.Y H:i'}</p>
                              </div>
                           </div>
                        {else}
                           <div class="row mt-3 form-group">
                              <label>Poprvé zobrazeno</label>
                              <p>Zatím nezobrazeno</p>
                           </div>
                        {/if}
                     {/if}
                     <div class="row form-group" n:if="$vydelek">
                        <label>{_'Můj výdělek'}:</label>
                        <p>{$vydelek|price}</p>
                     </div>

                     <div n:if="$event->getPriznak()" class="row form-group">
                        <label>{_'Příznak'}:</label>
                        <p>{$event->getPriznak()->nazev}</p>
                     </div>

                     <div class="row form-group">
                        <label>{_'Typ'}:</label>
                        <p>{$eventType->nazev_eventu}</p>
                     </div>
                     <div n:if="$event->getZdroj()" class="row form-group">
                        <label>{_'Zdroj'}</label>
                        <p>{$event->getZdroj()->nazev}</p>
                     </div>
                     <div class="row form-group">
                        <label>{_'Stav'}</label>
                        <p>{$event->getStatus()->getTranslatedTitle()}</p>
                     </div>

                     <div n:if="$event->getPredchoziEntita()" class="row form-group">
                        <label>{$event->getPredchoziEntita()->getEntityType()->getTranslatedTitle()}</label>
                        <p>
                           <a href="{$event->getPredchoziEntita()->getEntity()->getDetailUrl()}">
                              {isset($event->getPredchoziEntita()->getEntity()->nazev) ? $event->getPredchoziEntita()->getEntity()->nazev
                              : sprintf('ID: %d', $event->getPredchoziEntita()->getEntity()->getID())}
                           </a>
                        </p>
                     </div>
                  </div>
                  <div n:if="$canViewPrices" class="row mt-2">
                     <h3 class="lead">{_'Finance'}</h3>
                     <div class="row form-group">
                        <label>{_'Aktuální cena'}:</label>
                        <p class="lead">{$event->getSuma()|price}</p>
                     </div>
                     <div class="row form-group" n:if="$event->localtax">
                        <label>{_'Poplatek z pobytu'}:</label>
                        <p class="lead">{$event->getLocaltaxSuma()|price}</p>
                     </div>
                     <div class="row form-group" n:if="$event->odhad_cena > 0">
                        <label>{_'Původní cena'}:</label>
                        <p>{$event->getOdhad()|price}</p>
                     </div>
                     <div class="row form-group">
                        <label>{_'Zaplaceno'}:</label>
                        <p>{$event->getFinance()->zaplaceno|price}</p>
                     </div>
                     <div class="row form-group">
                        <label>{_'Odhad zisku'}</label>
                        <p>{$event->getFinance()->ziskOdhad|price}</p>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>

      {if $canViewPrices}
         <div class="fixed-bottom pe-none d-flex justify-content-center">
            <div class="d-inline-flex flex-column flex-lg-row flex-wrap align-items-center bg-white border border-light-gray radius-card shaddow-light shaddow-hover my-2 p-2 pe-auto">

               {if $event->getStatus()->isNovy() || $event->getStatus()->isZamitnuty()}
                  <div class="p-1">
                     <button type="submit"
                             name="btnSmazatEvent"
                             class="btn btn-outline-danger text-nowrap shaddow-hover mx-1"
                             data-confirm="{_'Opravdu chcete smazat event $1?', $event->nazev ?: $event->getID()}">
                        <i class="bi bi-trash me-2"></i>
                        {_'Smazat'}
                     </button>
                  </div>
               {/if}

               {if !$event->getStatus()->isZamitnuty()}
                  <div class="p-1">
                     <button type="submit"
                             name="btnZamitnoutEvent"
                             class="btn btn-outline-secondary text-nowrap shaddow-hover mx-1"
                             data-confirm="{_'Opravdu chcete zamítnout event $1?', $event->nazev ?: $event->getID()}">
                        <i class="bi bi-x-circle me-2"></i>
                        {_'Zrušit'}
                     </button>
                  </div>
               {/if}

               {if $event->canUzavrit()}
                  <div class="p-1">
                     <button type="button"
                             class="js-btn-uzavrit btn btn-primary text-nowrap shaddow-hover mx-1"
                             n:attr="disabled: $event->getStatus()->isNovy()">
                        <i class="bi bi-check2-square me-2"></i>
                        {_'Uzavřít'}
                     </button>
                  </div>
               {/if}

               <div class="p-1">
                  <div class="btn-group mx-1" role="group">
                     {if $event->getStatus()->isNovy()}
                        <button type="submit"
                                name="btnPotvrditEvent"
                                class="btn btn-outline-primary text-nowrap shaddow-hover"
                                data-confirm="{_'Opravdu chcete event potvrdit bez upozornění zákazníka?'}"
                                value="false">
                           {_'Potvrdit bez emailu'}
                           <i class="bi bi-info-circle ms-1"
                              data-bs-toggle="tooltip"
                              data-bs-title="{_'Qvamp neodešle zákazníkovi potvrzovací email. Zákazník bude mít možnost vidět tento event přímo ve svém Klientském portále.'}">
                           </i>
                        </button>

                        <button type="submit"
                                name="btnPotvrditEvent"
                                class="btn btn-primary text-nowrap shaddow-hover"
                                data-confirm="{_'Opravdu chcete event potvrdit? Zákazníkovi se odešle email s odkazem do klientského portálu'}"
                                value="true">
                           <i class="bi bi-calendar2-check me-2"></i>
                           {_'Potvrdit event'}
                           <i class="bi bi-info-circle ms-1"
                              data-bs-toggle="tooltip"
                              data-bs-title="{_'Když potvrdíte event, Qvamp automaticky odešle zákazníkovi potvrzovací email. Zákazník zároveň bude mít možnost vidět tento event přímo ve svém Klientském portále.'}">
                           </i>
                        </button>
                     {/if}
                  </div>
               </div>
            </div>
         </div>
      {/if}
   </form>

   <div class="row mt-4 mb-5 mb-md-3 pb-3">
      <h4 class="lead text-center mb-3">{_'Komunikace'}</h4>
      {$chatComponent}
   </div>
</div>

<script>
   $(function() {
      const body = $('body');

      body.on('click', 'button.js-btn-uzavrit', function() {
         body.find('button#' + {$panelUzavreniId} + '-tab').click();
      })
   })
</script>

{define aktivita}
   {varType app\system\model\event\data\aktivity\EventAktivityRow $aktivita}
   <div class="card py-3 px-2 row mb-2 shaddow-hover">
      <div class="row mx-0 p-0">
         <div class="col">
            <strong class="text-truncate d-block hover-icon-arrow cursor-pointer" n:attr="$_panel->getEditAktivityModalAttr($aktivita->id_aktivita)">{$aktivita->nazev}</strong>
            <div class="col-12 mt-2 gap-1">
               <small class="btn btn-sm btn-outline-info w-auto text-secondary cursor-text">
                  <i class="bi bi-calendar2-check me-1"></i>
                  {if $aktivita->dtStart}
                     {$aktivita->dtStart|date: 'j.n.Y H:i'}&nbsp;-&nbsp;{$aktivita->dtEnd|date: 'H:i'}
                  {else}
                     {$aktivita->termin|date: 'j.n.Y'}
                  {/if}
               </small>

               <small class="btn btn-sm btn-outline-info w-auto text-secondary cursor-text">
                  <i class="me-1 bi bi-people-fill"></i>{$aktivita->getJmenoPrirazeny()}
               </small>

               <small class="btn btn-sm btn-outline-info w-auto text-secondary cursor-text">
                  {$aktivita->getTypAktivity()->getTranslatedTitle()}
               </small>
            </div>
         </div>
         <div class="col-auto p-0 text-end">
            <a href="#" class="text-secondary display-6" n:attr="$_panel->getEditAktivityModalAttr($aktivita->id_aktivita)">
               <i class="bi bi-arrow-up-right-circle hover-icon-arrow"></i>
            </a>
         </div>
      </div>
   </div>
{/define}