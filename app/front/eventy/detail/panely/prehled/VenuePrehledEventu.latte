{varType app\system\model\event\VenueEventRow $event}
{varType array $mistnosti}

{extends '_layout.latte'}

{block mistnostiBlock}
   <div class="col-sm-auto d-sm-inline gap-2">
      <label for="mistnost">{_'Prostor'}/{_'Místnost'}</label>
      <select class="form-select js-event-save" id="mistnost" name="mistnost" required n:attr="disabled: !$event->getStatus()->isEditovatelny()">
         <option value="" n:if="!$event->id_mistnost">{_'Nevybraná místnost'}</option>
         {foreach $mistnosti as $mistnost}
            <option value="{$mistnost->id_mistnost}" {if $mistnost->id_mistnost === $event->id_mistnost}selected{/if}>
               {$mistnost->nazev}
            </option>
         {/foreach}
         <option value="all" {if $event->isRezervaceVsechMistnosti()}selected{/if}>{_'Všechny prostory'}</option>
      </select>
   </div>
{/block}