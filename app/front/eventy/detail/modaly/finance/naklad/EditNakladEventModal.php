<?php namespace app\front\eventy\detail\modaly\finance\naklad;

use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\model\naklady\EventNaklady;
use app\system\model\organizace\meny\Meny;
use app\system\modul\modal\AjaxModal;
use app\system\modul\modal\styles\ModalScaleEnum;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 08.05.2024 */
class EditNakladEventModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Detail nákladu';
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'naklad' => $naklad = EventNaklady::get($_POST['slug'], FrontApplicationEnvironment::get()->versionType),
         'mena' => Meny::from($naklad->id_mena_jazyk),
      ]);
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::SMALL;
}