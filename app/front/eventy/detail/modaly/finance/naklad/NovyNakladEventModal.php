<?php namespace app\front\eventy\detail\modaly\finance\naklad;

use app\system\component\Templater;
use app\system\model\event\BaseEventRow;
use app\system\model\organizace\meny\Meny;
use app\system\modul\modal\Modal;
use app\system\modul\modal\styles\ModalScaleEnum;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 08.05.2024 */
class NovyNakladEventModal extends Modal
{

   public function getTitleName() :string {
      return 'Nový náklad';
   }

   public function setEvent(BaseEventRow $event) :static {
      $this->event = $event;
      return $this;
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'naklad' => null,
         'mena' => Meny::from($this->event->id_mena_jazyk),
      ]);
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::SMALL;
   private BaseEventRow $event;
}