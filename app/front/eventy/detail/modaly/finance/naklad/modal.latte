{varType ?app\system\model\naklady\EventNakladyRow $naklad}
{varType app\system\model\organizace\meny\Meny $mena}

<form method="post">
   <div class="p-3">
      <div class="row mb-3">
         <label for="nakladEditNazev">{_'Název'}<span class="text-danger">&nbsp;*</span></label>
         <input type="text" name="nazev" id="nakladEditNazev" class="form-control" value="{$naklad?->nazev}" maxlength="120" required>
      </div>
      <div class="row mb-3">
         <label for="sumaNaklad">{_'Částka'}<span class="text-danger">&nbsp;*</span></label>
         <div class="input-group px-0">
            <input type="text" name="suma" id="sumaNaklad" value="{$naklad?->suma}" class="form-control js-price-mask input-levy-round" required>
            <div class="input-group-text input-pravy-round" data-target="#sumaNaklad">
               {$mena->getSymbol()}
            </div>
         </div>
      </div>
      <div class="row mb-3">
         <label for="datum_naklad">{_'Datum'}<span class="text-danger">&nbsp;*</span></label>
         <input type="date" name="datum_naklad" id="datum_naklad" class="form-control" value="{$naklad?->datum ?: new Dibi\DateTime()|date: 'Y-m-d'}" required>
      </div>
      <div class="row mb-3">
         <label for="nakladEditPopis">{_'Popis'}</label>
         <textarea name="popis" id="nakladEditPopis" class="form-control form-text">{$naklad?->popis ?: ''}</textarea>
      </div>
      <div class="row mb-3">
         <div n:if="$naklad" class="align-content-between">
            <small n:if="$naklad->created->getTimestamp() !== $naklad->updated->getTimestamp()">{_'Naposledy upraveno'}:&nbsp;{$naklad->updated|date: 'j.n.Y H:i'}</small>
            <small>{_'Vytvořeno'}:&nbsp;{$naklad->created|date: 'j.n.Y H:i'}</small>
         </div>
      </div>
      <div class="row justify-content-center">
         <input n:if="$naklad" type="hidden" name="id_naklad" value="{$naklad->id}">
         <div class="col-auto">
            <button type="submit" name="btnUlozitNaklad" class="btn btn-primary w-auto shaddow-hover">{_'Uložit náklad'}</button>
         </div>
         <div n:if="$naklad" class="col-auto">
            <button type="submit" name="btnSmazatNaklad" class="btn btn-outline-secondary w-auto shaddow-hover" data-confirm="{_'Opravdu chcete smazat tento náklad?'}"><i class="bi bi-x-circle me-1"></i>{_'Smazat'}</button>
         </div>
      </div>
   </div>
</form>