{varType app\system\model\platby\scenare\ScenarPlatbyRow $platba}
{varType app\system\model\event\platby\plan\EventPlanItem[] $platby}
{varType app\system\model\event\BaseEventRow $event}

<form method="post" id="eventNovaUhradaFormModal">
   <div class="p-3">
      <div class="row mb-3">
         <div class="col-md-6 col">
            <label for="datum_platby">{_'Datum platby'}</label>
            <input type="date" name="datum_platby" id="datum_platby" class="form-control"
                   value="{=new Dibi\DateTime()|date: 'Y-m-d'}" required>
         </div>
      </div>

      <div class="row mb-3">
         <div class="col">
            <label for="id_scenare_platby">{_'Platba'}</label>
            <select name="id_scenare_platby" id="id_scenare_platby" class="form-control form-select">
               {foreach $platby as $platba}
                  <option n:if="!$platba->zaplaceno && !$platba->is_zrusena" value={$platba->id_platby}>{$platba->nazev}</option>
               {/foreach}
            </select>
         </div>
         <div class="col">
            <label for="popis">{_'Popis'}</label>
            <input type="text" name="popis" id="popis" class="form-control">
         </div>
      </div>

      <div class="row mb-3">
         <div class="col">
               <label for="suma_zaplacena_platba">{_'Částka'}</label>
               <div class="input-group">
                  <div class="input-group-text input-levy-round" data-target="#suma_zaplacena_platba">
                     <span class="js-negative-icon" style="display: none"><i class="bi bi-dash"></i></span>
                     <span class="js-positive-icon" style="display: none"><i class="bi bi-plus"></i></span>
                  </div>
                  <input type="text" name="suma" id="suma_zaplacena_platba"
                         class="form-control js-price-mask input-pravy-round">
               </div>
            <div class="row mt-2">
               <div class="col-12">
                  <div class="form-check form-switch form-group">
                     <input type="checkbox" name="is_negative" id="is_negative"
                            class="form-check-input js-negative-suma">
                     <label for="is_negative" class="form-check-label">{_'Jedná se o vratku?'}</label>
                  </div>
               </div>
            </div>
         </div>

         <div class="col">
            <label for="zpusob_platby">{_'Způsob platby:'}</label>
            <select name="zpusob_platby" id="zpusob_platby" class="form-control form-select">
               <option value=1>{_'Karta'}</option>
               <option value=2>{_'Převod'}</option>
               <option value=3>{_'Hotovost'}</option>
            </select>
         </div>
      </div>
   </div>
   <div class="row d-flex justify-content-center">
      <input type="hidden" name="id_jazyk" value="{$event->id_jazyk}">
      <input type="hidden" name="id_jazyk_mena" value="{$event->getMena()->value}">
      <input type="hidden" name="id_mista" value="{$event->getIdOrganizace()}">
      <input type="hidden" name="id_event" value="{$event->getID()}">
      <div class="col-sm-3">
         <button type="submit" name="btnVytvoritPlatbu" class="btn btn-primary w-auto shaddow-hover">{_'Přidat úhradu'}</button>
      </div>
   </div>
</form>

<script>
   $(function() {
      const body = $('body');
      const form = body.find('form#eventNovaUhradaFormModal');

      // @TODO obalit do formu ať se neovlivnuje dál
      const negativeIcon = form.find('.js-negative-icon');
      const positiveIcon = form.find('.js-positive-icon');
      const negativeCheck = form.find('input.js-negative-suma');
      let scenar = form.find('#id_scenare_platby');
      let suma = form.find('#suma_zaplacena_platba');
      let popis = form.find('#popis');
      let id_scenare = scenar.val();

      if(id_scenare)
         findScenar(id_scenare);

      form.on('change', '#id_scenare_platby', function() {
         let id_scenare = scenar.val();
         findScenar(id_scenare);
      }).on('change', 'input.js-negative-suma', function() {
         const checked = $(this).is(':checked');
         alert("{_'Nelze změnit, pokud chcete platbu vrátit, založte novou'}");
         $(this).prop('checked', !checked);
      });

      function showData(response) {
         popis.val(response['scenar']['nazev']);
         popis.fadeIn(500);

         if(response['is_negative']){
            negativeIcon.show();
            positiveIcon.hide();
            negativeCheck.prop('checked', true);
         }else{
            positiveIcon.show();
            negativeIcon.hide();
            negativeCheck.prop('checked', false);
         }

         const value = (Number(response['dopocet']) / 100).toFixed(2).replace('.', ',');
         suma.val(value);
      }

      function findScenar(id_scenare) {
         ajaxHandler.post({$baseUrl} +'/findscenar', { id_scenare: id_scenare }, function(response) {
            showData(response);
         });
      }
   });
</script>