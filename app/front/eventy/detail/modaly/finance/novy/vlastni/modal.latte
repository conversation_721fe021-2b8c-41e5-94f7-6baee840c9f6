{varType app\system\model\event\BaseEventRow $event}

<form method="post" id="eventVlastniPlatbaModal">
   <div>
      <div class="row">
         <div class="col-12">
            <label for="popis">{_'Popis platby'}</label>
            <input type="text" name="nazev_platby" id="nazev_platby" class="form-control" required maxlength="100">
         </div>
      </div>

      <div class="row gap-md-2 gap-3 my-3">
         <div class="col-12 col-md-5">
            <label for="termin_platby">{_'Termín platby'}</label>
            <input type="date" name="termin_platby" id="termin_platby" class="form-control"
                   value="{=new Dibi\DateTime()|date: 'Y-m-d'}" required>
         </div>
         <div class="col-12 col-md">
            <label for="suma_vlastni_platba">{_'Částka'}</label>
            <div class="input-group">
               <div class="input-group-text" data-target="#suma_vlastni_platba">
                  <span class="js-negative-icon" style="display: none"><i class="bi bi-dash"></i></span>
                  <span class="js-positive-icon"><i class="bi bi-plus"></i></span>
               </div>
               <input type="text" name="suma_vlastni_platba" id="suma_vlastni_platba" class="form-control js-price-mask"
                      required>
               <div class="input-group-text " data-target="#suma_vlastni_platba">
                  {$event->getMena()->getSymbol()}
               </div>
            </div>
         </div>
      </div>
      <div class="row mx-2 gap-md-2 gap-3 my-3">
         <div class="col-12">
            <div class="row form-check form-switch form-group">
               <input type="checkbox" name="is_uhrazena" class="form-check-input" id="is_uhrazena"
                      data-bs-target="#zpusob_platby_block" data-bs-toggle="collapse">
               <label for="is_uhrazena" class="form-check-label">{_'Byla platba zaplacena?'}</label>
            </div>
            <div class="row form-check form-switch form-group">
               <input type="checkbox" name="is_negative" id="is_negative" class="form-check-input">
               <label for="is_negative" class="form-check-label">{_'Jedná se o vratku?'}</label>
            </div>
         </div>
      </div>
      <div class="row my-3 collapse" id="zpusob_platby_block">
         <h3>{_'Informace o platbě'}</h3>
         <hr>
         <div class="col-md col-12">
            <label for="datum_platby">{_'Datum zaplacení'}</label>
            <input type="date" name="datum_platby" id="datum_platby" class="form-control"
                   value="{=new Dibi\DateTime()|date: 'Y-m-d'}">
         </div>

         <div class="col-md col-12">
            <label for="zpusob_platby">{_'Způsob platby:'}</label>
            <select name="zpusob_platby" id="zpusob_platby" class="form-select">
               <option value=1>{_'Karta'}</option>
               <option value=2>{_'Převod'}</option>
               <option value=3>{_'Hotovost'}</option>
            </select>
         </div>
      </div>
   </div>
   <div class="row mt-3">
      <input type="hidden" name="id_event" value="{$event->getID()}">
      <div class="row d-flex justify-content-center">
         <input type="submit" name="btnVytvoritVlastniPlatbu" class="form-control btn btn-primary shaddow-hover w-auto"
                value="{_'Vytvořit platbu'}">
      </div>
   </div>
</form>

<script>
   $(function() {
      const body = $('body');
      const form = body.find('form#eventVlastniPlatbaModal');
      const negativeIcon = form.find('.js-negative-icon');
      const positiveIcon = form.find('.js-positive-icon');

      form.on('change', 'input[name="is_negative"]', function(e) {
         const checkbox = $(this);

         if(checkbox.is(':checked')){
            negativeIcon.show();
            positiveIcon.hide();
            return;
         }

         positiveIcon.show();
         negativeIcon.hide();
      });
   });
</script>