<?php namespace app\front\eventy\detail\modaly\finance\novy\vlastni;

use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\model\event\BaseEventRow;
use app\system\model\event\platby\plan\EventPlanPlatby;
use app\system\modul\modal\Modal;
use app\system\modul\modal\styles\ModalScaleEnum;

/** Created by <PERSON><PERSON><PERSON>. Date: 12.12.2022 */
class VlastniNovaPlatbaModal extends Modal
{

   public function getTitleName() :string {
      return 'Nová platba';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnVytvoritVlastniPlatbu', function($post) {
         EventPlanPlatby::createVlastniPlatbu($post, FrontApplicationEnvironment::get()->versionType);
      });
   }

   public function setEvent(int $id_event) :static {
      $this->id_event = $id_event;
      $this->event = FrontApplicationEnvironment::get()->versionType->getEvent($id_event);
      return $this;
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'event' => $this->event,
      ]);
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::SMALL;
   protected int $id_event;
   protected BaseEventRow $event;
}