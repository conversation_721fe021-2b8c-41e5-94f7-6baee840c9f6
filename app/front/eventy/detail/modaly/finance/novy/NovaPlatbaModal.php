<?php namespace app\front\eventy\detail\modaly\finance\novy;

use app\front\controllers\eventy\DetailEventuController;
use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\event\BaseEventRow;
use app\system\model\platby\event\uhrazene\EventUhrazenePlatby;
use app\system\modul\modal\AjaxModal;
use app\system\modul\modal\styles\ModalScaleEnum;

class NovaPlatbaModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Nová úhrada';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnVytvoritPlatbu', function($post) {
         if(!isset($post['id_scenare_platby'])){
            FlashMessages::setError('Musí být vybrána platba, kterou chcete uhradit');
            return;
         }

         EventUhrazenePlatby::saveFromPost($post, FrontApplicationEnvironment::get()->versionType);
      });
   }

   public function prepareModal(Templater $templater) {
      $this->setEvent(FrontApplicationEnvironment::get()->versionType->getEvent($_POST['slug']));

      $templater->addData([
         'platby' => $this->event->getFinance()->getNezaplacene(),
         'event' => $this->event,
         'baseUrl' => DetailEventuController::getUrl($this->event->getID()),
      ]);
   }

   public function setEvent(BaseEventRow $event) :static {
      $this->event = $event;
      return $this;
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::DEFAULT;
   public int $id;
   public BaseEventRow $event;
}