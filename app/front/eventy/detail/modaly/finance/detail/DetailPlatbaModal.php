<?php namespace app\front\eventy\detail\modaly\finance\detail;

use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\model\platby\event\uhrazene\EventUhrazenePlatby;
use app\system\modul\modal\AjaxModal;
use app\system\modul\modal\styles\ModalScaleEnum;

class DetailPlatbaModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Detail platby';
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'platba' => EventUhrazenePlatby::get($_POST['slug'], FrontApplicationEnvironment::get()->versionType),
         'platebniMetody' => EventUhrazenePlatby::$platebniMetodyArray,
         'canDelete' => FrontApplicationEnvironment::get()->isMajitel || FrontApplicationEnvironment::get()->personal?->hasAccessRight(PersonalAccessRightsEnum::ADMIN)
      ]);
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::SMALL;
   public int $id;
}