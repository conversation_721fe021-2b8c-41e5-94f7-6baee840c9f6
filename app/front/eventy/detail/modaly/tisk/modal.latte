{varType app\front\tisk\variabilni\bloky\ITiskBlok[] $freeBlocks}
{varType app\system\model\tisk\variabilni\event\VariabilniTiskEventSablonaRow[] $sablony}
{varType app\system\model\maily\bloky\data\OrganizaceObsahBlokRow[] $hlavicky}
{varType app\system\model\maily\bloky\data\OrganizaceObsahBlokRow[] $paticky}
{varType string $tiskUrl}
{varType int $id_event}

<div class="container card p-3 js-tiskova-sada-container">
   <div class="row">
      <h2>{_'Tisková sada eventu'}</h2>
   </div>

   <div class="row my-2 gap-3">

      <div class="row">
         <div class="col-sm-6">
            <label for="select_sablona">{_'Vyberte šablonu'}</label>
            <select name="sablona" id="select_sablona_tisk" class="form-select"
                    data-varprinter-action="{app\front\tisk\variabilni\VariabilniTiskActionEnum::LOAD->value}">
               <option value="0">{_'Vyberte šablonu'}</option>
               {foreach $sablony as $sablona}
                  <option value="{$sablona->id}">{$sablona->nazev}</option>
               {/foreach}
            </select>
         </div>

         <div class="col d-flex flex-wrap align-items-end gap-2 m-2 m-md-0 justify-content-center justify-content-md-start">
            <div class="col-auto">
               <button class="jsSaveSablona form-control btn btn-outline-success w-auto" type="button"
                       name="btnSaveSablona" data-varprinter-action="{app\front\tisk\variabilni\VariabilniTiskActionEnum::SAVE->value}">
                  <i class="bi bi-floppy"></i>
               </button>
            </div>
            <div class="col-auto">
               <button class="jsDeleteTiskSablona form-control btn btn-outline-danger w-auto" type="button"
                       name="btnDeleteSablona" data-varprinter-action="{app\front\tisk\variabilni\VariabilniTiskActionEnum::DELETE->value}">
                  <i class="bi bi-trash3"></i>
               </button>
            </div>
            <div class="col-auto">
               <button class="form-control btn btn-outline-secondary w-auto" type="button" name="btnResetBloky"
                    data-varprinter-action="{app\front\tisk\variabilni\VariabilniTiskActionEnum::CLEAR->value}">
                  <i class="bi bi-arrow-clockwise"></i>
               </button>
            </div>
         </div>
      </div>

      <div class="row">
         <div class="col-sm-6">
            <input class="form-control jsNazevSablonaTisk" type="text" placeholder="{_'Název nové šablony'}">
         </div>
      </div>
      <div class="row">
         <div class="col-sm-6">
            <label for="select_hlavicka_tisk">{_'Vyberte hlavičku'}</label>
            <select name="sablona" id="select_hlavicka_tisk" class="form-select">
               <option value="0">{_'Bez hlavičky'}</option>
               {foreach $hlavicky as $hlavicka}
                  <option value="{$hlavicka->id_blok}">{$hlavicka->nazev}</option>
               {/foreach}
            </select>
         </div>
      </div>
      <div class="row">
         <div class="col-sm-6">
            <label for="select_paticka_tisk">{_'Vyberte patičku'}</label>
            <select name="paticka" id="select_paticka_tisk" class="form-select">
               <option value="0">{_'Bez patičky'}</option>
               {foreach $paticky as $paticka}
                  <option value="{$paticka->id_blok}">{$paticka->nazev}</option>
               {/foreach}
            </select>
         </div>
      </div>

      <div class="col-12 col-md pt-3">
         <div class="card bg-gray-100" data-sada-container="used">
            <div class="background-body-bg-color rohy-2-2 text-center">
               <h5 class="card-title mt-2">{_'Tisková sada'}</h5>
            </div>
            <div class="card-body">
               <form method="post" id="tiskSadaUsedForm">
                  <div class="row">
                     <div id="sadaContainerUsed" style="min-height: 10vh"></div>
                  </div>
                  <div class="row">
                     <button class="form-control btn btn-primary w-100" type="button" name="jsBtnTiskSada" data-idevent="{$id_event}"
                             data-varprinter-action="{app\front\tisk\variabilni\VariabilniTiskActionEnum::PRINT->value}">
                        <i class="bi bi-printer me-2"></i>{_'Tisk'}
                     </button>
                  </div>
               </form>
            </div>
         </div>
      </div>

      <div class="col-12 col-md pt-3">
         <div class="card bg-gray-100" data-sada-container="free">
            <div class="background-body-bg-color rohy-2-2 text-center">
               <h5 class="card-title mt-2">{_'Bloky k dispozici'}</h5>
            </div>
            <div class="card-body">
               <div id="sadaContainerFree" style="min-height: 10vh">
                  {foreach $freeBlocks as $blok}
                     {include #blokTemplate, blok => $blok}
                  {/foreach}
               </div>
            </div>
         </div>
      </div>
   </div>

</div>

<script>
   $(function() {
      variabilniTisk.initEventVariabilniTisk({$tiskUrl});

      const dragulaItem = dragula([
         document.querySelector('#sadaContainerUsed'),
         document.querySelector('#sadaContainerFree'),
      ]);

      dragulaItem.on('drop', function(el, a1, a2) {
         console.log('drop', el, a1, a2);
      });

      dragulaItem.on('drag', function(el) {
         // here close popup if exist
         const blok = $(el);
         const dropdowns = blok.find('button[data-bs-toggle="dropdown"]');

         if(dropdowns.length === 0)
            return;

         const dropdownInstance = bootstrap.Dropdown.getInstance(dropdowns);

         if(dropdownInstance === null)
            return;

         dropdownInstance.hide();
      })
   });
</script>

{define #blokTemplate}
   {varType app\front\tisk\variabilni\bloky\BaseTiskBlok $blok}

   <div class="card mb-3 cursor-grab" data-bloktisk="true">

      <input type="hidden" name="{$blok->getUniqueID()}" value="true">

      <div class="card-body p-3">
         <div class="float-end me-n2" n:if="!empty($blok->getSettingsClassesNames())">
            <div class="btn-group dropstart">
               <button type="button" class="form-control btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false">
                  <i class="bi bi-gear"></i>
               </button>
               <div class="dropdown-menu radius-card">
                  <div class="p-2">
                     <label for="mistnostiGroup" class="form-label">{_'Nastavení bloku'}</label>
                     {foreach $blok->getSettingsClasses() as $settingClass}
                        <div class="form-check">
                           <input class="form-check-input" type="checkbox" id="blok[{$blok->getUniqueID()}][{$settingClass->getUniqueID()}]" name="{$blok->getUniqueID()}[{$settingClass->getUniqueID()}]" data-settingChecker="true">
                           <label class="form-check-label" for="blok[{$blok->getUniqueID()}][{$settingClass->getUniqueID()}]">
                              {_$settingClass->getTitle()}
                           </label>
                        </div>
                     {/foreach}
                  </div>
               </div>
            </div>
         </div>
         <strong class="ukol-nazev mb-3">{_$blok->getTitle()}</strong>
      </div>
   </div>
{/define}