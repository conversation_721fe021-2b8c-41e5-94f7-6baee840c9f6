<?php namespace app\front\eventy\detail\modaly\tisk;

use app\front\controllers\tisk\VariabilniTiskController;
use app\front\tisk\variabilni\bloky\settings\pagebreak\PagebreakSettingBlok;
use app\front\tisk\variabilni\bloky\TiskBlokuEnum;
use app\system\application\ApplicationVersion;
use app\system\component\Templater;
use app\system\model\event\BaseEventRow;
use app\system\model\maily\bloky\data\ObsahBlokyTypyEnum;
use app\system\model\maily\bloky\OrganizaceObsahBloky;
use app\system\model\tisk\variabilni\event\VariabilniTiskEventSablony;
use app\system\modul\modal\Modal;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 17.04.2024 */
class VariabilniTiskovaSadaModal extends Modal
{

   public function getTitleName() :string {
      return 'Nastavení tiskové sady';
   }

   public function setEvent(BaseEventRow $event) :static {
      $this->event = $event;
      return $this;
   }

   public function prepareModal(Templater $templater) {
      $frBloky = [];

      foreach(TiskBlokuEnum::getAvailableBloky(ApplicationVersion::VENUE) as $blokTyp){
         if($cls = $blokTyp->getClass())
            $frBloky[] = $cls;
      }

      $this->templateData = [
         'activeBlocks' => [],
         'freeBlocks' => $frBloky,
         'pagebreakSetting' => (new PagebreakSettingBlok())->getUniqueID(),
         'sablony' => VariabilniTiskEventSablony::getByOrganizace($this->event->getIdOrganizace()),
         'id_event' => $this->event->getID(),
         'hlavicky' => OrganizaceObsahBloky::getForOrganizace($this->event->getIdOrganizace(), ObsahBlokyTypyEnum::HLAVICKA),
         'paticky' => OrganizaceObsahBloky::getForOrganizace($this->event->getIdOrganizace(), ObsahBlokyTypyEnum::PATICKA),
         'tiskUrl' => VariabilniTiskController::getUrl(),
      ];

      $templater->addData($this->templateData);
   }

   private BaseEventRow $event;
   private array $templateData;
}