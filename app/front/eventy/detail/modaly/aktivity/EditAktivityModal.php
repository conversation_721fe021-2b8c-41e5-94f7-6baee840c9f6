<?php namespace app\front\eventy\detail\modaly\aktivity;

use app\System;
use app\system\application\ApplicationVersion;
use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\aktivity\TypyAktivit;
use app\system\model\event\data\aktivity\EventAktivityStavy;
use app\system\model\event\EventAktivity;
use app\system\model\organizace\personal\Personal;
use app\system\modul\modal\AjaxModal;
use app\system\tiny\TinyEditor;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 18.08.2022 */
class EditAktivityModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Úprava aktivity';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUlozitAktivitu', function($post) {
         $aktivita = EventAktivity::editAktivita($post, ApplicationVersion::from($post['versionType']));
         FlashMessages::setSuccess('Aktivita $1 byla ulož<PERSON>', $aktivita->nazev);
      });

      $this->isset('btnSmazatAktivitu', function($post) {
         EventAktivity::delete($post['id_aktivita'], ApplicationVersion::from($post['versionType']));
         FlashMessages::setSuccess('Aktivita byla smazána');
      });
   }

   public function prepareModal(Templater $templater) {
      $aktivita = EventAktivity::get($_POST['slug'], FrontApplicationEnvironment::get()->versionType);
      $vytvoril = 0;

      if($aktivita->id_vytvoril)
         $vytvoril = Personal::get($aktivita->id_vytvoril);

      $templater->addData([
         'isMobil' => System::get()->detect->isMobile(),
         'versionType' => FrontApplicationEnvironment::get()->versionType,
         'aktivita' => $aktivita,
         'aktivityStavy' => EventAktivityStavy::cases(),
         'typyAktivit' => TypyAktivit::cases(),
         'personalMista' => Personal::getByMistoAssoc($aktivita->id_mista),
         'vytvoril' => $vytvoril,
         'idEvent' => null,
         'tinyEditor' => TinyEditor::init()
            ->setInputName('popis')
            ->setPlaceholder('Detail aktivity')
            ->setContent($aktivita->popis)
            ->setHeightPX(400)
            ->render(),
      ]);
   }
}
