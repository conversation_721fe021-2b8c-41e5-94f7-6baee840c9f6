<?php namespace app\front\eventy\detail\modaly\aktivity;

use app\System;
use app\system\application\ApplicationVersion;
use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\aktivity\TypyAktivit;
use app\system\model\event\BaseEventRow;
use app\system\model\event\data\aktivity\EventAktivityRow;
use app\system\model\event\data\aktivity\EventAktivityStavy;
use app\system\model\event\EventAktivity;
use app\system\model\event\notifikace\NovaAktivitaEventuNotifikace;
use app\system\model\organizace\personal\Personal;
use app\system\modul\modal\Modal;
use app\system\tiny\TinyEditor;
use DateInterval;
use Dibi\DateTime;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 21.08.2022 */
class NovaAktivitaModal extends Modal
{

   public function getTitleName() :string {
      return 'Nová aktivita';
   }

   public function getBtnAttributes() :array {
      return [
         'data-bs-toggle' => 'modal',
         'data-bs-target' => '#' . $this->getIdModal(),
      ];
   }

   public function setEvent(BaseEventRow $event) :static {
      $this->event = $event;
      return $this;
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'isMobil' => System::get()->detect->isMobile(),
         'versionType' => FrontApplicationEnvironment::get()->versionType,
         'aktivita' => null,
         'aktivityStavy' => EventAktivityStavy::cases(),
         'typyAktivit' => TypyAktivit::cases(),
         'personalMista' => Personal::getByMistoAssoc($this->event->getIdOrganizace()),
         'vytvoril' => null,
         'idEvent' => $this->event->getID(),
         'tinyEditor' => TinyEditor::init()
            ->setInputName('popis')
            ->setPlaceholder('Detail aktivity')
            ->setHeightPX(400)
            ->render(),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnVytvorAktivitu', function($post) {
         $aktivita = $this->createAktivitu($post);

         if(!$aktivita)
            return;

         FlashMessages::setSuccess('Aktivita $1 byla vytvořena', $aktivita->nazev);
      });
   }

   protected function createAktivitu($post) :?EventAktivityRow {
      $version = ApplicationVersion::from($post['versionType']);
      $aktivita = new EventAktivityRow();

      foreach(['nazev', 'popis', 'stav', 'id_event'] as $prop)
         $aktivita->{$prop} = $post[$prop]?? null;

      $event = $version->getEvent($aktivita->id_event);
      $aktivita->id_mista = $event->getIdOrganizace();

      if(!($id_personal = intval($post['id_prirazeny'])) || !($personal = Personal::get($id_personal))){
         FlashMessages::setWarning('Není přiřazený žádný personál');
         $aktivita->id_prirazeny = 0;
         $personal = null;
      } else
         $aktivita->id_prirazeny = $personal->id_personal;

      $aktivita->termin = new DateTime($post['termin']);

      if(
         ($start = trim($post['timeStart']))
         && ($end = trim($post['timeEnd']))
      ){
         $startParts = explode(':', $start);
         $endParts = explode(':', $end);

         $aktivita->time_start = new DateInterval(sprintf('PT%dH%dM', intval($startParts[0]), intval($startParts[1])));
         $aktivita->time_end = new DateInterval(sprintf('PT%dH%dM', intval($endParts[0]), intval($endParts[1])));

      } else {
         $aktivita->time_start = $aktivita->time_end = null;
      }

      $aktivita->id_vytvoril = FrontApplicationEnvironment::get()->personal?->id_personal ?: 0;
      $aktivita->typ = TypyAktivit::from($post['aktivitaTyp'])->value;

      EventAktivity::save($aktivita, $version);

      if($personal?->id_uzivatel !== null && ($aktivitaClass = $version->getAktivitaEventu($aktivita->id_aktivita))){
         (new NovaAktivitaEventuNotifikace($version, $aktivitaClass))
            ->sendTo($personal);
      }

      return $aktivita;
   }

   protected int $id_event;
   protected BaseEventRow $event;
}
