{varType ?app\system\model\event\entity\aktivity\EventAktivita $aktivita}
{varType app\system\model\event\data\aktivity\EventAktivityStavy[] $aktivityStavy}
{varType app\system\model\organizace\personal\PersonalRow[] $personalMista}
{varType null|int|app\system\model\organizace\personal\PersonalRow $vytvoril}
{varType string $tinyEditor}
{varType bool $isMobil}
{varType app\system\application\ApplicationVersion $versionType}
{varType app\system\model\aktivity\TypyAktivit[] $typyAktivit}
{varType ?int $idEvent}

{*@TODO dočasně - pro PK přesunout do qvamp.scss, nebo upravit radio buttony *}
<style>
   .btn-outline-secondary:hover,
   .btn-outline-secondary:focus {
      background-color: #6c757d !important; /* secondary color */
      color: #fff !important;
      border-color: #6c757d !important;
   }
</style>

<div class="container">
   <form method="post" id="editAktivitaForm">
      <div class="row">
         <div class="col-md-8">
               <div class="form-floating">
                  <input type="text" class="form-control" name="nazev" value="{$aktivita?->nazev}" maxlength="120" required>
                  <label>{_'Co se má udělat'}</label>
               </div>
               <div class="form-group my-3">
                  {$tinyEditor|noescape}
               </div>
         </div>
         <div class="col-md">
            <label>{_'Kdo a kdy'}</label>
            <div class="row">
               <div class="col">
                  <div class="input-group mb-3">
                     <span class="input-group-text" id="basic-addon1"><i class="bi bi-person-check"></i></span>
                     <select name="id_prirazeny" class="form-select">
                        <option value="0">{_'Nepřiřazeno'}</option>
                        {foreach $personalMista as $personal}
                           <option n:attr="
                              value: $personal->id_personal,
                              selected: $aktivita?->id_prirazeny == $personal->id_personal"
                           >
                              {$personal->getFullName()}
                           </option>
                        {/foreach}
                     </select>
                  </div>
               </div>
               <div class="col">
                  <div class="input-group">
                     <div class="input-group date js-datepicker" id="termin" data-target-input="nearest">
                        <input type="text" class="form-control datetimepicker-input" data-target="#termin" name="termin" value="{$aktivita?->termin|date:'j.n.Y'}">
                        <div class="input-group-text" data-target="#termin" data-toggle="datetimepicker"><i class="bi bi-calendar2-event"></i></div>
                     </div>
                  </div>
               </div>
            </div>
            <div class="row mb-2">
               <div class="col">
                  <label for="aktivita_time_start">{_'Čas začátku'}</label>
                  <div class="form-group">
                     {if !$isMobil}
                        <input id="aktivita_time_start" type="text" class="form-control js-timepicker text-end" data-target="#aktivita_time_start" inputmode="none"
                               name="timeStart" pattern="{app\system\helpers\RegexPatterns::TIME}" data-toggle="datetimepicker" autocomplete="off" n:attr="value: $aktivita?->dtStart?->format('H:i')">
                     {else}
                        <input id="aktivita_time_start" type="time" class="form-control text-end" autocomplete="off" name="timeStart"
                                n:attr="value: $aktivita?->dtStart?->format('H:i')">
                     {/if}
                  </div>
               </div>

               <div class="col">
                  <label for="aktivita_time_start">{_'Čas konce'}</label>
                  <div class="form-group">
                     {if !$isMobil}
                        <input id="aktivita_time_end" type="text" class="form-control js-timepicker text-end" data-target="#aktivita_time_end" inputmode="none"
                               name="timeEnd" pattern="{app\system\helpers\RegexPatterns::TIME}" data-toggle="datetimepicker" autocomplete="off" n:attr="value: $aktivita?->dtEnd?->format('H:i')">
                     {else}
                        <input id="aktivita_time_end" n:attr="value: $aktivita?->dtEnd?->format('H:i')" type="time" class="form-control text-end" autocomplete="off" name="timeEnd">
                     {/if}
                  </div>
               </div>
            </div>

            <div class="row mb-2">
               <div class="col-12 mb-2">
                  <label class="form-label">{_'Typ aktivity'}</label>
               </div>
               <div class="col-12">
                  <div class="btn-group" role="group" aria-label="Activity Type">
                     {foreach $typyAktivit as $typ}
                        <input
                                type="radio"
                                class="btn-check"
                                name="aktivitaTyp"
                                id="editAktivitaTyp{$iterator->counter}"
                                autocomplete="off"
                                n:attr="
                                   value: $typ->value,
                                   checked: $aktivita ? $aktivita->typ === $typ : $iterator->isFirst()"
                        >
                        <label
                                class="btn btn-outline-secondary"
                                for="editAktivitaTyp{$iterator->counter}">
                           {$typ->getTranslatedTitle()}
                        </label>
                     {/foreach}
                  </div>
               </div>
            </div>

            <div class="row mb-2">
               <div class="col-12 mb-2">
                  <label class="form-label">{_'Stav'}</label>
               </div>
               <div class="col-12">
                  <div class="btn-group" role="group" aria-label="Status">
                     {foreach $aktivityStavy as $stav}
                        <input
                                type="radio"
                                class="btn-check"
                                name="stav"
                                id="editAktivitaStav{$iterator->counter}"
                                autocomplete="off"
                                n:attr="
                                   value: $stav->value,
                                   checked: $aktivita ? $aktivita->getStav() === $stav : $iterator->isFirst()"
                        >
                        <label
                                class="btn btn-outline-secondary"
                                for="editAktivitaStav{$iterator->counter}">
                           {$stav->getTranslatedTitle()}
                        </label>
                     {/foreach}
                  </div>
               </div>
            </div>

            <div n:if="$vytvoril !== null" class="row">
               <div class="col d-flex align-items-end justify-content-end">
                  <small class="text-gray"><i class="bi bi-person-fill-gear"></i> {if $vytvoril}{$vytvoril->getFullName()}{else}{_'Správce místa'}{/if}</small>
               </div>
            </div>
         </div>
      </div>

      <div class="row m-0 mt-4 d-flex justify-content-center gap-2">
         <input type="hidden" name="versionType" value="{$versionType->value}">
         {if $aktivita}
            <input type="hidden" name="id_aktivita" value="{$aktivita->id_aktivita}">
            <div class="col-auto">
               <button type="submit" name="btnUlozitAktivitu" class="btn btn-primary shaddow-hover w-auto">{_'Uložit změny'}</button>
            </div>
            <div class="col-auto">
               <button type="submit" name="btnSmazatAktivitu" class="btn btn-outline-secondary shaddow-hover w-auto no-validate"
                       data-confirm="{_'Opravdu si přejete odstarnit aktivitu?'}" formnovalidate>{_'Odstranit aktivitu'}<i class="ms-2 bi bi-x-circle"></i></button>
            </div>
         {else}
            <input type="hidden" name="id_event" value="{$idEvent}">
            <div class="col-auto">
               <button type="submit" name="btnVytvorAktivitu" class="btn btn-primary w-auto shaddow-hover">
                  {_'Vytvořit aktivitu'}
               </button>
            </div>
         {/if}

      </div>
   </form>
</div>

<script>
   $(function() {
      const form = $('form#editAktivitaForm');
      const inputDateStart = form.find('input[name="timeStart"]');
      const inputDateEnd = form.find('input[name="timeEnd"]');

      form.validate({
         ignore: '.no-validate',
         rules: {
            timeStart: {
               required: function(element) {
                  return inputDateEnd.val().trim() !== "";
               },
            },
            timeEnd: {
               required: function(element) {
                  return inputDateStart.val().trim() !== "";
               },
            }
         },
      });
   });
</script>