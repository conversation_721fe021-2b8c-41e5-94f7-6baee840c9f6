<?php namespace app\front\eventy\detail\modaly\personal;

use app\front\controllers\eventy\DetailEventuActionsWHController;
use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\event\BaseEventRow;
use app\system\model\event\notifikace\PrirazenyPersonalEventuNotifikace;
use app\system\model\event\personal\EventPersonal;
use app\system\model\event\personal\EventPersonalRow;
use app\system\model\organizace\personal\Personal;
use app\system\model\organizace\pozice\PozicePersonal;
use app\system\modul\modal\Modal;
use app\system\modul\modal\styles\ModalScaleEnum;
use DateInterval;
use Dibi\DateTime;

class EventNovyPersonalModal extends Modal
{

   public function getTitleName() :string {
      return 'Přidat personál do eventu';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnPridatEventPersonal', function($post) {
         if(
            !($id_event = intval($post['id_event']))
            || !($id_personal = intval($post['id_personal']))
            || !($event = ($version = FrontApplicationEnvironment::get()->versionType)->getEvent($id_event))
            || !($personal = Personal::get($id_personal))
         ){
            FlashMessages::setError('Chyba při ukládání');
            return;
         }

         $smena = new EventPersonalRow();
         $smena->id_prirazeny_uzivatel = $personal->id_personal;
         $smena->id_event = $event->getID();

         $smena->id_pozice = intval($post['id_pozice'] ?? null) ?: null;
         $smena->hodinovka = $post['hodinovka'] !== '' ? (int)bcmul($post['hodinovka'], '100') : null;
         $smena->hruba_mzda = $post['hruba-mzda'] !== '' ? (int)bcmul($post['hruba-mzda'], '100') : null;

         $startParts = explode(':', $post['time-start']);
         $endParts = explode(':', $post['time-end']);

         $smena->date = trim($post['datum'] ?? '') ? new DateTime($post['datum']) : $event->date_start;
         $smena->time_start = new DateInterval(sprintf('PT%dH%dM', intval($startParts[0]), intval($startParts[1])));
         $smena->time_end = new DateInterval(sprintf('PT%dH%dM', intval($endParts[0]), intval($endParts[1])));

         EventPersonal::save($smena, $version);

         if($personal->id_uzivatel !== null)
            (new PrirazenyPersonalEventuNotifikace($event))
               ->setSmena($smena)
               ->sendTo($personal);

         FlashMessages::setSuccess('$1 byl přidán do eventu', $personal->getFullName());
      });
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'pozice' => PozicePersonal::getByMisto($this->event->getIdOrganizace()),

         'event' => $this->event,
         'personal' => Personal::getByMistoAssoc($this->event->getIdOrganizace()),
         'id_modal' => $this->getIdModal(),
         'findPersonalUrl' => DetailEventuActionsWHController::getUrl($this->event->getID(), DetailEventuActionsWHController::ACTION_GET_PERSONAL),

         'smena' => null
      ]);
   }

   public function setEvent(BaseEventRow $eventRow) :static {
      $this->event = $eventRow;
      return $this;
   }

   protected BaseEventRow $event;
   protected ModalScaleEnum $scale = ModalScaleEnum::DEFAULT;

}