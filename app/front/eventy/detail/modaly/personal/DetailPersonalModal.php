<?php namespace app\front\eventy\detail\modaly\personal;

use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\event\personal\EventPersonal;
use app\system\model\organizace\pozice\PozicePersonal;
use app\system\modul\modal\AjaxModal;
use app\system\modul\modal\styles\ModalScaleEnum;
use DateInterval;
use Dibi\DateTime;

class DetailPersonalModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Detail směny';
   }

   public function prepareModal(Templater $templater) {
      $idSmena = intval($_POST['slug'] ?? null);
      $smena = FrontApplicationEnvironment::get()->versionType->getSmenaEventuItem($idSmena);

      $templater->addData([
         'pozice' => PozicePersonal::getByMisto($smena->personal->id_misto),
         'smena' => $smena,
         'event' => FrontApplicationEnvironment::get()->versionType->getEvent($smena->smena->id_event),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnOdebratEventPersonal', function($post) {
         EventPersonal::delete(intval($post['id_smena']), FrontApplicationEnvironment::get()->versionType);
         FlashMessages::setSuccess('Personál odstraněn z eventu');
      });

      $this->isset('btnUpravitEventPersonal', function($post) {
         $idSmena = intval($post['id_smena']);
         $version = FrontApplicationEnvironment::get()->versionType;

         $smena = $version->getSmenaEventu($idSmena);
         $event = $version->getEvent($smena->id_event);

         $smena->id_pozice = intval($post['id_pozice'] ?? null) ?: null;
         $smena->hodinovka = $post['hodinovka'] !== '' ? (int)bcmul($post['hodinovka'], '100') : null;
         $smena->hruba_mzda = $post['hruba-mzda'] !== '' ? (int)bcmul($post['hruba-mzda'], '100') : null;

         $startParts = explode(':', $post['time-start']);
         $endParts = explode(':', $post['time-end']);

         $smena->date = trim($post['datum'] ?? '') ? new DateTime($post['datum']) : $event->date_start;
         $smena->time_start = new DateInterval(sprintf('PT%dH%dM', intval($startParts[0]), intval($startParts[1])));
         $smena->time_end = new DateInterval(sprintf('PT%dH%dM', intval($endParts[0]), intval($endParts[1])));

         EventPersonal::save($smena, $version);
         FlashMessages::setSuccess('Směna byla uložena');
      });
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::DEFAULT;
}