{varType app\system\model\organizace\personal\PersonalRow[] $personal}
{varType app\system\model\event\BaseEventRow $event}
{varType app\system\model\organizace\pozice\PozicePersonalRow[] $pozice}
{varType string $id_modal}
{varType string $findPersonalUrl}

{varType ?app\system\model\event\personal\EventPersonalSmenaItem $smena}

{var $addPersonal = !($smena ?? false)}
{var $formID = $addPersonal ? 'newPersonalEvent' : 'editPersonalEvent'}

<form method="post" id="{$formID}">
   <div class="container p-3">
      <div id="pridat-personal-form">
         {if $addPersonal}
            {include personalSelect}
         {else}
            {include personalOverview}
         {/if}

         <div class="row mt-3">
            <div class="form-group col-md-6 col-12 pb-4">
               <label for="pozice_personal">{_'Pozice'}</label>
               <select name="id_pozice" id="pozice_personal" class="form-select">
                  <option value="">{_'Bez pozice'}</option>
                  {foreach $pozice as $poz}
                     <option value="{$poz->id_pozice}" n:attr="selected: $smena?->smena->id_pozice === $poz->id_pozice">{$poz->nazev}</option>
                  {/foreach}
               </select>
            </div>
         </div>

         <div class="row mt-3">
            <div class="form-group col-md col-6">
               <label for="{$formID}Datum">{_'Datum'}<span class="text-danger">&nbsp;*</span></label>
               <div class="input-group date js-datepicker" id="{$formID}modal_datum"
                    data-datepicker-min="{$event->date_start->sub(new \DateInterval('P1M'))|date: 'm/d/Y'}" data-datepicker-max="{$event->date_end->add(new \DateInterval('P1M'))|date: 'm/d/Y'}"
                    data-target-input="nearest">

                  <input type="text" class="form-control datetimepicker-input" name="datum"
                         data-target="#{$formID}modal_datum" id="{$formID}Datum"
                         value="{$smena?->smena->dtStart ?: $event->date_start|date:'j.n.Y'}" required>
                  <div class="input-group-text" data-target="#{$formID}modal_datum" data-toggle="datetimepicker"><i class="bi bi-calendar-date"></i></div>
               </div>
            </div>

            <div class="form-group col-md col-6">
               <label for="{$formID}CasStart">{_'Začátek'}<span class="text-danger">&nbsp;*</span></label>
               <div class="input-group date js-timepicker" id="{$formID}modal_start" data-target-input="nearest">
                  <input type="text" class="form-control datetimepicker-input" name="time-start"
                         data-target="#{$formID}modal_start" id="{$formID}CasStart" value="{$smena?->smena->dtStart ?: $event->date_start|date:'H:i'}" required>
                  <div class="input-group-text" data-target="#{$formID}modal_start" data-toggle="datetimepicker"><i class="bi bi-clock"></i></div>
               </div>
            </div>
            <div class="form-group col-md col-6">
               <label for="{$formID}CasEnd">{_'Konec'}<span class="text-danger">&nbsp;*</span></label>
               <div class="input-group date js-timepicker" id="{$formID}modal_end" data-target-input="nearest">
                  <input type="text" class="form-control datetimepicker-input" name="time-end"
                         data-target="#{$formID}modal_end" id="{$formID}CasEnd" value="{$smena?->smena->dtEnd ?: $event->date_end|date:'H:i'}" required>
                  <div class="input-group-text" data-target="#{$formID}modal_end" data-toggle="datetimepicker"><i class="bi bi-clock"></i></div>
               </div>
            </div>
         </div>
         <div class="row gap-2 pb-4">
            <div class="col-md-8 col-12 d-flex gap-2 personal-mzdy">
               <div class="form-group col">
                  <label for="{$formID}HodinovaMzda">{_'Hodinová odměna'}</label>
                  <input type="text" name="hodinovka" id="{$formID}HodinovaMzda"
                         class="form-control js-price-mask" maxlength="20"
                          n:attr="value: $smena?->smena->hodinovka ? $smena?->smena->getHodinovka()->getAmount()">
               </div>
               <div class="form-group col">
                  <label for="{$formID}Mzda">{_'Odměna celekem'}<span class="text-danger">&nbsp;*</span></label>
                  <input type="text" id="{$formID}Mzda" class="form-control js-price-mask"
                         name="hruba-mzda" maxlength="20"
                          n:attr="value: $smena?->smena->getMzda()->getAmount()" required>
               </div>
            </div>
            <div class="form-group col-md col-12">
               <label>{_'Započítané hodiny'}</label>
               <input type="text" class="form-control js-counted-wage" disabled>
            </div>
         </div>
         <div class="row mt-3 d-flex justify-content-center">
            {if $addPersonal}
               <input type="hidden" name="id_event" value="{$event->getID()}">
               <div class="col-auto">
                  <button type="submit" name="btnPridatEventPersonal" class="btn btn-primary w-auto shaddow-hover" n:attr="disabled: empty($personal)"><i class="bi bi-person-plus me-1"></i>{_'Přidat do eventu'}</button>
               </div>
            {else}
               <input type="hidden" name="id_event" value="{$smena->smena->id_event}">
               <input type="hidden" name="id_smena" value="{$smena->smena->id}">

               <div class="col-auto">
                  <button type="submit" name="btnUpravitEventPersonal" class="btn btn-primary shaddow-hover w-auto"><i class="bi bi-save me-1"></i>{_'Uložit změny'}</button>
               </div>
               <div class="col-auto">
                  <button type="submit" name="btnOdebratEventPersonal" class="btn btn-outline-secondary w-auto shaddow-hover"
                          data-confirm="{_'Opravdu chcete osobu odebrat?'}"><i class="bi bi-person-slash me-1"></i>{_'Odebrat z eventu'}</button>
               </div>
            {/if}
         </div>
      </div>
   </div>
</form>

<script>
   $(function() {
      const form = $('form#' + {$formID});

      const timeStartInput = form.find('input[name="time-start"]');
      const timeEndInput = form.find('input[name="time-end"]');
      const wageForHourInput = form.find('input[name="hodinovka"]');
      const wageInput = form.find('input[name="hruba-mzda"]');
      const wageHoursInput = form.find('input.js-counted-wage');

      const countedTime = {
         h: 0,
         m: 0,
         min: 0,
      };

      form.validate({});

   {if $addPersonal}
      const select = form.find('select.js-personal-select');

      select.select2({
         dropdownParent: $("#" + {$id_modal}),
      });

      select.on('select2:select', function (e) {
         const id_personal = Number(e.params['data']['id']);
         wageForHourInput.prop('disabled', true);
         form.find('input[type=submit]').prop('disabled', true);

         ajaxHandler.post({$findPersonalUrl}, { id_personal }, function (response) {
            wageForHourInput.val(response['hodinovaSazbaFormatted'] ? response['hodinovaSazbaFormatted'] : '');
            wageForHourInput.prop('disabled', null);
            form.find('input[type=submit]').prop('disabled', null);
            countWage();
         });
      });
   {/if}

      form.on('change', 'input[name="hodinovka"]', function(e) {
         countWage();
      });

      form.on('change::qvamp.datepicker', 'input[name="time-start"], input[name="time-end"]', function(eOriginal, e) {
         countHours();
         countWage();
         wageHoursInput.val(countedTime.h + 'h ' + countedTime.m + 'm ');
      });

      function countWage() {
         const priceForHour = wageForHourInput.val();

         if(!priceForHour)
            return;

         parseFloat(priceForHour.replace(",", "."));

         const totalEarnings = (countedTime.min * parseFloat(priceForHour.replace(",", ".")) / 60);

         wageInput.val(
            isNaN(totalEarnings) ? '' : totalEarnings.toFixed(2).replace(".", ",")
         );
      }

      function countHours() {
         const timeStart = timeStartInput.val();
         const timeEnd = timeEndInput.val();

         const startTimeParts = timeStart.split(":");
         const endTimeParts = timeEnd.split(":");
         const startHours = parseInt(startTimeParts[0], 10);
         const startMinutes = parseInt(startTimeParts[1], 10);
         const endHours = parseInt(endTimeParts[0], 10);
         const endMinutes = parseInt(endTimeParts[1], 10);

         // Calculate the time difference in minutes
         let timeDifferenceInMinutes = (endHours * 60 + endMinutes) - (startHours * 60 + startMinutes);

         // Handle negative time differences (when end time is before start time)
         if (timeDifferenceInMinutes < 0) {
            timeDifferenceInMinutes += 24 * 60; // Add a day's worth of minutes (1440)
         }

         // Round up the time difference to the nearest half hour
         timeDifferenceInMinutes = Math.ceil(timeDifferenceInMinutes / 30) * 30;

         // Calculate hours and minutes from the rounded time difference
         const hours = Math.floor(timeDifferenceInMinutes / 60);
         const remainingMinutes = timeDifferenceInMinutes % 60;

         countedTime.h = hours;
         countedTime.m = remainingMinutes;
         countedTime.min = timeDifferenceInMinutes;

         // Calculate total earnings based on rounded time difference and hourly wage
      }

      countHours();
      wageHoursInput.val(countedTime.h + 'h ' + countedTime.m + 'm ');
   });
</script>

{define personalSelect}
   {if empty($personal)}
      <strong>{_'Aktuálně nemáte žádný personál'}</strong>
   {else}
      <div class="row">
         <div class="form-group col-12">
            <label for="{$formID}personSelect">{_'Vyberte osobu ze seznamu'}<span class="text-danger">&nbsp;*</span></label>
            <select class="form-select js-personal-select" name="id_personal" id="{$formID}personSelect" required>
               <option value=""></option>
               <optgroup label="{_'Bez účtu Qvamp'}">
                  {foreach $personal as $per}
                     {if !isset($per->id_uzivatel)}
                        <option value="{$per->id_personal}">{$per->getFullName()}</option>
                     {/if}
                  {/foreach}
               </optgroup>
               <optgroup label="{_'S přístupem do Qvamp'}">
                  {foreach $personal as $per}
                     {if isset($per->id_uzivatel)}
                        <option value="{$per->id_personal}">{$per->getFullName()}</option>
                     {/if}
                  {/foreach}
               </optgroup>
            </select>
         </div>
      </div>
   {/if}
{/define}

{define personalOverview}
   <div class="row mt-3">
      <div class="form-group col-sm-4 col-6">
         <label for="{$formID}personalName">{_'Jméno'}</label>
         <input type="text" name="jmeno" class="form-control" value="{$smena->personal->jmeno}"
                id="{$formID}personalName" disabled>
      </div>
      <div class="form-group col-sm-4 col-6">
         <label for="{$formID}personalLastName">{_'Přijmení'}</label>
         <input type="text" name="prijmeni" class="form-control" id="{$formID}personalLastName"
                value="{$smena->personal->prijmeni}" disabled>
      </div>
   </div>
{/define}