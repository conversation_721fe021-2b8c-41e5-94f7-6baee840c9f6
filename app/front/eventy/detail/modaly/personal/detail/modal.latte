{varType app\system\model\event\personal\EventPersonalSmenaItem $personal}
{varType app\system\model\organizace\pozice\PozicePersonalRow[] $pozice}

<div class="row">
    <form method="post">
        <div class="row mt-3">
            <div class="form-group col-sm-4 col-6">
                <label>{_'Jméno'}</label>
                <input type="text" name="jmeno" class="form-control" value="{$personal->personal->jmeno}"
                       disabled>
            </div>
            <div class="form-group col-sm-4 col-6">
                <label>{_'Přijmení'}</label>
                <input type="text" name="prijmeni" class="form-control"
                       value="{$personal->personal->prijmeni}" disabled>
            </div>
        </div>

        <div class="row mt-3" id="personal-detail-datepickers">
            <div class="form-group col-sm-4 pb-4">
                <label>{_'Pozice'}</label>
                <select name="id_pozice" id="pozice_personal_edit" class="form-select">
                    <option value="">{_'Bez pozice'}</option>
                    {foreach $pozice as $poz}
                        <option value="{$poz->id_pozice}" {if $poz->id_pozice == $personal->smena->id_pozice}selected{/if}>{$poz->nazev}</option>
                    {/foreach}
                </select>
            </div>
            <div class="form-group col-sm-2 col-6">
                <label>{_'Začátek'}</label>
                <div class="input-group date js-timepicker mzda-personal" id="personal-modal-start" data-time="start"
                     data-partner="personal-modal-end" data-mzdy="personal-detail-mzdy"
                     data-target-input="nearest">{* @TODO změnit id/name na start-time, end-time*}
                    <input type="text" class="form-control datetimepicker-input personal-date-start"
                           data-target="#personal-modal-start"
                           name="time-start" value="{$personal->smena->dtStart|date:'H:i'}"
                           required>
                    <div class="input-group-text" data-target="#personal-modal-start" data-toggle="datetimepicker"><i
                                class="bi bi-calendar2-event"></i></div>
                </div>
            </div>
            <div class="form-group col-sm-2 col-6">
                <label>{_'Konec'}</label>
                <div class="input-group date js-timepicker mzda-personal" id="personal-modal-end"
                     data-target-input="nearest" data-time="end"
                     data-mzdy="personal-detail-mzdy" data-partner="personal-modal-start">
                    <input type="text" class="form-control datetimepicker-input personal-date-end"
                           data-target="#personal-modal-end"
                           name="time-end" value="{$personal->smena->dtEnd|date:'H:i'}" required>
                    <div class="input-group-text" data-target="#personal-modal-end" data-toggle="datetimepicker"><i
                                class="bi bi-calendar2-event"></i></div>
                </div>
            </div>
        </div>
        <div class="row personal-mzdy" id="personal-detail-mzdy">
            <div class="row col-sm-8 col-12">
               <div class="form-group col-6">
                  <label>{_'Hodinová odměna'}</label>
                  <div class="input-group g-1">
                     <input type="text" name="hodinovka" id="hodinovka-edit" value="{$personal->smena->getHodinovka()->getAmount()}"
                             class="form-control js-price-mask hodinovka-personal input-levy-round" data-pickers="personal-detail-datepickers">
                     <span class="input-group-text input-pravy-round" id="basic-addon1">{$personal->smena->getHodinovka()->getCurrency()}</span>
                  </div>
               </div>

               <div class="form-group col-6">
                  <label>{_'Odměna celekem'}</label>
                  <div class="input-group g-1">
                     <input type="text" name="hruba-mzda" id="hruba-mzda-inp" value="{$personal->smena->getMzda()->getAmount()}"
                            class="form-control js-price-mask mzda-personal input-levy-round">
                     <span class="input-group-text input-pravy-round" id="basic-addon2">{$personal->smena->getMzda()->getCurrency()}</span>
                  </div>
               </div>
           </div>
        </div>
        <div class="row mt-3 d-flex justify-content-center">
            <input type="hidden" name="id" value="{$personal->smena->id}">
            <input type="hidden" name="id_personal" value="{$personal->personal->id_personal}">
            <input type="hidden" name="id_event" value="{$personal->smena->id_event}">
            <div class="col-sm-3">
                <input type="submit" name="btnUpravitEventPersonal" class="form-control btn btn-primary" value="{_'Uložit změny'}">
            </div>
            <div class="col-sm-2">
                <input type="submit" name="btnOdebratEventPersonal" class="form-control btn btn-danger" value="{_'Odebrat z eventu'}"
                       data-confirm="{_'Opravdu chcete osobu odebrat?'}">
            </div>
        </div>
    </form>
</div>
