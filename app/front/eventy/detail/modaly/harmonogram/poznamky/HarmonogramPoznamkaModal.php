<?php namespace app\front\eventy\detail\modaly\harmonogram\poznamky;

use app\system\application\ApplicationVersion;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\event\BaseEventRow;
use app\system\model\harmonogram\HarmonogramPoznamkaRow;
use app\system\model\harmonogram\HarmonogramPoznamky;
use app\system\modul\modal\Modal;
use app\system\tiny\TinyEditor;
use Latte\Runtime\Html;

/** Created by Filip Pavlas. Date: 28.06.2024 */
class HarmonogramPoznamkaModal extends Modal
{

   public function getTitleName() :string {
      return 'Nová poznámka';
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'event' => $this->event,
         'poznamka' => null,
         'poznamkaEditor' => new Html(
            TinyEditor::init()
               ->setInputName('poznamka_text')
               ->render()
         ),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUlozitPoznamku', function($post) {
         $poznamka = isset($post['id_poznamka'])
            ? HarmonogramPoznamky::get($post['id_poznamka'])
            : new HarmonogramPoznamkaRow([
               'id_event' => $this->event->getID(),
               'version' => ApplicationVersion::getFromEvent($this->event)->value,
            ]);

         $poznamka->nazev = $post['poznamka_nazev'];
         $poznamka->text = $post['poznamka_text'];

         $poznamka->save();
         FlashMessages::setSuccess('Poznámka byla uložena');
      });

      $this->isset('btnSmazatPoznamku', function($post) {
         if(!isset($post['id_poznamka']))
            return;

         HarmonogramPoznamky::delete(HarmonogramPoznamky::get($post['id_poznamka']));
         FlashMessages::setSuccess('Poznámka byla smazána');
      });
   }

   public function setEvent(BaseEventRow $event) :static {
      $this->event = $event;
      return $this;
   }

   public BaseEventRow $event;
}