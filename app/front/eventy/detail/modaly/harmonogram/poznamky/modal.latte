{varType ?app\system\model\harmonogram\HarmonogramPoznamkaRow $poznamka}
{varType Latte\Runtime\Html $poznamkaEditor}

<form method="post">
   <div class="container">
      <div class="row py-2">
         <div class="col-12 mb-3 mb-sm-0">
            <div class="form-floating">
               <input type="text" name="poznamka_nazev" id="poznamka_nazev" class="form-control" n:attr="value: $poznamka?->nazev" maxlength="160" required>
               <label for="poznamka_nazev">{_'Název'}</label>
            </div>
         </div>
      </div>

      <div class="row d-flex justify-content-between py-2">
         <div class="col-12">
            {$poznamkaEditor}
         </div>
      </div>

      <div class="row d-flex justify-content-center fixed-bottom">
         <div class="d-flex col-md-8 bg-white border border-light-gray radius-card shaddow-light shaddow-hover gap-3 my-2 p-2">
            <div class="flex-grow-1 row d-flex justify-content-center">
               <input type="hidden" name="id_poznamka" value="{$poznamka->id}" n:if="$poznamka">
               <div class="col-auto">
                  <button type="submit" class="btn btn-primary shaddow-hover" name="btnUlozitPoznamku"><i class="bi bi-save me-1"></i>{_'Uložit'}</button>
               </div>
               <div class="col-auto">
                  <button type="submit" class="btn btn-outline-secondary shaddow-hover" name="btnSmazatPoznamku" data-confirm="{_'Opravdu chcete smazat poznámku harmonogramu?'}"><i class="bi bi-x-circle me-1"></i>{_'Smazat'}</button>
               </div>
            </div>
         </div>
      </div>
   </div>
</form>