<?php namespace app\front\eventy\detail\modaly\harmonogram\poznamky;

use app\system\component\Templater;
use app\system\model\harmonogram\HarmonogramPoznamkaRow;
use app\system\model\harmonogram\HarmonogramPoznamky;
use app\system\modul\modal\AjaxModal;
use app\system\tiny\TinyEditor;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON>. Date: 28.06.2024 */
class HarmonogramPoznamkaEditModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Správa poznámky';
   }

   public function prepareAjaxData() :void {
      $this->poznamka = HarmonogramPoznamky::get((int)$_POST['slug']);
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'poznamka' => $this->poznamka,
         'poznamkaEditor' => new Html(
            TinyEditor::init()
               ->setInputName('poznamka_text')
               ->setContent($this->poznamka?->text)
               ->render()
         ),
      ]);
   }

   protected ?HarmonogramPoznamkaRow $poznamka = null;
}