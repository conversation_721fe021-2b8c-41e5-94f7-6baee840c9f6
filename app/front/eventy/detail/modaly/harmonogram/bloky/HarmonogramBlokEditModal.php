<?php namespace app\front\eventy\detail\modaly\harmonogram\bloky;

use app\front\controllers\availability\AvailabilityCheckerType;
use app\system\application\ApplicationVersion;
use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\model\harmonogram\HarmonogramBlokRow;
use app\system\model\harmonogram\HarmonogramBloky;
use app\system\model\organizace\mistnosti\Mistnosti;
use app\system\model\organizace\personal\Personal;
use app\system\model\organizace\personal\prirazeni\component\PersonalAssignmentComponent;
use app\system\modul\modal\AjaxModal;
use app\system\tiny\TinyEditor;
use Latte\Runtime\Html;
use Nette\Utils\Random;

/** Created by <PERSON>lip Pavlas. Date: 14.06.2024 */
class HarmonogramBlokEditModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Správa bloku';
   }

   public function prepareAjaxData() :void {
      $this->blok = HarmonogramBloky::get((int)$_POST['slug']);
      $this->templateName = FrontApplicationEnvironment::get()->versionType->isVenue()
         ? 'venue'
         : 'vendor';
   }

   public function prepareModal(Templater $templater) {
      $id_organizace = FrontApplicationEnvironment::get()->id_organizace;
      $appVersion = ApplicationVersion::from($this->blok->version);

      $templater->addData([
         'blok' => $this->blok,
         'event' => $appVersion->getEvent($this->blok->id_event),
         'version' => $appVersion,
         'personal' => Personal::getByMisto($id_organizace),
         'mistnosti' => Mistnosti::getByMisto($id_organizace),
         'checkerMode' => AvailabilityCheckerType::HARMONOGRAM_BLOK,
         'idModal' => $this->getIdModal(),
         'randomID' => $randomID = Random::generate(),
         'poznamka' => new Html(TinyEditor::init()
            ->setInputName('popis')
            ->setId(sprintf('popis_%s', $randomID))
            ->setContent($this->blok->popis)
            ->setPlaceholder('Zde můžete doplnit informace, které chcete zobrazit zákazníkovi v Klientském portále')
            ->setHeightPX(350)
            ->render()
         ),
         'poznamkaInterni' => new Html(TinyEditor::init()
            ->setInputName('popis_interni')
            ->setId(sprintf('popis_interni_%s', $randomID))
            ->setContent($this->blok->popis_interni)
            ->setPlaceholder('Prostor pro vaše interní poznámky, které se váží k tomuto konkrétnímu bloku.')
            ->setHeightPX(350)
            ->render()
         ),
         'assignmentIcons' => PersonalAssignmentComponent::getForEntity(
            $this->blok,
            FrontApplicationEnvironment::get()->personal,
         )->render(),
      ]);
   }

   protected ?HarmonogramBlokRow $blok = null;
}