{varType ?app\system\model\harmonogram\HarmonogramBlokRow $blok}
{varType app\system\model\organizace\mistnosti\MistnostRow[] $mistnosti}
{varType string $randomID}

{layout 'modal.latte'}

{block location}
   <div class="form-group">
      <label for="select-mistnosti-harmonogram-{$randomID}">{_'Prostor'}/{_'Místnost'}</label>
      <p class="d-inline-block ml-1 mb-1"></p>
      <div class="input-group" data-target-input="nearest">
         <select id="select-mistnosti-harmonogram-{$randomID}" class="form-select-sm" multiple="multiple"
                 name="mistnost[]">
            <option></option>
            {foreach $mistnosti as $mistnost}
               <option n:attr="selected: (isset($blok) && array_key_exists($mistnost->id_mistnost, $blok->getMistnosti()))"
                    value="{$mistnost->id_mistnost}">
                  {$mistnost->nazev}
               </option>
            {/foreach}
         </select>
      </div>
   </div>
{/block}