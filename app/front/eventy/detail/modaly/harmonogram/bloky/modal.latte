{varType ?app\system\model\harmonogram\HarmonogramBlokRow $blok}
{varType app\system\model\event\BaseEventRow $event}
{varType app\system\application\ApplicationVersion $version}
{varType ?Latte\Runtime\Html $terminChecker}
{varType Latte\Runtime\Html $poznamka}
{varType Latte\Runtime\Html $poznamkaInterni}
{varType Latte\Runtime\Html $assignmentIcons}
{varType app\front\controllers\availability\AvailabilityCheckerType $checkerMode}
{varType string $randomID}
{varType string $idModal}

<form method="post" data-formular="{$idModal}" class="js-edit-blok">
   <div class="container">
      <div class="js-termin-checker-block" n:if="$version->isVenue()">{$terminChecker ?? ''}</div>
      <div class="row gap-2">
         <div class="col-lg-7 col-xl-7">
            <div class="p-3">
               <div class="row pb-3">
                  <div class="col-12">
                     <div class="form-floating">
                        <input type="text" name="blokName" id="blok_nazev_{$randomID}"
                               class="form-control" n:attr="value: $blok?->nazev" maxlength="160" required>
                        <label for="blok_nazev_{$randomID}">{_'Název bloku'}</label>
                     </div>
                  </div>
               </div>

               <div class="row">
                  <div class="col-md d-sm-inline gap-2 form-group">
                     <label for="blok_date_start_{$randomID}">{_'Datum'}</label>
                     <div class="input-group date js-datepicker" id="blok_date_start_{$randomID}"
                          data-target-input="nearest">
                        <input type="text" class="form-control datetimepicker-input"
                               data-target="#blok_date_start_{$randomID}" id="blok_date_start_input_{$randomID}"
                               name="date_start" value="{$blok->date_start?? $event->date_start|date: 'j.n.Y'}"
                               required>
                        <div class="input-group-text" data-target="#blok_date_start_{$randomID}"
                             data-toggle="datetimepicker">
                           <i class="bi bi-calendar2-event"></i>
                        </div>
                     </div>
                  </div>
                  <div class="col-md col-6 form-group">
                     <label for="blok_time_start_{$randomID}">{_'Začátek'}</label>
                     <div class="input-group date js-timepicker" id="blok_time_start_{$randomID}"
                          data-target-input="nearest">
                        <input type="text" class="form-control datetimepicker-input js-form-change"
                               data-target="#blok_time_start_{$randomID}"
                               name="blok_start_time" value="{$blok->date_start?? $event->date_start|date: 'H:i'}"
                               required>
                        <div class="input-group-text" data-target="#blok_time_start_{$randomID}"
                             data-toggle="datetimepicker">
                           <i class="bi bi-clock"></i>
                        </div>
                     </div>
                  </div>
                  <div class="col-md col-6 form-group">
                     <label for="blok_time_end_{$randomID}">{_'Konec:'}</label>
                     <div class="input-group date js-timepicker" id="blok_time_end_{$randomID}"
                          data-target-input="nearest">
                        <input type="text" class="form-control datetimepicker-input js-form-change"
                               data-target="#blok_time_end_{$randomID}"
                               name="blok_end_time" value="{$blok->date_end?? $event->date_end|date: 'H:i'}">
                        <div class="input-group-text" data-target="#blok_time_end_{$randomID}"
                             data-toggle="datetimepicker">
                           <i class="bi bi-clock"></i>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="row my-3">
                  <div class="input-group col-12">
                     <label for="select-osoby-{$randomID}">{_'odpovědnost'}</label>
                     {$assignmentIcons}
                  </div>
               </div>
            </div>
         </div>
         <div class="vr p-0 mx-1 d-none d-lg-block"></div>
         <div class=" col-md col-xl">
            <div class="py-3">
               <div class="col-12 d-flex d-lg-block justify-content-center">
                  <div class="form-group col-auto">
                     <div class="input-group">
                        <div class="btn-group" data-radioswitch="isInterniBlok[{$blok->id ?? 'new'}]">
                           <a class="btn btn-primary btn-sm notActive" data-value="false">{_'Klientský'}</a>
                           <input type="hidden" name="isInterniBlok[{$blok->id ?? 'new'}]"
                                  id="isInterniBlok{$blok->id ?? 'new'}"
                                  value="{$blok?->isInterniAsText() ?? 'true'}">
                           <a class="btn btn-primary btn-sm notActive" data-value="true">{_'Interní'}</a>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="col-12 my-3 form-group">
                  <label for="blok_pocet_hostu" class="ms-2">{_'Počet hostů'}
                     <span id="blok_pocet_hostu_{$randomID}-display">1</span>
                  </label>
                  <input type="range" name="pocet_hostu" class="form-range" n:attr="value: $blok?->pocet_hostu"
                         id="blok_pocet_hostu_{$randomID}" min="0" max="{$event->getPocetHostu()}" step="1" value="0">
               </div>
               <div class="col-12">
                  {block location}{/block}
               </div>
            </div>
         </div>
      </div>

      <div class="row d-flex justify-content-between py-2">
         <div class="col-12">
            <div class="form-group">
               <label for="popis_{$randomID}">{_'Poznámka pro zákazníka'}</label>
               {$poznamka}
            </div>
         </div>
      </div>

      <div class="row d-flex justify-content-between py-2">
         <div class="col-12">
            <div class="form-group">
               <label for="popis_interni_{$randomID}">{_'Interní poznámka'}</label>
               {$poznamkaInterni}
            </div>
         </div>
      </div>

      <div class="row d-flex justify-content-center fixed-bottom">
         <div class="d-flex col-md-8 bg-white border border-light-gray radius-card shaddow-light shaddow-hover gap-3 my-2 p-2">
            <div class="flex-grow-1 row d-flex justify-content-center">
               <input type="hidden" name="id_blok" value="{$blok->id}" n:if="$blok">
               <div class="col-auto">
                  <button type="submit" class="btn btn-primary shaddow-hover" name="btnUlozitBlok"><i class="bi bi-save me-1"></i>{_'Uložit'}</button>
               </div>
               <div class="col-auto">
                  <button type="submit" class="btn btn-outline-secondary shaddow-hover" name="btnSmazatBlok" data-confirm="{_'Opravdu chcete smazat blok harmonogramu?'}"><i class="bi bi-x-circle me-1"></i>{_'Smazat'}</button>
               </div>
            </div>
         </div>
      </div>
   </div>
</form>

<script>
   $(function() {
      const form = $('form[data-formular=' + {$idModal} + ']');
      const dateStartInput = $('#blok_date_start_input_' + {$randomID});

      $('#select-osoby-' + {$randomID}).select2({
         dropdownParent: $('#' + {$idModal}),
         placeholder: "{_'Vyber osobu'}",
         delay: '350ms',
         width: '600px',
      })

      {if $version->isVenue()}
      const checkerContainer = form.find('div.js-termin-checker-block');

      window.qvampSystem.availabilityChecker.initContainer({
         container: checkerContainer[0],
         dateStart: dateStartInput[0],
         entity: {
            entityID: Number({$event->getID()}),
            mode: String({$checkerMode->value}),
         },
      });
      {/if}

       document.getElementById('blok_pocet_hostu_' + {$randomID}).addEventListener('input', function() {
           document.getElementById('blok_pocet_hostu_' + {$randomID} + '-display').innerText = this.value;
       });

       document.getElementById('blok_pocet_hostu_' + {$randomID} + '-display').innerText = document.getElementById('blok_pocet_hostu_' + {$randomID}).value;
   });
</script>

<script>
   $(function(){
      let mySelect = $('#select-mistnosti-harmonogram-' + {$randomID});
      let myModal = mySelect.closest('.js-beatmodal');
      let options = {
         placeholder: "{_'Vyber místnost'}",
         closeOnSelect: true,
         delay: '350ms',
         width: '100%',
      };

      if(myModal.length > 0)
         options['dropdownParent'] = myModal;

      mySelect.select2(options);
   })
</script>