<?php namespace app\front\eventy\detail\modaly\harmonogram\bloky;

use app\front\availability\AvailabilityChecker;
use app\front\controllers\availability\AvailabilityCheckerType;
use app\front\organizace\model\organizace\Organizace;
use app\system\application\ApplicationVersion;
use app\system\application\FrontApplicationEnvironment;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\event\BaseEventRow;
use app\system\model\harmonogram\eventy\EditHarmonogramBlokEvent;
use app\system\model\harmonogram\HarmonogramBlokRow;
use app\system\model\harmonogram\HarmonogramBloky;
use app\system\model\organizace\mistnosti\Mistnosti;
use app\system\model\organizace\personal\Personal;
use app\system\model\organizace\personal\prirazeni\component\PersonalAssignmentComponent;
use app\system\model\organizace\personal\prirazeni\event\ChangePersonalPrirazeniEvent;
use app\system\model\organizace\personal\prirazeni\PersonalAssignmentType;
use app\system\modul\modal\Modal;
use app\system\tiny\TinyEditor;
use Latte\Runtime\Html;
use Nette\Utils\Random;

/** Created by Filip Pavlas. Date: 01.07.2024 */
class HarmonogramBlokModal extends Modal
{

   public function getTitleName() :string {
      return 'Nový blok';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUlozitBlok', function($post) {
         $editEvent = (new EditHarmonogramBlokEvent($post, $this->event));

         if(isset($post['id_blok']) && ($blok = HarmonogramBloky::get($post['id_blok'])))
            $editEvent->setHarmonogramBlok($blok);

         $editEvent->call();

         (new ChangePersonalPrirazeniEvent($editEvent->getBlok(), $post['assignedPersonal'] ?? []))->call();

         FlashMessages::setSuccess('Blok harmonogramu byl uložen');
      });

      $this->isset('btnSmazatBlok', function($post) {
         if(!isset($post['id_blok']))
            return;

         HarmonogramBloky::delete(HarmonogramBloky::get($post['id_blok']));
         FlashMessages::setSuccess('Blok byl smazán');
      });
   }

   public function prepareModal(Templater $templater) :void {
      $id_organizace = FrontApplicationEnvironment::get()->id_organizace;

      $templater->addData([
         'blok' => $this->blok,
         'event' => $this->event,
         'version' => ApplicationVersion::getFromEvent($this->event),
         'personal' => Personal::getByMisto($id_organizace),
         'mistnosti' => Mistnosti::getByMisto($id_organizace),
         'checkerMode' => AvailabilityCheckerType::HARMONOGRAM_BLOK,
         'terminChecker' => (new AvailabilityChecker(Organizace::getMisto($id_organizace)))
            ->setDatum($this->event->date_start, $this->event->isVicedenni() ? $this->event->date_end : null)
            ->processCheck()
            ->renderComponent(),
         'idModal' => $this->getIdModal(),
         'randomID' => $randomID = Random::generate(),
         'poznamka' => new Html(TinyEditor::init()
            ->setInputName('popis')
            ->setId(sprintf('popis_%s', $randomID))
            ->setPlaceholder('Zde můžete doplnit informace, které chcete zobrazit zákazníkovi v Klientském portále')
            ->setHeightPX(350)
            ->render()
         ),
         'poznamkaInterni' => new Html(TinyEditor::init()
            ->setInputName('popis_interni')
            ->setId(sprintf('popis_interni_%s', $randomID))
            ->setPlaceholder('Prostor pro vaše interní poznámky, které se váží k tomuto konkrétnímu bloku.')
            ->setHeightPX(350)
            ->render()
         ),
         'assignmentIcons' => PersonalAssignmentComponent::getForTypEntity(
            PersonalAssignmentType::HARMONOGRAM,
            Organizace::getMisto($id_organizace),
            FrontApplicationEnvironment::get()->personal,
         )->render(),
      ]);
   }

   public function setEvent(BaseEventRow $eventRow) :static {
      $this->event = $eventRow;
      $this->templateName = ApplicationVersion::getFromEvent($eventRow)->isVenue()
         ? 'venue'
         : 'vendor';

      return $this;
   }

   protected ?HarmonogramBlokRow $blok = null;
   private BaseEventRow $event;
}