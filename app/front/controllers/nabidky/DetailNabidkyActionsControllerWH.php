<?php namespace app\front\controllers\nabidky;

use app\front\prilohy\kalkulace\KalkulacePrilohyComponent;
use app\system\application\ApplicationVersion;
use app\system\controller\ActionController;
use app\system\Environment;
use app\system\flash\FlashMessages;
use app\system\lay\error\Error404Page;
use app\system\model\nabidka\event\PridatKalkulacePrilohaEvent;
use app\system\model\nabidka\prilohy\KalkulacePrilohy;
use app\system\model\nabidka\smlouva\podepisujici\SmlouvaPodepisujici;
use app\system\model\organizace\kalkulace\introduction\KalkulaceIntroductionSablony;
use app\system\model\organizace\kalkulace\introduction\SaveKalkulaceIntroductionSablonaPostEvent;
use app\system\model\organizace\prilohy\kalkulace\data\OrganizacePrilohy;
use app\system\router\NewRouter;
use app\system\router\Route;
use app\system\traits\JsonAjax;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 14.05.2023 */
#[Route('^/nabidkaactions(?:-*)(?<id_nabidka>\d*)(?:/*)(?<action>\w*)$')]
class DetailNabidkyActionsControllerWH
{

   use ActionController;
   use JsonAjax;

   const ACTION_DELETEOSOBA = 'deleteosoba';
   public function action_deleteosoba() {
      if(!Environment::isAjax())
         Error404Page::show403();

      SmlouvaPodepisujici::delete($_POST['osoba']);
   }

   const ACTION_ODEBRATPRILOHU = 'odebratprilohu';
   public function action_odebratprilohu() {
      if($priloha = KalkulacePrilohy::get($_POST['priloha'], self::getIdKalkulace(), $this->environment->versionType)) {
         $organizacePriloha = OrganizacePrilohy::get($priloha->id_priloha);
         $option = [
            'id' => $organizacePriloha->id,
            'nazev' => $organizacePriloha->nazev,
         ];

         KalkulacePrilohy::delete($priloha);
         FlashMessages::setSuccess('Příloha odebrána');
      }

      $nabidka = $this->environment->versionType->getNabidka(self::getIdKalkulace());

      $this->sendAjaxResponse([
         'prilohy' => (string)KalkulacePrilohyComponent::getComponent()->setKalkulace($nabidka),
         'option' => $option ?? null
      ]);
   }

   const ACTION_PRIDATPRILOHU = 'pridatprilohu';
   public function action_pridatprilohu() {
      $nabidka = $this->environment->versionType->getNabidka(self::getIdKalkulace());

      (new PridatKalkulacePrilohaEvent($nabidka))
         ->setPrilohaTemplate(OrganizacePrilohy::get($_POST['id_template']))
         ->call();

      FlashMessages::setSuccess('Příloha byla přidána');
      $this->sendAjaxResponse(['prilohy' => (string)KalkulacePrilohyComponent::getComponent()->setKalkulace($nabidka)]);
   }

   private static function getIdKalkulace() :int {
      return NewRouter::get()->getRoute()->getParametrs()['id_nabidka'];
   }

   const string ACTION_LOADINTRODUCTION = 'loadintroduction';
   public function action_loadintroduction() :void {
      if(
         !($sablonaID = intval($this->getRequestBody()['id_template'] ?? 0))
         || !($sablona = KalkulaceIntroductionSablony::get($sablonaID))
      ) {
         FlashMessages::setError('Šablonu se nepodařilo načíst');
         $this->sendAjaxResponse(['success' => false]);
      }

      $this->sendAjaxResponse([
         'success' => true,
         'text' => $sablona->text,
      ]);
   }

   const string ACTION_SAVEINTRODUCTION = 'saveintroduction';
   public function action_saveintroduction() :void {
      if(
         !($sablonaID = intval($this->getRequestBody()['id_template'] ?? 0))
         || !($sablona = KalkulaceIntroductionSablony::get($sablonaID))
      ) {
         FlashMessages::setError('Šablonu se nepodařilo načíst');
         $this->sendAjaxResponse(['success' => false]);
      }

      (new SaveKalkulaceIntroductionSablonaPostEvent(
         $sablona,
         [
            'introduction' => $this->getRequestBody()['content'],
            'nazev_sablona' => $sablona->title,
            'id_jazyk_sablona' => null
         ]
      ))->call();

      FlashMessages::setSuccess('Šablona byla uložena');

      $this->sendAjaxResponse([
         'success' => true,
      ]);
   }

   const string ACTION_CREATEINTRODUCTION = 'createintroduction';
   public function action_createintroduction() :void {
      if(!isset($this->getRequestBody()['content'])){
         FlashMessages::setError('Šablonu se nepodařilo uložit');
         $this->sendAjaxResponse(['success' => false]);
      }

      $version = ApplicationVersion::from($this->getRequestBody()['version']);
      $nabidka = $version->getNabidka(self::getIdKalkulace());

      $createSablonaEvent = SaveKalkulaceIntroductionSablonaPostEvent::createFromPost(
         $nabidka->getIdOrganizace(),
         [
            'introduction' => $this->getRequestBody()['content'],
            'nazev_sablona' => $this->getRequestBody()['name'],
            'id_jazyk_sablona' => $nabidka->id_jazyk,
         ]
      )->call();

      FlashMessages::setSuccess('Šablona byla uložena');

      $this->sendAjaxResponse([
         'success' => true,
         'text' => $createSablonaEvent->get()->text,
         'title' => $createSablonaEvent->get()->title,
         'id_template' => $createSablonaEvent->get()->id,
      ]);
   }
}