<?php namespace app\front\controllers\nabidky;

use app\front\prilohy\kalkulace\KalkulacePrilohyComponent;
use app\system\controller\ActionController;
use app\system\Environment;
use app\system\flash\FlashMessages;
use app\system\lay\error\Error404Page;
use app\system\model\nabidka\event\PridatKalkulacePrilohaEvent;
use app\system\model\nabidka\prilohy\KalkulacePrilohy;
use app\system\model\nabidka\smlouva\podepisujici\SmlouvaPodepisujici;
use app\system\model\organizace\prilohy\kalkulace\data\OrganizacePrilohy;
use app\system\router\NewRouter;
use app\system\router\Route;
use app\system\traits\JsonAjax;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 14.05.2023 */
#[Route('^/nabidkaactions(?:-*)(?<id_nabidka>\d*)(?:/*)(?<action>\w*)$')]
class DetailNabidkyActionsControllerWH
{

   use ActionController;
   use JsonAjax;

   const ACTION_DELETEOSOBA = 'deleteosoba';
   public function action_deleteosoba() {
      if(!Environment::isAjax())
         Error404Page::show403();

      SmlouvaPodepisujici::delete($_POST['osoba']);
   }

   const ACTION_ODEBRATPRILOHU = 'odebratprilohu';
   public function action_odebratprilohu() {
      if($priloha = KalkulacePrilohy::get($_POST['priloha'], self::getIdKalkulace(), $this->environment->versionType)) {
         $organizacePriloha = OrganizacePrilohy::get($priloha->id_priloha);
         $option = [
            'id' => $organizacePriloha->id,
            'nazev' => $organizacePriloha->nazev,
         ];

         KalkulacePrilohy::delete($priloha);
         FlashMessages::setSuccess('Příloha odebrána');
      }

      $nabidka = $this->environment->versionType->getNabidka(self::getIdKalkulace());

      $this->sendAjaxResponse([
         'prilohy' => (string)KalkulacePrilohyComponent::getComponent()->setKalkulace($nabidka),
         'option' => $option ?? null
      ]);
   }

   const ACTION_PRIDATPRILOHU = 'pridatprilohu';
   public function action_pridatprilohu() {
      $nabidka = $this->environment->versionType->getNabidka(self::getIdKalkulace());

      (new PridatKalkulacePrilohaEvent($nabidka))
         ->setPrilohaTemplate(OrganizacePrilohy::get($_POST['id_template']))
         ->call();

      FlashMessages::setSuccess('Příloha byla přidána');
      $this->sendAjaxResponse(['prilohy' => (string)KalkulacePrilohyComponent::getComponent()->setKalkulace($nabidka)]);
   }

   private static function getIdKalkulace() :int {
      return NewRouter::get()->getRoute()->getParametrs()['id_nabidka'];
   }
}