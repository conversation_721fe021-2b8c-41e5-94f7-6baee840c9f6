<?php namespace app\front\controllers\nabidky;

use app\front\nabidky\detail\panely\nahled\NahledKlientskeKalkulacePage;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\Controller;
use app\system\controller\exceptions\page403\Exception403;
use app\system\controller\exceptions\page404\Exception404;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\model\organizace\personal\prirazeni\IEntityPrirazenyPersonal;
use app\system\router\NewRouter;
use app\system\router\Route;

/** Created by Filip Pavlas. Date: 24.04.2025 */
#[Route([
   'cs' => '^/nabidka-(?<id_nabidky>\d*)-nahled$',
   'en' => '^/offer-(?<id_nabidky>\d*)-preview$',
   'pl' => '^/oferta-(?<id_nabidky>\d*)-zapowiedz$',
   'hu' => '^/ajanlat-(?<id_nabidky>\d*)-elonezet$',
   'de' => '^/angebot-(?<id_nabidky>\d*)-vorschau$',
])]
class NahledKalkulaceController
{

   use Controller;

   public function call() :void {
      $id = NewRouter::get()->getRoute()->getParametrs()['id_nabidky'];
      $this->env = FrontApplicationEnvironment::get();

      $nabidka = $this->env->versionType
         ->getNabidka($id);

      if(!$nabidka || $nabidka->getIdOrganizace() !== $this->env->id_organizace)
         throw new Exception404();

      $this->checkPermissions($nabidka);

      NahledKlientskeKalkulacePage::getComponent()
         ->setKalkulace($nabidka)
         ->echo();
   }

   public function checkPermissions(IEntityPrirazenyPersonal $entity) :void {
      if($this->env->uzivatel->isMajitel || $this->env->uzivatel->isSpravce)
         return;

      if(!$this->env->personal->hasAccessRight(PersonalAccessRightsEnum::KALKULACE))
         throw new Exception403('Chybějící právo pro zobrazení');

      if($this->env->personal->getAccessRightValue(PersonalAccessRightsEnum::KALKULACE) === 1)
         return;

      if(!$entity->getPrirazenyPersonal($this->env->personal->id_personal))
         throw new Exception403('Chybějící přístup');
   }

   private FrontApplicationEnvironment $env;
}