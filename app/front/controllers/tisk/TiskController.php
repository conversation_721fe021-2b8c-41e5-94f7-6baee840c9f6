<?php namespace app\front\controllers\tisk;

use app\front\tisk\TiskovaSada;
use app\system\controller\Controller;
use app\system\Environment;
use app\system\router\Route;
use app\system\traits\JsonAjax;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 11.01.2023 */
#[Route('^/printer$')]
class TiskController
{

   use Controller;
   use JsonAjax;

   public function call() :void {
      if(!Environment::isAjax() || !isset($_POST['class']))
         $this->exit404();

      $sada = TiskovaSada::findSada($_POST['class'], $_POST['input'], $_POST['addedInput']?? null);

      if($sada === null)
         $this->sendAjaxResponse([
            'error' => true,
         ]);
      else
         $this->sendAjaxResponse([
            'html' => $sada->render()
         ]);
   }
}