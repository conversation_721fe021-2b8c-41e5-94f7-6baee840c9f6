<?php namespace app\front\controllers\tisk;

use app\front\tisk\variabilni\VariabilniTiskActionEnum;
use app\front\tisk\variabilni\VariabilniTiskovaSada;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\Controller;
use app\system\Environment;
use app\system\flash\FlashMessages;
use app\system\model\tisk\variabilni\event\VariabilniTiskEventSablonaRow;
use app\system\model\tisk\variabilni\event\VariabilniTiskEventSablony;
use app\system\router\Route;
use app\system\traits\JsonAjax;

/** Created by <PERSON>lip Pavlas. Date: 03.06.2024 */
#[Route('^/variableprinter$')]
class VariabilniTiskController
{

   use Controller;
   use JsonAjax;

   public function call() :void {
      if(!Environment::isAjax())
         $this->exit404();

      $action = VariabilniTiskActionEnum::tryFrom($_POST['printAction']);
      if(!$action instanceof VariabilniTiskActionEnum)
         $this->sendAjaxResponse(['response' => 'chybná akce']);

      match($action) {
         VariabilniTiskActionEnum::PRINT => $this->variabilniTisk(),
         VariabilniTiskActionEnum::LOAD => $this->loadtisksablona(),
         VariabilniTiskActionEnum::SAVE => $this->savetisksablona(),
         VariabilniTiskActionEnum::DELETE => $this->deletetisksablona(),
         VariabilniTiskActionEnum::CLEAR => null,
      };
   }

   public function savetisksablona() :void {
      $unparsedSettings = $_POST['blokySettings'] ?? [];
      $blokySettings = [];

      if(empty($unparsedSettings)) {
         FlashMessages::setError('Prázdné nastavení šablony');
         $this->sendAjaxResponse(['response' => 'empty settings blok array']);
      }

      foreach($unparsedSettings['aktivni'] as $aktivniBlokID){
         $blokySettings[$aktivniBlokID] = $unparsedSettings['nastaveni'][intval($aktivniBlokID)] ?? [];
      }

      if(($sablona = VariabilniTiskEventSablony::get($_POST['id_sablona']))
         && ($_POST['sablona_name'] === '' || $_POST['sablona_name'] === $sablona->nazev)
      ) {
         $sablona->bloky = json_encode($blokySettings);
         $sablona->id_hlavicka = $_POST['id_hlavicka'];
         $sablona->id_paticka = $_POST['id_paticka'];
         $sablona->save();

         FlashMessages::setSuccess('Šablona $1 uložena', $sablona->nazev);
         $this->sendAjaxResponse(['saved' => true]);
      }

      $sablona = new VariabilniTiskEventSablonaRow();
      $sablona->nazev = $_POST['sablona_name'];
      $sablona->bloky = json_encode($blokySettings);
      $sablona->id_hlavicka = $_POST['id_hlavicka'];
      $sablona->id_paticka = $_POST['id_paticka'];
      $sablona->id_organizace = FrontApplicationEnvironment::get()->id_organizace;

      $sablona->save();

      FlashMessages::setSuccess('Šablona $1 uložena', $sablona->nazev);
      $this->sendAjaxResponse(['sablona' => ['nazev' => $sablona->nazev, 'value' => $sablona->id]]);
   }

   public function loadtisksablona() :void {
      if($_POST['id_sablona'] === '0')
         $this->sendAjaxResponse(['response' => 'Not selected template', 'reset' => true]);

      if((!$sablona = VariabilniTiskEventSablony::get($_POST['id_sablona']))
         || $sablona->id_organizace !== FrontApplicationEnvironment::get()->id_organizace )
      {
         FlashMessages::setError('Šablonu se nepodařilo načíst');
         $this->sendAjaxResponse(['response' => 'couldnt load template for print']);
      }

      $bloky = [];
      $aktivniNastaveni = [];
      $blokySettings = json_decode($sablona->bloky);

      foreach($blokySettings as $blok => $nastaveniArray) {
         $bloky[] = $blok;

         if(!empty($nastaveniArray)){
            foreach($nastaveniArray as $id_nastaveni => $nastaveni)
               $aktivniNastaveni[] = sprintf('%s[%s]', $blok, $id_nastaveni);
         }
      }

      $this->sendAjaxResponse([
         'bloky' => $bloky,
         'nastaveni' => $aktivniNastaveni,
         'id_hlavicka' => $sablona->id_hlavicka,
         'id_paticka' => $sablona->id_paticka,
      ]);
   }

   public function deletetisksablona() :void {
      if((!$sablona = VariabilniTiskEventSablony::get($_POST['id_sablona']))
         || $sablona->id_organizace !== FrontApplicationEnvironment::get()->id_organizace )
      {
         FlashMessages::setError('Šablonu se nepodařilo nalézt');
         $this->sendAjaxResponse(['response' => 'couldnt find template']);
      }

      $nazevDeleted = $sablona->nazev;
      $idDeleted = $sablona->id;
      VariabilniTiskEventSablony::delete($sablona);

      FlashMessages::setSuccess('Šablona $1 smazána', $nazevDeleted);
      $this->sendAjaxResponse(['deleted' => $idDeleted]);
   }

   public function variabilniTisk() :void {
      $unparsedSettings = $_POST['blokySettings'] ?? [];

      if(!($id = ($_POST['id_event'] ?? null)) || !($event = $this->environment->versionType->getEvent($id)) || empty($unparsedSettings)){
         FlashMessages::setError('Šablonu se nepodařilo vytisknout, zkontrolujte nastavení');
         $this->sendAjaxResponse(['response' => 'couldnt print template']);
      }

      $blokySettings = [];

      foreach($unparsedSettings['aktivni'] as $aktivniBlokID){
         $blokySettings[$aktivniBlokID] = $unparsedSettings['nastaveni'][intval($aktivniBlokID)] ?? [];
      }

      $this->sendAjaxResponse([
         'printHtml' => VariabilniTiskovaSada::prepareForEvent(
            $event,
            $blokySettings,
            $_POST['hlavicka'],
            $_POST['paticka']
         )->getHtml(),
      ], true);
   }
}