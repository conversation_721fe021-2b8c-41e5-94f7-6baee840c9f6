<?php namespace app\front\controllers\api;

use app\system\controller\InternApiController;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 24.04.2025 */
#[Route('^/csrf-token-generator$')]
class ApiHandshakeController
{

   use InternApiController;

   protected function action_default() :void {
      $this->sendAjaxResponse([
         'csrf' => $this->getCsfrToken()
      ], true);
   }
}