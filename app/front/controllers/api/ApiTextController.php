<?php namespace app\front\controllers\api;

use app\front\api\modul\texty\ApiNahledTextuPage;
use app\system\controller\ActionController;
use app\system\mailer\templates\Templates;
use app\system\model\translator\Jazyky;
use app\system\router\Route;

#[Route('^/nahled-(?<action>\w*)(?:-*)(?<slug>\w*)$')]
class ApiTextController
{

   use ActionController;

   public const string ACTION_TEXT = 'text';
   public function action_text() :void {
      ApiNahledTextuPage::getComponent()->setIdText($_GET['template'])->echo();
   }

   public const string ACTION_EMAIL = 'email';
   public function action_email() :void {
      $jazyk = Jazyky::tryFrom($_GET['lang']);
      $template = Templates::tryFrom($_GET['template']);

      if(!$template || !$jazyk)
         $this->exit404();

      echo $template
         ->getInstance()
         ->renderRaw($jazyk);
   }

   public function action_sablona() {
      $jazyk = Jazyky::tryFrom($_GET['lang']);
      $template = Templates::tryFrom($_GET['template']);
      $organizaceID = intval($_GET['venue']);

      if(!$template || !$jazyk || !$organizaceID)
         $this->exit404();

      echo $template
         ->getInstance()
         ->renderRaw($jazyk, $organizaceID);
   }
}