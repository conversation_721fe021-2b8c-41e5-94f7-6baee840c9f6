<?php namespace app\front\controllers\api;

use app\front\organizace\model\organizace\Organizace;
use app\katalog\organizace\OrganizationPoptavkaController;
use app\system\controller\ActionController;
use app\system\lay\error\Error404Page;
use app\system\Redirect;
use app\system\router\NewRouter;
use app\system\router\Route;
use app\system\SystemVersion;

#[Route('^/(?<venue_code>[a-z-0-9]*)/poptavka$')]
class ApiPoptavkaController
{

   use ActionController;

   public function call() :void {
      $odkaz = NewRouter::get()->getRoute()->getParametrs()['venue_code'];

      if($misto = Organizace::getByPortalOdkaz($odkaz))
         Redirect::to(OrganizationPoptavkaController::getUrl($misto->portal_odkaz), SystemVersion::CLIENT);
      else
         Error404Page::show();

   }
}