<?php namespace app\front\controllers\texty;

use app\system\controller\ActionController;
use app\system\flash\FlashMessages;
use app\system\helpers\text\blok\TextBlokyComponent;
use app\system\mailer\data\MailsModel;
use app\system\model\texty\sablony\SablonyPodtypuTextu;
use app\system\model\texty\sablony\SablonyTexty;
use app\system\model\texty\Texty;
use app\system\model\texty\TextyBloky;
use app\system\router\Route;
use app\system\traits\JsonAjax;

#[Route('^/text(?:-*)(?<action>\w*)(-(?<slug>\w*))?$')]
class TextyActionController
{

   use ActionController;
   use JsonAjax;

   public const ACTION_DELETE_BLOCK = 'deleteBlok';
   public const ACTION_MOVE_BLOCK = 'moveBlok';

   public function action_moveBlok() {
      Texty::moveBlok($_POST);
      $bloky = (string)TextBlokyComponent::getComponent()->setIdText((int)$_POST['id_text'])->setType(TextBlokyComponent::TEXT);

      $this->sendAjaxResponse([$bloky], true);
   }

   public function action_deleteBlok() {
      Texty::deleteBlok($_POST);
      TextyBloky::deleteBlok($_POST['id_blok']);
      $bloky = (string)TextBlokyComponent::getComponent()->setIdText((int)$_POST['id_text'])->setType(TextBlokyComponent::TEXT);

      $this->sendAjaxResponse([$bloky], true);
   }

   public function action_podtypy() {
      $podtypy = SablonyPodtypuTextu::getByTyp((int)$_POST['id_typ']);
      if($podtypy)
         $texty = SablonyTexty::getByPodtyp($podtypy[0]->id_podtyp);

      $this->sendAjaxResponse(['podtypy' => $podtypy, 'texty' => $texty?? '']);
   }

   public function action_najitTexty() {
      $texty = SablonyTexty::getByPodtyp((int)$_POST['id_podtyp']);

      $this->sendAjaxResponse(['texty' => $texty]);
   }

   public const string ACTION_DELETE_ATTACHMENT = 'deleteAttachment';
   public function action_deleteAttachment() {
      $post = $this->getRequestBody();

      if(
         !($mailID = intval($post['mailID']))
         || !($attachmentID = intval($post['attachmentID']))
         || !($mail = MailsModel::get($mailID))
         || !($attachment = MailsModel::getAttachment($attachmentID))
         || $mail->id_organizace !== $this->environment->id_organizace
      ) {
         FlashMessages::setError('Tato příloha nelze smazat');
         $this->sendAjaxResponse([
            'success' => false
         ]);
      }

      $attachment->delete();
      $this->sendAjaxResponse([
         'success' => true
      ]);
   }
}