<?php namespace app\front\controllers\texty;

use app\libs\deepl\DeepLApi;
use app\system\controller\Controller;
use app\system\controller\InternApiController;
use app\system\model\translator\Jazyky;
use app\system\router\Route;
use app\system\traits\JsonAjax;

#[Route('^/~deepl-automatic-translate$')]
class DeeplAutomaticTranslateController
{

   use InternApiController;

   protected function action_default() :void {
      $results = [];

      $body = $this->getRequestBody();

      $mainText = $body['text'];
      $mainLanguage = Jazyky::from($body['mainLanguage']);
      $translations = $body['langsToTranslate'];

      foreach ($translations as $translation) {
         $secondaryLanguage = Jazyky::from($translation);
         $translatedText = DeepLApi::translate($mainText, $secondaryLanguage, $mainLanguage);

         $results[] = [
            'preklad' => $translatedText,
            'jazyk' => $secondaryLanguage
         ];
      }

      $this->sendAjaxResponse($results);
   }
}