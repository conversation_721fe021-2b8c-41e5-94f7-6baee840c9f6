<?php namespace app\front\controllers\tymy;

use app\system\controller\ActionController;
use app\system\model\tymy\TymyPersonal;
use app\system\model\tymy\TymyPersonalRow;
use app\system\router\Route;
use app\system\traits\JsonAjax;

#[Route('^/tymy(?:-*)(?<action>\w*)$')]
/** Created by <PERSON>. Date: 09.02.2023 */
class TymyActionController
{

   use ActionController;
   use JsonAjax;

   public function action_addpersonal() {
      $personal = new TymyPersonalRow;
      $personal->id_tym = $_POST['id_tymu'];
      $personal->id_personal = $_POST['id_personalu'];

      TymyPersonal::insertWorkers($personal);

      $this->sendAjaxResponse();
   }

   public function action_removepersonal() {
      TymyPersonal::deleteWorker($_POST['id_personalu'], $_POST['id_tymu']);

      $this->sendAjaxResponse();
   }
}