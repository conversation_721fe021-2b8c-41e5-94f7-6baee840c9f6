<?php namespace app\front\controllers\poptavky;

use app\front\poptavky\detail\BaseDetailPoptavkaPage;
use app\front\poptavky\detail\VendorDetailPoptavkaPage;
use app\front\poptavky\detail\VenueDetailPoptavkaPage;
use app\system\application\ApplicationVersion;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\Controller;
use app\system\controller\exceptions\page403\Exception403;
use app\system\controller\exceptions\page404\Exception404;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\model\poptavky\BasePoptavkaRow;
use app\system\router\NewRouter;
use app\system\router\Route;

#[Route([
   'cs' => '^/detail-poptavka(?:-*)(?<slug>\d*)$',
   'en' => '^/detail-request(?:-*)(?<slug>\d*)$',
   'pl' => '^/szczegoly-zapytania(?:-*)(?<slug>\d*)$',
   'hu' => '^/erdeklodes-reszletei(?:-*)(?<slug>\d*)$',
])]
class DetailPoptavkyController
{

   use Controller;

   public function call() :void {
      $id = intval(NewRouter::get()->getRoute()->getParametrs()['slug'] ?: 0);
      $this->env = FrontApplicationEnvironment::get();

      $poptavka = $this->env->versionType
         ->getPoptavka($id);

      if(!$poptavka || $poptavka->getIdOrganizace() !== $this->env->id_organizace)
         throw new Exception404();

      $this->checkPermissions($poptavka);

      $this
         ->getDetailPoptavkyPage()
         ->setEnvironment($this->env)
         ->setPoptavka($poptavka)
         ->echo();
   }

   private function checkPermissions(BasePoptavkaRow $poptavkaRow) :void {
      if($this->env->uzivatel->isMajitel || $this->env->uzivatel->isSpravce)
         return;

      if(!$this->env->personal->hasAccessRight(PersonalAccessRightsEnum::POPTAVKY))
         throw new Exception403('Chybějící právo pro zobrazení');

      if($this->env->personal->getAccessRightValue(PersonalAccessRightsEnum::POPTAVKY) === 1)
         return;

      if(!$poptavkaRow->getPrirazenyPersonal($this->env->personal->id_personal))
         throw new Exception403('Chybějící přístup');
   }

   private function getDetailPoptavkyPage() :?BaseDetailPoptavkaPage {
      return match ($this->env->versionType) {
         ApplicationVersion::VENUE => VenueDetailPoptavkaPage::getComponent(),
         ApplicationVersion::VENDOR => VendorDetailPoptavkaPage::getComponent(),
         default => null
      };
   }

   protected FrontApplicationEnvironment $env;
}