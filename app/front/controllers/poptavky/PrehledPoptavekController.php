<?php namespace app\front\controllers\poptavky;

use app\front\organizace\model\organizace\Organizace;
use app\front\poptavky\prehled\BaseLeadyPage;
use app\front\poptavky\prehled\VendorLeadyPage;
use app\front\poptavky\prehled\VenueLeadyPage;
use app\system\application\ApplicationVersion;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\Controller;
use app\system\controller\exceptions\page403\Exception403;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\router\Route;

#[Route([
   'cs' => '^/prehled-poptavek$',
   'en' => '^/requests-list$',
   'pl' => '^/przeglad-zapytania$',
   'hu' => '^/attekintes-erdeklodes$',
])]
class PrehledPoptavekController
{

   use Controller;

   public function call() :void {
      $this->env = FrontApplicationEnvironment::get();
      $this->checkPermissions();

      $this
         ->getSeznamPoptavekPage()
         ->setOrganizace(Organizace::getMisto($this->env->id_organizace))
         ->echo();
   }

   private function checkPermissions() :void {
      if($this->env->uzivatel->isMajitel || $this->env->uzivatel->isSpravce)
         return;

      if($this->env->personal->hasAccessRight(PersonalAccessRightsEnum::POPTAVKY))
         return;

      throw new Exception403('Chybějící právo pro zobrazení');
   }

   private function getSeznamPoptavekPage() :?BaseLeadyPage {
      return match ($this->env->versionType) {
         ApplicationVersion::VENUE => VenueLeadyPage::getComponent(),
         ApplicationVersion::VENDOR => VendorLeadyPage::getComponent(),
         default => null
      };
   }

   protected FrontApplicationEnvironment $env;
}