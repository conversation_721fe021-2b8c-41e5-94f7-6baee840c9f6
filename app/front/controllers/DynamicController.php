<?php namespace app\front\controllers;

use app\front\organizace\model\organizace\Organizace;
use app\system\controller\ActionController;
use app\system\Environment;
use app\system\lay\error\Error404Page;
use app\system\model\calendar\Calendar;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\modul\modal\AjaxModal;
use app\system\modul\panels\Panel;
use app\system\router\Route;
use app\system\table\DynamicTable;
use app\system\table\DynamicTableRequest;
use app\system\traits\JsonAjax;
use Dibi\DateTime;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 05.06.2022 */

#[Route('^/dynamic(?:/*)(?<action>\w*)$')]
class DynamicController
{

   use ActionController;
   use JsonAjax;

   protected function addProperties() :void {
      if(!(Environment::isAjax() || $_SERVER['REQUEST_METHOD'] === 'POST'))
         Error404Page::show();
   }

   public function action_modal() {
      $cl = AjaxModal::decodeClass($_POST['cls']);

      if(class_exists($cl)){
         /** @var AjaxModal $instance */
         $instance = $cl::init();
         $instance->sendContent();
      }
   }

   public function action_table() {
      $class = gzinflate(urldecode($_GET['class']));

      $request = new DynamicTableRequest();
      $request->setOrder($_POST['order']);
      $request->setColumns($_POST['columns']);
      $request->setLimits($_POST['start'], $_POST['length']);
      if(isset($_POST['custom']))
         $request->setCustom($_POST['custom']);

      /** @var DynamicTable $source */
      $source = new $class;
      $source->prepareQuery($request);
      $source->response();
   }

   public function action_panel() {
      $instance = Panel::init(Panel::decodeClass($_POST['class']));

      if(!empty($_POST['parameters']))
         foreach($_POST['parameters'] as $key => $value)
            $instance->addParameter($key, $value);

      $instance->getLazyContent();
   }

   public function action_calendar() {
      $timeZone = $_POST['timeZone'];
      $from = new DateTime(sprintf('%s %s', $_POST['start'], $timeZone));
      $to = new DateTime(sprintf('%s %s', $_POST['end'], $timeZone));

      $cal = new Calendar(Organizace::getMisto($this->environment->id_organizace));

      $cal->setAccessRights(
         $this->environment->isMajitel
            ? [PersonalAccessRightsEnum::ADMIN]
            : $this->environment->personal->getAccessRights()
      );

      if($filterSet = ($_POST['filters'] ?? null))
         $cal->setFilters($filterSet);

      $this->sendSingleArrayResponse($cal->getData($from, $to));
   }
}