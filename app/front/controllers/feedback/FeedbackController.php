<?php namespace app\front\controllers\feedback;

use app\system\controller\Controller;
use app\system\model\feedback\FeedbackReporty;
use app\system\router\Route;
use app\system\traits\JsonAjax;

#[Route('^/save-feedback$')]
class FeedbackController
{

   use Controller;
   use JsonAjax;

   public function call() :void {
      FeedbackReporty::saveFromPost($_POST);

      $this->sendAjaxResponse([true], true);
   }
}