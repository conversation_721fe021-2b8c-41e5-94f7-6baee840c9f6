<?php namespace app\front\controllers\navody;

use app\front\navody\katalog\PrehledNavoduKatalogPage;
use app\front\navody\PrehledNavoduPage;
use app\front\organizace\model\organizace\Organizace;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\Controller;
use app\system\model\organizace\subscription\OrganizaceLicenceTyp;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 10.08.2022 */
#[Route([
   'cs' => '^/navody$',
   'en' => '^/guides$',
   'pl' => '^/instrukcje$',
   'hu' => '^/utasitasok$',
])]
class PrehledNavoduController
{

   use Controller;

   public function call() :void {
      $org = Organizace::getMisto(FrontApplicationEnvironment::get()->id_organizace);

      match ($org->getLicenceTyp()) {
         OrganizaceLicenceTyp::FULL => PrehledNavoduPage::echoThis(),
         OrganizaceLicenceTyp::ONLY_KATALOG => PrehledNavoduKatalogPage::echoThis(),
      };
   }
}