<?php namespace app\front\controllers\eventy;

use app\front\eventy\detail\panely\finance\component\PrehledFinanciComponent;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\ActionController;
use app\system\flash\FlashMessages;
use app\system\helpers\price\PriceFormatter;
use app\system\model\event\EventAktivity;
use app\system\model\event\platby\plan\data\EventPlanPlatbyData;
use app\system\model\event\platby\plan\EventPlanItem;
use app\system\model\Eventy;
use app\system\model\organizace\personal\Personal;
use app\system\model\platby\event\uhrazene\EventUhrazenePlatby;
use app\system\router\NewRouter;
use app\system\router\Route;
use app\system\traits\JsonAjax;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 17.08.2022 */
#[Route('^/event(?:-*)(?<slug>\d*)(?:/)(?<action>\w*)$')]
class DetailEventuActionsController
{

   use ActionController;
   use JsonAjax;

   protected function addProperties() :void {
      $this->id_event = NewRouter::get()->getRoute()->getParametrs()['slug'];
   }

   public function action_findscenar() {
      $platba = EventPlanPlatbyData::get($_POST['id_scenare'], $this->environment->versionType);

      $uhrazene = EventUhrazenePlatby::getForPlatbuEventu($platba->id, $this->environment->versionType);
      $item = new EventPlanItem($platba);

      foreach($uhrazene as $platbaRow)
         $item->addUhrazena($platbaRow);

      $dopocet = $item->uhrazeno
         ? ($item->suma->isPositive() ? $item->suma->subtract($item->uhrazeno) : $item->suma->add($item->uhrazeno))
         : $item->suma;

      if($negative = $dopocet->isNegative())
         $dopocet = $dopocet->multiply(-1);

      $this->sendAjaxResponse([
         'scenar' => $platba,
         'dopocet' => $dopocet->getAmount(),
         'is_negative' => $negative,
      ], true);
   }

   public const ACTION_ODSTRANIT_PLATBU = 'odstranit_platbu';
   public function action_odstranit_platbu() {
      $event = Eventy::get($this->environment->versionType, $this->id_event);
//      delete, decline
      if(($action = trim($_POST['action'])) === 'delete'){
         EventPlanPlatbyData::delete($_POST['id_platba'], $this->environment->versionType);
         FlashMessages::setSuccess('Platba byla smazána');
      }elseif($action === 'decline'){
         $platba = EventPlanPlatbyData::get($_POST['id_platba'], $this->environment->versionType);
         $platba->is_zruseno = 1;
         EventPlanPlatbyData::save($platba, $this->environment->versionType);
         FlashMessages::setSuccess('Platba byla zrušena');
      }

      $this->sendAjaxResponse([
         'financeComponent' => (string)PrehledFinanciComponent::getComponent()
            ->setEvent($event)
            ->setIsDetail($_POST['isDetail']),
         'moreActivePayments' => !empty($event->getFinance()->getNezaplacene()),
      ]);
   }

   public function action_saveukol() {
      $aktivita = EventAktivity::get($_POST['id_aktivita'], FrontApplicationEnvironment::get()->versionType);
      $aktivita->stav = $_POST['newStatus'];
      $aktivita->save(FrontApplicationEnvironment::get()->versionType);

      FlashMessages::setSuccess('Úkol $1 byl označen jako: $2', $aktivita->nazev, $aktivita->getStav()->getTranslatedTitle());
      $this->sendAjaxResponse();
   }

   public function action_findperson() {
      $id = (int)$_POST['id'];

      $arr = [];
      if($personal = Personal::get($id)) {
         $arr = (array)$personal;
         $arr['hodinova-sazba'] = PriceFormatter::getDecimalString($personal->getCenaHodina(), ',');
      }

      $this->sendAjaxResponse($arr, true);
   }

   protected int $id_event;
}