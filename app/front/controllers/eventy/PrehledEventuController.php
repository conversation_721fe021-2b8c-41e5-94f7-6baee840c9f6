<?php namespace app\front\controllers\eventy;

use app\front\eventy\prehled\PrehledEventuPage;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\Controller;
use app\system\controller\exceptions\page403\Exception403;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\router\Route;

#[Route([
   'cs' => '^/prehled-eventu$',
   'en' => '^/event-overview$',
   'pl' => '^/przeglad-wydarzen$',
   'hu' => '^/esemenyek-attekintese$',
])]
class PrehledEventuController
{

   use Controller;

   public function call() :void {
      $this->env = FrontApplicationEnvironment::get();
      $this->checkPermissions();

      PrehledEventuPage::getComponent()
         ->setEnvironment($this->env)
         ->setIdMista($this->env->id_organizace)
         ->echo();
   }

   private function checkPermissions() :void {
      if($this->env->uzivatel->isMajitel || $this->env->uzivatel->isSpravce)
         return;

      if($this->env->personal->hasAccessRight(PersonalAccessRightsEnum::EVENTY))
         return;

      throw new Exception403('Chybějící právo pro zobrazení');
   }

   protected FrontApplicationEnvironment $env;
}