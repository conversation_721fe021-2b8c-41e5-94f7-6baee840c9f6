<?php namespace app\front\controllers\eventy;

use app\front\eventy\detail\DetailEventuPage;
use app\front\eventy\detail\personal\DetailEventuPersonalPage;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\Controller;
use app\system\controller\exceptions\page403\Exception403;
use app\system\controller\exceptions\page404\Exception404;
use app\system\model\event\BaseEventRow;
use app\system\model\event\personal\EventPersonal;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\router\NewRouter;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 16.06.2022 */
#[Route('^/event(?:-*)(?<id_event>\d*)$')]
class DetailEventuController
{

   use Controller;

   public function call() :void {
      $id = NewRouter::get()->getRoute()->getParametrs()['id_event'];
      $this->env = FrontApplicationEnvironment::get();

      $event = $this->env->versionType
         ->getEvent($id);

      if(!$event || $event->getIdOrganizace() !== $this->env->id_organizace)
         throw new Exception404();

      if(
         $this->env->uzivatel->isMajitel
         || $this->env->uzivatel->isSpravce
      ){
         $this->renderDetailPage($event);
         return;
      }

      if(!$this->env->personal)
         throw new Exception404();

      if(
         ($eventAccess = $this->env->personal->getAccessRightValue(PersonalAccessRightsEnum::EVENTY))
         && ($eventAccess === 1 || $event->getPrirazenyPersonal($this->env->personal->id_personal))
      ){
         $this->renderDetailPage($event);
         return;
      }

      $smenyEventu = EventPersonal::getForEventAndPersonal($event, $this->env->personal->id_personal);

      if(!empty($smenyEventu)){
         DetailEventuPersonalPage::getComponent()
            ->setEvent($event)
            ->setSmeny($smenyEventu)
            ->setPersonal($this->env->personal)
            ->echo();
         return;
      }

      throw new Exception403('Chybějící přístup');
   }

   private function renderDetailPage(BaseEventRow $event) :void {
      DetailEventuPage::getComponent()
         ->setEnvironment($this->env)
         ->setEvent($event)
         ->echo();
   }

   protected FrontApplicationEnvironment $env;
}