<?php namespace app\front\controllers\eventy;

use app\front\tisk\variabilni\VariabilniTiskovaSada;
use app\System;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\ActionController;
use app\system\flash\FlashMessages;
use app\system\helpers\price\PriceFormatter;
use app\system\model\event\notifikace\PrirazenyPersonalEventuNotifikace;
use app\system\model\event\personal\EventPersonal;
use app\system\model\event\personal\EventPersonalRow;
use app\system\model\organizace\aktivity\aktivita\OrganizaceAktivity;
use app\system\model\organizace\aktivity\sada\OrganizaceSadaAktivit;
use app\system\model\organizace\personal\Personal;
use app\system\router\NewRouter;
use app\system\router\Route;
use app\system\traits\JsonAjax;
use DateInterval;
use dibi;
use Dibi\DateTime;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 03.04.2024 */
#[Route('^/event(?:-*)(?<id_event>\d*)(?:~)(?<action>\w*)$')]
class DetailEventuActionsWHController
{

   use ActionController;
   use JsonAjax;

   public const ACTION_PRIDAT_TYM = 'addtym';
   public function action_addtym() {
      $idEvent = intval(NewRouter::get()->getRoute()->getParametrs()['id_event']);
      $idTym = intval($_POST['id_tym'] ?? null);
      $environment = FrontApplicationEnvironment::get();

      $event = $environment->versionType->getEvent($idEvent);
      $existujiciSmeny = EventPersonal::getForEvent($event);
      $prirazenyPersonalIDS = [];
      $inserted = 0;

      $mzdaCounterFn = function(EventPersonalRow $newSmena) use($event) {
         return $newSmena->hodinovka * $event->getCasovyBlok();
      };

      foreach($existujiciSmeny as $eventPersonal)
         $prirazenyPersonalIDS[$eventPersonal->id_prirazeny_uzivatel][$eventPersonal->date->format('j.n.y')] = $eventPersonal->id_prirazeny_uzivatel;

      $smenaDate = trim($_POST['datum'] ?? '') ? new DateTime($_POST['datum']): $event->date_start;

      $startParts = trim($_POST['timeStart'] ?? '') ? explode(':', trim($_POST['timeStart'])) : [];
      $smenaStart = new DateInterval(sprintf('PT%dH%dM', $startParts[0] ?? $event->date_start->format('H'), $startParts[1] ?? $event->date_start->format('i')));

      $endParts = trim($_POST['timeEnd'] ?? '') ? explode(':', trim($_POST['timeEnd'])) : [];
      $smenaEnd = new DateInterval(sprintf('PT%dH%dM', $endParts[0] ?? $event->date_end->format('H'), $endParts[1] ?? $event->date_end->format('i')));

      dibi::begin();

      foreach(Personal::getPersonalByTeam($idTym) as $personal){
         if(isset($prirazenyPersonalIDS[$personal->id_personal][$smenaDate->format('j.n.y')])){
            FlashMessages::setWarning('Personál $1 je již přidán', $personal->getFullName());
            continue;
         }

         $newSmena = new EventPersonalRow();
         $newSmena->id_event = $event->getID();
         $newSmena->id_prirazeny_uzivatel = $personal->id_personal;

         $newSmena->date = $smenaDate;
         $newSmena->time_start = $smenaStart;
         $newSmena->time_end = $smenaEnd;

         $newSmena->hodinovka = $personal->hodinova_sazba;
         $newSmena->hruba_mzda = !$newSmena->hodinovka ? 0 : $mzdaCounterFn($newSmena);

         $newSmena->save($environment->versionType);
         $inserted++;

         if($personal->id_uzivatel !== null)
            (new PrirazenyPersonalEventuNotifikace($event))
               ->setSmena($newSmena)
               ->sendTo($personal);
      }

      dibi::commit();
      $reload = $inserted > 0;

      $this->sendAjaxResponse(['reload' => $reload], returnDataOnly: $reload);
   }

   public const ACTION_GET_PERSONAL = 'get_personal';
   public function action_get_personal() {
      $id = intval($_POST['id_personal'] ?? 0);

      if(!$id || !($personal = Personal::get($id)) || $personal->id_misto !== $this->environment->id_organizace){
         FlashMessages::setError('Chybný personál');
         $this->sendAjaxResponse();
      }

//      @TODO kontrola existující směny, warning s časem poslední směny

      $this->sendAjaxResponse([
         'hodinovaSazbaFormatted' => $personal->hodinova_sazba !== null ? PriceFormatter::getDecimalString($personal->getCenaHodina(), ',') : false,
      ], true);
   }

   const ACTION_FINDAKTIVITY = 'findaktivity';
   public function action_findaktivity() {
      $search = $_POST['search']?? null;
      $id_organizace = FrontApplicationEnvironment::get()->id_organizace;

      $query = dibi::select('CONCAT("s", s.id_sada) AS id, s.nazev AS text')
         ->from(OrganizaceSadaAktivit::TABLE, 's')
         ->where('s.id_mista = %i', $id_organizace);

      if($search && trim($search))
         $query->where('s.nazev LIKE %~like~', $search);

      $sady = $query->fetchAll();

      $queryAktivity = dibi::select('CONCAT("a", a.id_aktivita) AS id,  a.nazev AS text')
         ->from(OrganizaceAktivity::TABLE, 'a')
         ->where('a.id_mista = %i', $id_organizace);

      if($search && trim($search))
         $queryAktivity->where('a.nazev LIKE %~like~', $search);

      $aktivity = $queryAktivity->fetchAll();


      $result = [
         [
            'text' => (string)System::getTranslator()->translate('Sady aktivit'),
            'children' => $sady,
         ],
         [
            'text' => (string)System::getTranslator()->translate('Jednotlivé aktivity'),
            'children' => $aktivity,
         ]
      ];

      $this->sendAjaxResponse($result, true);
   }

   const ACTION_GENERATE_DOPLATEK = 'generate_doplatek';
   public function action_generate_doplatek() {
      $idEvent = intval(NewRouter::get()->getRoute()->getParametrs()['id_event']);
      $event = $this->environment->versionType->getEvent($idEvent);

      $this->sendAjaxResponse([
         'suma' => PriceFormatter::getDecimalString($event->getFinance()->platbyDoplatek, ','),
      ]);
   }

   const ACTION_VARIABILNITISK = 'variabilni_tisk';
   public function action_variabilni_tisk() {
      $unparsedSettings = $_POST['blokySettings'] ?? [];

      if(
         !($id = intval(NewRouter::get()->getRoute()->getParametrs()['id_event']))
         || !($event = $this->environment->versionType->getEvent($id))
         || empty($unparsedSettings)
      ){
         return;
      }

      $blokySettings = [];

      foreach($unparsedSettings['aktivni'] as $aktivniBlokID)
         $blokySettings[$aktivniBlokID] = $unparsedSettings['nastaveni'][intval($aktivniBlokID)] ?? [];

      $this->sendAjaxResponse([
         'html' => VariabilniTiskovaSada::prepareForEvent($event, $blokySettings)->getHtml(),
      ], true);
   }
}