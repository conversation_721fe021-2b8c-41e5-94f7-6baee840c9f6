<?php namespace app\front\controllers\subscription;

use app\front\subscription\neaktivni\NeplatnySubscriptionPage;
use app\front\subscription\vyber\VyberSubscriptionPage;
use app\system\controller\ActionController;
use app\system\flash\FlashMessages;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\model\organizace\subscription\event\GetPaymentLinkEvent;
use app\system\model\organizace\subscription\event\RenewSubscriptionEvent;
use app\system\model\organizace\subscription\MistoSubscription;
use app\system\model\organizace\subscription\MistoSubscriptionData;
use app\system\model\uzivatele\session\data\OrganizationLicenceData;
use app\system\Redirect;
use app\system\router\Route;
use app\system\traits\JsonAjax;
use Exception;
use Tracy\Debugger;
use Tracy\ILogger;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>zy<PERSON>. Date: 27.08.2022 */
#[Route('^/subscription(?:-*)(?<action>\w*)$')]
class SubscriptionController
{

   use ActionController;
   use JsonAjax;

   public const ACTION_EXPIRED = 'expired';
   public function action_expired() :void {
      if(MistoSubscription::get($this->environment->id_organizace)?->isValid(false)){
//         @TODO HOTFIX ideálně upravit v eventech zpracujících platbu
         if(OrganizationLicenceData::get($this->environment->id_organizace))
            OrganizationLicenceData::get($this->environment->id_organizace)->delete();

         Redirect::homepage();
      }

      if(!$this->environment->isMajitel && !$this->environment->personal->hasAccessRight(PersonalAccessRightsEnum::ADMIN)){
         NeplatnySubscriptionPage::getComponent()
            ->setEnvironment($this->environment)
            ->echo();

         exit;
      }

      VyberSubscriptionPage::getComponent()
         ->setUzivatel($this->environment->uzivatel)
         ->echo();
   }

//   Vrátí link pro první platbu přes bránu, buď vygeneruje, nebo vrátí aktivní co mohl být vytvořen ale nezaplacen
//   Používá se v subscription.es6
   public const ACTION_GET_LINK = 'getlink';
   public function action_getlink() :void {
      try{
         $this->sendAjaxResponse([
            'payLink' => GetPaymentLinkEvent::getLink($this->environment, intval($_POST['id_subscription'])),
         ]);
      }catch(Exception $e) {
         FlashMessages::setError('Chyba při vytváření platby');

         $this->sendAjaxResponse([
            'payLink' => false,
         ]);
      }
   }

//   @TODO nepoužívá se
//   Pokusí se o platbu skrz klasický subscription, pokud neexistuje následující platba, která není uhrazená nebo expirovaná,
//   pokud se nepodaří vygeneruje se nová platba jako pro nový subscription
//   Používá se v subscription.es6 - ale nikde není html class použitá
   public const ACTION_PAY_SUBSCRIPTION = 'paysubscription';
   public function action_paysubscription() :void {
      $id_subs = $_POST['id_subscription'];

      $event = RenewSubscriptionEvent::init()->setIdSubscription($id_subs);
      $event->call();

      if(!$event->getSuccess()){
         FlashMessages::setWarning('Obnovení se nepodařilo budete přesměrování na platební bránu');
//         $this->action_getlink();
         $this->sendAjaxResponse([
            'payLink' => false,
         ]);
      }else{
         FlashMessages::setSuccess('Obnovení proběhlo úspěšně');
         $this->sendAjaxResponse(['success' => true], true);
      }
   }

//   Vynuluje renew_at u subscriptionu, takže nedojde k automatické obnově
   public const ACTION_UNSUBSCRIBE = 'unsubscribe';
   public function action_unsubscribe() {
      $id_subs = $_POST['id_subscription'];
//      @TODO dodělat event, ať o této akci víme a jsem schopni targetovat marketing
      MistoSubscriptionData::nullRenewAt($id_subs);
      $this->sendAjaxResponse();
   }
}