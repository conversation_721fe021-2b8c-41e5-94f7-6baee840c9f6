<?php namespace app\front\controllers\kalendar;

use app\system\controller\Controller;
use app\system\controller\exceptions\page404\Exception404;
use app\system\Environment;
use app\system\helpers\Files;
use app\system\model\organizace\kalendar\export\ExportOrganizaceKalendar;
use app\system\model\organizace\kalendar\OrganizaceKalendar;
use app\system\router\NewRouter;
use app\system\router\Route;

#[Route('^/ical-export/(?<token>[a-zA-Z0-9\.\-_]+)$')]
class IcalController
{
   use Controller;

   public function call(): void {
      $params = NewRouter::get()->getRoute()->getParametrs();
      $token = $params['token'] ?? '';

      if (empty($token)) {
         throw new Exception404();
      }

      $calendarRow = OrganizaceKalendar::getByToken($token);
      if (!$calendarRow) throw new Exception404();

      $this->log(sprintf('Export iCal pro token: %s', $token));

      header('Content-Type: text/calendar; charset=utf-8');
      header('Content-Disposition: inline; filename="cal.ics"');

      echo ExportOrganizaceKalendar::generateIcalFeed($calendarRow);
   }

   public static function createURL($token) :string {
     return self::getUrl($token);
   }

   private function log(string $message) :void {
      Files::checkDir($logDir = Files::prepareRootDirPath(Files::LOG_CLIENT_DIR));
      file_put_contents(
         sprintf('%s/%s', $logDir, 'kalendar-export.log'),
         sprintf(
            '[%s] | %s | IP: %s',
            date('Y-m-d H:i:s'),
            $message,
            Environment::getIpAddress()
         ) . PHP_EOL,
         FILE_APPEND
      );
   }
}