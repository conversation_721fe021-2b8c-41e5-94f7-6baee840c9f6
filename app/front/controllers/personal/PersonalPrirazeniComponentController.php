<?php namespace app\front\controllers\personal;

use app\front\organizace\model\organizace\Organizace;
use app\system\controller\ActionController;
use app\system\flash\FlashMessages;
use app\system\model\organizace\personal\prirazeni\component\PersonalPrirazeniSaveRules;
use app\system\model\organizace\personal\prirazeni\event\ChangePersonalPrirazeniEvent;
use app\system\model\organizace\personal\prirazeni\PersonalAssignmentType;
use app\system\router\Route;
use app\system\traits\JsonAjax;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 24.02.2025
 *
 * Routa se používá ve skriptu personalPrirazeniComponent
 * @link personalPrirazeniComponent.es6
 */
#[Route('^/~personal(?:-*)(?<action>\w*)$')]
class PersonalPrirazeniComponentController
{

   use ActionController;
   use JsonAjax;

   public function action_save() {
      $this->checkLoggedUser();

      $post = $this->getRequestBody();

      /**
       * Vycház<PERSON> podle @typedef PrirazeniSaveRules - personalPrirazeni.es6
       * @var $saveRules array{
       *    saveType: string,
       *    organizaceID: int,
       *    entityID: int,
       *    entityType: string,
       * }
       */
      $saveRules = $post['saveRules'];

      if(
         !PersonalPrirazeniSaveRules::tryFrom($saveRules['saveType'])
         || !($entityType = PersonalAssignmentType::tryFrom($saveRules['entityType']))
         || !($organizace = Organizace::getMisto($saveRules['organizaceID']))
         || !($entity = $entityType->getEntity($organizace->getVersion(), $saveRules['entityID']))
      ){
         FlashMessages::setError('Chyba ukládání');
         $this->sendAjaxResponse([
            'success' => false,
         ]);
      }

      (new ChangePersonalPrirazeniEvent($entity, $post['assignedPersonal']))->call();

      FlashMessages::setSuccess('Přiřazený personál je uložený!')->setNoTranslate();
      $this->sendAjaxResponse([
         'success' => true,
      ]);
   }
}