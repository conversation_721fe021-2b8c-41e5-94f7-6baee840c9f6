<?php namespace app\front\controllers\products;

use app\system\application\ApplicationVersion;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\exceptions\page404\Exception404;
use app\system\controller\InternApiController;
use app\system\model\entity\polozky\container\IEntityProducts;
use app\system\model\entity\polozky\container\item\ProductItemFactory;
use app\system\model\entity\polozky\finder\ProductFinder;
use app\system\model\entity\polozky\ProductsEntityType;
use app\system\model\organizace\meny\Meny;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\router\Route;
use Tracy\Debugger;
use Tracy\ILogger;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 03.05.2025 */
#[Route('^/~entity-products(?:/*)(?<action>\w*)$')]
class EntityProductsController
{

   use InternApiController;

   private function getProductsEntity() :?IEntityProducts {
      $params = $this->getRequestBody();

      if(
         !($entityType = ProductsEntityType::tryFrom($params['entityType']))
         || !($version = ApplicationVersion::tryFrom($params['entityVersion']))
         || !($entityID = intval($params['entityId']))
         || !($entity = $entityType->getEntity($version, $entityID))
      ){
         throw new Exception404();
      }

      return $entity;
   }

   protected function action_default() :void {
      $entity = $this->getProductsEntity();
      $container = $entity->getProductsContainer();

      $env = FrontApplicationEnvironment::get();

      $hasFinance = $env->uzivatel->isMajitel || $env->uzivatel->isSpravce
         || $env->personal?->hasAccessRight(PersonalAccessRightsEnum::ZOBRAZUJE_FINANCE_EVENT);

      $this->sendAjaxResponse([
         'productsList' => $container->getProductItems(),
         'hourMultiplier' => $entity->getCasovyBlok() ?: 1,
         'vatRates' => Meny::getDphRates(),
         'summary' => $container->getTotalPrice(),

         'editable' => $hasFinance && $entity->isEditovatelna(),
         'visiblePrices' => $hasFinance,
      ], true);
   }

   protected function action_insertManualProduct() {
      $params = $this->getRequestBody();
      $entity = $this->getProductsEntity();
      $container = $entity->getProductsContainer();

      $productParam = $params['product'];
      $productParam['productPrice']['currency'] = $entity->getMena()->getCode();

      $productItem = $container->insertManualProduct(
         ProductItemFactory::createManualFromArray($productParam)
      );

      $this->sendAjaxResponse([
         'product' => $productItem,
         'summary' => $container->getTotalPrice(),
      ], true);
   }

   protected function action_delete() {
      $params = $this->getRequestBody();
      $_cid = $params['product']['_cid'];

      $container = $this->getProductsEntity()->getProductsContainer();

      $container->deleteByCid($_cid);

      $this->sendAjaxResponse([
         'success' => true,
         'cid' => $_cid,
         'summary' => $container->getTotalPrice(),
      ], true);
   }

   protected function action_insertProducts() {
      $params = $this->getRequestBody();
      $container = $this->getProductsEntity()->getProductsContainer();

      $products = [];

      foreach($params['products'] as $productArray)
         $products[] = ProductItemFactory::createSearchItemFromArray($productArray);

      $this->sendAjaxResponse([
         'products' => $container->insertSearchItems($products),
         'summary' => $container->getTotalPrice(),
      ], true);
   }

   protected function action_save() {
      $params = $this->getRequestBody();
      $container = $this->getProductsEntity()->getProductsContainer();

      try{
         $product = ProductItemFactory::createFromArray($params['product']);

         $this->sendAjaxResponse([
            'updated' => $container->updateProduct($product),
            'summary' => $container->getTotalPrice(),
         ], true);
      }catch(\Exception $e){
         Debugger::log($e, ILogger::ERROR);
         $this->sendAjaxResponse([
            'success' => false,
         ], true);
      }
   }

   protected function action_changeOrder() {
      $params = $this->getRequestBody();
      $product = ProductItemFactory::createFromArray($params['product']);

      $this->getProductsEntity()
         ->getProductsContainer()
         ->changePosition($product, intval($params['newIndex']) + 1);

      $this->sendAjaxResponse([
         'success' => true,
      ]);
   }

   protected function action_search() {
      $params = $this->getRequestBody();
      $entity = $this->getProductsEntity();

      $productFinder = new ProductFinder(
         $entity->getIdOrganizace(),
         $entity->getMena(),
         $entity->getJazyk()
      );

      $this->sendAjaxResponse([
         'productsList' => $productFinder->search($params['term']),
      ]);
   }
}