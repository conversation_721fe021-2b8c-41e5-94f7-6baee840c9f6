<?php namespace app\front\controllers\baliky;

use app\system\controller\ActionController;
use app\system\flash\FlashMessages;
use app\system\model\balik\Balik;
use app\system\model\balik\Baliky;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\polozky\Polozky;
use app\system\router\Route;
use app\system\traits\JsonAjax;
use dibi;

#[Route('^/baliky(?:-*)(?<action>\w*)$')]
class BalikyActionController
{

   use ActionController;
   use JsonAjax;

   public const FIND_POLOZKY = 'findpolozky';
   public function action_findpolozky() {
      $id_mista = intval($_POST['id_mista']);
      $query = dibi::select('p.id_polozky AS id, COALESCE(pno.nazev, pnp.nazev) AS text')
         ->from(Polozky::TABLE, 'p')
         ->leftJoin('polozky_nazvy', 'pno')->on('p.id_polozky = pno.id_polozka AND pno.id_jazyk = %i', $_POST['id_jazyk'])
         ->join('polozky_nazvy', 'pnp')->on('p.id_polozky = pnp.id_polozka AND pnp.id_jazyk = %i', OrganizaceJazyky::getPrimary($id_mista))
         ->where('p.id_mista = %i', $id_mista)
         ->groupBy('p.id_polozky')
         ->orderBy('text');

      if(isset($_POST['search']) && ($search = trim($_POST['search'])))
         $query->where('(pno.nazev LIKE %~like~ OR pnp.nazev LIKE %~like~)', $search, $search);

      $this->sendAjaxResponse($query->fetchAll(), true);
   }

   public const ADD_POLOZKA = 'addpolozka';
   public function action_addpolozka() {
      $id_polozka = $_POST['id_polozka'];
      $id_balik = (int)$_POST['id_baliku'];

      $relation = Baliky::getOrCreateRelation($id_balik, $id_polozka);
      Baliky::increaseMnozstvi($relation);

      $balik = Balik::get($id_balik);
      FlashMessages::setSuccess('Položka přidána do balíčku');
      $this->sendAjaxResponse(['odhady' => $balik->getOdhadyFormatted()]);
   }

   public const ADJUST_POLOZKA = 'adjustpolozka';
   public function action_adjustpolozka() {
      $id_baliky_polozky = $_POST['id_baliky_polozky'];
      $adjustNum = intval($_POST['adjust']);
      $id_balik = Baliky::getIdByPolozkaRelation($id_baliky_polozky);

      if($adjustNum == 0 || (Baliky::getAmountPolozky($id_baliky_polozky) + $adjustNum) <= 0){
         Baliky::dropPolozka($id_baliky_polozky);
         FlashMessages::setSuccess('Položka byla odstraněna z balíčku');
      }else{
         Baliky::increaseMnozstvi($id_baliky_polozky, $adjustNum);
         FlashMessages::setSuccess('Položka uložena');
      }

      $balik = Balik::get($id_balik);
      $this->sendAjaxResponse(['odhady' => $balik->getOdhadyFormatted()]);
   }
}