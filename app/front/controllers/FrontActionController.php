<?php namespace app\front\controllers;

use app\libs\digisign\DigiSignApi;
use app\libs\registry\HandleRegisterException;
use app\libs\registry\RegisterFiremEnum;
use app\system\controller\ActionController;
use app\system\flash\FlashMessages;
use app\system\lay\error\Error404Page;
use app\system\model\nabidka\smlouva\obalka\signers\ObalkaSigners;
use app\system\model\nabidka\smlouva\obalka\SmlouvaObalka;
use app\system\router\Route;
use app\system\traits\JsonAjax;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 08.11.2023 */
#[Route('^/~action(?:-*)(?<action>\w*)$')]
class FrontActionController
{

   use ActionController;
   use JsonAjax;

   const ACTION_SIGN_LINK = 'signlink';
   public function action_signlink() {
      $signer = ObalkaSigners::get($_POST['signer']);
      $obalka = SmlouvaObalka::get($_POST['envelope']);

      if((!$signer || !$obalka) || $signer->id_obalka !== $obalka->id)
         Error404Page::show403();

      $result = DigiSignApi::getClient()
         ->envelopes()
         ->recipients($obalka->uid)
         ->embed($signer->uid, []);

      $this->sendAjaxResponse(['url' => $result->url]);
   }

   const ACTION_REGISTER = 'register';
   public function action_register() {
      $inputName = trim($_POST['inputName']);
      $registerType = RegisterFiremEnum::tryFrom($_POST['registerType']);
      $search = trim($_POST['search'] ?? '');

      if(!$inputName || !$registerType)
         $this->sendAjaxResponse(['succes' => false]);

      $endpoint = $registerType->getApiEndpoint();

      try {
         $subjekt = match($inputName){
            'vat' => $endpoint->handleVat($search),
            'name' => $endpoint->handleName($search),
         };
      }catch(HandleRegisterException $e){
         FlashMessages::setError($e->getMessage());
         $this->sendAjaxResponse(['succes' => false]);

      }

      $this->sendAjaxResponse(['subjekt' => $subjekt, 'succes' => true]);
   }
}