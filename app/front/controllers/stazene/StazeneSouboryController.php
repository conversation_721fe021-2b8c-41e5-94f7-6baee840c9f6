<?php namespace app\front\controllers\stazene;

use app\system\application\ApplicationVersion;
use app\system\application\ClientApplicationEnvironment;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\ActionController;
use app\system\Environment;
use app\system\helpers\Files;
use app\system\lay\error\Error404Page;
use app\system\model\nabidka\smlouva\DownloadManager;
use app\system\model\nabidka\smlouva\obalka\SmlouvaObalka;
use app\system\model\nabidka\smlouva\obalka\SmlouvaObalkaRow;
use app\system\model\nabidka\smlouva\stazene\SmlouvaObalkaStazene;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\model\organizace\personal\prava\OrganizacePersonalPrava;
use app\system\router\NewRouter;
use app\system\router\Route;
use app\system\traits\JsonAjax;

/** Created by <PERSON><PERSON>v<PERSON>. Date: 28.07.2023 */
#[Route('^/stazeny-soubor(?:/*)(?<action>\w*)(-(?<smlouva>\w*))?$')]
class StazeneSouboryController
{

   use ActionController;
   use JsonAjax;

   const ACTION_DOWNLOAD_LINK = 'downloadlink';
   public function action_downloadlink() {
      if(!Environment::isAjax())
         Error404Page::show403();

      $obalka = SmlouvaObalka::get($_POST['envelope']);

      if(!$obalka || !$this->checkCanViewObalka($obalka))
         Error404Page::show403();

      $stazene = DownloadManager::getSmlouvaPdf($obalka->id);

      $this->sendAjaxResponse(['url' => StazeneSouboryController::getUrl(StazeneSouboryController::ACTION_SMLOUVA, $stazene->id_obalka)]);
   }

   const ACTION_SMLOUVA = 'smlouva';
   public function action_smlouva() {
      $stazeny = SmlouvaObalkaStazene::get(NewRouter::get()->getRoute()->getParametrs()['smlouva']);

      if(!$stazeny || !($obalka = SmlouvaObalka::get($stazeny->id_obalka)) || !$this->checkCanViewObalka($obalka))
         Error404Page::show();

      $filename = $this->getSmlouvaFileName($stazeny->path);

      header("Content-type: application/pdf");
      header("Content-Length: " . filesize($filename));

      readfile($filename);
   }

   private function getSmlouvaFileName(string $path) :string {
      if(str_starts_with($path, '/www/hosting'))
         return $path;

      if(file_exists($path))
         return $path;

      return Files::prepareRootDirPath($path);
   }

   private function checkCanViewObalka(SmlouvaObalkaRow $obalka) :bool {
      $id_organizace = ApplicationVersion::from($obalka->version)
         ->getNabidka($obalka->id_nabidka)
         ->getPoptavka()
         ->id_organizace;

      if($this->environment instanceof FrontApplicationEnvironment){
         if($this->environment->id_organizace !== $id_organizace)
            return false;

         if($this->environment->isMajitel)
            return true;

         if(!$this->environment->personal)
            return false;

         foreach(OrganizacePersonalPrava::getByPersonal($this->environment->personal->id_personal) as $pravo)
            if(in_array($pravo, PersonalAccessRightsEnum::canDownloadSmlouva()))
               return true;

         return false;
      }

      if($this->environment instanceof ClientApplicationEnvironment){
         if(!($this->environment->zakaznik->getOrganizaceZakaznik($id_organizace) ?? null))
            return false;

         return true;
      }

      return false;
   }
}