<?php namespace app\front\controllers;

use app\System;
use app\system\controller\Controller;
use app\system\Redirect;
use app\system\router\Route;
use app\system\SystemVersion;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 19.12.2023 */
#[Route('^/toggle-translate')]
class TranslateToggleController
{

   use Controller;

   public function call() :void {
      setcookie(
         'qvamp_translate',
         !($_COOKIE['qvamp_translate'] ?? false),
         time() + 60 * 60 * 24 * 14,
         '/',
         '.' . SystemVersion::getMainHost(System::get()->getRequest()->getHost()),
         true,
         true
      );

      Redirect::back();
   }
}