<?php namespace app\front\controllers\organizace;

use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\modul\detail\uzivatele\zalozky\aktivity\table\SeznamPrirazenychAktivitTable;
use app\front\organizace\modul\detail\zakladni\zalozky\api\component\form\BasicApiFormComponent;
use app\system\controller\ActionController;
use app\system\Environment;
use app\system\flash\FlashMessages;
use app\system\lay\error\Error404Page;
use app\system\model\mista\images\OrganizaceImages;
use app\system\model\organizace\meny\Meny;
use app\system\model\organizace\personal\Personal;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\model\organizace\subscription\faktury\SubscriptionFaktury;
use app\system\model\maily\bloky\OrganizaceObsahBloky;
use app\system\model\mista\OrganizaceMista;
use app\system\model\organizace\subscription\faktury\FakturaGenerator;
use app\system\model\organizace\subscription\platby\MistoSubscriptionPlatby;
use app\system\model\organizace\aktivity\aktivita\OrganizaceAktivity;
use app\system\model\organizace\aktivity\sada\OrganizaceSadaAktivity;
use app\system\model\platby\scenare\PlatebniScenare;
use app\system\model\uzivatele\SessionUzivatel;
use app\system\router\NewRouter;
use app\system\router\Route;
use app\system\traits\JsonAjax;
use dibi;

/** Created by Filip Pavlas. Date: 18.01.2023 */
#[Route([
   'cs' => '^/nastaveni-organizace(?:-*)(?<slug>\d*)/(?<action>\w*)$',
   'en' => '^/setup-organization(?:-*)(?<slug>\d*)/(?<action>\w*)$',
   'pl' => '^/ustawienia-organizacji(?:-*)(?<slug>\d*)/(?<action>\w*)$',
   'hu' => '^/setup-organizacio(?:-*)(?<slug>\d*)/(?<action>\w*)$',
])]
class DetailMistaActionController
{

   use ActionController;
   use JsonAjax;

   public function action_aktivita() {
      OrganizaceObsahBloky::changeAktivita($_POST['id_blok']);
      $this->sendAjaxResponse([], true);
   }

   public function action_findaktivity() {
      $id_mista = $_POST['id_mista'];
      $sada = OrganizaceSadaAktivity::getAktivityBySady([$_POST['id_sada']]);
      $ukoly = [];

      if(!empty($sada[$_POST['id_sada']]))
         foreach($sada[$_POST['id_sada']] as $ukol)
            $ukoly[] = $ukol->id_aktivita;

      $ids_ukoly = sprintf('(%s)', implode(", ", $ukoly));

      $query = dibi::select('u.id_aktivita AS id, u.nazev AS text')
         ->from(OrganizaceAktivity::TABLE . ' as u')
         ->where('u.id_mista = %i', $id_mista);

      if(!empty($ukoly))
         $query->where('u.id_aktivita NOT IN %ex', $ids_ukoly);

      if(isset($_POST['search']) && trim($_POST['search']))
         $query->where('u.nazev LIKE %~like~', $_POST['search']);

      $this->sendAjaxResponse($query->fetchAll(), true);
   }

   public function action_add_sada() {
      OrganizaceSadaAktivity::addUkoly($_POST['ids_aktivity'], $_POST['id_sada']);
      FlashMessages::setSuccess('Aktivity přidány do sady');

      $this->sendAjaxResponse([ 'aktivityTable' => (string)(new SeznamPrirazenychAktivitTable())->setIdSady($_POST['id_sada'])]);
   }

   public const ACTION_FAKTURA = 'faktura';
   public function action_faktura() {
      $id_mista = NewRouter::get()->getRoute()->getParametrs()['slug'];

      $misto = Organizace::getMisto($id_mista);

      if(
         !$misto || !isset($_GET['platba']) || !is_numeric($_GET['platba'])
         || !$this->environment->isLogged() || $this->environment->id_organizace !== $misto->id_organizace
         || !($this->environment->isMajitel || $this->environment->personal?->hasAccessRight(PersonalAccessRightsEnum::ADMIN))
         || ($platba = MistoSubscriptionPlatby::get($_GET['platba'], $misto->id_organizace)) === null
         || !file_exists($filename = ($platba->getFaktura()?->getPdfFile() ?: SubscriptionFaktury::create($misto->id_organizace, $platba)->getPdfFile()))
      ){
         Error404Page::show();
      }

      header("Content-type: application/pdf");
      header("Content-Length: " . filesize($filename));
      readfile($filename);
   }

   public const ACTION_API_FORM = 'apiform';
   public function action_apiform() {
      $id_mista = NewRouter::get()->getRoute()->getParametrs()['slug'];
//      @TODO u#8677ch03u ošetřit práva buď majitel nebo správce zatím pouze majitel
      if(!in_array((int)$id_mista, array_column(SessionUzivatel::getUzivatel()->getVlastneneMista(), 'id_organizace')))
         Error404Page::show403();

      $this->sendAjaxResponse(
         ['form' => BasicApiFormComponent::get($id_mista, $_POST['source'], $_POST['key'])], true
      );
   }

   public const ACTION_SAVE_IMAGE_NAME = 'saveimgname';
   public function action_saveimgname() {
      if(!Environment::isAjax() || !($image = OrganizaceImages::get($_POST['id_image'])) || $image->id_organizace !== (int)$_POST['id_organizace']){
         http_response_code(400);
         exit;
      }

      $image->nazev = trim($_POST['name']) ?: null;
      $image->save();

      $this->sendAjaxResponse(['success' => true]);
   }

   public const ACTION_FIND_SCENARE = 'findscenare';
   public function action_findscenare() {
      if(
         !Environment::isAjax()
         || !($mena = Meny::tryFrom($_POST['id_mena']))
         || intval(NewRouter::get()->getRoute()->getParametrs()['slug']) !== $this->environment->id_organizace
      ){
         http_response_code(400);
         exit;
      }

      $this->sendAjaxResponse([
         'scenare' => PlatebniScenare::getByMistoPairs($this->environment->id_organizace, $mena)
      ]);
   }

   //@TODO přesunout do environment controller
   public const ACTION_FINDMISTO = 'findmisto';
   public function action_findmisto() {
      $id_organizace = NewRouter::get()->getRoute()->getParametrs()['slug'];
      $search = '';

      //$query = dibi::select('m.id_mista AS id, CONCAT(m.nazev, " - ", CONCAT(m.mesto, " ", m.ulice)) AS text')
      $query = dibi::select('m.id_mista AS data, m.nazev as value, m.mesto as mesto, m.ulice as ulice, m.psc as psc, m.id_stat AS stat')
         ->from(OrganizaceMista::TABLE . ' as m')
         ->where('m.id_organizace = %i', $id_organizace);

      if(isset($_POST['search']) && trim($_POST['search'])) {
         $query->where('m.nazev LIKE %~like~', trim($_POST['search']));
      }

      $this->sendAjaxResponse($query->fetchAll(), true);
   }

   public function action_filladress() {
      $id_organizace = NewRouter::get()->getRoute()->getParametrs()['slug'];

      $misto = OrganizaceMista::get($_POST['id']);

      $this->sendAjaxResponse(['misto' => $misto], true);
   }
}