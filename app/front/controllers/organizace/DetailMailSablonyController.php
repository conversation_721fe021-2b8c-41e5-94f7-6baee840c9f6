<?php namespace app\front\controllers\organizace;

use app\front\organizace\modul\detail\texty\zalozky\emaily\detail\DetailMistaMailSablonyPage;
use app\system\controller\Controller;
use app\system\router\NewRouter;
use app\system\router\Route;

#[Route('^/detail-email-sablona(?:-*)(?<template>\d*)(?:-*)(?<venue>\d*)$')]
class DetailMailSablonyController
{

   use Controller;

   public function call() :void {
      $params = NewRouter::get()->getRoute()->getParametrs();

//      @TODO kontrola jestli může místo editovat

      DetailMistaMailSablonyPage::getComponent()
         ->setIdTemplate($params['template'])
         ->setIdMista($params['venue'])
         ->echo();
   }
}