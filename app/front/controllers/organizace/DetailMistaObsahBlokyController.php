<?php namespace app\front\controllers\organizace;

use app\front\texty\bloky\detail\DetailMistaObsahBlokyPage;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\Controller;
use app\system\controller\exceptions\page404\Exception404;
use app\system\model\maily\bloky\OrganizaceObsahBloky;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\router\NewRouter;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 17.01.2023 */
#[Route('^/detail-obsah-bloky(?:-*)(?<slug>\w*)$')]
class DetailMistaObsahBlokyController
{

   use Controller;

   public function call() :void {
      $id = NewRouter::get()->getRoute()->getParametrs()['slug'];
      $blok = OrganizaceObsahBloky::get($id);

      $env = FrontApplicationEnvironment::get();

      if(
         !$blok
         || $blok->id_misto !== $env->id_organizace
         || !($env->isMajitel || $env->personal?->hasAccessRight(PersonalAccessRightsEnum::ADMIN))
      ){
         throw new Exception404();
      }

      DetailMistaObsahBlokyPage::getComponent()
         ->setBlok($blok)
         ->echo();
   }
}