<?php namespace app\front\controllers\organizace;

use app\system\application\FrontApplicationEnvironment;
use app\system\controller\Controller;
use app\system\model\organizace\nastaveni\ChybejiciNastaveni;
use app\system\model\uzivatele\SessionUzivatel;
use app\system\Redirect;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 16.10.2022 */
#[Route([
   'cs' => '^/zakladni-nastaveni-organizace$',
   'en' => '^/basic-organization-settings$',
   'pl' => '^/ustawienia-podstawowe-organizacji$',
   'hu' => '^/alapszintu-szervezeti-beallitasok$',
])]
class MissingSettingsController
{

   use Controller;

   public function call() :void {
      $env = FrontApplicationEnvironment::get();

      if(!ChybejiciNastaveni::isMissingImportantSettings($env->id_organizace)){
         if($env->uzivatel->missingSettings)
            SessionUzivatel::reloadMista();

         Redirect::homepage();
      }

//      @TODO zde zobrazit u OrganizaceLicenceTyp::ONLY_KATALOG platební bránu
      $nastaveniTyp = ChybejiciNastaveni::getImportantByOrganizace($env->id_organizace);

      $nastaveniTyp->getPageComponent()
         ->setOrganizaceID($env->id_organizace)
         ->echo();
   }
}