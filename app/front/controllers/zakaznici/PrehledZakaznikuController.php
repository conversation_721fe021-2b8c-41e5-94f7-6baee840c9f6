<?php namespace app\front\controllers\zakaznici;

use app\front\zakaznici\prehled\PrehledZakaznikuPage;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\Controller;
use app\system\controller\exceptions\page403\Exception403;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 06.07.2022 */
#[Route([
   'cs' => '^/prehled-zakazniku$',
   'en' => '^/customer-overview$',
   'pl' => '^/przeglad-klientow$',
   'hu' => '^/ugyfel-attekintes$',
])]
class PrehledZakaznikuController
{

   use Controller;

   public function call() :void {
      $this->env = FrontApplicationEnvironment::get();
      $this->checkPermissions();

      PrehledZakaznikuPage::echoThis();
   }

   private function checkPermissions() :void {
      if($this->env->uzivatel->isMajitel || $this->env->uzivatel->isSpravce)
         return;

      if($this->env->personal->hasAccessRight(PersonalAccessRightsEnum::ZAKAZNICI))
         return;

      throw new Exception403('Chybějící právo pro zobrazení');
   }

   protected FrontApplicationEnvironment $env;
}