<?php namespace app\front\controllers\zakaznici;

use app\system\controller\ActionController;
use app\system\model\organizace\zakaznici\OrganizaceZakaznici;
use app\system\router\Route;
use app\system\traits\JsonAjax;
use dibi;

/** Created by <PERSON><PERSON><PERSON>. Date: 01.09.2022, 1:14 */

// action_saverating pouzity v rozhrani.es6
#[Route('^/zakaznici(?:-*)(?<action>\w*)(?:-*)(?<slug>\w*)$')]
class ZakazniciActionController
{
   use ActionController;
   use JsonAjax;

   public function action_saverating(){
      OrganizaceZakaznici::saveRating($_POST['id_zakaznik'], $_POST['rating']);
      $this->sendAjaxResponse([], true);
   }
}