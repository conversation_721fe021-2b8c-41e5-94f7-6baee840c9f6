<?php namespace app\front\controllers\mista;

use app\front\mista\PrehledOrganizaceMistaPage;
use app\system\controller\Controller;
use app\system\model\uzivatele\SessionUzivatel;
use app\system\router\Route;

/** Created by <PERSON><PERSON>. Date: 28.03.2023 */
#[Route([
   'cs' => '^/prehled-organizace-mista$',
   'en' => '^/organization-and-location-overview$',
   'pl' => '^/przeglad-organizacji-i-miejsc$',
   'hu' => '^/szervezetek-es-helyszinek-attekintese$',
])]
class PrehledOrganizaceMistaController
{

   use Controller;

   public function call() :void {
      PrehledOrganizaceMistaPage::getComponent()
         ->setIdOrganizace(SessionUzivatel::getActiveMisto())
         ->echo();
   }
}