<?php namespace app\front\controllers\mista;

use app\front\mista\detail\DetailOrganizaceMistaPage;
use app\system\controller\Controller;
use app\system\router\NewRouter;
use app\system\router\Route;

/** Created by <PERSON><PERSON>. Date: 28.03.2023 */
#[Route('^/detail-organizace-mista(?:-*)(?<slug>\d*)$')]
class DetailOrganizaceMistaController
{

   use Controller;

   public function call() :void {
      $id_mista = NewRouter::get()->getRoute()->getParametrs()['slug'];

      DetailOrganizaceMistaPage::getComponent()
         ->setIdMista($id_mista)
         ->echo();
   }
}