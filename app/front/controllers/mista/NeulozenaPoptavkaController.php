<?php namespace app\front\controllers\mista;

use app\front\api\InfoPage;
use app\system\controller\Controller;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 12.04.2023 */
#[Route('^/unsaved-lead$')]
class NeulozenaPoptavkaController
{

   use Controller;

   public function call() :void {
      InfoPage::getComponent()
         ->setContent('Lead wasn\'t save',
            trim($_GET['back'])
               ? gzinflate(urldecode(trim($_GET['back'])))
               : null,
            trim($_GET['message'])
               ? gzinflate(urldecode(trim($_GET['message'])))
               : 'Somenthing went wrong'
         )->echo();
   }
}