<?php namespace app\front\controllers\mista;

use app\front\organizace\modul\detail\texty\zalozky\smlouvy\detail\DetailSmlouvyPage;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\Controller;
use app\system\controller\exceptions\page404\Exception404;
use app\system\model\mista\smlouvy\OrganizaceSmlouvy;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\router\NewRouter;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 07.05.2023 */
#[Route('^/detail-smlouva(?:-*)(?<smlouva>\w*)$')]
class DetailSmlouvyController
{

   use Controller;

   public function call() :void {
      $smlouva = OrganizaceSmlouvy::get(NewRouter::get()->getRoute()->getParametrs()['smlouva']);
      $env = FrontApplicationEnvironment::get();

      if(
         !$smlouva
         || $smlouva->id_organizace !== $env->id_organizace
         || !($env->isMajitel || $env->personal?->hasAccessRight(PersonalAccessRightsEnum::ADMIN))
      ){
         throw new Exception404();
      }

      DetailSmlouvyPage::getComponent()
         ->setSmlouvaData($smlouva)
         ->echo();
   }
}