<?php namespace app\front\controllers\formular;

use app\front\formular\DetailFormularPage;
use app\system\controller\Controller;
use app\system\flash\FlashMessages;
use app\system\lay\error\Error404Page;
use app\system\model\formular\Formular;
use app\system\model\formular\enumy\FormularViewerModeEnum;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\router\NewRouter;
use app\system\router\Route;

/** Created by Filip Pavlas. Date: 04.12.2023 */
#[Route([
   'cs' => '^/formular-(?<formular>\d*)(-(?<view_mode>\w*))?$',
   'en' => '^/form-(?<formular>\d*)(-(?<view_mode>\w*))?$',
   'pl' => '^/formularz-(?<formular>\d*)(-(?<view_mode>\w*))?$',
   'hu' => '^/urlap-(?<formular>\d*)(-(?<view_mode>\w*))?$',
])]
class FormulareController
{
   use Controller;

   protected ?Formular $formular;
   protected ?FormularViewerModeEnum $viewmode;

   public function call() :void {
      $params = NewRouter::get()->getRoute()->getParametrs();
      $id = intval($params['formular']);

      $this->formular = Formular::get($id);

      if($viewmodeId = $params['view_mode'] ?? null){
         $this->viewmode = FormularViewerModeEnum::tryFrom($viewmodeId);
      }
      elseif(!$this->formular->stav->isVyplnitelny() && !$this->formular->stav->isLocked()){
         $this->viewmode = FormularViewerModeEnum::EDIT;
      }
      elseif($this->formular->stav->isVyplnitelny() && !$this->formular->stav->isLocked()){
         $this->viewmode = FormularViewerModeEnum::FILL;
      }
      else{
         $this->viewmode = FormularViewerModeEnum::READ;
      }

      if(
         !$this->formular
         || $this->formular->id_organizace !== $this->environment->id_organizace
         || !$this->isPersonalMajtelOrPersonalAdmin()
      ) {
         Error404Page::show();
      }

      $this->prepareViewmode();

      DetailFormularPage::getComponent()
         ->setFormular($this->formular)
         ->setControllerViewmode($this->viewmode)
         ->echo();
   }

   private function isPersonalMajtelOrPersonalAdmin() :bool {
      return ($this->environment->isMajitel && $this->environment->personal !== null)
         || !!$this->environment->personal?->hasAccessRight(PersonalAccessRightsEnum::ADMIN)
         || !!$this->environment->personal?->hasAccessRight(PersonalAccessRightsEnum::EVENTY);
   }

   private function prepareViewmode() :void {
      if($this->formular->typ->isSablonaType()){
         if(!$this->isPersonalMajtelOrPersonalAdmin() || $this->viewmode === FormularViewerModeEnum::FILL)
            Error404Page::show();

         if($this->viewmode === null)
            $this->viewmode = FormularViewerModeEnum::EDIT;

         return;
      }

      if(
         $this->viewmode === FormularViewerModeEnum::FILL
         && (!$this->formular->isRespondent($this->environment->personal) && !$this->isPersonalMajtelOrPersonalAdmin())
      ) {
         Error404Page::show();
      }
      elseif(
         !$this->formular->isRespondent($this->environment->personal)
         && $this->isPersonalMajtelOrPersonalAdmin()
         && $this->formular->stav->isLocked()
      ){
         $this->viewmode = FormularViewerModeEnum::READ;
      }

      if($this->environment->personal === null && $this->environment->isMajitel){
         FlashMessages::setError('Nejste členem personálu');
         $this->viewmode = FormularViewerModeEnum::READ;
         return;
      }
      if($this->viewmode == null)
         $this->viewmode = FormularViewerModeEnum::READ;
   }
}