<?php namespace app\front\controllers\formular;

use app\front\formular\body\FormularBodyViewer;
use app\system\controller\ActionController;
use app\system\model\formular\enumy\FormularStavy;
use app\system\model\formular\eventy\ZmenaStavuFormulareEvent;
use app\system\model\formular\Formular;
use app\system\model\formular\enumy\FormulareTypyEnum;
use app\system\model\formular\enumy\FormularViewerModeEnum;
use app\system\model\formular\FormulareModel;
use app\system\model\formular\FormularInputOption;
use app\system\model\formular\FormularRespondent;
use app\system\Redirect;
use app\system\router\NewRouter;
use app\system\router\Route;
use app\system\traits\JsonAjax;

/** Created by Filip Pavlas. Date: 23.02.2023 */
#[Route([
   'cs' => '^/formular(?:-*)(?<formular>\d*)(?:/)(?<action>\w*)$',
   'en' => '^/form(?:-*)(?<formular>\d*)(?:/)(?<action>\w*)$',
   'pl' => '^/formularz(?:-*)(?<formular>\d*)(?:/)(?<action>\w*)$',
   'hu' => '^/urlap(?:-*)(?<formular>\d*)(?:/)(?<action>\w*)$',
])]
class FormulareActionController
{

   use ActionController;
   use JsonAjax;

   const ACTION_DELETE_OPTION = 'deleteOption';
   public function action_deleteOption() {
      if(isset($_POST['id_option'])
         && ($option = FormularInputOption::get($_POST['id_option']))
         && !empty($option)
      )
         FormularInputOption::delete($option->id);

      $this->sendAjaxResponse([]);
   }

   const ACTION_FINDFORMULARE = 'findformulare';
   public function action_findformulare() {
      $this->sendAjaxResponse(FormulareModel::getForSelect(
         $_POST['id_mista'],
         FormulareTypyEnum::TEMPLATE->value,
         $_POST['id_jazyk'],
         $_POST['search']?? null
      ), true);
   }

   const ACTION_SHOWRESPONDENT = 'showrespondent';
   public function action_showrespondent() {
      $form = Formular::get(NewRouter::get()->getRoute()->getParametrs()['formular']);

      if($form && ($respondent = FormularRespondent::get($_POST['selected_respondent']))){
         $body = (new FormularBodyViewer($form))
            ->setViewmode(FormularViewerModeEnum::FILL)
            ->showRespondentsValue($respondent)
            ->render();

         $this->sendAjaxResponse(['body' => (string)$body], true);
      }
   }

   const ACTION_SENDFORM = 'sendform';
   public function action_sendform() {
      $form = Formular::get(NewRouter::get()->getRoute()->getParametrs()['formular']);

      (new ZmenaStavuFormulareEvent($form))
         ->setStav(FormularStavy::ODESLANY)
         ->call();

      Redirect::back();
   }
}