<?php namespace app\front\controllers\chat;

use app\system\chat\data\ChatDataModel;
use app\system\controller\ActionController;
use app\system\router\Route;
use app\system\traits\JsonAjax;
use Nette\Utils\Strings;

/** Created by <PERSON><PERSON>. Date: 28.09.2023 */
#[Route('^/chat(?:-*)(?<action>\w*)$')]
class ChatActionController
{

   use ActionController;
   use JsonAjax;

   const ACTION_REPLY_MESSAGE = 'replymessage';
   public function action_replymessage() {
      $message = ChatDataModel::getZprava($_POST['id_reply']);

      $this->sendAjaxResponse(['message' => Strings::truncate(strip_tags(html_entity_decode($message->message)), 80, '...')], true);
   }

   const ACTION_CHANGE_VISIBILITY = 'changevisibility';
   public function action_changevisibility() {
      $changed = false;
      if($message = ChatDataModel::getZprava($_POST['id_zprava'])) {
         $message->is_interni = ($message->is_interni == 0)
            ? 1
            : 0;

         $message->save();
         $changed = true;
      }

      $this->sendAjaxResponse(['changed' => $changed, 'visible' => $message?->is_interni], true);
   }
}