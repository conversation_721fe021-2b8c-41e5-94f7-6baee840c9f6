<?php namespace app\front\controllers;

use app\api\enpoints\thepay\ThePayResponseController;
use app\front\controllers\organizace\DetailMistaController;
use app\front\uzivatel\LoginController;
use app\system\application\ClientApplicationEnvironment;
use app\system\controller\ActionController;
use app\system\controller\exceptions\page404\Exception404;
use app\system\Environment;
use app\system\flash\FlashMessages;
use app\system\lay\error\Error404Page;
use app\system\model\organizace\zakaznici\firma\OrganizaceZakaznikFirma;
use app\system\model\organizace\zakaznici\OrganizaceZakaznici;
use app\system\model\organizace\zakaznici\telefon\OrganizaceZakazniciTelefon;
use app\system\model\staty\Staty;
use app\system\model\translator\Jazyky;
use app\system\model\uzivatele\SessionUzivatel;
use app\system\model\uzivatele\Uzivatel;
use app\system\model\zakaznici\Zakaznici;
use app\system\Redirect;
use app\system\router\Route;
use app\system\SystemVersion;
use app\system\traits\JsonAjax;
use dibi;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberUtil;

/** Created by Kryštof Czyź. Date: 26.08.2022 */
#[Route('^/environment(?:-*)(?<action>\w*)$')]
class EnvironmentController
{

   use ActionController;
   use JsonAjax;

   public function action_changevenue() {
      if(!Environment::isAjax() || $this->environment instanceof ClientApplicationEnvironment)
         Error404Page::show();

      if(!$this->environment->isLogged()){
         FlashMessages::setError('Vaše přihlášení vypršelo, prosím přihlašte se znovu');
         Redirect::to(LoginController::getUrl());
      }

      $uzivatel = SessionUzivatel::getUzivatel()
         ->setActualVenue($_POST['id_mista']);

      FlashMessages::setSuccess('Nyní spravujete místo $1', $uzivatel->mistaLabels[$uzivatel->activeMisto]);
      $this->sendAjaxResponse(returnDataOnly: true);
   }

   const string ACTION_RELOAD_SESSION = 'reloadsession';
   public function action_reloadsession() {
      if(SessionUzivatel::getUzivatel())
         SessionUzivatel::refreshAll();

      if(($_SERVER['HTTP_REFERER'] ?? null) === Redirect::check(ThePayResponseController::getUrl(), SystemVersion::APP))
         Redirect::to(DetailMistaController::getUrl(), SystemVersion::APP);

      Redirect::homepage(version: SystemVersion::APP);
   }

//   @TODO napojit na InternApiController
   public function action_changelang() {
      if(!Environment::isAjax())
         throw new Exception404();

      if(!($jazyk = Jazyky::tryFrom(intval($this->getRequestBody()['id_jazyk']))))
         return;

      $this->environment->saveJazyk($jazyk);
      FlashMessages::setSuccess('Jazyk byl nastaven na $1', $jazyk->getTitle());
      $this->sendAjaxResponse(returnDataOnly: true);
   }

   public const ACTION_LOGOUT = 'logout';
   public function action_logout() {
      if($this->environment->isLogged())
         Uzivatel::logout();

      Redirect::to(LoginController::getUrl());
   }

   public const ACTION_CHECK_TELEPHONE = 'check_telephone';
   public function action_check_telephone() {
      if(!Environment::isAjax())
         Error404Page::show();

      $telefon = trim(strip_tags($_POST['telefon']));
      $prefix = trim(strip_tags($_POST['prefix']));
      $stat = !str_starts_with($prefix, 'c')
         ? Staty::getForDialPrefix($prefix)
         : Staty::getForISO_A2(ltrim($prefix, 'c'));

      if(!$telefon || !$prefix || !$stat){
         $this->sendAjaxResponse(['success' => false], true);
         return;
      }

      $phoneUtil = PhoneNumberUtil::getInstance();

      try {
         $phoneNumber = $phoneUtil->parse($telefon, $stat->iso_a2);
         $status = $phoneUtil->isValidNumber($phoneNumber);
      } catch (NumberParseException $e) {
         $this->sendAjaxResponse(['success' => false], true);
         return;
      }

      $this->sendAjaxResponse(['success' => $status], true);
   }

   public const ACTION_FIND_ZAKAZNIK = 'find_zakaznik';
   public function action_find_zakaznik() {
      if(!Environment::isAjax() || $this->environment instanceof ClientApplicationEnvironment)
         Error404Page::show();

      if(!$this->environment->isLogged()){
         FlashMessages::setError('Vaše přihlášení vypršelo, prosím přihlašte se znovu');
         $this->sendAjaxResponse([
            'unloggedUser' => LoginController::getUrl()
         ], true);
      }

      $q = dibi::select('mz.full_name, z.email, mz.id_jazyk')
         ->select('ozt.prefix, ozt.telefon')
         ->select('ozf.nazev AS nazev_firmy, ozf.ico, ozf.dic, ozf.ulice, ozf.mesto, ozf.psc')
         ->from(OrganizaceZakaznici::TABLE, 'mz')
         ->join(Zakaznici::TABLE, 'z')->on('mz.id_zakaznik = z.id_zakaznik')
         ->join(OrganizaceZakazniciTelefon::TABLE, 'ozt')->on('mz.id = ozt.id_zakaznik AND ozt.is_primarni = 1')
         ->leftJoin(OrganizaceZakaznikFirma::TABLE, 'ozf')->on('mz.id = ozf.id_zakaznik')
         ->where('mz.id_organizace = %i', $this->environment->id_organizace);

      if($_POST['type'] === 'jmeno')
         $q->where('mz.full_name LIKE %~like~' , $_POST['search']);
      elseif($_POST['type'] === 'email')
         $q->where('z.email LIKE %like~', $_POST['search']);

      $this->sendAjaxResponse([
         'data' => $q->fetchAll()
      ]);
   }

   public function action_ping() {
      $this->sendAjaxResponse([
         'success' => true,
      ]);
   }
}