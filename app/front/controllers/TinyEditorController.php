<?php namespace app\front\controllers;

use app\System;
use app\system\controller\ActionController;
use app\system\flash\FlashMessages;
use app\system\model\mista\images\ImageUploadException;
use app\system\Redirect;
use app\system\router\Route;
use app\system\SystemVersion;
use app\system\tiny\images\BaseEditorImages;
use app\system\tiny\images\EditorImageMode;
use app\system\tiny\images\EditorImagesListException;
use app\system\traits\JsonAjax;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 04.11.2024 */
#[Route('^/tinyeditor(?:-*)(?<action>\w*)$')]
class TinyEditorController
{

   use ActionController;
   use JsonAjax;

   const string ACTION_UPLOAD_TINY_PHOTO = 'uploadTinyPhoto';
   public function action_uploadTinyPhoto() {
      if(
         !System::get()->getApplication()->environment->isLogged()
      ){
         $this->sendAjaxResponse([
            'error' => System::getTranslator()->translateFlash('<PERSON><PERSON><PERSON><PERSON> být přih<PERSON>')
         ], true);
      }

      $rulesArr = json_decode($_POST['imageRules'], true);

      if(
         !isset($rulesArr['mode'])
         || !($mode = EditorImageMode::tryFrom($rulesArr['mode']))
      ){
         $this->sendAjaxResponse([
            'error' => 'Malformed parameters...'
         ], true);
      }

      try{
         $rules = BaseEditorImages::create($mode, $rulesArr['extra']);
      }catch(\Exception $e){
         $this->sendAjaxResponse([
            'error' => 'Unable to load images, rules missing'
         ]);
      }

      try{
         $this->sendAjaxResponse([
            'location' => Redirect::check($rules->uploadPhotoFile($_FILES['file']), SystemVersion::APP)
         ], true);
      }catch(ImageUploadException $e) {
         $this->sendAjaxResponse([
            'error' => System::getTranslator()->translateFlash($e->getMessage()),
         ]);
      }
   }

   const string ACTION_GALLERY_LIST = 'galleryList';
   public function action_galleryList() {
      if(
         !System::get()->getApplication()->environment->isLogged()
      ){
         FlashMessages::setError('Musíte být přihlášeni');
         $this->sendAjaxResponse([
            'error' => true
         ], true);
      }

      $post = $this->getRequestBody();

      if(
         !isset($post['mode'])
         || !($mode = EditorImageMode::tryFrom($post['mode']))
      ){
         FlashMessages::setError('Chyba při načítání galerie');
         $this->sendAjaxResponse([
            'error' => true
         ], true);
      }

      try{
         $rules = BaseEditorImages::create($mode, $post['extra']);
      }catch(\Exception $e){
         FlashMessages::setError('Chyba při načítání galerie');
         $this->sendAjaxResponse([
            'error' => true
         ]);
      }

      try{
         $images = $rules->getImageList();

         $this->sendAjaxResponse([
            'images' => $images,
         ], true);
      }catch(EditorImagesListException $e) {
         FlashMessages::setError($e->getMessage());
         $this->sendAjaxResponse([
            'error' => true,
         ]);
      }
   }
}