<?php namespace app\front\controllers\home;

use app\front\home\HomeDashboardPage;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\Controller;
use app\system\router\Route;

#[Route('^/$')]
class HomepageController
{

   use Controller;

   public function call() :void {
      HomeDashboardPage::getComponent()
         ->setUzivatel(($enviroment = FrontApplicationEnvironment::get())->uzivatel)
         ->setEnvironment($enviroment)
         ->echo();
   }
}