<?php namespace app\front\controllers;

use app\front\organizace\model\organizace\Organizace;
use app\system\controller\ActionController;
use app\system\model\uzivatele\Uzivatel;
use app\system\notifikace\data\Notifikace;
use app\system\notifikace\DeletedDataNotifikaceException;
use app\system\notifikace\NotifikaceContainer;
use app\system\router\Route;
use app\system\traits\JsonAjax;
use Tracy\Debugger;
use Tracy\ILogger;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 16.05.2022 */
#[Route('^/notifikace(?:/*)(?<action>\w*)$')]
class NotifikaceController
{

   use ActionController;
   use JsonAjax;

   public function action_list() {
      $entityBody = $this->getRequestBody();

      $cont = NotifikaceContainer::getFor(
         Uzivatel::get($this->environment->uzivatel->id_uzivatele),
         Organizace::getMisto($this->environment->id_organizace),
      );

      $offset = intval($entityBody['offset']);

      $notifikaceAll = $cont->getNotifikace($offset);
      $notArr = [];
      $notDelivered = [];
      $deletedException = false;

      foreach($notifikaceAll as $notifikaceRow){
         try{
            $html = (string)$notifikaceRow->getClass()->getHtml();

            $notArr[$notifikaceRow->id] = $html;

            if($notifikaceRow->delivered === null)
               $notDelivered[$notifikaceRow->id] = $notifikaceRow;
         } catch (\TypeError|DeletedDataNotifikaceException $e) {
            if($e instanceof \TypeError)
               Debugger::log(json_encode([
                  'message' => $e->getMessage(),
                  'notifikaceRow' => $notifikaceRow->toArray(),
               ]), ILogger::WARNING);

            $deletedException = true;
            Notifikace::multipleDelete([$notifikaceRow->id], $notifikaceRow->id_uzivatel);
         }
      }

      $responseData = [
         'reloadCounts' => false,
         'notifikace' => $notArr,
         'lastTimestamp' => $cont->getLastTimestamp(),
      ];

      if(!empty($notDelivered))
         Notifikace::setDeliver(array_keys($notDelivered), $this->environment->uzivatel->id_uzivatele);

      if($deletedException){
         $cont = $cont->reload();
         $responseData['reloadCounts'] = true;
         $responseData['counts'] = $cont->getCounts();
      }

      $this->sendAjaxResponse($responseData);
   }

   public function action_setSeen() {
      $entityBody = $this->getRequestBody();

      $seenAll = $entityBody['all'] ?? false;

      if($seenAll){
         Notifikace::seenAll($entityBody['userID'], $entityBody['organizationID']);
         $this->sendAjaxResponse(['success' => true]);
      }

      $affected = Notifikace::multipleSeen($entityBody['idsNotifications'], $entityBody['userID']);
      bdump([
         'success' => count($entityBody['idsNotifications']) === $affected,
      ]);

      $this->sendAjaxResponse(['success' => true]);
   }

   public function action_setDelete() {
      $entityBody = $this->getRequestBody();

      $deleteAll = $entityBody['all'] ?? false;

      if($deleteAll){
         Notifikace::deleteAll($entityBody['userID'], $entityBody['organizationID']);
         $this->sendAjaxResponse(['success' => true]);
      }

      $affected = Notifikace::multipleDelete($entityBody['idsNotifications'], $entityBody['userID']);
      bdump([
         'success' => count($entityBody['idsNotifications']) === $affected,
      ]);

      $this->sendAjaxResponse([
         'success' => true,
      ]);
   }

   public function action_test() {
      $entityBody = $this->getRequestBody();

      $userID = $entityBody['userID'];
      $organizationID = $entityBody['organizationID'];


      $notifications = NotifikaceContainer::getFor(
         Uzivatel::get($userID),
         Organizace::getMisto($organizationID),
      );

      $this->sendAjaxResponse([
         'success' => true,
         'userID' => $userID,
         'orgID' => $organizationID,
         'notifications' => $notifications->getCounts(),
      ]);
   }

   public function action_deliver() {
      $entityBody = $this->getRequestBody();
      $userID = $entityBody['userID'];
      $notificationID = $entityBody['notificationID'];

      Notifikace::setDeliver([$notificationID], $userID);
      $this->sendAjaxResponse([
         'success' => true,
      ]);
   }

   public function action_counts() {
      $entityBody = $this->getRequestBody();

      $userID = $entityBody['userID'];
      $organizationID = $entityBody['organizationID'];

      $notifications = NotifikaceContainer::getFor(
         Uzivatel::get($userID),
         Organizace::getMisto($organizationID),
      );

      $this->sendAjaxResponse($notifications->getCounts());
   }

   protected function isEnabledCheckLogged() :bool {
      return true;
   }
}