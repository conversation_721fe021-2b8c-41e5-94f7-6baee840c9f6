<?php namespace app\front\controllers\reporty;

use app\front\reporty\ReportyPage;
use app\system\application\FrontApplicationEnvironment;
use app\system\controller\Controller;
use app\system\controller\exceptions\page403\Exception403;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 06.07.2022 */
#[Route([
   'cs' => '^/reporty$',
   'en' => '^/reports$',
   'pl' => '^/raporty$',
   'hu' => '^/jelentesek$',
])]
class ReportyPageController
{

   use Controller;

   public function call() :void {
      $this->env = FrontApplicationEnvironment::get();
      $this->checkPermissions();

      ReportyPage::getComponent()
         ->setEnvironment($this->env)
         ->echo();
   }

   private function checkPermissions() {
      if($this->env->uzivatel->isMajitel || $this->env->uzivatel->isSpravce)
         return;

      if($this->env->personal->hasAccessRight(PersonalAccessRightsEnum::REPORTY))
         return;

      throw new Exception403('Chybějící právo pro zobrazení');
   }

   protected FrontApplicationEnvironment $env;
}