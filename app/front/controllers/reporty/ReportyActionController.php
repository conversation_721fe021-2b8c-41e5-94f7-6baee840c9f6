<?php namespace app\front\controllers\reporty;

use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\front\reporty\panely\eventy\component\calculator\MoreEventsCalculator;
use app\front\reporty\panely\eventy\component\overview\EventyReportOverview;
use app\front\reporty\panely\eventy\component\table\ReportEventyTable;
use app\front\reporty\panely\eventy\component\table\ReportyEventySeznamComponent;
use app\front\reporty\panely\finance\component\obrat\IncreaseTurnoverCalculator;
use app\front\reporty\panely\finance\component\platby\ReportFinancePlatbyTableComponent;
use app\front\reporty\panely\finance\component\ReportFinancialOverviewBody;
use app\front\reporty\panely\obchod\component\ReportComercialOverviewBody;
use app\front\reporty\panely\obchod\component\sells\ReportSellsOverviewBody;
use app\front\reporty\panely\obchod\component\sells\ReportSellsOverviewComponent;
use app\front\reporty\panely\zakladni\component\ReportOverviewBody;
use app\front\reporty\table\platby\prehled\PrehledPlatebTable;
use app\system\controller\ActionController;
use app\system\model\reporty\OrganizaceEventReport;
use app\system\router\Route;
use app\system\traits\JsonAjax;

/** Created by Filip Pavlas. Date: 13.12.2023 */
#[Route([
   'cs' => '^/reporty(?:/*)(?<action>\w*)$',
   'en' => '^/reports(?:/*)(?<action>\w*)$',
   'pl' => '^/raporty(?:/*)(?<action>\w*)$',
   'hu' => '^/jelentesek(?:/*)(?<action>\w*)$',
])]
class ReportyActionController
{

   use ActionController;
   use JsonAjax;

   const ACTION_EVENTOVERVIEW = 'eventoverview';
   public function action_eventoverview() {
      $this->checkSession();
      $this->sendAjaxResponse([
         'body' => (string)(new EventyReportOverview(
            $report = new OrganizaceEventReport($this->organizace, $_POST['year'])
         ))->render(),
         'eventyTable' => (new ReportyEventySeznamComponent($report))->renderHtml()->__toString()
      ], true);
   }

   const ACTION_OVERVIEW = 'overview';
   public function action_overview() {
      $this->checkSession();
      $this->sendAjaxResponse(['overview' => (new ReportOverviewBody(
         new OrganizaceEventReport($this->organizace, $_POST['year'])
      ))->render()], true);
   }

   const ACTION_EVENTY_TABLE = 'eventdyntable';
   public function action_eventdyntable() {
      $this->checkSession();

      $this->sendAjaxResponse([
         'eventyTable' => (string)(new ReportEventyTable())
            ->setIdOrganizace($this->organizace->id_organizace)
            ->setStartDate($_POST['start_date'])
            ->setEndDate($_POST['end_date'])
      ], true);
   }

   const ACTION_PAYMENTS = 'payments';
   public function action_payments() {
      $this->checkSession();
      $table = (new PrehledPlatebTable())
         ->setIdOrganizace($this->organizace->id_organizace)
         ->setStartDate($_POST['start_date'])
         ->setEndDate($_POST['end_date']);

      $this->sendAjaxResponse(['paymentTable' => (string)$table], true);
   }

   const ACTION_FINANCIAL_OVERVIEW = 'financialoverview';
   public function action_financialoverview() {
      $this->checkSession();
      $this->sendAjaxResponse([
         'financialOverview' => (string)(new ReportFinancialOverviewBody(
            $report = new OrganizaceEventReport($this->organizace, $_POST['year'])
         ))->render(),
         'platbyContainer' => (new ReportFinancePlatbyTableComponent($report))->renderHtml()->__toString(),
      ], true);
   }

   const ACTION_EVENT_CALCULATOR = 'eventcalculator';
   public function action_eventcalculator() {
      $this->checkSession();
      $this->sendAjaxResponse(['calculator' => (string)(new MoreEventsCalculator(
         (new OrganizaceEventReport($this->organizace, Date('Y'))),
         (int)$_POST['pocet'],
      ))->render()], true);
   }

   const ACTION_TURNOVER_CALCULATOR = 'turnovercalculator';
   public function action_turnovercalculator() {
      $this->checkSession();
      $this->sendAjaxResponse(['calculator' => (string)(new IncreaseTurnoverCalculator(
         new OrganizaceEventReport($this->organizace, $_POST['year']),
         bcmul((int)$_POST['navysenaCena'], '100'),
      ))->render()], true);
   }

   const ACTION_COMERCIAL_OVERVIEW = 'comercialoverview';
   public function action_comercialoverview() {
      $this->checkSession();
      $year = intval($_POST['year']);

      $this->sendAjaxResponse([
         'overview' => (string)(new ReportComercialOverviewBody(
            $report = new OrganizaceEventReport($this->organizace, $year)
         ))
            ->setActiveYears($_POST['activeYears'])
            ->render(),
         'polozkyComponent' => ReportSellsOverviewComponent::getComponent()
            ->setEventReport($report)->renderHtml()->__toString()
      ], true);
   }

   const ACTION_SELLS_TABLES = 'sellstables';
   public function action_sellstables() {
      $this->checkSession();
      $this->sendAjaxResponse([
         'tables' => (string)(new ReportSellsOverviewBody(
            new OrganizaceEventReport($this->organizace, $_POST['year'])
         ))
            ->setDateRange($_POST['start_date'], $_POST['end_date'])
            ->render(),
      ], true);
   }

   private function checkSession() :void {
      if(!$this->environment->isLogged())
         $this->sendAjaxResponse([202]);

      $this->organizace = Organizace::getMisto($this->environment->id_organizace);
   }

   private OrganizaceRow $organizace;
}