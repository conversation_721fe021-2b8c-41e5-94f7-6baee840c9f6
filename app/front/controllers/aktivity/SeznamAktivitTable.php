<?php namespace app\front\controllers\aktivity;

use app\front\eventy\detail\modaly\aktivity\EditAktivityModal;
use app\System;
use app\system\application\FrontApplicationEnvironment;
use app\system\model\event\BaseEventRow;
use app\system\model\event\data\aktivity\EventAktivityRow;
use app\system\model\event\data\aktivity\EventAktivityStavy;
use app\system\model\event\EventAktivity;
use app\system\model\organizace\personal\Personal;
use app\system\table\DynamicTable;
use app\system\table\DynamicTableColumn;
use Dibi\DateTime;
use Dibi\Fluent;
use Nette\Utils\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 06.12.2024 */
class SeznamAktivitTable extends DynamicTable
{

   public function __construct() {
      $this->environment = FrontApplicationEnvironment::get();
      $this->now = new DateTime();
   }

   function prepareTable() :void {
      $this->setOnPage(25);
      $this->addDataPreparator(function(array $rows) {
         $this->events = $this->environment->versionType->getEventyByID(array_values(array_unique(array_column($rows, 'id_event'))));
      });

      $this->addText('nazev', 'Název aktivity');

      if($this->environment->uzivatel->isMajitel || $this->environment->uzivatel->isSpravce)
         $this
            ->appendPersonalSelect()
            ->setWidth(DynamicTableColumn::WIDTH_15);

      $this
         ->appendTermin()
         ->setWidth(DynamicTableColumn::WIDTH_15)
         ->setDefaultOrder(DynamicTableColumn::SORT_ASC);

      $this->appendNazevEventu();

      $this
         ->appendStav()
         ->setWidth(DynamicTableColumn::WIDTH_15);

      $this->appendDetail();
   }

   function getQuery() :Fluent {
      $q = EventAktivity::find($this->environment->versionType)
         ->where('ea.id_mista = %i', $this->environment->id_organizace);

      if($this->environment->uzivatel->isMajitel || $this->environment->uzivatel->isSpravce)
         return $q;

      return $q->where('ea.id_prirazeny = %i', $this->environment->personal->id_personal);
   }

   private function appendTermin() :DynamicTableColumn {
      return $this->addDate('termin', 'Termín')
         ->setFormatter(
            function(DateTime $value, EventAktivityRow $row) :string {
               if(!$row->getStav()->isHotovo() && $value < $this->now)
                  return sprintf('<span class="badge bg-danger col-auto">%s</span>', $this->getTerminString($row));
               elseif($row->getStav()->isHotovo())
                  return sprintf('<span class="badge bg-success col-auto">%s</span>', $this->getTerminString($row));

               return $this->getTerminString($row);
            }
         );
   }

   private function appendNazevEventu() :DynamicTableColumn {
      return $this->addText('id_event', 'Event')
         ->setSearchable(false)->setOrderable(false)
         ->setFormatter(
            function(int $value) :string {
               return Html::el('a', [
                  'href' => $this->events[$value]->getDetailUrl()
               ])->setText($this->events[$value]->nazev ?: $this->events[$value]->getDefaultNazev());
            }
         );
   }

   private function appendPersonalSelect() :DynamicTableColumn {
      $columnPrirazeny = $this->addSelect('id_prirazeny', 'Personál', [0 => System::getTranslator()->translate('Nepřiřazeno')] + Personal::getPairsByOrganizace($this->environment->id_organizace));

      if($this->environment->personal)
         $columnPrirazeny->setDefaultFilterValue($this->environment->personal->id_personal);

      return $columnPrirazeny;
   }

   private function appendStav() :DynamicTableColumn {
      return $this->addSelect('stav', 'Stav', [0 => System::getTranslator()->translate('Pouze nedokončené')] + EventAktivityStavy::getTranslatedPairs())
         ->setDefaultFilterValue(0)
         ->setSearchCallback(function(Fluent $query, int $search) {
            if($search === 0)
               $query->where('ea.stav != %i', EventAktivityStavy::HOTOVO->value);
            else
               $query->where('ea.stav = %i', EventAktivityStavy::from($search)->value);
         });
   }

   private function appendDetail() :DynamicTableColumn {
      return $this->addText('id.id_aktivita', ' ')
         ->setSearchable(false)->setOrderable(false)
         ->setFormatter(
            function($value) :string {
               return Html::el('a', [
                     'href' => '#',
                  ] + EditAktivityModal::getShowAttributes($value))->setHtml('<span class="text-secondary"><i class="bi bi bi-arrow-up-right-circle hover-icon-arrow"></i></span>');
            }
         );
   }

   private function getTerminString(EventAktivityRow $row) :string {
      if($row->dtStart && $row->dtEnd)
         return sprintf(
            '%s - %s',
            $row->dtStart->format('j.n.Y H:i'),
            $row->dtEnd->format('H:i')
         );

      return $row->termin->format('j.n.Y');
   }

   private FrontApplicationEnvironment $environment;

   /** @var array<int,BaseEventRow> */
   private array $events;
   private DateTime $now;
}