<?php namespace app\front\formular;

use app\front\controllers\formular\FormulareActionController;
use app\front\controllers\home\HomepageController;
use app\front\formular\body\FormularBodyViewer;
use app\front\formular\header\FormularHeaderViewer;
use app\front\formular\modals\input\novy\NovyFormularInputModal;
use app\system\component\Component;
use app\system\model\formular\eventy\EditInputFormularEvent;
use app\system\model\formular\Formular;
use app\system\model\formular\data\FormularRespondentData;
use app\system\model\formular\enumy\FormInputEnum;
use app\system\model\formular\enumy\FormularRespondentTypEnum;
use app\system\model\formular\enumy\FormularViewerModeEnum;
use app\system\model\organizace\personal\PersonalRow;
use app\system\model\organizace\personal\prava\data\PersonalAccessRightsEnum;
use app\system\model\organizace\zakaznici\OrganizaceZakaznikRow;
use app\system\Redirect;
use app\system\traits\PostListener;
use Latte\Runtime\Html;
use Nette\Utils\Html as HtmlElements;

/** Created by Filip Pavlas. Date: 24.11.2023 */
class FormularViewerComponent extends Component
{

   use PostListener;

   public function setViewerU(OrganizaceZakaznikRow|PersonalRow $viewer) :static {
      if($viewer instanceof OrganizaceZakaznikRow){
         $this->zakaznik = $viewer;
         return $this;
      }

      /** @noinspection PhpConditionAlreadyCheckedInspection */
      if($viewer instanceof PersonalRow){
         $this->personal = $viewer;
         return $this;
      }

      return $this;
   }

   public function setForm(Formular $formular) :static {
      $this->form = $formular;
      return $this;
   }

   protected function preparePostListeners() :void {
      $this->isset('btnTvorbaInputu', function($post) {
         if($inputTyp = FormInputEnum::tryFrom($post['typInputu'])) {
            (new EditInputFormularEvent(null))
               ->setIdFormular($this->form->getId())
               ->setInputType($inputTyp)
               ->setIsBigSize(isset($post['is_big_size']))
               ->setIsRequired(isset($post['is_required']))
               ->setTitle($post['title'])
               ->setDescription($post['description'] ?? null)
               ->setOptions($post['news'] ?? null)
               ->call();

            Redirect::self();
         }
      });
   }

   public function setData() :array {
      if($this->viewMode !== FormularViewerModeEnum::EDIT)
         $this->setDisplayedRespondent();

      $this->appendComponentBody();

      if(
         $this->viewMode === FormularViewerModeEnum::FILL
         && isset($this->personal)
         && $this->personal->hasAccessRight(PersonalAccessRightsEnum::ADMIN)
      ) {
         $this->appendRespondents();
      }

      return $this->dataComponent;
   }

   public function setViewMode(FormularViewerModeEnum $viewMode) :static {
      $this->viewMode = $viewMode;
      return $this;
   }

   /** @return Html[] */
   private function prepareAddingBar() :array {
      $bar = [];
      foreach(FormInputEnum::cases() as $inputEnum)
         $bar[] = HtmlElements::el('button')
            ->addAttributes(['class' => 'form-control btn btn-outline-primary w-auto js-novy-input'])
            ->addAttributes(NovyFormularInputModal::getShowAttributesMultipleSugs([
               'inputType' => $inputEnum->value,
               'id_form' => $this->form->getId(),
            ]))
            ->addHtml($inputEnum->getTranslatedTitle());

      return $bar;
   }

   private function appendRespondents() :void {
      $this->dataComponent += [
         'respondents' => $this->form->getRespondents(),
         'respondentsTypes' => FormularRespondentTypEnum::getTypesLabels(),
         'showRespondentUrl' => FormulareActionController::getUrl($this->form->getId(), FormulareActionController::ACTION_SHOWRESPONDENT),
      ];
   }

   private function appendComponentBody() :void {
      $this->dataComponent += [
         'formHeader' => (new FormularHeaderViewer($this->form))
            ->setViewer($this->zakaznik ?? $this->personal)
            ->setViewmode($this->viewMode)
            ->render(),
         'formBody' => (new FormularBodyViewer($this->form))
            ->showRespondentsValue($this->respondent ?? null)
            ->setViewmode($this->viewMode)
            ->render(),
         'viewMode' => $this->viewMode,
         'enableAddBar' => ($this->form->stav->canBeEdited() &&  $this->viewMode->isEditable()),
         'addingBar' => $this->prepareAddingBar(),
         'isFront' => isset($this->personal),
         'homepageUrl' => HomepageController::getUrl(),
         'entity' => $this->form->getAssignedEntity(),
         'canSelectRespondent' => (isset($this->personal)
               && $this->personal->hasAccessRight(PersonalAccessRightsEnum::ADMIN)
            && (count($this->form->getRespondents()) > 1)),
         'respondentValues' => $this->respondent ?? null,
         'formData' => $this->form,
         'actionDeleteOption' => FormulareActionController::getUrl($this->form->getId(), FormulareActionController::ACTION_DELETE_OPTION),
      ];
   }

   private function setDisplayedRespondent() :void {
      $this->respondent = isset($this->zakaznik)
         ? $this->form->getRespondent($this->zakaznik)
         : (
            $this->form->getRespondent($this->personal)
            ?? $this->form->getRespondent(
               $this->form->getAssignedEntity()->getFormularEntityType()->getEntityZakaznik($this->form->getAssignedEntityId()))
         );
   }

   protected FormularRespondentData $respondent;
   protected FormularViewerModeEnum $viewMode;
   private OrganizaceZakaznikRow $zakaznik;
   private PersonalRow $personal;
   private Formular $form;
   private array $dataComponent = [];
}