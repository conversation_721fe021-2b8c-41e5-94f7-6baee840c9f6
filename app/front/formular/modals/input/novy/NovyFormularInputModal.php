<?php namespace app\front\formular\modals\input\novy;

use app\front\controllers\formular\FormulareActionController;
use app\system\component\Templater;
use app\system\model\formular\enumy\FormInputEnum;
use app\system\modul\modal\AjaxModal;

/** Created by <PERSON><PERSON>. Date: 27.11.2023 */
class NovyFormularInputModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Nové pole';
   }

   public function prepareModal(Templater $templater) {
      $decodedData = json_decode($_POST['slug'], true);

      $templater->addData([
         'typEnum' => FormInputEnum::tryFrom($decodedData['inputType']),
         'actionDeleteOption' => FormulareActionController::getUrl($decodedData['id_form'], FormulareActionController::ACTION_DELETE_OPTION),
      ]);
   }
}