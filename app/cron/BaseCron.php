<?php namespace app\cron;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 26.10.2022 */
abstract class BaseCron
   implements ICronEvent
{

   abstract function onStart() :void;

   public function execute() :void {
      $this->prepare();
      $this->onStart();
      $this->evaluation();
   }

   private function prepare() {
//      nastavení měřičů pro paměť a čas
   }

   private function evaluation() {
//      vyhodnocení měření
   }
}