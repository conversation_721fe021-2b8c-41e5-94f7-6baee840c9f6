<?php namespace app\cron;

use app\cron\types\CronType;
use app\system\helpers\Files;
use <PERSON><PERSON>\DateTime;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 04.04.2023 */
class CronLogger
{

   public static function log(CronType $type, DateTime $date, float|int $time, array $data = []) :void {
      (new self($type, $date, $time, $data))->saveLog();
   }

   public function __construct(
      private readonly CronType $type,
      private readonly DateTime $date,
      private readonly float|int $time,
      private readonly array $data = [],
   ) {
      $this->prepareDirectory();
   }

   public function saveLog() :void {
      $this->putLastActivityLog();
      $this->putChangesLog();
   }

   private function prepareDirectory() :void {
      Files::checkDir(Files::prepareRootDirPath(Files::LOG_CRON_DIR));
   }

   private function putLastActivityLog() :void {
      $file = $this->type->lastActivityFile();

      if(file_exists($file) && date($this->type->getFileExpirationFormatter(), filemtime($file)) != date($this->type->getFileExpirationFormatter()))
         unlink($file);

      file_put_contents($file, sprintf('[%s] Time: %.2f ms', $this->date->format('Y-m-d H:i:s'), $this->time) . PHP_EOL, FILE_APPEND);
   }

   private function putChangesLog() :void {
      if(empty($this->data))
         return;

      file_put_contents(
         $this->type->changesFile(),
         sprintf(
            '[%s] Time: %.2f ms | %s',
            $this->date->format('Y-m-d H:i:s'),
            $this->time,
            implode(' | ', $this->data),
         ) . PHP_EOL,
         FILE_APPEND
      );
   }

}