<?php namespace app\cron\types;

use app\cron\CronLogger;
use app\system\controller\ActionController;
use app\system\event\queue\EventQueue;
use app\system\model\nabidka\event\KontrolaExpiraceObalekEvent;
use app\system\model\nabidka\event\VendorKontrolaPlatnostiNabidekEvent;
use app\system\model\nabidka\event\VenueKontrolaPlatnostiNabidekEvent;
use app\system\model\organizace\subscription\event\RenewSubscriptionEvent;
use app\system\router\Route;
use app\system\tracker\OrganizaceKatalogTracker;
use Dibi\DateTime;
use JetBrains\PhpStorm\NoReturn;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 31.10.2023 */
#[Route('^/cron(?:/*)(?<action>\w*)$')]
class CronController
{

   use ActionController;

   protected function addProperties() :void {
      $this->date = new DateTime();
      $this->timer = microtime(true);
      $this->type = CronType::from($this->actionName);
   }

   public function action_queue() {
      EventQueue::callStack();
      $this->close();
   }

   public function action_minute() {
      $data = [];

      $upozorneno_nabidek = VenueKontrolaPlatnostiNabidekEvent::init()->call()->getPocet();
      $upozorneno_nabidek += VendorKontrolaPlatnostiNabidekEvent::init()->call()->getPocet();

      if($upozorneno_nabidek)
         $data[] = sprintf('Upozorneno nabidek: %d', $upozorneno_nabidek);

      $this->close($data);
   }

   public function action_hour() {
      $this->close();
   }

   public function action_day() {
      $data = [];

//      @TODO upravit event na queued event, static funkce pouze vygeneruje call pro všechny
      if(!empty($obnova = RenewSubscriptionEvent::lookupAndRenew()))
         $data[] = sprintf('Obnoveni licenci: [%s]', implode(',', $obnova));

      if($upozorneno = KontrolaExpiraceObalekEvent::init()->call()->getUpozorneniCount())
         $data[] = sprintf('Upozorneno nabidek: %d', $upozorneno);

      OrganizaceKatalogTracker::cronRollupAndRotate();
      $this->close($data);
   }

   #[NoReturn]
   private function close(array $customLogData = []) :void {
      CronLogger::log(
         $this->type,
         $this->date,
         round(microtime(true) - $this->timer, 4) * 100,
         $customLogData,
      );
      exit;
   }

   private float $timer;
   private CronType $type;
   private DateTime $date;
}