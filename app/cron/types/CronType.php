<?php namespace app\cron\types;

use app\cron\CronApplication;
use app\cron\CronLogger;
use app\system\helpers\Files;
use app\system\Redirect;
use app\system\SystemVersion;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 31.10.2023 */
enum CronType :string
{

   case QUEUE = 'queue';
   case MINUTE = 'minute';
   case HOUR = 'hour';
   case DAY = 'day';

   public function logLastActivityFileName() :string {
      return sprintf('cron-%s-last.log', $this->value);
   }

   public function logChangesFileName() :string {
      return sprintf('cron-%s-changes.log', $this->value);
   }

   public function lastActivityFile(string $directory = Files::LOG_CRON_DIR) :string {
      return sprintf('%s/%s', Files::prepareRootDirPath($directory), $this->logLastActivityFileName());
   }

   public function changesFile(string $directory = Files::LOG_CRON_DIR) :string {
      return sprintf('%s/%s', Files::prepareRootDirPath($directory), $this->logChangesFileName());
   }

   public function existLastActivity(string $directory = Files::LOG_CRON_DIR) :bool {
      return file_exists($this->lastActivityFile($directory));
   }

   public function existChangesLog(string $directory = Files::LOG_CRON_DIR) :bool {
      return file_exists($this->changesFile($directory));
   }

   public function renderLastActivity(string $directory = Files::LOG_CRON_DIR) :Html {
      return new Html(str_replace(PHP_EOL, '<br>',file_get_contents($this->lastActivityFile($directory))));
   }

   public function getUrl() :string {
      return Redirect::check(CronController::getUrl($this->value), SystemVersion::APP)  . '?password=' . CronApplication::getPasswordHash();
   }

   public function getFileExpirationFormatter() :string {
      return match ($this) {
         self::QUEUE, self::MINUTE => 'H',
         self::HOUR => 'd',
         self::DAY => 'n',
      };
   }
}