<?php namespace app\cron;

use app\cron\types\CronController;
use app\System;
use app\system\Application;
use app\system\application\environment\BlankApplicationEnvironment;
use app\system\application\IEnvironment;
use app\system\Environment;
use app\system\lay\error\Error404Page;
use app\system\router;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 26.10.2022 */
// /cron.php?password={password}&type={type}
class CronApplication extends Application
{

//         c173624e17f829f4b286bc65cd411a51
   protected static string $password = '29zCyas2T%8s';

   public static function getPasswordHash() :string {
      static $hash;

      if(isset($hash))
         return $hash;

      if(Environment::isLocalhost() || Environment::isDevelopment())
         self::$password = Environment::isDevelopment() ? '32zCyte2T%Rs' : 'test';

      return md5(self::$password);
   }

   protected function onPrepare() :void {
      $this->setBeforeStart(function() {
         if(!isset($_GET['password']) || $_GET['password'] !== self::getPasswordHash())
            Error404Page::show();

         if(Environment::isLocalhost())
            ini_set('max_execution_time', 120);
      });
   }

   public function prepareRoutes(router\RoutesContainer $container) :void {
      $container->addControllers(
         CronController::class,
      );
   }

   protected function prepareApplicationEnvironment(System $system) :IEnvironment {
      return BlankApplicationEnvironment::init($system);
   }

   protected function sessionLoader() :void { }
}