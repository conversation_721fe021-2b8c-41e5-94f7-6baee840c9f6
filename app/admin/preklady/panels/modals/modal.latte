{varType app\system\model\translator\Jazyky[] $jazyky}
{varType app\system\model\translator\data\model\TranslateHashRow $hash}
{varType app\system\model\translator\data\model\TranslateTextRow[] $preklady}
{varType app\system\model\translator\data\model\TranslateModelType $type}

<div class="container">
   <div class="row mb-2">
      Originální text [{$hash->hash}]: <span class="text-secondary">{$hash->original}</span>
   </div>
   <form method="post">
      <div class="row">
         {foreach $jazyky as $jazyk}
            <div n:if="!$jazyk->isRootLanguage()" class="row my-2">
               {var $preklad = $preklady[$jazyk->value] ?? null}
               <div class="form-group">
                  <label for="jazyk{$jazyk->value}">{$jazyk->getTitle()}<span n:if="$preklad && $preklad->waiting_correction === 1" class="text-danger"> - Ček<PERSON> na korekci</span></label>
                  <div class="input-group">
                     <input n:class="form-control, $preklad && $preklad->waiting_correction === 1 ? 'border-danger input-levy-round'" type="text" name="preklad[{$jazyk->value}][text]" id="jazyk{$jazyk->value}" value="{$preklad?->text ?: ''}">
                     <div n:if="$preklad && $preklad->waiting_correction === 1" class="input-group-text input-pravy-round border-danger">
                        <input class="form-check-input m-0" type="checkbox" name="preklad[{$jazyk->value}][korekce]" aria-label="Korekce dokončení" title="Korekce dokončení">
                     </div>
                  </div>
               </div>
            </div>
         {/foreach}
      </div>
      <div class="row mt-5">
         <div class="row">
            Datum vytvoření: <span class="text-secondary">{$hash->created|date: 'j.n.Y H:i'}</span>
         </div>
      </div>
      <div class="row mt-4 justify-content-end">
         <input type="hidden" name="model_type" value="{$type->value}">
         <div class="col-auto">
            <button class="btn btn-danger" type="submit" name="btnSmazatPreklady" data-confirm="Opravdu chcete smazat překlad?" value="{$hash->id}">Smazat</button>
         </div>
         <div class="col-auto">
            <button class="btn btn-primary" type="submit" name="btnUlozitPreklady" value="{$hash->id}">Uložit</button>
         </div>
      </div>
   </form>
</div>