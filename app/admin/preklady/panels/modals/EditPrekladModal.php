<?php namespace app\admin\preklady\panels\modals;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\translator\data\model\TranslateHash;
use app\system\model\translator\data\model\TranslateHashRow;
use app\system\model\translator\data\model\TranslateModelType;
use app\system\model\translator\data\model\TranslateTextRow;
use app\system\model\translator\data\model\TranslateTexts;
use app\system\model\translator\Jazyky;
use app\system\modul\modal\AjaxModal;
use Nette\Utils\Strings;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 17.12.2023 */
class EditPrekladModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Upravit překlad';
   }

   protected function preparePostListeners(): void{
      $this->isset('btnSmazatPreklady', function($post) {
         $type = TranslateModelType::from($post['model_type']);
         if($affected = $this->getHashRow($post['btnSmazatPreklady'], $type)->delete($type))
            FlashMessages::setSuccess('Překlad byl smazán, smazáno řádků $1', $affected)
               ->setNoTranslate();
      });

      $this->isset('btnUlozitPreklady', function($post) {
         $type = TranslateModelType::from($post['model_type']);
         $row = $this->getHashRow($post['btnUlozitPreklady'], $type);
         $translates = TranslateTexts::getForHash($row->id, $type);
         $preklady = $post['preklad'];

         foreach(Jazyky::cases() as $jazyk){
            if($jazyk->isRootLanguage())
               continue;

            $postTr = $preklady[$jazyk->value] ?? [];

            $preklad = $translates[$jazyk->value] ?? null;
            $novyPreklad = trim($postTr['text'] ?? '');

            if($preklad === null && !$novyPreklad)
               continue;

            if(!$novyPreklad){
               TranslateTexts::delete($preklad, $type);
               continue;
            }

            if($preklad?->text !== $novyPreklad){
               ($preklad?: TranslateTextRow::createAutoTranslate($row->id, $jazyk))
                  ->setText($novyPreklad)
                  ->anulateCorrenction(
                     !$preklad || $preklad->waiting_correction === 1 && isset($postTr['korekce'])
                  )
                  ->save($type);
               continue;
            }

            if($preklad && $preklad->waiting_correction === 1 && isset($postTr['korekce']))
               $preklad
                  ->anulateCorrenction()
                  ->save($type);
         }

         FlashMessages::setSuccess('Překlad $1 uložen', Strings::truncate($row->original, 40))
            ->setNoTranslate();
      });
   }

   public static function getShowAttributesByModel(string $value, TranslateModelType $type) :array {
      return self::getShowAttributes(sprintf('%s-%s', $type->value, $value));
   }

   public function prepareAjaxData() :void {
      $slug = $_POST['slug'];

      if(!str_contains($slug, '-')){
         trigger_error('Tabulka ' . __CLASS__ . ' nemá nastavený typ, použij getShowAttributesByModel()', E_USER_DEPRECATED);
         $this->id_hash = $_POST['slug'];
         $this->model_type = TranslateModelType::OBECNE;
         return;
      }

      $parts = explode('-', $slug);
      $this->model_type = TranslateModelType::from($parts[0]);
      $this->id_hash = $parts[1];
   }

   public function prepareModal(Templater $templater) {
      $preklady = TranslateTexts::getForHash($this->id_hash, $this->model_type);

      $templater->addData([
         'jazyky' => Jazyky::cases(),
         'hash' => TranslateHash::get($this->id_hash, $this->model_type),
         'preklady' => $preklady,
         'type' => $this->model_type,
      ]);
   }

   public int $id_hash;

   private function getHashRow(int $id_hash, TranslateModelType $type) :TranslateHashRow {
      return TranslateHash::get($id_hash, $type);
   }

   private TranslateModelType $model_type;
}