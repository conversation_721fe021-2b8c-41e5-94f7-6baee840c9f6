<?php namespace app\admin\preklady\panels\flash;

use app\admin\preklady\panels\tables\SeznamPrekladyTable;
use app\system\model\translator\data\model\TranslateModelType;
use app\system\modul\panels\Panel;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 23.01.2024 */
class FlashPrekladyPanel extends Panel
{

   function getMenuName() :string {
      return 'Flash message';
   }

   public function returnString() :?string {
      return (new SeznamPrekladyTable())
         ->setModelType(TranslateModelType::FLASH);
   }
}