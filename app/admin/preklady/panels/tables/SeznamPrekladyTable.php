<?php namespace app\admin\preklady\panels\tables;

use app\admin\preklady\panels\modals\EditPrekladModal;
use app\system\helpers\HtmlBuilder;
use app\system\model\translator\data\model\TranslateHash;
use app\system\model\translator\data\model\TranslateModelType;
use app\system\model\translator\data\model\TranslateTextRow;
use app\system\model\translator\data\model\TranslateTexts;
use app\system\model\translator\event\AutomaticTranslateEvent;
use app\system\model\translator\Jazyky;
use app\system\table\DynamicTable;
use app\system\table\DynamicTableColumn;
use dibi;
use Dibi\Fluent;
use Nette\Utils\Strings;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 17.12.2023 */
class SeznamPrekladyTable extends DynamicTable
{

   public function setModelType(TranslateModelType $type) :static {
      $this->type = $type;
      $this->addParametrQuery('model_type', $type->value);
//      $this->set
      return $this;
   }

   protected function generateIdTable() :string {
      $id = parent::generateIdTable();

      return sprintf('%s-%s', $id, $this->type->name);
   }

   private function getType() :TranslateModelType {
      return $this->type ??= TranslateModelType::from($_GET['model_type']);
   }

   function getQuery() :Fluent {
      return dibi::select('th.*')
         ->from($this->getType()->getHashTable(), 'th');
   }

   function prepareTable(): void {
      $this->addDataPreparator(function($rows) {
         $this->preklady = TranslateTexts::getAllAssocForHashes(array_column($rows, 'id'), $this->getType());
      });

      $this->addText('id', 'ID')
         ->setWidth('3%');

      $this->addText('hash', 'Hash')
         ->setWidth('3%');

      $this->addText('original', 'Originalní text')->setWidth(DynamicTableColumn::WIDTH_20);

      $this->appendPrelozeno();
      $this->appendKorekce();

      $this->addText('id3.id', ' ')
         ->setWidth(DynamicTableColumn::WIDTH_5)
         ->setOrderable(false)->setSearchable(false)
         ->setFormatter(function($value) {
            return HtmlBuilder::buildElement('a', array_merge([
               'href' => '#'
            ], EditPrekladModal::getShowAttributesByModel($value, $this->getType())))->setHtml('Detail')->get();
         });

      $this->setDefaultOrder('id');
      $mass = $this->prepareMultipleAction('id');

      $mass->addFunction(
         'AutoTranslateMissing',
         'Automatický překlad - chybějící jazyky',
         function($ids) {
            AutomaticTranslateEvent::translateMissing($ids, $this->getType());
         }
      );

      foreach(Jazyky::cases() as $jazyk){
         if($jazyk->isRootLanguage())
            continue;

         $mass->addFunction(
            'AutoTranslate' . $jazyk->value,
            sprintf('Automatický překlad - %s', $jazyk->getTitle()),
            function($ids) use($jazyk) {
               AutomaticTranslateEvent::translateBulk($ids, $jazyk, $this->getType());
//               FlashMessages::setSuccess('Překlady byly zaregistrované pro překlad'); neposílají se flashmessage
            }
         );
      }

      $mass->addFunction(
         'DeleteTranslates',
         'Smazat vybrané překlady',
         function($ids) {
            $rows = TranslateHash::getByIds($ids, $this->getType());

            foreach($rows as $row)
               $row->delete($this->getType());
         }
      );
   }

   private function appendKorekce() {
      $this->addSelect('id5.id', 'Čeká na korekci', [0 => 'ANO', 1 => 'NE'])
         ->setWidth(DynamicTableColumn::WIDTH_5)
         ->setOrderable(false)
         ->setFormatter(
            function($value, $row) {
               if(!isset($this->preklady[$row->id]))
                  return '-';

               $ret = [];
               foreach($this->preklady[$row->id] as $preklad){
                  if($preklad->waiting_correction === 0)
                     continue;

                  $jazyk = Jazyky::from($preklad->id_jazyk);
                  $ret[] = sprintf(
                     '%s - %s',
                     $jazyk->getISO6391(),
                     'ANO'
                  );
               }

               return !empty($ret)
                  ? implode('<br>', $ret)
                  : 'NE';
            }
         )->setSearchCallback(function(Fluent $query, int $search) {
            if($search === 0)
               return $query->join($this->getType()->getTextsTable(), 'tt')->on('tt.id_hash = th.id')
                  ->where('tt.waiting_correction = 1')
                  ->groupBy('th.id');

            return $query->join($this->getType()->getTextsTable(), 'tt')->on('tt.id_hash = th.id')
               ->where('tt.waiting_correction = 0')
               ->groupBy('th.id');
         });
   }

   private function appendPrelozeno() {
      $select = [];

      foreach(Jazyky::cases() as $jazyk)
         $select[$jazyk->value] = $jazyk->isRootLanguage() ? 'Bez překladů' : sprintf('Chybí - %s', $jazyk->getISO6391());

      $this->addSelect('id2.id', 'Přeloženo', $select)
         ->setWidth(DynamicTableColumn::WIDTH_10)
         ->setOrderable(false)
         ->setFormatter(
            function($value, $row) {
               if(!isset($this->preklady[$row->id]))
                  return '-';

               $ret = [];

               foreach($this->preklady[$row->id] as $preklad){
                  $jazyk = Jazyky::from($preklad->id_jazyk);
                  $ret[] = sprintf(
                     '<span title="%s"><img src="%s" class="img-flag" width="20" height="15" alt="flag-%s">&nbsp;(%s)</span>',
                     $preklad->text,
                     $jazyk->getIcon() ?: '',
                     $jazyk->getISO6391(),
                     Strings::truncate($preklad->text, 30)
                  );
               }

               return implode('<br>', $ret);
            }
         )->setSearchCallback(function(Fluent $query, int $search) {
            if(($jazyk = Jazyky::from($search))->isRootLanguage())
               return $query
                  ->leftJoin($this->getType()->getTextsTable(), 'tt')->on('tt.id_hash = th.id')
                  ->where('tt.id_jazyk IS NULL');

            return $query
               ->leftJoin($this->getType()->getTextsTable(), 'tt')->on('tt.id_hash = th.id AND tt.id_jazyk = %i', $jazyk->value)
               ->where('tt.id_jazyk IS NULL');
         });
   }

   /** @var TranslateTextRow[][]  */
   protected array $preklady = [];
   private TranslateModelType $type;
}