<?php namespace app\admin\preklady\panels\obecne;

use app\admin\preklady\panels\tables\SeznamPrekladyTable;
use app\system\model\translator\data\model\TranslateModelType;
use app\system\modul\panels\Panel;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 17.12.2023 */
class ObecnePrekladyPanel extends Panel
{

   function getMenuName() :string {
      return 'Obecné';
   }

   public function returnString() :?string {
      return (new SeznamPrekladyTable())
         ->setModelType(TranslateModelType::OBECNE);
   }
}