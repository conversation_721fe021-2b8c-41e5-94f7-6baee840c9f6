{varType app\system\event\queue\EventQueueRow[] $queued_events}
{varType app\system\event\queue\history\EventQueueHistoryRow[] $history_events}
{varType app\cron\types\CronType[] $crony}
{varType string $btnCallStack}
{varType string $btnTestEvent}


<div class="container-fluid">
   <div class="row p-2">
      <div class="card">
         <div class="card-body">
            <h3>Crony</h3>
            <div class="container">
               <table class="table">
                  <tr>
                     <th>Typ cronu</th>
                     <th>URL</th>
                     <th>Poslední aktivita</th>
{*                     <th>Soubor se změnami</th>*}
                  </tr>
                  {foreach $crony as $cron}
                     <tr>
                        <td>{$cron->name}</td>
                        <td>
                           {$cron->getUrl()}
                        </td>
                        <td><div n:if="$cron->existLastActivity()">{$cron->renderLastActivity()}</div></td>
{*                        <td><button n:if="$cron->existChangesLog()" class="btn btn-warning">Stáhnout soubor</button></td>*}
                     </tr>
                  {/foreach}
               </table>

            </div>
         </div>

      </div>
   </div>

   <div class="row">
      <div class="col-md-6 p-2">
         <div class="card">
            <div class="card-title">
               <div class="row p-2">
                  <div class="col-4"><h3>Event QUEUE</h3></div>
                  <div class="col-4"><a href="{$btnTestEvent}" class="btn btn-warning">Testovací eventy</a></div>
                  <div class="col-4"><a href="{$btnCallStack}" target="_blank" class="btn btn-primary">Spustit</a></div>
               </div>
            </div>
            <div class="card-body">
               <table class="table">
                  <tr>
                     <th>Class</th>
                     <th>Parametry</th>
                     <th>Status</th>
                     <th></th>
                  </tr>

                  {if empty($queued_events)}
                     <tr>
                        <td colspan="3">Nejsou eventy</td>
                     </tr>
                  {else}
                     {foreach $queued_events as $event}
                        <tr>
                           <td>{$event->class}</td>
                           <td>{$event->getParametersHumanLike()}</td>
                           <td>{$event->getStatus()->name}</td>
                           <td></td>
                        </tr>
                     {/foreach}
                  {/if}
               </table>
            </div>
         </div>
      </div>
      <div class="col-md-6 p-2">
         <div class="card">
            <div class="card-title"></div>
            <div class="card-body">
               <h3>Poslení úspěšné eventy</h3>
               <table class="table">
                  <tr>
                     <th>Event</th>
                     <th>Čas</th>
                     <th>Přidání do fronty</th>
                     <th>Čas dokončení</th>
                  </tr>

                  {foreach $history_events as $event}
                     <tr>
                        <td>{$event->class}</td>
                        <td>{$event->time} ms</td>
                        <td>{$event->stack_start|date: 'j.n H:i:s'}</td>
                        <td>{$event->created|date: 'j.n H:i:s'}</td>
                     </tr>
                  {/foreach}
               </table>
            </div>
         </div>
      </div>
   </div>

</div>