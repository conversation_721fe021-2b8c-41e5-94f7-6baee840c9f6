<?php namespace app\admin\controllers;

use app\front\uzivatel\LoginController;
use app\System;
use app\system\controller\Controller;
use app\system\model\uzivatele\admin\session\SessionAdminUzivatelFactory;
use app\system\Redirect;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 23.10.2024 */
#[Route('^/user-logout$')]
class AdminLogoutController
{

   use Controller;

   public function call() :void {
      SessionAdminUzivatelFactory::logout(System::get());
      Redirect::to(LoginController::getUrl());
   }
}