<?php namespace app\admin\controllers\test;

use app\system\controller\ActionController;
use app\system\router\NewRouter;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 05.06.2022 */
#[Route('^/admin/akce(?:-*)(?<action>\w*)(?:-*)(?<slug>\w*)$')]
class TestActionController
{

   use ActionController;

   public function action_test() {
      echo 'hello action test';
   }

   public function action_expert() {
      echo 'hello expert';
      bdump(NewRouter::get()->getRoute()->getParametrs());
      bdump(self::getUrl('expert'));
   }
}