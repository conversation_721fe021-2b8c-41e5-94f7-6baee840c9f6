<?php namespace app\admin\controllers\test;

use app\system\event\Event;
use app\system\event\queue\IEventQueue;
use app\system\model\poptavky\VenuePoptavky;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.10.2023 */
class TestEvent extends Event
   implements IEventQueue
{

   public function setTestProp(int $test) :static {
      $this->test = $test;
      return $this;
   }

   public function onCall() :void {
//      @TODO slouží pro vynucení chyby při testování
      if($this->test === 0)
         $this->test = null;

      $poptavka = VenuePoptavky::get($this->test);

//      @TODO zámerně neošetřeno
//      $nabidky = $poptavka?->getAllNabidky() ?: [];
      $nabidky = $poptavka->getAllNabidky();

      bdump([
         'id' => $this->test,
         'poptavka' => $poptavka,
         'nabidky' => $nabidky,
      ]);
   }

   public function onBeforeQueuedCall(array $params) :void {
      $this->test = $params['test'];
   }

   public function getQueueParams() :array {
      return [
         'test' => $this->test,
      ];
   }

   private ?int $test;
}