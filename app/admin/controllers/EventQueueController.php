<?php namespace app\admin\controllers;

use app\admin\controllers\test\TestEvent;
use app\system\controller\ActionController;
use app\system\event\queue\EventQueue;
use app\system\flash\FlashMessages;
use app\system\model\poptavky\VenuePoptavky;
use app\system\Redirect;
use app\system\router\Route;
use dibi;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 31.10.2023 */
#[Route('^/admin/~stack(?:-*)(?<action>\w*)(?:-*)(?<slug>\w*)$')]
class EventQueueController
{

   use ActionController;

   const ACTION_CALL_STACK = 'call';
   public function action_call() {
      EventQueue::callStack();
   }

   const ACTION_TEST_EVENT = 'test';
   public function action_test() {
      /** @var object $obj */
      $obj = dibi::select('MIN(%n) AS lowest_id, MAX(%n) AS highest_id', VenuePoptavky::COLUMN_ID, VenuePoptavky::COLUMN_ID)
         ->from(VenuePoptavky::TABLE)
         ->fetch();

      $random = random_int(1, 9);

      for($i = 1; $i <= 10; $i++){
         (new TestEvent())
            ->setTestProp(random_int($obj->lowest_id, $obj->highest_id))
            ->call();

         if($i === $random)
            (new TestEvent())
               ->setTestProp(0)
               ->call();
      }

      FlashMessages::setInfo('Bylo vygenerováno 11 eventů, z toho minimálně jeden chybový')
         ->setTitle('Test založení eventů')
         ->setTimeout(20000)
         ->setNoTranslate();

      Redirect::back();
   }
}