<?php namespace app\admin\controllers\misto;

use app\admin\mista\panelySeznamu\subscriptions\component\UpgradePolozkyDropdownComponent;
use app\system\controller\ActionController;
use app\system\model\subscription\polozky\SubscriptionPolozky;
use app\system\router\Route;
use app\system\traits\JsonAjax;

/** Created by <PERSON><PERSON>. Date: 04.04.2023 */
#[Route('^/organizace(?:-*)(?<venue_id>\d*)(?:/*)(?<action>\w*)$')]
class DetailMistaAdminActionController
{

   use ActionController;
   use JsonAjax;

   public const ACTION_UPGRADE_POLOZKY_SELECT = 'selectupgrade';
   public function action_selectupgrade() {
      $this->sendAjaxResponse([
         'upgrade' => (string)UpgradePolozkyDropdownComponent::getComponent()
            ->setDefaultPolozka(SubscriptionPolozky::from($_POST['id_polozky']))
      ], true);
   }
}