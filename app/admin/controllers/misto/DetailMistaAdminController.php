<?php namespace app\admin\controllers\misto;

use app\admin\mista\panelySeznamu\DetailMistaAdminPage;
use app\system\controller\Controller;
use app\system\router\NewRouter;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 10.11.2022 */
#[Route('^/organizace(?:-*)(?<venue_id>\d*)$')]
class DetailMistaAdminController
{

   use Controller;

   public function call() :void {
      $params = NewRouter::get()->getRoute()->getParametrs();

      DetailMistaAdminPage::getComponent()
         ->setIdMista($params['venue_id'])
         ->echo();
   }
}