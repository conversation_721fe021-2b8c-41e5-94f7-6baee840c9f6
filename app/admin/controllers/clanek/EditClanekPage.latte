{varType array $languages}
{varType int $randomID}
{varType ?app\system\model\clanky\SystemClanekRow $clanek}
{varType Latte\Runtime\Html $tinyEditor}
{varType array $availablePriorities}

<div class="container">
   <form method="post" enctype="multipart/form-data" id="clanek-form-{$randomID}">
      <div class="mb-3">
         <label for="nazev" class="form-label">{_'Název'}</label>
         <input type="text"
                class="form-control"
                name="nazev"
                id="nazev"
                required
                 n:attr="value => $clanek?->nazev ?? ''">
      </div>

      <div class="mb-3">
         <label for="id_jazyk" class="form-label">{_'Jazyk'}</label>
         <select name="id_jazyk" id="id_jazyk" class="form-select" required>
            {foreach $languages as $id_language => $name_language}
               <option value="{$id_language}"
                       n:attr="selected => isset($clanek) && $clanek->id_jazyk === $id_language">
                  {$name_language}
               </option>
            {/foreach}
         </select>
      </div>

      <div class="mb-3">
         <label for="meta_tag" class="form-label">{_'Meta tag'}</label>
         <input type="text"
                class="form-control"
                name="meta_tag"
                id="meta_tag"
                maxlength="255"
                 n:attr="value => $clanek?->meta_tag ?? ''">
      </div>

      <div class="mb-3">
         <label for="meta_title" class="form-label">{_'og:title'}</label>
         <input type="text"
                class="form-control"
                name="meta_title"
                id="meta_title"
                maxlength="70"
                 n:attr="value => $clanek?->meta_title ?? ''">
      </div>

      <div class="mb-3">
         <label for="meta_description" class="form-label">{_'og:desc'}</label>
         <input type="text"
                class="form-control"
                name="meta_description"
                id="meta_description"
                maxlength="170"
                 n:attr="value => $clanek?->meta_description ?? ''">
      </div>

      {ifset $availablePriorities}
         <div class="mb-3">
            <label for="priority" class="form-label">{_'Priorita'}</label>
            <select name="priority" id="priority" class="form-select">
               <option value="" n:attr="selected => $clanek?->priority === null">
                  Žádná priorita
               </option>
               {ifset $clanek?->priority}
                  {if !in_array($clanek->priority, $availablePriorities, true)}
                     <option value="{$clanek->priority}" selected>
                        {$clanek->priority}
                     </option>
                  {/if}
               {/ifset}

               {foreach $availablePriorities as $priority}
                  <option value="{$priority}"
                          n:attr="selected => $clanek?->priority === $priority">
                     {$priority}
                  </option>
               {/foreach}
            </select>
         </div>
      {/ifset}

      <div class="mb-3">
         {$tinyEditor}
      </div>

      <div class="mb-3">
         <label class="form-label">{_'Obrázek k článku'}</label>
         <div>
            <table class="table table-sm align-middle text-center w-100">
               <thead>
               <tr>
                  <th style="width: 40px;"></th>
                  <th></th>
                  <th></th>
               </tr>
               </thead>
               <tbody class="sortable-photo-table-{$randomID}" data-attachment-image-container>
               {if !empty($clanek->getPhotos())}
                  {foreach $clanek->getPhotos() as $index => $photo}
                     <tr data-attachment-file data-photo-id="{$photo->id}" data-position="{$photo->poradi ?? 0}"
                         class="{if $index === 0}table-thumbnail-row{/if}">
                        <td class="drag-handle align-middle text-muted" style="cursor: move;">
                           <i class="bi bi-grip-vertical fs-3 sortable-placeholder"></i>
                        </td>
                        <td>
                           <div class="photo-cell-wrapper">
                              <img src="{$photo->getFullSrc()}"
                                   alt="photo"
                                   class="img-thumbnail photo-preview"
                                   style="max-height: 70px;">
                              <input type="hidden" name="existing_photos[]" value="{$photo->id}">
                           </div>
                        </td>
                        <td>
                           <a class="btn btn-sm btn-outline-danger" data-attachment-delete title="Smazat">
                              <i class="bi bi-trash"></i>
                           </a>
                        </td>
                     </tr>
                  {/foreach}
               {/if}
               </tbody>
            </table>

         </div>
         {if isset($clanek)}
            <input type="hidden"
                   name="id_clanek"
                   required
                    n:attr="value => $clanek?->id ?? ''">
         {/if}
         <button type="button" class="btn btn-sm btn-outline-secondary" data-add-image-attachment>
            <i class="bi bi-paperclip"></i>{_'Přidat fotografii'}
         </button>
      </div>
      <div class="row mt-6 justify-content-end">
         <div class="col-auto">
            <button type="submit"
                    class="btn btn-outline-secondary no-validate"
                    name="btnDeleteClanek"
                    data-confirm="{_'Opravdu chce smazat článek?'}"
            >{_'Smazat'}</button>
         </div>

         {if !isset($clanek->published)}
            <div class="col-auto">
               <button type="submit"
                       class="btn btn-outline-primary no-validate"
                       name="btnPublish"
               >{_'Publikovat'}</button>
            </div>
         {else}
            <div class="col-auto">
               <button type="submit"
                       class="btn btn-outline-secondary no-validate"
                       name="btnDeletePublish"
                       data-confirm="{_'Opravdu chce smazat článek z publikace ?'}"
               >{_'Odstranit z publikace'}</button>
            </div>
         {/if}

         <div class="col-auto">
            <button type="submit" class="btn btn-primary" name="btnSaveClanek">{_'Uložit'}</button>
         </div>
      </div>
   </form>
</div>


<script>
   $(function() {
      const form = $('#clanek-form-' + {$randomID});

      form.validate({
         ignore: '.no-validate',
      });

   });

   $(function() {
      updatePhotoTableUI();
      const form = $('form#clanek-form-' + {$randomID});
      const btnAddAttachment = form.find('[data-add-image-attachment]');
      const fileInputContainer = form.find('[data-attachment-image-container]');
      const maxFilesContainerSize = 20 * 1024 * 1024;
      const maxFileSize = 5 * 1024 * 1024;
      const allowedExtensions = ['jpg', 'jpeg', 'png'];
      let filesContainerSize = 0;

      form.on('submit', function() {
         const orderedRows = fileInputContainer.find(' [data-attachment-file]');

         orderedRows.each(function(index) {
            const $row = $(this);
            const position = index + 1;
            let photoId = null;

            const existingInput = $row.find('input[name="existing_photos[]"]');
            if(existingInput.length){
               photoId = existingInput.val();
            }else{
               const fileInput = $row.find('input[type="file"]');
               if(fileInput.length && fileInput[0].files.length > 0){
                  photoId = fileInput.attr('name').match(/\[([^\]]+)\]/)?.[1] || null;
               }
            }

            if(photoId){
               const orderInput = document.createElement('input');
               orderInput.type = 'hidden';
               orderInput.name = 'photo_order[' + photoId + ']';
               orderInput.value = position;
               $row.append(orderInput);
            }
         });
      });

      fileInputContainer.sortable({
         items: '[data-attachment-file]',
         handle: '.drag-handle',
         placeholder: 'sortable-placeholder',
         helper: function(e, tr) {
            const $originals = tr.children();
            const $helper = tr.clone();
            $helper.children().each(function(index) {
               $(this).width($originals.eq(index).width());
            });
            $helper.css('background-color', tr.css('background-color'));
            return $helper;
         },
         update: function() {
            fileInputContainer.find('[data-attachment-file]').each(function(index) {
               $(this).attr('data-position', index + 1);
            });
            updatePhotoTableUI();
         },
      });


      btnAddAttachment.on('click', function(e) {
         e.preventDefault();

         const tempFileInput = document.createElement('input');
         tempFileInput.type = 'file';
         tempFileInput.name = 'attachments[]';
         tempFileInput.style.display = 'none';
         tempFileInput.setAttribute('accept', allowedExtensions.map(ext => `.${ ext }`).join(','));

         tempFileInput.click();

         tempFileInput.addEventListener('change', function() {
            if(
               tempFileInput.files.length === 0
            ){
               toastr['error']('Příloha neobsahuje žádné soubory', '', {
                  positionClass: 'toast-bottom-left',
                  progressBar: true,
               });
               tempFileInput.remove();
               return;
            }

            const file = tempFileInput.files[0];

            if(!(file instanceof File)){
               toastr['error']('Příloha je poškozena zkuste to znovu za chvíli, nebo kontaktujte podporu', '', {
                  positionClass: 'toast-bottom-left',
                  progressBar: true,
               });
               tempFileInput.remove();
               return;
            }

            const fileExtension = file.name.split('.').pop().toLowerCase();

            if(!allowedExtensions.includes(fileExtension)){
               toastr['error']('Omlouváme se, ale soubor, který jste nahráli, není ve formátu, který podporujeme.', '', {
                  positionClass: 'toast-bottom-left',
                  progressBar: true,
               });
               tempFileInput.remove();
               return;
            }

            const humanFileSizeMiB = (file.size / (1024 * 1024)).toFixed(2);
            const humanFileSize = (file.size / (1000 * 1000)).toFixed(2);

            if(file.size > maxFileSize){
               const humanMaxFileSize = (maxFileSize / (1024 * 1024)).toFixed(2);

               toastr['error'](`Omlouváme se, ale soubor, který jste nahráli je větší jak ${ humanMaxFileSize } MB (${ humanFileSize } MB)`, '', {
                  positionClass: 'toast-bottom-left',
                  progressBar: true,
               });
               tempFileInput.remove();
               return;
            }

            if((file.size + filesContainerSize) > maxFilesContainerSize){
               toastr['error'](`Omlouváme se, ale další soubor již nelze nahrát`, '', {
                  positionClass: 'toast-bottom-left',
                  progressBar: true,
               });
               tempFileInput.remove();
               return;
            }

            filesContainerSize += file.size;

            const tableBody = form.find('.sortable-photo-table-' +{$randomID})[0];
            console.log('.sortable-photo-table-' +{$randomID});
            console.log(tableBody);
            if(!tableBody) return;

            const row = document.createElement('tr');
            row.setAttribute('data-attachment-file', '');
            row.setAttribute('data-position', tableBody.children.length + 1);

            const fileURL = URL.createObjectURL(file);

            const handleCell = document.createElement('td');
            handleCell.className = 'drag-handle align-middle text-muted';
            handleCell.style.cursor = 'move';
            handleCell.innerHTML = '<i class="bi bi-grip-vertical fs-3"></i>';

            const photoCell = document.createElement('td');

            const photoWrapper = document.createElement('div');
            photoWrapper.className = 'photo-cell-wrapper d-flex align-items-center gap-2';
            photoWrapper.style.marginLeft = '50px';

            const img = document.createElement('img');
            img.src = URL.createObjectURL(file);
            img.alt = file.name;
            img.className = 'img-thumbnail photo-preview';
            img.style.maxHeight = '70px';

            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'new_photo_ids[]';
            hiddenInput.value = file.name;

            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            let count = fileInputContainer.children('[data-attachment-file]').length + 1;
            fileInput.name = 'attachments[add_' + count + ']';
            fileInput.style.display = 'none';
            fileInput.files = tempFileInput.files;

            const orderInput = document.createElement('input');
            orderInput.type = 'hidden';
            orderInput.name = 'photo_order[add_' + count + ']';
            orderInput.className = 'photo-order-input';
            orderInput.value = tableBody.children.length + 1;

            photoWrapper.appendChild(img);
            photoWrapper.appendChild(hiddenInput);
            photoWrapper.appendChild(fileInput);

            photoCell.appendChild(photoWrapper);
            photoCell.appendChild(orderInput);


            const actionCell = document.createElement('td');
            const deleteBtn = document.createElement('a');
            deleteBtn.className = 'btn btn-sm btn-outline-danger';
            deleteBtn.setAttribute('data-attachment-delete', '');
            deleteBtn.title = 'Smazat';
            deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';

            actionCell.appendChild(deleteBtn);

            row.appendChild(handleCell);
            row.appendChild(photoCell);
            row.appendChild(actionCell);
            row.appendChild(orderInput);

            tableBody.appendChild(row);

            updatePhotoTableUI();
         });
      });

      function updatePhotoTableUI() {
         const rows = $('[data-attachment-file]');
         rows.removeClass('table-thumbnail-row');
         rows.each(function(index) {
            $(this).find('.photo-order-input').val(index + 1);
            $(this).attr('data-position', index + 1);

            if(index === 0){
               $(this).addClass('table-thumbnail-row');
            }
         });
      }

      form.on('click', '[data-attachment-delete]', function(e) {
         const fileCard = e.target.closest('[data-attachment-file]');

         if(fileCard === undefined)
            return;
         fileCard.remove();
         btnAddAttachment.show();
         updatePhotoTableUI();
      });
   });
</script>

<style>
   .table-thumbnail-row {
      background-color: rgba(165, 74, 211, 0.18);
   }
   .sortable-placeholder {
      height: 70px;
      background-color: transparent;
      border: none;
   }
   .drag-handle i {
      transition: color 0.2s ease-in-out;
      color: #999;
   }

   .drag-handle:hover i {
      color: #d50ccc;
   }
   .photo-cell-wrapper {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-left: 50px;
   }
</style>