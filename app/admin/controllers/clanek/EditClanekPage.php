<?php namespace app\admin\controllers\clanek;

use app\admin\controllers\sablonyTexty\PrehledSablonTextuController;
use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\lay\dashboard\DashboardLayout;
use app\system\model\clanky\event\SaveClanekEvent;
use app\system\model\clanky\SystemClanek;
use app\system\model\clanky\SystemClanekRow;
use app\system\model\translator\Jazyky;
use app\system\Redirect;
use app\system\tiny\TinyEditor;
use app\system\traits\PostListener;
use Dibi\DateTime;
use Latte\Runtime\Html;
use Nette\Utils\Random;

class EditClanekPage extends DashboardLayout
{

   use PostListener;

   protected function prepareTemplate(Templater $templater) {
      $editor = TinyEditor::init()
         ->setInputName('text')
         ->setContent($this->clanekAttributes->text ?? '')
         ->setPlaceholder('text')
         ->setHeightPX(300);

      $templater->addData([
         'languages' => Jazyky::getPairs(),
         'randomID' => Random::generate(),
         'clanek' => $this->clanekAttributes ?? null,
         'tinyEditor' => new Html($editor->render()),
         'availablePriorities' => SystemClanek::getAvailablePriorities(),
      ]);
   }

   public function getPageName() :string {
      return 'Editace článku';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnSaveClanek', function ($post) {
         $this->validatePostData($post);
         $clanek = $this->findClanekIfExists($post);

         $post['id_jazyk'] = (int) $post['id_jazyk'];
         $post['priority'] = $post['priority'] === '' ? null : (int) $post['priority'];

         if((new SaveClanekEvent($clanek, $post, $_FILES))->call()->getSuccess())
            FlashMessages::setSuccess('Uloženo')
               ->setNoTranslate();
      });

      $this->isset('btnDeleteClanek', function($post) {
         $clanek = $this->findClanekIfExists($post);

            SystemClanek::delete($clanek->id);
            FlashMessages::setSuccess('Článek byl úspěšně smazán')
               ->setNoTranslate();

            Redirect::to(PrehledSablonTextuController::getUrl());
      });

      $this->isset('btnPublish', function($post) {
         $clanek = $this->findClanekIfExists($post);
         $clanek->published = new DateTime();
         SystemClanek::save($clanek);

         FlashMessages::setSuccess('Článek byl úspěšně publikován')
            ->setNoTranslate();
      });

      $this->isset('btnDeletePublish', function($post) {
         $clanek = $this->findClanekIfExists($post);
         $clanek->published = null;
         SystemClanek::save($clanek);

         FlashMessages::setSuccess('Článek byl úspěšně odpublikován')
            ->setNoTranslate();
      });
   }

   private function validatePostData(array $post) :void {
      foreach ($this->requiredKeys as $key) {
         if (!isset($post[$key])) {
            throw FlashException::create('Některé povinné údaje nebyly vyplněny')
               ->setNoTranslate();
         }
      }
   }

   private function findClanekIfExists(array $post) :SystemClanekRow {
      if (($clanek = SystemClanek::get($post['id_clanek'])) === null)
         throw FlashException::create('Článek nebyl nalezen')
            ->setNoTranslate();

      return $clanek;
   }


   public function setClanekAttributes(?SystemClanekRow $clanekAttributes) :self {
      $this->clanekAttributes = $clanekAttributes;

      return $this;
   }

   private SystemClanekRow $clanekAttributes;
   private array $requiredKeys = [
      'nazev',
      'id_jazyk',
   ];
}