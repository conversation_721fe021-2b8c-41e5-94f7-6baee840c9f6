<?php namespace app\admin\controllers\clanek;

use app\system\controller\Controller;
use app\system\controller\exceptions\page404\Exception404;
use app\system\model\clanky\SystemClanek;
use app\system\router\NewRouter;
use app\system\router\Route;

#[Route('^/edit-clanek-(?<clanek_id>\d+)$')]
class EditClanekController
{

   use Controller;

   public function call() :void {
      $params = NewRouter::get()
         ->getRoute()
         ->getParametrs();

      if(
         !($id_clanek = (int)($params['clanek_id'] ?? 0))
         || !($clanek = SystemClanek::get($id_clanek))
      ){
         throw new Exception404();
      }

      EditClanekPage::getComponent()
         ->setClanekAttributes($clanek)
         ->echo();
   }
}