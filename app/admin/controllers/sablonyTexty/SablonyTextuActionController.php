<?php namespace app\admin\controllers\sablonyTexty;

use app\system\controller\ActionController;
use app\system\flash\FlashMessages;
use app\system\helpers\text\blok\TextBlokyComponent;
use app\system\mailer\data\MailsModel;
use app\system\model\texty\sablony\SablonyPodtypuTextu;
use app\system\model\texty\sablony\SablonyTextBloky;
use app\system\model\texty\sablony\SablonyTexty;
use app\system\router\Route;
use app\system\traits\JsonAjax;

#[Route('^/admin/text(?:-*)(?<action>\w*)(?:-*)(?<slug>\w*)$')]
class SablonyTextuActionController
{

   use ActionController;
   use JsonAjax;

   public function action_moveBlok() {
      SablonyTexty::moveBlok($_POST);
      $bloky = (string)TextBlokyComponent::getComponent()->setIdText((int)$_POST['id_text'])->setType(TextBlokyComponent::SABLONA);

      $this->sendAjaxResponse([$bloky], true);
   }

   public function action_deleteBlok() {
      SablonyTexty::deleteBlok($_POST);
      SablonyTextBloky::deleteBlok($_POST['id_blok']);
      $bloky = (string)TextBlokyComponent::getComponent()->setIdText((int)$_POST['id_text'])->setType(TextBlokyComponent::SABLONA);

      $this->sendAjaxResponse([$bloky], true);
   }

   public function action_podtypy() {
      $podtypy = SablonyPodtypuTextu::getByTyp($_POST['id_typ']);
      $this->sendAjaxResponse(['podtypy' => $podtypy]);
   }

   public const string ACTION_DELETE_ATTACHMENT = 'deleteAttachment';
   public function action_deleteAttachment() {
      $post = $this->getRequestBody();

      if(
         !($mailID = intval($post['mailID']))
         || !($attachmentID = intval($post['attachmentID']))
         || !($attachment = MailsModel::getAttachment($attachmentID))
      ) {
         FlashMessages::setError('Tato příloha nelze smazat');
         $this->sendAjaxResponse([
            'success' => false
         ]);
      }

      $attachment->delete();
      $this->sendAjaxResponse([
         'success' => true
      ]);
   }
}