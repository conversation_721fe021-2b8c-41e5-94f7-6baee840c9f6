<?php namespace app\admin\controllers\sablonyTexty;

use app\admin\texty\detail\email\DetailSablonyEmailuPage;
use app\system\controller\Controller;
use app\system\router\NewRouter;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.09.2022 */
#[Route('^/admin/detail-email-sablona(?:-*)(?<slug>\w*)$')]
class DetailSablonyEmailuController
{

   use Controller;

   public function call() :void {
      DetailSablonyEmailuPage::getComponent()
         ->setIdTemplate(NewRouter::get()->getRoute()->getParametrs()['slug'])
         ->echo();
   }
}