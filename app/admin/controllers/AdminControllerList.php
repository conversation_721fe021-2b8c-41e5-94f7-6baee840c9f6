<?php namespace app\admin\controllers;

use app\admin\controllers\clanek\EditClanekController;
use app\admin\controllers\digisign\DigisignEnvelopeUpdateController;
use app\admin\controllers\digisign\DigisignNastaveniController;
use app\admin\controllers\misto\DetailMistaAdminController;
use app\admin\controllers\misto\PrehledMistController;
use app\admin\controllers\nastaveni\AdminNastaveniController;
use app\admin\controllers\sablonyTexty\DetailSablonyEmailuController;
use app\admin\controllers\sablonyTexty\DetailSablonyTextuController;
use app\admin\controllers\sablonyTexty\PrehledSablonTextuController;
use app\admin\controllers\sablonyTexty\SablonyTextuActionController;
use app\admin\controllers\subscription\NastaveniSubscriptionController;
use app\admin\controllers\test\TestActionController;
use app\admin\controllers\test\TestController;
use app\admin\home\AdminHomeController;
use app\admin\statistika\AdminStatistikaController;
use app\front\controllers\DynamicController;
use app\front\controllers\TinyEditorController;
use app\front\controllers\TranslateToggleController;
use app\front\uzivatel\LoginController;
use app\system\application\AdminApplicationEnvironment;
use app\system\router\RoutesContainer;

/** Created by Kryštof Czyź. Date: 05.06.2022 */
class AdminControllerList
{

   public static function prepareControllers(RoutesContainer $container, AdminApplicationEnvironment $environment) :void {
      $container->addControllers(
         LoginController::class,
      );

      if(!$environment->isLogged())
         return;

      $container->addControllers(
         DynamicController::class,
         TranslateToggleController::class,
         AdminLogoutController::class,
         TinyEditorController::class,
         TestActionController::class,
         TestController::class,
         AdminHomeController::class,
         UzivateleController::class,
         PrehledSablonTextuController::class,
         DetailSablonyTextuController::class,
         SablonyTextuActionController::class,
         PrekladyController::class,
         NastaveniSubscriptionController::class,
         DetailSablonyEmailuController::class,
         PrehledMistController::class,
         DetailMistaAdminController::class,
         DigisignNastaveniController::class,
         DigisignEnvelopeUpdateController::class,
         EventQueueController::class,
         AdminNastaveniController::class,
         EditClanekController::class,
         AdminStatistikaController::class,
      );
   }
}