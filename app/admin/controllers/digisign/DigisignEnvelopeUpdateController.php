<?php namespace app\admin\controllers\digisign;

use app\system\controller\Controller;
use app\system\flash\FlashMessages;
use app\system\model\nabidka\smlouva\event\UpdateObalkaEvent;
use app\system\model\nabidka\smlouva\obalka\SmlouvaObalka;
use app\system\Redirect;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 28.08.2025 */
#[Route('^/digisign-update-(?<obalkaID>\w*)$')]
class DigisignEnvelopeUpdateController
{

   use Controller;

   public function call() :void {
      bdump([
         $this->getRouteParameters()['obalkaID'],
      ], 'test');

      if(!($obalka = SmlouvaObalka::get($this->getRouteParameters()['obalkaID']))){
         FlashMessages::setError('Neexistující obálka')->setNoTranslate();
         Redirect::to(DigisignNastaveniController::getUrl());
      }

      UpdateObalkaEvent::update($obalka)->call();
      FlashMessages::setSuccess('Update obálky proběhl úspěšně');
      Redirect::to(DigisignNastaveniController::getUrl());
   }
}