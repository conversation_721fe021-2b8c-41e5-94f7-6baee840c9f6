<?php namespace app\admin\layout\header;

use app\admin\controllers\AdminLogoutController;
use app\system\application\AdminApplicationEnvironment;
use app\system\component\Component;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.10.2024 */
class AdminHeaderComponent extends Component
{

   function setData() :array {
      return [
         'urlLogout' => AdminLogoutController::getUrl(),
         'user' => $this->environment->user,
      ];
   }

   public function setEnvironment(AdminApplicationEnvironment $environment) :AdminHeaderComponent {
      $this->environment = $environment;
      return $this;
   }

   private AdminApplicationEnvironment $environment;
}