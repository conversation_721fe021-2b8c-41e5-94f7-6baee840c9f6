{varType string $urlLogout}
{varType app\system\model\uzivatele\admin\session\SessionAdminUzivatel $user}

<nav class="navbar navbar-expand navbar-theme sticky-top">
   <a class="sidebar-toggle d-flex me-2">
      <i class="hamburger align-self-center"></i>
   </a>
   <div class="navbar-collapse collapse sticky-top">
      <ul class="navbar-nav ms-auto">
         {include uzivatel}
      </ul>
   </div>
</nav>

{define uzivatel}
   <li class="nav-item dropdown ms-lg-2">
      <a class="nav-link dropdown-toggle position-relative" href="#" id="userDropdown" data-bs-toggle="dropdown">
         <div class="nav-flex border-corner">
            {$user->name}
         </div>
      </a>
      <div class="dropdown-menu dropdown-menu-end js-dropdown-keep" aria-labelledby="userDropdown">
         <div class="dropdown-item">
            <label>
               <input type="checkbox" class="js-switchtranslate" {if $_COOKIE['qvamp_translate'] ?? false ?? false}checked{/if}>
               Překlady debug
            </label>
         </div>
         <div class="dropdown-divider"></div>
         <a class="dropdown-item" href="{$urlLogout}"><i class="align-middle me-1 bi bi-box-arrow-right"></i> {_'Odhlásit se'}</a>
      </div>
   </li>
{/define}