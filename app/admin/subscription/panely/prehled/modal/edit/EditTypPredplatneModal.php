<?php namespace app\admin\subscription\panely\prehled\modal\edit;

use app\system\component\Templater;
use app\system\model\subscription\plan\SubscriptionPlan;
use app\system\model\subscription\plan\SubscriptionPlanRow;
use app\system\modul\modal\AjaxModal;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 26.08.2022 */
class EditTypPredplatneModal extends AjaxModal
{
   protected function preparePostListeners() :void {
      $this->isset('btnSavePlan', function ($post) {
         $plan = SubscriptionPlan::createFromPost($post, false);
         $plan->id_plan = $post['id_plan'];
         SubscriptionPlan::save($plan);
      });
   }

   public function getTitleName() :string {
      return 'Úprava typu předplatného';
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'plan' => $this->plan,
         'isActive' => ($this->plan->is_active) ? 'true' : 'false',
      ]);
   }

   public function prepareAjaxData(): void {
      $this->plan = SubscriptionPlan::get($_POST['slug']);
   }

   protected ?SubscriptionPlanRow $plan;
}