<?php namespace app\admin\subscription\panely\prehled\table;

use app\admin\subscription\panely\prehled\modal\edit\EditTypPredplatneModal;
use app\system\helpers\HtmlBuilder;
use app\system\model\subscription\plan\SubscriptionPlan;
use app\system\table\DynamicTable;
use dibi;
use Dibi\Fluent;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 26.08.2022 */
class SeznamTypuPredplatnychTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addText('nazev', 'Název');
      $this->addText('id_plan', ' ')
         ->setFormatter(function($value) {
            return HtmlBuilder::buildElement('a', array_merge([
               'href' => '#'
            ], EditTypPredplatneModal::getShowAttributes($value)))->setHtml('edit')->get();
         });
   }

   function getQuery() :Fluent {
      return dibi::select('*')
         ->from(SubscriptionPlan::TABLE);
   }
}