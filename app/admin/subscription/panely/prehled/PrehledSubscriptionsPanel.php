<?php namespace app\admin\subscription\panely\prehled;

use app\admin\subscription\panely\prehled\table\SeznamTypuPredplatnychTable;
use app\system\component\Templater;
use app\system\modul\panels\Panel;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 26.07.2022 */
class PrehledSubscriptionsPanel extends Panel
{

   function getMenuName() :string {
      return 'Přehled';
   }

   public function preparePanel(Templater $templater) :void {
      $templater->addData([
         'tableTypy' => (string)(new SeznamTypuPredplatnychTable())
      ]);
   }
}