<?php namespace app\admin\subscription;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\lay\dashboard\DashboardLayout;
use app\system\model\subscription\polozky\SubscriptionPolozky;
use app\system\model\subscription\polozky\SubscriptionPolozkyCeny;
use app\system\model\translator\Jazyky;
use app\system\traits\PostListener;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 24.07.2022 */
class AdminSubscriptionPage extends DashboardLayout
{

   use PostListener;

   function getPageName() :string {
      return 'Přehled subscription položek';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnSaveSubsPolozkyCeny', function($post) {
         $insert = [];
         $cenyPost = $post['ceny'];
         $nazvy = $post['nazvy'];

         foreach(SubscriptionPolozky::cases() as $polozka)
            foreach(Jazyky::cases() as $jazyk)
               if(trim($nazvy[$polozka->value][$jazyk->value]) && ($cenyPost[$polozka->value][$jazyk->value] || $cenyPost[$polozka->value][$jazyk->value] !== '0.00'))
                  $insert[] = [
                     'id_polozka%i' => $polozka->value,
                     'id_jazyk%i' => $jazyk->value,
                     'cena%i' => (int)bcmul($cenyPost[$polozka->value][$jazyk->value]?? '0', '100'),
                     'nazev%s' => trim($nazvy[$polozka->value][$jazyk->value]?? '')
                  ];

         if(!empty($insert))
            FlashMessages::setSuccess('Mutace položek byly uloženy, ovlivněno $1 řádků', SubscriptionPolozkyCeny::saveBulk($insert));
      });
   }

   protected function prepareTemplate(Templater $templater) {
      $templater->addData([
         'polozky' => SubscriptionPolozky::cases(),
         'jazyky' => Jazyky::cases(),
         'ceny' => SubscriptionPolozkyCeny::getAssocAll(),
      ]);
   }
}