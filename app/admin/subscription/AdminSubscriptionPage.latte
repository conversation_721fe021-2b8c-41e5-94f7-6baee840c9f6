{varType app\system\model\subscription\polozky\SubscriptionPolozky[] $polozky}
{varType app\system\model\translator\Jazyky[] $jazyky}
{varType app\system\model\subscription\polozky\SubscriptionPolozkyCenyRow[][] $ceny}

<div class="container-fluid">
   <form method="post">
      <div class="table-responsive">
         <table class="table">
            <tr>
               <td class="border-bottom"></td>
               {foreach $jazyky as $jazyk}
                  <td class="text-center border-bottom">
                     <h4>{$jazyk->getTitle()}</h4>
                  </td>
               {/foreach}
            </tr>
            {foreach $polozky as $polozka}
               <tr>
                  <td class="border-bottom">{$polozka->name}</td>
                  {foreach $jazyky as $jazyk}
                     <td class="border-bottom">
                        <div class="row mb-2">
                           <div class="input-group">
                              <span class="input-group-text input-levy-round">Název</span>
                              <input type="text" name="nazvy[{$polozka->value}][{$jazyk->value}]"
                                     value="{$ceny[$polozka->value][$jazyk->value]?->nazev?? ''}"
                                     class="form-control input-pravy-round" maxlength="120">
                           </div>
                        </div>
                        <div class="row">
                           <div class="input-group">
                              <span class="input-group-text input-levy-round">Cena</span>
                              <input type="text" name="ceny[{$polozka->value}][{$jazyk->value}]"
                                     value="{$ceny[$polozka->value][$jazyk->value]?->cena?? ''}"
                                     class="js-price-mask form-control text-end" data-mask="true" style="border-radius: 0 !important;">
                              <span class="input-group-text input-pravy-round">{$jazyk->getMainCurrencyCode()}</span>
                           </div>
                        </div>
                     </td>
                  {/foreach}
               </tr>
            {/foreach}
         </table>
      </div>
      <div class="row">
         <input type="submit" class="btn btn-primary w-auto" name="btnSaveSubsPolozkyCeny" value="Uložit">
      </div>
   </form>
</div>