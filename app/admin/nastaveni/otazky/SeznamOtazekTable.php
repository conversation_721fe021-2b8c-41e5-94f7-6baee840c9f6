<?php namespace app\admin\nastaveni\otazky;

use app\admin\nastaveni\otazky\edit\EditOtazkaModal;
use app\system\helpers\HtmlBuilder;
use app\system\model\katalog\otazky\OtazkyPreklady;
use app\system\model\translator\Jazyky;
use app\system\table\DynamicTable;
use Dibi\Fluent;

/** Created by <PERSON><PERSON><PERSON>. Date: 13.02.2025 */
class SeznamOtazekTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addText('id_otazka', 'ID');
      $this->addText('nazev', 'Nazev');
      $this->addText('id1.id_otazka', ' ')
         ->setFormatter(
            function($value) {
               return HtmlBuilder::buildElement('a', array_merge([
                  'href' => '#'
               ], EditOtazkaModal::getShowAttributes($value)))->setHtml('Detail')->get();
            }
         );
   }

   function getQuery() :Fluent {
      return OtazkyPreklady::findForSpecificLanguage(Jazyky::CZ);
   }
}