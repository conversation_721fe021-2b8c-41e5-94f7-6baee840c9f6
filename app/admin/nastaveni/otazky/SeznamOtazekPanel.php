<?php namespace app\admin\nastaveni\otazky;

use app\admin\nastaveni\otazky\edit\PridaniOtazkaModal;
use app\system\component\Templater;
use app\system\modul\panels\Panel;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON>. Date: 13.02.2025*/
class SeznamOtazekPanel extends Panel
{

   function getMenuName() :string {
      return 'Otázky';
   }

   public function preparePanel(Templater $templater) :void {
      $templater->addData([
         'tabulka' => new Html(new SeznamOtazekTable()),
         'modalAttr' => PridaniOtazkaModal::init()->btnToggleAttributes()
      ]);
   }
}