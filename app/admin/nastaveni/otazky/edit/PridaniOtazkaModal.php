<?php namespace app\admin\nastaveni\otazky\edit;

use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\model\katalog\otazky\event\SaveOtazkaEvent;
use app\system\model\translator\Jazyky;
use app\system\modul\modal\Modal;
use Nette\Utils\Random;

/** Created by <PERSON><PERSON><PERSON>. Date: 13.02.2025 */
class PridaniOtazkaModal extends Modal
{
   
   public function getTitleName() :string {
      return 'Přidání otázky';
   }

   public function prepareModal(Templater $templater) :void {
      $templater->addData([
         'otazkaPreklady' => [],
         'otazka' =>  [],
         'jazyky' => Jazyky::getPairs(),
         'randomID' => Random::generate(),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnAddOtazka', function($post) {
         if((new SaveOtazkaEvent($post))->call()->getSuccess())
            FlashMessages::setSuccess('Otázka byla úspěšně přidána');
      });
   }
}