<?php namespace app\admin\nastaveni\otazky\edit;

use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\model\katalog\otazky\event\SaveOtazkaEvent;
use app\system\model\katalog\otazky\Otazky;
use app\system\model\katalog\otazky\OtazkyPreklady;
use app\system\model\katalog\otazky\OtazkyPrekladyRow;
use app\system\model\translator\Jazyky;
use app\system\modul\modal\AjaxModal;
use Nette\Utils\Random;

/** Created by <PERSON><PERSON><PERSON>. Date: 13.02.2025 */
class EditOtazkaModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Úprava otázky';
   }

   public function prepareAjaxData() :void {
      $this->otazka = OtazkyPreklady::getAllLanguages($_POST['slug']);
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'otazkaPreklady' => $this->otazka,
         'otazka' => current($this->otazka),
         'jazyky' => Jazyky::getPairs(),
         'randomID' => Random::generate(),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUpravitOtazka', function($post) {
         if((new SaveOtazkaEvent($post))->call()->getSuccess())
            FlashMessages::setSuccess('Otázka byla úspěšně upravena');
      });

      $this->isset('btnDeleteOtazka', function($post) {
         if(!isset($post['id_otazka']))
            throw FlashException::create('Chyba při mazání otázky');

         Otazky::delete($post['id_otazka']);
      });
   }

   /** @var OtazkyPrekladyRow[] $otazky */
   private array $otazka;
}