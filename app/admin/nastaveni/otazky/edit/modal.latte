{varType ?app\system\model\katalog\otazky\OtazkyPrekladyRow[] $otazkaPreklady}
{varType app\system\model\translator\Jazyky[] $jazyky}
{varType ?app\system\model\katalog\otazky\OtazkyPrekladyRow[] $otazka}
{varType int $randomID}

<div class="container">
   <form method="post" id="otazka-form-{$randomID}">
      <div class="row">
         <h5>{_'Obecné informace'}</h5>
         {foreach $jazyky as $id_jazyk => $nazev_jazyk}
            <div class="col-6">
               <div class="mb-3">
                  <label for="nazev_{$id_jazyk}" class="form-label">
                     {$nazev_jazyk}
                  </label>
                  <input type="text" class="form-control odpovedi-input" id="nazev_{$id_jazyk}" name="otazky[{$id_jazyk}]"
                          n:attr="value:(isset($otazkaPreklady) && array_key_exists($id_jazyk, $otazkaPreklady)) ? $otazkaPreklady[$id_jazyk]->nazev"
                         required>
               </div>
            </div>
         {/foreach}
      </div>
      <div class="row gap-3 mt-3 d-flex justify-content-end">
         <div n:if="$otazka" class="col-auto">
            <input type="hidden" name="id_otazka" value="{$otazka->id_otazka}" >
            <button  type="submit" class="btn btn-primary shaddow-hover w-auto" name="btnUpravitOtazka">{_'Uložit'}</button>
         </div>
         <div n:if="$otazka" class="col-auto">
            <button type="submit"
                    class="btn btn-outline-secondary w-auto shaddow-hover no-validate"
                    data-confirm="{_'Opravdu si přejete odstarnit otázku?'}" formnovalidate
                    name="btnDeleteOtazka">{_'Smazat'}
            </button>
         </div>
         <div n:if="!$otazka" class="col-auto">
            <button type="submit" class="btn btn-primary w-auto shaddow-hover" name="btnAddOtazka">{_'Přidat otázku'}</button>
         </div>
      </div>
   </form>
</div>

<script>
   $(function () {
      const form = $('form#otazka-form-' + {$randomID});

      form.validate({
         ignore: '.no-validate'
      });

      form.find('.odpovedi-input').each(function () {
         $(this).rules("add", {
            noSpaceOnly: true,
            required: true,
         });
      });
   });

</script>