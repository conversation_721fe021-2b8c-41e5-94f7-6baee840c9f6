<?php namespace app\admin\nastaveni\digisign;

use app\admin\nastaveni\digisign\panel\envelopes\DigisignEnvelopePanel;
use app\admin\nastaveni\digisign\panel\webhook\DigisignWebhookPanel;
use app\system\lay\panels\PanelsLayout;
use app\system\traits\PostListener;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 06.06.2023 */
//@TODO do panelu NastaveniPage
class DigisignNastaveniPage extends PanelsLayout
{

   use PostListener;

   function getPageName() :string {
      return 'Digisign';
   }

   function preparePanely() :void {
      $this->subscribePanel(
         DigisignWebhookPanel::init()
      );

      $this->subscribePanel(
         DigisignEnvelopePanel::init()
      );
   }
}