{varType app\system\webhooks\digisign\WebhookMapping[] $webhooks}

<div class="card">
   <div class="card-body">
      <div class="container-fluid">
         <form method="post">
            <table class="table">
               <tr>
                  <th>Název</th>
                  <th>Status</th>
                  <th>Poslední z<PERSON>cován<PERSON></th>
                  <th></th>
               </tr>
               <tr n:foreach="$webhooks as $webhook">
                  <td>{$webhook->value}</td>
                  <td>
                     {if !$webhook->getSecret()}
                        <button class="btn btn-warning" name="btnInitWebhook" value="{$webhook->value}">Inicializovat</button>
                     {else}
                        {if $webhook->getSecret()->is_active}
                           <button class="btn btn-primary" name="btnDeaktivovatWebhook" value="{$webhook->value}">Aktivní</button>
                        {else}
                           <button class="btn btn-danger" name="btnAktivovatWebhook" value="{$webhook->value}">Neaktivní</button>
                        {/if}
                     {/if}
                  </td>
                  <td>
                     {if !$webhook->getSecret() || !$webhook->lastHistory()}
                        ---
                     {else}
                        {$webhook->lastHistory()->created|date: 'j.n.Y H:i:s'}
                     {/if}
                  </td>
                  <td>
                     <button class="btn btn-warning" name="btnOtestovatWebhook" n:attr="disabled: !$webhook->getSecret() || !$webhook->getSecret()->is_active, value: $webhook->value">Otestovat</button>
                  </td>
               </tr>
            </table>
         </form>
      </div>
   </div>
</div>
