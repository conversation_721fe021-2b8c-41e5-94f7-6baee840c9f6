<?php namespace app\admin\nastaveni\digisign\panel\envelopes\table;

use app\admin\controllers\digisign\DigisignEnvelopeUpdateController;
use app\system\model\nabidka\smlouva\obalka\SmlouvaObalka;
use app\system\model\nabidka\smlouva\obalka\SmlouvaObalkaRow;
use app\system\model\nabidka\smlouva\obalka\SmlouvaObalkaStavy;
use app\system\table\DynamicTable;
use Dibi\Fluent;
use Nette\Utils\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 28.08.2025 */
class DigisignEnvelopesTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addText('id', 'ID');

      $this->addText('nabidka.id', 'Nabidka')
         ->setFormatter(
            function(int $value, SmlouvaObalkaRow $row) {
               return "{$row->getVersion()->name}: $row->id_nabidka";
            }
         )->setSearchCallback(
            function(Fluent $fluent, string $search) {
               $fluent->where('nso.id_nabidka = %i', $search);
            }
         );

      $this->addSelect('status', 'Status', SmlouvaObalkaStavy::getPairs());

      $this->addText('detail.id', ' ')
         ->setOrderable(false)
         ->setSearchable(false)
         ->setFormatter(
            function(int $value, SmlouvaObalkaRow $row) {
               return Html::el('a', [
                  'href' => DigisignEnvelopeUpdateController::getUrl($row->id),
               ])->setText('Update')->__toString();
            }
         );
   }

   function getQuery() :Fluent {
      return SmlouvaObalka::find();
   }
}