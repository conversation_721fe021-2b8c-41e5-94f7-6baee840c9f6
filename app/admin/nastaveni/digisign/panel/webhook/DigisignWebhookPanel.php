<?php namespace app\admin\nastaveni\digisign\panel\webhook;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\webhook\DigisignWebhookStatus;
use app\system\model\webhook\event\RegisterDigiSignWebhook;
use app\system\model\webhook\event\StatusDigiSignWebhook;
use app\system\model\webhook\event\TestDigiSignWebhook;
use app\system\modul\panels\Panel;
use app\system\webhooks\digisign\WebhookMapping;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 28.08.2025 */
class DigisignWebhookPanel extends Panel
{

   function getMenuName() :string {
      return 'Webhooky';
   }

   public function preparePanel(Templater $templater) :void {
      $templater->addData([
         'webhooks' => WebhookMapping::cases(),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnInitWebhook', function($post) {
         if(RegisterDigiSignWebhook::register(WebhookMapping::from($post['btnInitWebhook']))
            ->call()
            ->getSuccess())
            FlashMessages::setSuccess('Webhook byl zaregistrován');
      });

      $this->isset('btnAktivovatWebhook', function($post) {
         if(($webhook = WebhookMapping::from($post['btnAktivovatWebhook']))->getSecret() === null){
            FlashMessages::setError('Webhook není inicializován');
            return;
         }

         if(StatusDigiSignWebhook::change($webhook, DigisignWebhookStatus::ENABLED)
            ->call()
            ->getSuccess())
            FlashMessages::setSuccess('Webhook je aktivovaný');
      });

      $this->isset('btnDeaktivovatWebhook', function($post) {
         if(StatusDigiSignWebhook::change(WebhookMapping::from($post['btnDeaktivovatWebhook']), DigisignWebhookStatus::DISABLED)
            ->call()
            ->getSuccess())
            FlashMessages::setWarning('Webhook je deaktivovaný');
      });

      $this->isset('btnOtestovatWebhook', function($post) {
         if(TestDigiSignWebhook::test(WebhookMapping::from($post['btnOtestovatWebhook']))->call()->getSuccess())
            FlashMessages::setSuccess('Požadavek na otestování byl odeslán');
      });
   }
}