<?php namespace app\admin\nastaveni\staty\edit;

use app\system\component\Templater;
use app\system\model\organizace\meny\Meny;
use app\system\model\staty\StatRow;
use app\system\model\staty\Staty;
use app\system\model\translator\Jazyky;
use app\system\modul\modal\AjaxModal;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 18.01.2024 */
class EditStatModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Edit státu';
   }

   public function prepareAjaxData() :void {
      $this->stat = Staty::get($_POST['slug']);
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'stat' => $this->stat,
         'meny' => Meny::getPairs(),
         'jazyky' => Jazyky::getPairs(),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUpravitStat', function($post) {
         $stat = Staty::get($post['id_stat']);

         $stat->is_favorite = isset($post['is_favorite']) ? 1 : 0;
         $stat->save();

         $stat->getTelefonMask()->mask = $post['telefon-mask'] ?? null;
         $stat->getTelefonMask()->save();
      });
   }

   private StatRow $stat;
}