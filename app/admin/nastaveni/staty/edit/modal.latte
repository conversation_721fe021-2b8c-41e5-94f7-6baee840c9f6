{varType app\system\model\staty\StatRow $stat}
{varType app\system\model\translator\Jazyky[] $jazyky}
{varType app\system\model\organizace\meny\Meny[] $meny}

<div class="container">
   <form method="post">
      <div class="row">
         <h5>{_'Obecné informace'}</h5>
         <div class="col-10">
            <div class="row">
               <div class="col-4">
                  <div class="row">
                     <label>{_'Název'}</label>
                  </div>
                  <div class="row">
                     <strong>{$stat->name}</strong>
                  </div>
               </div>
               <div class="col-1">
                  <div class="row">
                     <label>{_'Zkratka'}</label>
                  </div>
                  <div class="row">
                     <strong>{$stat->iso_a2}</strong>
                  </div>
               </div>
               <div class="col-1">
                  <div class="row">
                     <label>{_'Zkratka'}</label>
                  </div>
                  <div class="row">
                     <strong>{$stat->iso_a3}</strong>
                  </div>
               </div>
               <div class="col-1">
                  <div class="row">
                     <label>{_'Vlajka'}</label>
                  </div>
                  <div class="row">
                     <img src="{$stat->flag_img}" width="50%">
                  </div>
               </div>
            </div>
         </div>
         <div class="col-2">
            <div class="row">
               <label>{_'Oblíbený'}</label>
            </div>
            <div class="row">
               <input type="checkbox" class="form-check" name="is_favorite" n:attr="checked: $stat->isFavorite()">
            </div>
         </div>
      </div>
      <hr>
      <div class="my-3">
         <h5>{_'Jazyky a měny'}</h5>

         <div class="row">
            {foreach $stat->getPouzivaneJazyky() as $jazyk}
               <div class="col-auto">
                  <div class="row">
                     <label>{_'Zkratka jazyka'}</label>
                  </div>
                  <strong>
                     {$jazyk->lang}
                  </strong>
               </div>
            {/foreach}
            <div class="col-auto" n:if="$stat->system_currency">
               <div class="row">
                  <label>{_'Systémová měna'}</label>
               </div>
               <div class="row">
                  <strong>
                     {$meny[$stat->system_currency]}
                  </strong>
               </div>
            </div>
            <div class="col-auto" n:if="$stat->system_lang">
               <div class="row">
                  <label>{_'Systémový jazyk'}</label>
               </div>
               <div class="row">
                  <strong>
                     {$jazyky[$stat->system_lang]}
                  </strong>
               </div>
            </div>
         </div>
      </div>
      <hr>
      <div class="my-3">
         <h5>{_'Používané telefony'}</h5>

         <div class="row">
            <div class="col-auto mt-1">
               <div class="row">
                  <label>{_'Předvolba'}</label>
               </div>
               <div class="row">
                  <strong>
                     {$stat->getTelefonMask()->prefix}
                  </strong>
               </div>
            </div>
            <div class="col-auto">
               <div class="row">
                  <label>{_'Maska čísla'}</label>
               </div>
               <div class="row">
                  <input type="text" name="telefon-mask" class="form-control" maxlength="30"
                          n:attr="value: $stat->getTelefonMask()->mask">
               </div>
               <div class="row">
                  <div>
                     <strong>0 </strong><small>-{_' Pro povinné čísla'}</small>
                     <strong>9 </strong><small>-{_' Pro nepovinné čísla'}</small>
                  </div>
               </div>
            </div>
         </div>
      </div>
      <hr>
      <div class="row">
         <input type="hidden" name="id_stat" value="{$stat->id}">
         <div class="col-auto">
            <input type="submit" class="btn btn-primary" name="btnUpravitStat" value="{_'Upravit'}">
         </div>
      </div>
   </form>
</div>