<?php namespace app\admin\nastaveni\staty;

use app\admin\nastaveni\staty\edit\EditStatModal;
use app\system\helpers\HtmlBuilder;
use app\system\model\staty\Staty;
use app\system\table\DynamicTable;
use Dibi\Fluent;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 18.01.2024 */
class SeznamStatuTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addText('id');
      $this->addText('name', 'Nazev');
      $this->addText('iso_a2', 'ISO zkratka');
      $this->addText('id2.id', ' ')
         ->setFormatter(
            function($value) {
               return HtmlBuilder::buildElement('a', array_merge([
                  'href' => '#'
               ], EditStatModal::getShowAttributes($value)))->setHtml('Detail')->get();
            }
         );
   }

   function getQuery() :Fluent {
      return Staty::find();
   }
}