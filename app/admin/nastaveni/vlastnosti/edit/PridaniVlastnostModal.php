<?php namespace app\admin\nastaveni\vlastnosti\edit;

use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\model\SystemIcony;
use app\system\model\translator\Jazyky;
use app\system\model\vlastnosti\Vlastnosti;
use app\system\model\vlastnosti\VlastnostiNazev;
use app\system\model\vlastnosti\VlastnostiRow;
use app\system\modul\modal\Modal;
use dibi;
use Nette\Utils\Random;

/** Created by <PERSON><PERSON><PERSON>. Date: 10.02.2025 */
class PridaniVlastnostModal extends Modal
{

   public function getTitleName() :string {
      return 'Přidání vlastnosti';
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'vlastnost' => null,
         'jazyky' => Jazyky::getPairs(),
         'icons' => SystemIcony::getAllIcons(),
         'randomId' => Random::generate()
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnAddVlastnost', function ($post) {
         if(empty($post['vlastnosti']))
            throw FlashException::create('Musí být zadaný název vlastnosti');

         $vlastnost = new VlastnostiRow();
         $vlastnost->icon = $post['icon'];

         try{
            dibi::begin();
            Vlastnosti::save($vlastnost);
            VlastnostiNazev::saveMultiple($post['vlastnosti'], $vlastnost->id);
            dibi::commit();
         } catch(\Exception $e) {
            dibi::rollback();
            throw $e;
         }

         FlashMessages::setSuccess('Vlastnost byla uložena');
      });
   }
}