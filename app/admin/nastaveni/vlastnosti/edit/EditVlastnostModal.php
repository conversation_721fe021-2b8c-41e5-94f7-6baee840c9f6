<?php namespace app\admin\nastaveni\vlastnosti\edit;

use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\model\SystemIcony;
use app\system\model\translator\Jazyky;
use app\system\model\vlastnosti\Vlastnosti;
use app\system\model\vlastnosti\VlastnostiNazev;
use app\system\modul\modal\AjaxModal;
use dibi;
use Nette\Utils\Random;

/** Created by <PERSON><PERSON><PERSON>. Date: 10.02.2025 */
class EditVlastnostModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Úprava vlastnosti';
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'vlastnost' => Vlastnosti::get($_POST['slug']),
         'jazyky' => Jazyky::getPairs(),
         'icons' => SystemIcony::getAllIcons(),
         'randomId' => Random::generate()
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUpravitVlastnost', function($post) {
         if(
            !isset($post['id_vlastnost'])
            || !($vlastnost = Vlastnosti::get($post['id_vlastnost']))
         ){
            FlashMessages::setError('Vlastnost se nepodařilo upravit');
            return;
         }

         if(empty($post['vlastnosti']))
            throw FlashException::create('Musí být zadaný název vlastnosti');

         $vlastnost->icon = $post['icon'];

         try{
            dibi::begin();
            Vlastnosti::save($vlastnost);
            VlastnostiNazev::saveMultiple($post['vlastnosti'], $vlastnost->id);
            dibi::commit();
         } catch(\Exception $e) {
            dibi::rollback();
            throw $e;
         }

         FlashMessages::setSuccess('Vlastnost byla upravena');
      });

      $this->isset('btnDeleteVlastnost', function($post) {
         Vlastnosti::delete($post['id_vlastnost']);
         FlashMessages::setSuccess('Vlastnost byla smazána');
      });
   }
}