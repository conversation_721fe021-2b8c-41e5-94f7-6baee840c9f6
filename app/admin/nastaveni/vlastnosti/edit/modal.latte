{varType app\system\model\translator\Jazyky[] $jazyky}
{varType ?app\system\model\vlastnosti\VlastnostiRow $vlastnost}
{varType array $icons}
{varType int $randomId}

<div class="container" id="vlastnosti_form-{$randomId}">
    <form method="post">
        <div class="row">
            <h5>{_'Obecné informace'}</h5>
            {foreach $jazyky as $id_jazyk => $nazev_jazyk}
                <div class="col-6">
                    <div class="mb-3">
                        <label for="nazev_{$id_jazyk}" class="form-label">
                            {$nazev_jazyk}
                        </label>
                        <input type="text" class="form-control" id="nazev_{$id_jazyk}" name="vlastnosti[{$id_jazyk}]"
                                n:attr="value: isset($vlastnost) ? $vlastnost->getNazev($id_jazyk)->nazev" required>
                    </div>
                </div>
            {/foreach}
        </div>

        <label for="js-select-icon-{$randomId}" class="form-label">{_'Vyberte ikonu'}</label>
        <select name="icon" id="js-select-icon-{$randomId}" class="form-select">
            {foreach $icons as $icon}
                <option
                     value="{$icon}"
                     data-icon="fa {$icon}"
                     n:attr="selected: isset($vlastnost) && $vlastnost->icon === $icon"
                >
                </option>
            {/foreach}
        </select>

        <div class="row mt-3" n:if="$vlastnost">
            <div class="col-auto">
                <input type="hidden" name="id_vlastnost" value="{$vlastnost->id}">
                <button  type="submit" class="btn btn-primary" name="btnUpravitVlastnost">{_'Uložit'}</button>
            </div>
            <div class="col-auto">
                <button type="submit" class="btn btn-outline-danger" name="btnDeleteVlastnost">{_'Smazat'}</button>
            </div>
        </div>
        <div class="row mt-3" n:else>
            <div class="col-auto">
                <button type="submit" class="btn btn-primary" name="btnAddVlastnost">{_'Vytvořit'}</button>
            </div>
        </div>
    </form>
</div>

<script>
    $(document).ready(function() {
        function format(state) {
            if (!state.id) return state.text;
            return $('<span><i class="' + state.id + ' fa-lg" style="margin-right:5px;"></i> ' + state.text + '</span>');
        }

        const form = $("#vlastnosti_form-" + {$randomId});
        const select2Element = form.find("#js-select-icon-" + {$randomId});
        select2Element.select2({
            dropdownParent: form,
            templateResult: format,
            templateSelection: format,
            escapeMarkup: function(m) { return m; }
        });
    });
</script>