<?php namespace app\admin\nastaveni\vlastnosti;

use app\admin\nastaveni\vlastnosti\edit\PridaniVlastnostModal;
use app\system\modul\panels\Panel;
use app\system\component\Templater;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON>. Date: 10.02.2025 */
class SeznamVlastnostiPanel extends Panel
{

    function getMenuName() :string {
        return 'Seznam vlastnosti';
    }

    public function preparePanel(Templater $templater) :void {
        $templater->addData([
            'tabulka' => new Html(new SeznamVlastnostiTable()),
            'modalAttr' => PridaniVlastnostModal::init()->btnToggleAttributes()
        ]);
    }
}