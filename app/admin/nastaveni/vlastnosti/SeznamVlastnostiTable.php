<?php namespace app\admin\nastaveni\vlastnosti;

use app\admin\nastaveni\vlastnosti\edit\EditVlastnostModal;
use app\system\helpers\HtmlBuilder;
use app\system\model\translator\Jazyky;
use app\system\model\vlastnosti\VlastnostiNazev;
use app\system\table\DynamicTable;
use Dibi\Fluent;

/** Created by <PERSON><PERSON><PERSON>. Date: 10.02.2025 */
class SeznamVlastnostiTable extends DynamicTable
{
    function prepareTable() :void {
        $this->addText('id_vlastnost','ID');
        $this->addText('nazev', 'Nazev');
        $this->addText('edit.id_vlastnost', ' ')
            ->setFormatter(
                function($value) {
                    return HtmlBuilder::buildElement('a', array_merge([
                        'href' => '#'
                    ], EditVlastnostModal::getShowAttributes($value)))->setHtml('Detail')->get();
                }
            );
    }

    function getQuery() :Fluent {
        return VlastnostiNazev::findAllVlastnostiForLanguage(Jazyky::CZ);
    }
}