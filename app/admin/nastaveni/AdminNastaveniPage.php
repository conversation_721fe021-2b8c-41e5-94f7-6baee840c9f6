<?php namespace app\admin\nastaveni;

use app\admin\nastaveni\otazky\edit\EditOtazkaModal;
use app\admin\nastaveni\otazky\edit\PridaniOtazkaModal;
use app\admin\nastaveni\otazky\SeznamOtazekPanel;
use app\admin\nastaveni\mesta\edit\EditMestoModal;
use app\admin\nastaveni\mesta\edit\PridaniMestaModal;
use app\admin\nastaveni\mesta\SeznamMestPanel;
use app\admin\nastaveni\sluzby\edit\EditSystemSluzbaModal;
use app\admin\nastaveni\sluzby\edit\NovaSystemSluzbaModal;
use app\admin\nastaveni\sluzby\SeznamSluzebPanel;
use app\admin\nastaveni\staty\edit\EditStatModal;
use app\admin\nastaveni\staty\SeznamStatuPanel;
use app\admin\nastaveni\tagy\modal\AddTagModal;
use app\admin\nastaveni\tagy\modal\UpravaTaguModal;
use app\admin\nastaveni\tagy\SeznamTaguPanel;
use app\admin\nastaveni\typyAkci\edit\EditKatalogAkceTypModal;
use app\admin\nastaveni\typyAkci\edit\PridaniAkceTypModal;
use app\admin\nastaveni\typyAkci\SeznamTypuAkciPanel;
use app\admin\nastaveni\vlastnosti\edit\EditVlastnostModal;
use app\admin\nastaveni\vlastnosti\edit\PridaniVlastnostModal;
use app\admin\nastaveni\vlastnosti\SeznamVlastnostiPanel;
use app\system\lay\panels\PanelsLayout;

/** Created by Kryštof Czyź. Date: 18.01.2024 */
class AdminNastaveniPage extends PanelsLayout
{

   function getPageName() :string {
      return 'QVAMP Admin';
   }

   function preparePanely() :void {
      $this->subscribePanel(
         SeznamStatuPanel::init()
            ->addModal(EditStatModal::init())
      );

      $this->subscribePanel(
         SeznamTaguPanel::init()
            ->addModal(UpravaTaguModal::init())
            ->addModal(AddTagModal::init())
      );

      $this->subscribePanel(
         SeznamTypuAkciPanel::init()
            ->addModal(PridaniAkceTypModal::init())
            ->addModal(EditKatalogAkceTypModal::init())
      );

      $this->subscribePanel(
         SeznamVlastnostiPanel::init()
            ->addModal(EditVlastnostModal::init())
            ->addModal(PridaniVlastnostModal::init())
      );

      $this->subscribePanel(
         SeznamSluzebPanel::init()
            ->addModal(EditSystemSluzbaModal::init())
            ->addModal(NovaSystemSluzbaModal::init())
      );

      $this->subscribePanel(
         SeznamMestPanel::init()
            ->addModal(PridaniMestaModal::init())
            ->addModal(EditMestoModal::init())
      );

      $this->subscribePanel(SeznamOtazekPanel::init()
         ->addModal(EditOtazkaModal::init())
         ->addModal(PridaniOtazkaModal::init()));
   }
}