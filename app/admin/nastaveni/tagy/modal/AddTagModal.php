<?php namespace app\admin\nastaveni\tagy\modal;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\tagy\SystemTagy;
use app\system\model\tagy\SystemTagyNazvy;
use app\system\model\tagy\SystemTagyNazvyRow;
use app\system\model\translator\Jazyky;
use app\system\modul\modal\Modal;


class AddTagModal extends Modal
{

   public function getTitleName() :string {
      return 'Přidat nový tag';
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'jazyky' => Jazyky::cases(),
         'tagyNazvy' => [],
         'tag' => null,
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnAddTag', function($post) {
         $novyTagID = SystemTagy::getNewTagID();
         $insert = [];

         foreach($post['tagy'] as $jazykID => $nazev){
            $nazev = trim($nazev);
            $jazyk = Jazyky::from($jazykID);

            if(!$nazev)
               continue;

            $tagNazev = new SystemTagyNazvyRow();
            $tagNazev->id_tag = $novyTagID;
            $tagNazev->id_jazyk = $jazyk->value;
            $tagNazev->nazev = $nazev;

            $insert[] = $tagNazev;
         }

         SystemTagyNazvy::saveMultiple(...$insert);
         FlashMessages::setSuccess('Nový tag vytvořen')->setNoTranslate();
      });
   }

}
