<?php namespace app\admin\nastaveni\tagy\modal;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\tagy\SystemTagRow;
use app\system\model\tagy\SystemTagy;
use app\system\model\tagy\SystemTagyNazvy;
use app\system\model\tagy\SystemTagyNazvyRow;
use app\system\model\translator\Jazyky;
use app\system\modul\modal\AjaxModal;

class UpravaTaguModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Úprava tagu';
   }

   public function prepareAjaxData() :void {
      $this->tag = SystemTagy::get($_POST['slug']);
      $this->tagyNazvy = SystemTagyNazvy::getNazvyPairs($this->tag->id);
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'jazyky' => Jazyky::cases(),
         'tagyNazvy' => $this->tagyNazvy,
         'tag' => $this->tag,
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnEditTag', function($post) {
         $tag = SystemTagy::get($post['id_tag']);

         if(!$tag){
            FlashMessages::setError('Tag nebyl nalezen')->setNoTranslate();
            return;
         }

         $tagyNazvy = SystemTagyNazvy::getForTag($tag);
         $insert = [];
         $delete = [];

         foreach($post['tagy'] as $jazykID => $novyNazev){
            $novyNazev = trim($novyNazev) ?: null;
            $tagNazev = $tagyNazvy[$jazykID] ?? null;

            if(!$novyNazev){
               if($tagNazev !== null)
                  $delete[] = $tagyNazvy[$jazykID];

               continue;
            }

            if($tagNazev?->nazev === $novyNazev)
               continue;

            if(!$tagNazev){
               $tagNazev = new SystemTagyNazvyRow();
               $tagNazev->id_tag = $tag->id;
            }

            $tagNazev->nazev = $novyNazev;
            $insert[] = $tagNazev;
         }

         if(!empty($insert))
            SystemTagyNazvy::saveMultiple(...$insert);

         if(!empty($delete))
            foreach($delete as $deleteNazev)
               SystemTagyNazvy::delete($deleteNazev);

         if(!empty($insert) || !empty($delete))
            FlashMessages::setSuccess('Změny byly uloženy')->setNoTranslate();
      });

      $this->isset('btnDeleteTag', function($post) {
         SystemTagy::delete($post['id_tag']);
         FlashMessages::setSuccess('Tag byl smazán')->setNoTranslate();
      });
   }

   private SystemTagRow $tag;

   /** @var array<int, string> */
   private array $tagyNazvy;
}
