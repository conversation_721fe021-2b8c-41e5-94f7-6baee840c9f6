{varType app\system\model\translator\Jazyky[] $jazyky}
{varType string[] $tagyNazvy}
{varType ?app\system\model\tagy\SystemTagRow $tag}

<div class="container">
   <form method="post">
      <div class="row">
         <h5>{_'Obecné informace'}</h5>

         {foreach $jazyky as $jazyk}
            <div class="col-6">
               <div class="mb-3">
                  <label for="nazev_{$jazyk->value}" class="form-label">
                     {$jazyk->getTitle()}
                  </label>

                  <input type="text" class="form-control" id="nazev_{$jazyk->value}" name="tagy[{$jazyk->value}]"
                          n:attr="value: $tagyNazvy[$jazyk->value] ?? null, required: $jazyk->isRootLanguage()">
               </div>
            </div>
         {/foreach}
      </div>

      <div class="row mt-3" n:if="$tag">
         <div class="col-auto">
            <input type="hidden" name="id_tag" value="{$tag->id}">
            <button type="submit" class="btn btn-primary" name="btnEditTag">Upravit</button>
         </div>
         <div class="col-auto">
            <button type="submit" class="btn btn-danger" name="btnDeleteTag"
                    data-confirm="Opravdu chcete smazat tento tag?">Smazat</button>
         </div>
      </div>
      <div class="row mt-3" n:else>
         <div class="col-auto">
            <button type="submit" class="btn btn-primary" name="btnAddTag">Vytvořit tag</button>
         </div>
      </div>
   </form>
</div>



