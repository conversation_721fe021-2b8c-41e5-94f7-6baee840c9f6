<?php namespace app\admin\nastaveni\tagy;

use app\admin\nastaveni\tagy\modal\AddTagModal;
use app\system\component\Templater;
use app\system\modul\panels\Panel;

class SeznamTaguPanel extends Panel
{

   function getMenuName() :string {
      return 'SystemTagy';
   }

   public function preparePanel(Templater $templater) :void {
      $templater->addData([
         'tabulka' => new SeznamTaguTable(),
         'modalAttr' => AddTagModal::init()->btnToggleAttributes()
      ]);
   }
}