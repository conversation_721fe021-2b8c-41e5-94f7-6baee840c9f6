<?php namespace app\admin\nastaveni\tagy;


use app\admin\nastaveni\tagy\modal\UpravaTaguModal;
use app\system\helpers\HtmlBuilder;
use app\system\model\tagy\SystemTagyNazvy;
use app\system\model\translator\Jazyky;
use app\system\table\DynamicTable;
use Dibi\Fluent;

class SeznamTaguTable extends DynamicTable
{

   function prepareTable() :void {

      $this->addText('id_tag', 'ID');

      $this->addText('nazev', 'Nazev')
         ->setFormatter(
            function($value) {
               return htmlspecialchars($value);
            }
         );

      $this->addText('id2.id_tag', ' ')
         ->setFormatter(
            function($value) {
               return HtmlBuilder::buildElement('a', array_merge(['href' => '#',], UpravaTaguModal::getShowAttributes($value)))
                  ->setHtml('Detail')->get();
            }
         );
   }

   function getQuery() :Fluent {
      return SystemTagyNazvy::findAllTagsForLanguage(Jazyky::CZ);
   }
}