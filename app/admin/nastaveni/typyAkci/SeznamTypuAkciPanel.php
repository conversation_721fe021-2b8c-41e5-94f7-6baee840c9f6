<?php namespace app\admin\nastaveni\typyAkci;

use app\admin\nastaveni\typyAkci\edit\PridaniAkceTypModal;
use app\system\modul\panels\Panel;
use app\system\component\Templater;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON>. Date: 12.02.2025 */
class SeznamTypuAkciPanel extends Panel
{

   function getMenuName() :string {
      return 'Typy Akci';
   }

   public function preparePanel(Templater $templater): void{
      $templater->addData([
         'tabulka' => new Html(new SeznamTypuAkciTable()),
         'modalAttr' => PridaniAkceTypModal::init()->btnToggleAttributes()
      ]);
   }
}