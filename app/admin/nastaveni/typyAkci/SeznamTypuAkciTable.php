<?php namespace app\admin\nastaveni\typyAkci;

use app\admin\nastaveni\typyAkci\edit\EditKatalogAkceTypModal;
use app\system\helpers\HtmlBuilder;
use app\system\model\akce\typ\AkceTypNazev;
use app\system\model\translator\Jazyky;
use app\system\table\DynamicTable;
use Dibi\Fluent;

/** Created by <PERSON><PERSON><PERSON>. Date: 12.02.2025 */
class SeznamTypuAkciTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addText('id_typ','ID');
      $this->addText('nazev', 'nazev');
      $this->addText('id1.id_typ', ' ')
         ->setFormatter(
            function($value) {
               return HtmlBuilder::buildElement('a', array_merge([
                  'href' => '#'
               ], EditKatalogAkceTypModal::getShowAttributes($value)))->setHtml('Detail')->get();
            }
         );
   }

   function getQuery() :Fluent {
      return AkceTypNazev::findTypAkceForSpecificLanguage(Jazyky::CZ);
   }
}