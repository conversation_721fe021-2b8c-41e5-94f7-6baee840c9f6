<?php namespace app\admin\nastaveni\typyAkci\edit;

use app\system\component\Templater;
use app\system\model\akce\typ\AkceTypNazev;
use app\system\model\akce\typ\AkceTyp;
use app\system\model\akce\typ\AkceTypNazevRow;
use app\system\model\translator\Jazyky;
use app\system\modul\modal\Modal;

/** Created by <PERSON><PERSON><PERSON>. Date: 12.02.2025 */
class PridaniAkceTypModal extends Modal
{

   public function getTitleName() :string {
      return 'Přidání typu akce';
   }

   public function prepareModal(Templater $templater) :void {
      $templater->addData([
         'jazyky' => Jazyky::getPairs(),
         'typAkce' => [],
         'typAkceNazvy' => [],
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnPridatTypAkce', function($post) {
         $vlozeneID = AkceTyp::save();
         $insert = [];
         $baseTyp = new AkceTypNazevRow();
         $baseTyp->id_typ = $vlozeneID;

         foreach($post['typy_akci'] as $id_jazyk => $typNazev) {
            $typAkce = clone $baseTyp;
            $typAkce->nazev = $typNazev;
            $typAkce->id_jazyk = $id_jazyk;
            $insert[] = $typAkce;
         }

         AkceTypNazev::save($insert);
      });
   }
}