{varType ?app\system\model\akce\typ\AkceTypNazevRow[] $typAkce}
{varType ?app\system\model\translator\Jazyky[] $jazyky}
{varType ?app\system\model\akce\typ\AkceTypNazevRow[] $typAkceNazvy}

<div class="container">
   <form method="post">
      <div class="row">
         <h5>{_'Obecné informace'}</h5>
         {foreach $jazyky as $id_jazyk => $nazev_jazyk}
            <div class="col-6">
               <div class="mb-3">
                  <label for="nazev_{$id_jazyk}" class="form-label">
                     {$nazev_jazyk}
                  </label>
                  <input type="text" class="form-control" id="nazev_{$id_jazyk}" name="typy_akci[{$id_jazyk}]"
                          n:attr="value:(isset($typAkceNazvy) && array_key_exists($id_jazyk, $typAkceNazvy)) ? $typAkceNazvy[$id_jazyk]->nazev"
                         required>
               </div>
            </div>
         {/foreach}
      </div>
      <div class="row mt-3" n:if="$typAkce">
         <div class="col-auto">
            <input type="hidden" name="id_typ" value="{$typAkce->id_typ}" >
            <button  type="submit" class="btn btn-primary" name="btnUpravitTypAkce">{_'Upravit'}</button>
         </div>
         <div class="col-auto">
            <button type="submit" class="btn btn-danger" name="btnSmazatTypAkce">{_'Smazat'}</button>
         </div>
      </div>
      <div class="row mt-3" n:else>
         <div class="col-auto">
            <button type="submit" class="btn btn-primary" name="btnPridatTypAkce">{_'Přidat vlastnosti'}</button>
         </div>
      </div>
   </form>
</div>