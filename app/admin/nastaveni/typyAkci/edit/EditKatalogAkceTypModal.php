<?php namespace app\admin\nastaveni\typyAkci\edit;

use app\system\component\Templater;
use app\system\model\akce\typ\AkceTyp;
use app\system\model\akce\typ\AkceTypNazev;
use app\system\model\akce\typ\AkceTypNazevRow;
use app\system\model\translator\Jazyky;
use app\system\modul\modal\AjaxModal;

/** Created by <PERSON><PERSON><PERSON>. Date: 12.02.2025 */
class EditKatalogAkceTypModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Úprava typu akce';
   }

   public function prepareAjaxData() :void {
      $this->typAkce = AkceTypNazev::get($_POST['slug']);
   }

   public function prepareModal(Templater $templater) :void {
      $templater->addData([
         'jazyky' => Jazyky::getPairs(),
         'typAkce' => current($this->typAkce),
         'typAkceNazvy' => $this->typAkce,
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnSmazatTypAkce', function($post) {
        AkceTyp::delete($post['id_typ']);
      });

      $this->isset('btnUpravitTypAkce', function($post) {
         $insert = [];
         foreach ($post['typy_akci'] as $id_jazyk => $typNazev) {
            $typAkce = new AkceTypNazevRow();
            $typAkce->id_typ = $post['id_typ'];
            $typAkce->nazev = $typNazev;
            $typAkce->id_jazyk = $id_jazyk;
            $insert[] = $typAkce;
         }

          AkceTypNazev::save($insert);
      });
   }

   /** @var AkceTypNazevRow[] $typAkce*/
   private array $typAkce;
}