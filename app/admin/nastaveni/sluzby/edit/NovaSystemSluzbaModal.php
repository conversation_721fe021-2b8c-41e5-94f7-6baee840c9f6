<?php namespace app\admin\nastaveni\sluzby\edit;

use app\system\application\ApplicationVersion;
use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\model\sluzby\event\SaveSystemSluzbyEvent;
use app\system\model\SystemIcony;
use app\system\model\translator\Jazyky;
use app\system\modul\modal\Modal;
use Nette\Utils\Random;

class NovaSystemSluzbaModal extends Modal
{
   public function getTitleName() :string {
      return 'Přidání nového typu služby';
   }

   public function prepareModal(Templater $templater) :void {
      $templater->addData([
         'versions' => ApplicationVersion::getTranslatedPairs(),
         'sluzba' => null,
         'jazyky' => Jazyky::getPairs(),
         'icons' => SystemIcony::getAllIcons(),
         'randomId' => Random::generate()
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnAddSluzba', function ($post) {
         if(empty($post['sluzbyNazvy']))
            throw FlashException::create('Vyplňte všechny názvy')->setNoTranslate();

         SaveSystemSluzbyEvent::create($post)->call();
         FlashMessages::setSuccess('Typ služby byl vytvořen')->setNoTranslate();
      });
   }
}