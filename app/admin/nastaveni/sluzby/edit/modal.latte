{varType app\system\model\translator\Jazyky[] $jazyky}
{varType array $icons}
{varType int $randomId}
{varType ?app\system\model\sluzby\SystemSluzbyRow $sluzba}
{varType array $versions}

<div class="container" id="sluzby_form-{$randomId}">
   <form method="post">
      <div class="row">
         {foreach $jazyky as $id_jazyk => $nazev_jazyk}
            <div class="col-6">
               <div class="mb-3">
                  <label for="nazev_{$id_jazyk}" class="form-label">
                     {$nazev_jazyk}
                  </label>
                  <input type="text" class="form-control" id="nazev_{$id_jazyk}" name="sluzbyNazvy[{$id_jazyk}]"
                          n:attr="value: isset($sluzba) ? $sluzba->getNazev($id_jazyk)" required>
               </div>
            </div>
         {/foreach}
      </div>

      <label for="js-select-icon-{$randomId}" class="form-label">{_'Vyberte ikonu'}</label>
      <select name="icon" id="js-select-icon-{$randomId}" class="form-select" required>
         {var $selectedVersion = $sluzba?->version ?? null}
         {foreach $icons as $icon}
            <option
                    value="{$icon}"
                    data-icon="fa {$icon}"
                    n:attr="selected: isset($sluzba) && $sluzba->icon === $icon"
            >
            </option>
         {/foreach}
      </select>

      <label for="js-select-version-{$randomId}" class="form-label">{_'Vyberte verzi'}</label>
      <select name="version" id="js-select-version-{$randomId}" class="form-select" required>
         <option value="0">Všechny verze</option>
         {foreach $versions as $index => $name}
            <option
                    value="{$index}"
                    n:if="in_array($index, [1, 2])"
                    n:attr="selected: $selectedVersion === $index"
            >
               {$name}
            </option>
         {/foreach}
      </select>

      <div class="row mt-3" n:if="$sluzba">
         <div class="col-auto">
            <input type="hidden" name="id_sluzba" value="{$sluzba->id}">
            <button  type="submit" class="btn btn-primary" name="btnEditSluzba">{_'Uložit'}</button>
         </div>
         <div class="col-auto">
            <button type="submit" class="btn btn-outline-danger" name="btnDeleteSluzba">{_'Smazat'}</button>
         </div>
      </div>
      <div class="row mt-3" n:else>
         <div class="col-auto">
            <button type="submit" class="btn btn-primary" name="btnAddSluzba">{_'Vytvořit'}</button>
         </div>
      </div>
   </form>
</div>

<script>
   $(document).ready(function() {
      function format(state) {
         if (!state.id) return state.text;
         return $('<span><i class="' + state.id + ' fa-lg" style="margin-right:5px;"></i> ' + state.text + '</span>');
      }

      const form = $("#sluzby_form-" + {$randomId});
      const select2IconElement = form.find("#js-select-icon-" + {$randomId});

      select2IconElement.select2({
         dropdownParent: form.closest('.modal'),
         templateResult: format,
         templateSelection: format,
         escapeMarkup: function(m) { return m; }
      });
   });
</script>