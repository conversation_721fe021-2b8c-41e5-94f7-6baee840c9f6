<?php namespace app\admin\nastaveni\sluzby\edit;

use app\system\application\ApplicationVersion;
use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\model\sluzby\event\DeleteSystemSluzbaEvent;
use app\system\model\sluzby\event\SaveSystemSluzbyEvent;
use app\system\model\sluzby\SystemSluzby;
use app\system\model\SystemIcony;
use app\system\model\translator\Jazyky;
use app\system\modul\modal\AjaxModal;
use Nette\Utils\Random;

class EditSystemSluzbaModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Upravit typ služby';
   }

   public function prepareModal(Templater $templater) :void {
      $templater->addData([
         'versions' => ApplicationVersion::getPairs(),
         'sluzba' => SystemSluzby::get($_POST['slug']),
         'jazyky' => Jazyky::getPairs(),
         'icons' => SystemIcony::getAllIcons(),
         'randomId' => Random::generate()
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnEditSluzba', function ($post) {
         if(!($post['id_sluzba'] ?? null) || !($sluzba = SystemSluzby::get($post['id_sluzba'])))
            throw FlashException::create('Chyba při ukládání')->setNoTranslate();

         if(empty($post['sluzbyNazvy']))
            throw FlashException::create('Vyplňte všechny názvy')->setNoTranslate();

         (new SaveSystemSluzbyEvent($sluzba, $post))->call();
         FlashMessages::setSuccess('Typ služby byl uložen');
      });

      $this->isset('btnDeleteSluzba', function ($post) {
         if(empty($post['id_sluzba']))
            throw FlashException::create('Chyba při mázání služby')->setNoTranslate();

         (new DeleteSystemSluzbaEvent(SystemSluzby::get($post['id_sluzba'])))->call();

         FlashMessages::setSuccess('Typ služby byl smazán')
            ->setNoTranslate();
      });
   }
}