<?php namespace app\admin\nastaveni\sluzby;

use app\admin\nastaveni\sluzby\edit\EditSystemSluzbaModal;
use app\system\helpers\HtmlBuilder;
use app\system\model\sluzby\SystemSluzbyNazev;
use app\system\model\translator\Jazyky;
use app\system\table\DynamicTable;
use Dibi\Fluent;

class SeznamSluzebTable extends DynamicTable
{
   function prepareTable() :void {
      $this->addText('id_sluzba', 'ID');
      $this->addText('nazev', 'Název');
      $this->addText('edit.id_sluzba', ' ')
         ->setFormatter(
            function ($value) {
               return HtmlBuilder::buildElement('a', array_merge(['href' => '#'],
                  EditSystemSluzbaModal::getShowAttributes($value)))->setHtml('Detail')->get();
            }
         );
   }

   function getQuery() :Fluent {
      return SystemSluzbyNazev::findSluzby(Jazyky::CZ);
   }
}