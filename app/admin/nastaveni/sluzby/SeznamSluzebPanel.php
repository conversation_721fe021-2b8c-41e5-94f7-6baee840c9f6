<?php namespace app\admin\nastaveni\sluzby;


use app\admin\nastaveni\sluzby\edit\NovaSystemSluzbaModal;
use app\system\component\Templater;
use app\system\modul\panels\Panel;
use Latte\Runtime\Html;

class SeznamSluzebPanel extends Panel
{
   function getMenuName() :string {
      return 'Seznam služeb';
   }

   public function preparePanel(Templater $templater) :void {
      $templater->addData([
         'tabulka' => new Html(new SeznamSluzebTable()),
         'modalAttr' => NovaSystemSluzbaModal::init()->btnToggleAttributes(),
      ]);
   }
}