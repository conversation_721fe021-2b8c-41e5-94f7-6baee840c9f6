<?php namespace app\admin\nastaveni\mesta;

use app\admin\nastaveni\mesta\edit\PridaniMestaModal;
use app\system\component\Templater;
use app\system\modul\panels\Panel;
use Latte\Runtime\Html;

class SeznamMestPanel extends Panel
{

   function getMenuName() :string {
      return 'Města';
   }

   public function preparePanel(Templater $templater) :void {
      $templater->addData([
         'tabulka' => new Html(new SeznamMestTable()),
         'modalAttr' => PridaniMestaModal::init()->btnToggleAttributes()
      ]);
   }
}