{varType ?app\system\model\mesta\SystemMestoRow $mesto}
{varType app\system\model\translator\Jazyky[] $jazyky}
{varType int $randomId}

<div class="container" id="mesto_form-{$randomId}">
   <form method="post">
      <div class="row">
         <h5>{_'Obecné informace'}</h5>
         {foreach $jazyky as $jazyk}
            {var $id_jazyk = $jazyk->value}
            <div class="col-6">
               <div class="mb-3">
                  <label for="nazev_{$id_jazyk}" class="form-label">
                     {$jazyk->getTitle()}
                  </label>
                  <input type="text" class="form-control" id="nazev_{$id_jazyk}" name="mesta[{$id_jazyk}]"
                          n:attr="value: isset($mesto) ? $mesto->getNazev($jazyk), required: $jazyk->isRootLanguage()">
                  {ifset $mesto}
                  <label for="urls{$id_jazyk}" class="form-label">
                        {$jazyk->getTitle()} URL
                  </label>
                  <input type="text" class="form-control" id="urls{$id_jazyk}" name="urls[{$id_jazyk}]"
                          n:attr="value: isset($mesto) ? $mesto->getUrl($jazyk), required: $jazyk->isRootLanguage()">
                  {/ifset}
               </div>
            </div>
         {/foreach}
      </div>

      <div class="row mt-3" n:if="$mesto">
         <div class="col-auto">
            <input type="hidden" name="id_mesto" value="{$mesto->id}">
            <button type="submit" class="btn btn-primary" name="btnUpravitMesto">{_'Uložit'}</button>
         </div>
         <div class="col-auto">
            <button type="submit" class="btn btn-outline-danger" name="btnDeleteMesto">{_'Smazat'}</button>
         </div>
      </div>
      <div class="row mt-3" n:else>
         <div class="col-auto">
            <button type="submit" class="btn btn-primary" name="btnAddMesto">{_'Vytvořit'}</button>
         </div>
      </div>
   </form>
</div>

<script>

   $(function() {
      const form = $("form#mesto_form-" + {$randomId});

      form.find('button[name=btnDeleteMesto]').click(() => {
         form.find('input').each((index, input) => {
            $(input).prop('required', false);
         });
      });
   });
</script>