<?php namespace app\admin\nastaveni\mesta\edit;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\mesta\Eventy\DeleteMestoEvent;
use app\system\model\mesta\Eventy\SaveMestoEvent;
use app\system\model\mesta\SystemMesta;
use app\system\model\mesta\SystemMestoRow;
use app\system\model\translator\Jazyky;
use app\system\modul\modal\AjaxModal;
use Nette\Utils\Random;

class EditMestoModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Úprava města';
   }

   public function prepareAjaxData() :void {
      $this->setMesto($_POST['slug']);
   }

   public function prepareModal(Templater $templater) :void {
      $templater->addData([
         'mesto' => $this->getMesto(),
         'jazyky' => Jazyky::cases(),
         'randomId' => Random::generate()
      ]);
   }

   public function getMesto() :SystemMestoRow {
      return $this->mesto;
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUpravitMesto', function($post) {
         if(
            !isset($post['id_mesto'])
            || !$this->setMesto($post['id_mesto'])
         ){
            FlashMessages::setError('Chyba ukládání')->setNoTranslate();
            return;
         }

         (new SaveMestoEvent($this->getMesto(), $post))
            ->call();

         FlashMessages::setSuccess('Město bylo uložené')->setNoTranslate();
      });

      $this->isset('btnDeleteMesto', function($post) {
         if(
            !isset($post['id_mesto'])
            || !$this->setMesto($post['id_mesto'])
         ){
            FlashMessages::setError('Chyba')->setNoTranslate();
            return;
         }

         (new DeleteMestoEvent($this->getMesto()))
            ->call();

         FlashMessages::setSuccess('Smazáno')->setNoTranslate();
      });
   }

   private function setMesto(int $mestoID) :?SystemMestoRow {
      return $this->mesto ??= SystemMesta::get($mestoID);
   }

   private SystemMestoRow $mesto;
}