{varType app\system\model\organizace\subscription\MistoSubscription $subscription}
{varType app\system\model\subscription\polozky\SubscriptionPolozky[] $selectPolozek}
{varType string $selectUrl}

<div class="row">
   <div class="col-6">
      <form method="post">
         <div class="card">
            <div class="card-header">
               <h5 class="card-title">{_'Informace o platnosti'}</h5>
            </div>
            <div class="card-body">
               <div class="row">
                  <div class="form-group">
                     <div class="form-check">
                        <input class="form-check-input js-trial-check" type="checkbox" id="trial" name="trial" n:attr="checked: $subscription->isTrial()">
                        <label class="form-check-label" for="trial">
                           {_'Zkušební verze'}
                        </label>
                     </div>
                  </div>
               </div>
               <div class="row mt-2">
                  <h5>{_'Předplatné je platné do:'}</h5>
                  <div class="form-group">
                     <div class="input-group date js-datepicker" id="datum_expirace" data-target-input="nearest">
                        <input type="text" class="form-control datetimepicker-input" data-target="#datum_expirace"
                               name="datum_expirace" value="{$subscription->getExpirationDate()|date: 'j.n.Y'}" required>
                        <div class="input-group-text" data-target="#datum_expirace" data-toggle="datetimepicker"><i
                                   class="bi bi-calendar2-event"></i></div>
                     </div>
                  </div>
               </div>
               <div class="row mt-2">
                  <h5>{_'Automatické obnovení:'}</h5>
                  <div class="form-group">
                     <div class="input-group date js-datepicker" id="datum_obnoveni" data-target-input="nearest">
                        <input type="text" class="form-control datetimepicker-input" data-target="#datum_obnoveni"
                               name="datum_obnoveni" value="{$subscription->getRenewDate()?? ''|date: 'j.n.Y'}" n:attr="disabled: $subscription->isTrial()">
                        <div class="input-group-text" data-target="#datum_obnoveni" data-toggle="datetimepicker"><i
                                   class="bi bi-calendar2-event"></i></div>
                     </div>
                  </div>
               </div>
               <div class="row mt-2">
                  <div class="col-auto">
                     <button class="btn btn-primary" type="submit"
                             name="btnEditExpirationSubscription" data-confirm="{_'Opravdu chcete data uložit? Tato změna je nevratná a závislá na funkčnosti systému místa'}">{_'Uložit'}</button>
                  </div>
               </div>
            </div>
         </div>
      </form>
   </div>

   <div class="col-4">
      <div class="card">
         <div class="card-header">
            <h5 class="card-title">{_'Cena za měsíc'}</h5>
         </div>
         <div class="card-body">
            <div class="row">
               <h5>{_'Pravidelná cena'}</h5>
               <p class="mb-0">{$subscription->getCena()|price}</p>
               <small>{$subscription->getCena(true)|price} {_'s DPH'}</small>
            </div>
            <div class="row mt-4" n:if="$subscription->hasChangedPolozkyForNextPeriod()">
               <h5>{_'Aktuální období'}</h5>
               <p class="h2">{$subscription->getCena()|price}</p>
               <small>{_'S DPH'}: {$subscription->getCena(true)|price}</small>
            </div>
         </div>
      </div>
   </div>
</div>

<form method="post">
   <div class="col-12">
      <div class="card">
         <div class="card-header">
            <h5 class="card-title">{_'Položky'}</h5>
         </div>
         <div class="card-body">
            <div class="row">
               <table class="table">
                  <tr>
                     <th>{_'Název'}</th>
                     <th>{_'Množství'}</th>
                     <th>{_'Cena'}</th>
                     <th>{_'Aktivní od'}</th>
                     <th></th>
                  </tr>

                  {foreach $subscription->getPolozky() as $polozka}
                     <tr n:class="$polozka->is_new? table-success, $polozka->deleted_at? table-danger, $polozka->mnozstvi_next? table-success, js-polozky-row">
                        <td><input class="form-control" name="array_nazev[{$polozka->id_polozky}]" type="text"
                                   value="{$polozka->nazev}"></td>
                        <td>
                           <input class="form-control" type="number" value="{$polozka->getRealAmount()}" min="1"
                                  max="100" name="array_mnozstvi[{$polozka->id_polozky}]"
                                  {if $polozka->isSubscription()}disabled{/if}>
                        </td>
                        <td>
                           <div class="input-group g-1">
                              <input class="form-control js-price-mask" name="array_cena[{$polozka->id_polozky}]"
                                     type="text"
                                     value="{$polozka->getPrice()->multiply($polozka->mnozstvi)->getAmount()}">
                              <span class="input-group-text input-pravy-round">{$polozka->getPrice()->getCurrency()}</span>
                           </div>
                        </td>
                        <td>{$polozka->created|date: 'j.n.Y'}</td>
                        <td>
                           <button n:if="!$polozka->isSubscription()" class="btn btn-primary" type="submit"
                                                                      name="btnDeleteSubscriptionItem"
                                                                      value="{$polozka->id_polozky}">{_'Smazat'}</button>
                        </td>
                        <td>
                           {if (!isset($polozka->deleted_at)) && (!$polozka->isSubscription())}
                              <div class="dropdown js-upgrade-container">
                                 <a class="btn btn-primary js-upgrade-btn dropdown-toggle" data-bs-auto-close="outside"
                                    aria-expanded="false" data-bs-toggle="dropdown"
                                    data-polozka="{$polozka->id_polozky}">{_'Vylepšit'}</a>

                                 <div class="dropdown-menu dropdown-menu-lg">
                                    <div class="px-4 py-3 load js-upgrade-dropdown">
                                       <div class="spinner-border spinner-border-sm" role="status">
                                       </div>
                                       <span>{_'Načítání'}...</span>
                                    </div>
                                 </div>
                              </div>
                           {else}
                           {/if}
                        </td>
                     </tr>
                  {/foreach}
               </table>
            </div>
            <div class="row">
               <div class="col-md-12">
                  <div class="row">
                     <div class="col-6">
                        {if !empty($selectPolozek)}
                           <select class="form-select js-select-polozka" name="id_typ_polozky">
                              <option value="0"></option>
                              {foreach $selectPolozek as $polozka}
                                 <option value="{$polozka->value}">{$polozka->name}</option>
                              {/foreach}
                           </select>
                        {/if}
                     </div>
                     <div class="col-6">
                        <div class="row d-flex justify-content-between">
                           <div class="col-auto">
                              {if !empty($selectPolozek)}
                                 <button class="btn btn-primary" type="submit"
                                         name="btnAddSubscriptionItem" disabled>{_'Přidat'}</button>
                              {/if}
                           </div>
                           <div class="col-auto">
                              <div class="row">
                                 <div class="col-auto">
                                    <button class="btn btn-secondary" type="submit" name="btnProdlouzitSubscription"
                                            data-confirm="{_'Opravdu chcete bezplatně prodloužit předplatné?'}">{_'Prodloužit'}</button>
                                 </div>
                                 <div class="col-auto">
                                    <button class="btn btn-primary" type="submit"
                                            name="btnSaveSubscriptionItems">{_'Uložit'}</button>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</form>

<div class="col-12">
   <div class="card">
      <div class="card-header">
         <h5 class="card-title">Platby</h5>
      </div>
      <div class="card-body">
         <table class="table">
            <thead>
            <tr class="zahlavi-tabulek ">
               <th>{_'Kolik'}</th>
               <th>{_'Zaplaceno'}</th>
               <th>{_'Platba vystavena'}</th>
               <th></th>
            </tr>
            </thead>
            <tbody>
            {*            @TODO sloupec s platbou a fakturou *}
               {foreach $subscription->getPlatby() as $platba}
               <tr>
                  <td>{$platba->getPrice()|price}</td>
                  <td>
                     {if $platba->paid === null}
                        {_'Ne'}
                     {else}
                        {$platba->paid|date: 'j.n.Y H:i'}
                     {/if}
                  </td>
                  <td>{$platba->created|date: 'j.n.Y'}</td>
                  <td><a href="?smazatPlatbu={$platba->id}" n:if="$platba->isCreated()">Smazat platbu</a> {$platba->getResponseStatus()->getTitle()}</td>
               </tr>
            {/foreach}
            </tbody>
         </table>
      </div>
   </div>
</div>

<script>
   $(function() {
      $('body').on('click', ' .js-upgrade-btn', function() {
         const btn = $(this);
         const container = btn.closest('.js-upgrade-container');

         ajaxHandler.post({$selectUrl}, { id_polozky: btn.attr('data-polozka') }, function(response) {
            container.find('.js-upgrade-dropdown').html(response['upgrade']);
         });
      }).on('change', '.js-select-polozka', function() {
         $('button[name=btnAddSubscriptionItem]').prop('disabled', ($(this).val() <= 0));
      }).on('change', '.js-trial-check', function() {
         $('input[name=datum_obnoveni]').prop('disabled', $(this).is(':checked'));
      });
   });
</script>
