<?php namespace app\admin\mista\panelySeznamu\subscriptions;

use app\admin\controllers\misto\DetailMistaAdminActionController;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\organizace\subscription\event\SubscriptionActivateEvent;
use app\system\model\organizace\subscription\MistoSubscription;
use app\system\model\organizace\subscription\MistoSubscriptionData;
use app\system\model\organizace\subscription\polozky\SubscriptionPolozkyPostHandler;
use app\system\model\subscription\polozky\SubscriptionPolozky;
use app\system\modul\panels\Panel;
use Dibi\DateTime;

class DetailSubscriptionAdminPanel extends Panel
{

   function getMenuName() :string {
      return 'Detail předplatného';
   }

   public function returnString() :?string {
      $this->subscription = MistoSubscription::get($this->getParameter('id_mista'));

      if($this->subscription === null)
         return 'Místo ještě nedokončilo základní nastavení a nemá založený subscription';

      return null;
   }


   public function preparePanel(Templater $templater) :void {
      $templater->addData([
         'subscription' => $this->subscription,
         'selectPolozek' => $this->subscription->getUnusedOptionalPolozky(),
         'selectUrl' => DetailMistaAdminActionController::getUrl($this->getParameter('id_mista'), DetailMistaAdminActionController::ACTION_UPGRADE_POLOZKY_SELECT),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnAddSubscriptionItem', function($post) {
         $this->getHandler()
            ->addPolozka(SubscriptionPolozky::from($post['id_typ_polozky']));

         FlashMessages::setSuccess('Položka byla přidána');
      });

      $this->isset('btnDeleteSubscriptionItem', function($post) {
         $this->getHandler()
            ->deletePolozka(SubscriptionPolozky::from($post['btnDeleteSubscriptionItem']));

         FlashMessages::setSuccess('Položka byla smazána');
      });

      $this->isset('btnUpgradovatPolozku', function($post) {
         $this->getHandler()
            ->upgradePolozka(
               SubscriptionPolozky::from($post['id_puvodni']),
               SubscriptionPolozky::from($post['id_nova'])
            );

         FlashMessages::setSuccess('Položka byla upgradována');
      });

      $this->isset('btnSaveSubscriptionItems', function($post) {
         $this->getHandler()
            ->processPostArrays($post['array_mnozstvi']?? [], $post['array_nazev'], $post['array_cena']);

         FlashMessages::setSuccess('Změny byly uloženy');
      });

      $this->isset('btnEditExpirationSubscription', function($post) {
         $subs = MistoSubscriptionData::getForMisto($this->getParameter('id_mista'));

         $subs->valid_to = new DateTime($post['datum_expirace']);
         $subs->is_trial = (int)isset($post['trial']);
         $subs->renew_at = trim($post['datum_obnoveni']?? '') && !$subs->is_trial ? new DateTime($post['datum_obnoveni']) : null;
         $subs->save();

         FlashMessages::setSuccess('Nastavení expirace bylo uloženo');
      });

      $this->isset('btnProdlouzitSubscription', function() {
         SubscriptionActivateEvent::renew(MistoSubscription::get($this->getParameter('id_mista')), notification: false);
      });
   }

   private function getHandler() :SubscriptionPolozkyPostHandler {
      return SubscriptionPolozkyPostHandler::prepare(
         MistoSubscriptionData::getForMisto($this->getParameter('id_mista'))
      );
   }

   private ?MistoSubscription $subscription;
}