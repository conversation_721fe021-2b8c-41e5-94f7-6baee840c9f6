<?php namespace app\admin\mista\panelySeznamu\subscriptions\component;

use app\system\component\Component;
use app\system\model\subscription\polozky\SubscriptionPolozky;

/** Created by <PERSON><PERSON>. Date: 12.04.2023 */
class UpgradePolozkyDropdownComponent extends Component
{

   function setData() :array {
      $polozky = array_filter(SubscriptionPolozky::getOptionalPolozky(), function(SubscriptionPolozky $val) {
         return $val->value !== $this->polozka->value;
      });

      return [
         'polozky' => $polozky,
         'default_polozka' => $this->polozka,
      ];
   }

   public function setDefaultPolozka(SubscriptionPolozky $polozka) :static {
      $this->polozka = $polozka;
      return $this;
   }

   protected SubscriptionPolozky $polozka;
}