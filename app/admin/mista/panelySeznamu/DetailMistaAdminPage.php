<?php namespace app\admin\mista\panelySeznamu;

use app\admin\controllers\misto\DetailMistaAdminController;
use app\admin\mista\panelySeznamu\detail\DetailMistaAdminPanel;
use app\admin\mista\panelySeznamu\import\OrganizaceImportPanel;
use app\admin\mista\panelySeznamu\nastaveni\NastaveniOrganizaceAdminPanel;
use app\admin\mista\panelySeznamu\subscriptions\DetailSubscriptionAdminPanel;
use app\admin\mista\panelySeznamu\uzivateleMista\UzivateleMistaPanel;
use app\libs\thepay\ResponseStateEnum;
use app\libs\thepay\ThePayApi;
use app\system\flash\FlashMessages;
use app\system\lay\panels\PanelsLayout;
use app\system\model\organizace\subscription\platby\MistoSubscriptionPlatby;
use app\system\Redirect;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 10.11.2022 */
class DetailMistaAdminPage extends PanelsLayout
{

   function getPageName() :string {
      return 'Základní info';
   }

   public function checkPost(array $post) {
      if(isset($_GET['smazatPlatbu'])){
         if(!($platba = MistoSubscriptionPlatby::get(intval($_GET['smazatPlatbu']))) || !$platba->isCreated()){
            FlashMessages::setError('Platba nebyla smazána')
               ->setNoTranslate();
            Redirect::to(DetailMistaAdminController::getUrl($this->id_mista));
         }

         try{
            ThePayApi::prepare()->getClient()->invalidatePayment($platba->uid_subscription);
         }catch(\Exception $e) {
            FlashMessages::setWarning($e->getMessage())
               ->setNoTranslate();
         }

         $platba->return_status = ResponseStateEnum::INVALIDATED->value;
         $platba->save();

         FlashMessages::setSuccess('Platba smazána')
            ->setNoTranslate();
         Redirect::to(DetailMistaAdminController::getUrl($this->id_mista));
      }
   }

   function preparePanely() :void {
      $this->subscribePanel(
         DetailMistaAdminPanel::init()
            ->addParameter('id_mista', $this->id_mista)
      );
      $this->subscribePanel(
         UzivateleMistaPanel::init()
            ->addParameter('id_mista', $this->id_mista)
      );

      $this->subscribePanel(
         NastaveniOrganizaceAdminPanel::init()
            ->setIdOrganizace($this->id_mista)
      );

      $this->subscribePanel(
         DetailSubscriptionAdminPanel::init()
            ->addParameter('id_mista', $this->id_mista)
      );

      $this->subscribePanel(
         OrganizaceImportPanel::init()
            ->addParameter('id_organizace', $this->id_mista)
      );
   }

   public function setIdMista(int $id_mista) :DetailMistaAdminPage {
      $this->id_mista = $id_mista;
      return $this;
   }

   protected int $id_mista;
}