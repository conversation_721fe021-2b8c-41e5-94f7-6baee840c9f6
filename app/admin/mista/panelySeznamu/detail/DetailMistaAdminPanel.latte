{varType app\front\organizace\model\organizace\OrganizaceRow $organizace}
{varType app\system\model\uzivatele\UzivatelRow $majitel}

{varType ?app\system\model\organizace\kontakt\OrganizaceKontaktRow $kontakt}
{varType Latte\Runtime\Html $tinyEditor}


<div class="container">
   <div class="row">
      <div class="col-md-6">

         <div class="row mb-1">
            <div class="bg-secondary radius-card p-3 gap-2">
               <h2 class="lead text-white">{$organizace->nazev}</h2>
               <small class="text-gray-light">{$organizace->getVersion()->getTitle()} ID: {$organizace->id_organizace}</small>
            </div>
         </div>

         <div class="row mt-2">
            <div class="border radius-card border-light-gray p-3 bg-white">
               <label>Majitel</label>
               <p class="lead">{$majitel->getFullName()}</p>
               <span><i class="bi bi-envelope me-1"></i>{$majitel->email}</span><span class="mx-3"><i class="bi bi-telephone me-1"></i>{$majitel->telefon}</span>
            </div>
         </div>
         <div class="row mt-3 pt-3">
            <h2>Kontaktní informace organizace</h2>
         </div>
         <div class="row mt-2">
            <div class="card">
               <div class="card-body">
                  <div class="row"><h4>Základní</h4></div>

                  <div class="row pb-4">
                     <div class="form-floating col-md-12">
                        <input type="text" id="nazev" name="nazev" class="form-control col-sm-9"
                               value="{$organizace->nazev}" minlength="1" maxlength="60" required>
                        <label for="nazev" class="ms-2">Název<span class="text-danger">&nbsp;*</span></label>
                     </div>
                  </div>

                  <div class="row pb-4">
                     <div class="form-floating col-md-12">
                        <input type="text" id="portal_odkaz" name="portal_odkaz" class="form-control col-sm-9"
                               value="{$organizace->portal_odkaz}" minlength="1" maxlength="140" required>
                        <label for="portal_odkaz" class="ms-2">Portál odkaz<span class="text-danger">&nbsp;*</span></label>
                     </div>
                  </div>

                  {if $kontakt}
                     <hr class="mt-3">
                     <div class="row"><h4>Kontakt a adresa</h4></div>

                     <div class="row pb-4">
                        <div class="form-floating col-md-12">
                           <input type="text" id="ulice" name="ulice" class="form-control col-sm-9"
                                  value="{$kontakt->ulice}" maxlength="120">
                           <label for="ulice" class="ms-2">{_'Ulice'}</label>
                        </div>
                     </div>

                     <div class="row pb-4">
                        <div class="form-floating col-md-12">
                           <input type="text" id="mesto" name="mesto" class="form-control col-sm-9"
                                  value="{$kontakt->mesto}" maxlength="80">
                           <label for="mesto" class="ms-2">{_'Město'}</label>
                        </div>
                     </div>

                     <div class="row pb-4">
                        <div class="form-floating col-lg-6">
                           <input type="text" id="psc" name="psc" class="form-control col-sm-9"
                                  value="{$kontakt->psc}" maxlength="8">
                           <label for="psc" class="ms-2">{_'PSČ'}</label>
                        </div>
                     </div>

                     <div class="row pb-4">
                        <div class="form-floating col-lg-6">
                           <input type="text" id="stat" class="form-control col-sm-9"
                                  value="{$kontakt->getStat()->getTranslatedName()}" disabled>
                           <label for="stat" class="ms-2">{_'Stát'}</label>
                        </div>
                     </div>

                     <div class="row mb-1">
                        <div class="form-group col-md-10 col-12 align-items-center d-inline-flex gap-2">
                           <label>ICO:</label>
                           <h4 class="h4">{$kontakt->ic}</h4>
                        </div>
                     </div>

                     <div class="row mb-1">
                        <div class="form-group col-md-10 col-12 align-items-center d-inline-flex gap-2">
                           <label>DIC:</label>
                           <h4 class="h4">{$kontakt->dic}</h4>
                        </div>
                     </div>

                     <div class="row mb-1">
                        <div class="form-group col-md-10 col-12 align-items-center d-inline-flex gap-2">
                           <label>WEB:</label>
                           <h4 class="h4">{$kontakt->website}</h4>
                        </div>
                     </div>

                     <div class="row mb-1">
                        <div class="form-group col-md-10 col-12 align-items-center d-inline-flex gap-2">
                           <label>Kontaktní email:</label>
                           <h4 class="h4">{$kontakt->kontakt_email}</h4>
                        </div>
                     </div>

                     <div class="row mb-1">
                        <div class="form-group col-md-10 col-12 align-items-center d-inline-flex gap-2">
                           <label>Telefon primární:</label>
                           <h4 class="h4">{$kontakt->prim_tel}</h4>
                        </div>
                     </div>

                     <div class="row mb-1">
                        <div class="form-group col-md-10 col-12 align-items-center d-inline-flex gap-2">
                           <label>Telefon sekundární:</label>
                           <h4 class="h4">{$kontakt->sec_tel}</h4>
                        </div>
                     </div>
                  {/if}
               </div>
            </div>
         </div>
      </div>
      <div class="col-md-6">
         <div class="row card mx-1 mb-1 py-3">
            <h2>Interní poznámka k organizaci</h2>
         </div>
         <form method="post" enctype="multipart/form-data">
            {$tinyEditor}
            <div class="row mt-3 justify-content-end">
               <div class="col-auto">
                  <button class="btn btn-primary" type="submit" name="btnUlozitPoznamku">Uložit</button>
               </div>
            </div>
         </form>
      </div>
   </div>
</div>