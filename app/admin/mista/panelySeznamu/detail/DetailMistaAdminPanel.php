<?php namespace app\admin\mista\panelySeznamu\detail;

use app\front\organizace\model\organizace\Organizace;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\organizace\admin\AdminOrganizacePoznamky;
use app\system\model\organizace\admin\event\SaveAdminOrganizacePoznamkaEvent;
use app\system\model\organizace\kontakt\OrganizaceKontakt;
use app\system\model\uzivatele\Uzivatel;
use app\system\modul\panels\Panel;
use app\system\tiny\TinyEditor;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 10.11.2022 */
class DetailMistaAdminPanel extends Panel
{

   function getMenuName() :string {
      return 'Detail';
   }

   public function preparePanel(Templater $templater) :void {
      $id_mista = $this->getParameter('id_mista');
      $misto = Organizace::getMisto($id_mista);
      $poznamkaRow = AdminOrganizacePoznamky::getByOrganizace($id_mista);

      $editor = TinyEditor::init()
         ->setInputName('poznamka')
         ->setContent($poznamkaRow->poznamka ?? '')
         ->setPlaceholder('poznamka');

      $templater->addData([
         'organizace' => $misto,
         'majitel' => Uzivatel::get($misto->id_uzivatel),
         'kontakt' => OrganizaceKontakt::get($id_mista),
         'tinyEditor' => new Html($editor->render()),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUlozitPoznamku', function($post) {
         (new SaveAdminOrganizacePoznamkaEvent(
            $this->getParameter('id_mista'),
            $post['poznamka'],
         ))->call();

         FlashMessages::setSuccess('Poznámka byla uložena');
      });
   }
}