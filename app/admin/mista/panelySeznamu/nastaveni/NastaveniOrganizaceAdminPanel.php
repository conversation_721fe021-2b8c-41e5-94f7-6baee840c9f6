<?php namespace app\admin\mista\panelySeznamu\nastaveni;

use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\organizace\jazyk\OrganizaceJazykRow;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\organizace\meny\Meny;
use app\system\model\organizace\meny\OrganizaceMenaAdder;
use app\system\model\organizace\meny\OrganizaceMeny;
use app\system\model\translator\Jazyky;
use app\system\modul\panels\Panel;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 24.10.2023 */
class NastaveniOrganizaceAdminPanel extends Panel
{

   function getMenuName() :string {
      return 'Nastavení';
   }

   public function setIdOrganizace(int $id_mista) :static {
      $this->id_organizace = $id_mista;
      $this->addParameter('id_mista', $this->id_organizace);
      return $this;
   }

   public function getIdOrganizace() :int {
      return $this->id_organizace ??= $this->getParameter('id_mista');
   }

   protected function preparePostListeners() :void {
      $this->isset('btnNastavitPrimarniJazyk', function($post) {
         OrganizaceJazyky::setPrimary($this->getIdOrganizace(), $jazyk = Jazyky::from(intval($post['btnNastavitPrimarniJazyk'])));

         FlashMessages::setSuccess('Primární jazyk byl nastaven na ' . $jazyk->getTitle())
            ->setNoTranslate();
      });

      $this->isset('btnPridatJazyk', function($post) {
         $row = new OrganizaceJazykRow();
         $row->id_jazyk = ($jazyk = Jazyky::from(intval($post['selected_jazyk'])))->value;
         $row->id_mista = $this->getIdOrganizace();
         OrganizaceJazyky::insert($row);

         FlashMessages::setSuccess(sprintf('Jazyk %s byl přidán', $jazyk->getTitle()))
            ->setNoTranslate();
      });

      $this->isset('btnSmazatJazyk', function($post) {
         if(!($jazykRow = OrganizaceJazyky::getOrganizaceJazyk($this->getIdOrganizace(), $jazyk = Jazyky::from(intval($post['btnSmazatJazyk'])))))
            return;

         OrganizaceJazyky::delete($jazykRow);
         FlashMessages::setSuccess(sprintf('Jazyk %s byl smazán', $jazyk->getTitle()))
            ->setNoTranslate();
      });

      $this->isset('btnNastavitPrimarniMenu', function($post) {
         OrganizaceMeny::setPrimary($this->getIdOrganizace(), $mena = Meny::from(intval($post['btnNastavitPrimarniMenu'])));

         FlashMessages::setSuccess('Primární měna byla nastavena na ' . $mena->getTitle())
            ->setNoTranslate();
      });

      $this->isset('btnPridatMenu', function($post) {
         if(!($mena = Meny::from(intval($post['selected_mena']))))
            return;

         $adder = (new OrganizaceMenaAdder($this->getIdOrganizace(), $mena))
            ->add();

         if($adder->isAdded())
            FlashMessages::setSuccess(sprintf('Měna %s byla přidána', $mena->getTitle()))
               ->setNoTranslate();
      });
   }

   public function preparePanel(Templater $templater) :void {
      $this->data['organizace'] = $this->organizace = Organizace::getMisto($this->getIdOrganizace());
      $this->data['jazyky'] = OrganizaceJazyky::getAll($this->organizace->id_organizace);
      $this->data['allJazyky'] = Jazyky::cases();
      $this->data['meny'] = OrganizaceMeny::getAll($this->organizace->id_organizace);
      $this->data['allMeny'] = Meny::cases();


      $templater->addData($this->data);
   }

   private array $data = [];
   private OrganizaceRow $organizace;
   private int $id_organizace;
}