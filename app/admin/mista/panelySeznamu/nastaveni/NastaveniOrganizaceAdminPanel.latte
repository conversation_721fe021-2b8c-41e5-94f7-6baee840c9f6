{varType app\front\organizace\model\organizace\OrganizaceRow $organizace}

{varType app\system\model\organizace\jazyk\OrganizaceJazykRow[] $jazyky}
{varType app\system\model\translator\Jazyky[] $allJazyky}
{varType app\system\model\organizace\meny\OrganizaceMenaRow[] $meny}
{varType app\system\model\organizace\meny\Meny[] $allMeny}

<div class="container">
   <div class="row"><h1>Nastavení organizace: {$organizace->nazev}</h1></div>

   <div class="row mt-5">
      <div class="col-lg-6">
         <div class="card">
            <div class="card-body">
               <div class="row">
                  <div class="col-md-6 col-xl-4">
                     <div class="row"><h3>Jazyky</h3></div>

                     <form method="post">
                        <div class="row my-2 p-2">
                           {foreach $jazyky as $jazyk}
                              <div class="row my-1">
                                 <div class="d-inline-block">
                                    {$jazyk->getJazykTitle()}
                                    <small n:if="$jazyk->is_primary === 1" class="text-danger"> ({_'Primární'})</small>
                                    <button n:if="$jazyk->is_primary !== 1"
                                            class="btn btn-sm btn-outline-danger" name="btnNastavitPrimarniJazyk" value="{$jazyk->id_jazyk}"
                                            data-confirm="Opravdu chcete nastavit {$jazyk->getJazykTitle()} jako primární jazyk?">Nastavit primární</button>
                                    <button n:if="$jazyk->is_primary !== 1"
                                            class="btn btn-sm btn-outline-danger" name="btnSmazatJazyk" value="{$jazyk->id_jazyk}"
                                            data-confirm="Opravdu chcete smazat {$jazyk->getJazykTitle()}?">Smazat</button>
                                 </div>
                              </div>
                           {/foreach}
                        </div>
                     </form>

                     <form method="post">
                        <div class="row mt-4">
                           <div class="col-8">
                              <select class="form-select" name="selected_jazyk">
                                 {foreach $allJazyky as $jazyk}
                                    <option n:if="!in_array($jazyk->value, array_column($jazyky, 'id_jazyk'))" value="{$jazyk->value}">{$jazyk->getTitle()}</option>
                                 {/foreach}
                              </select>
                           </div>
                           <div class="col-4"><button type="submit" class="btn btn-outline-success" name="btnPridatJazyk">Přidat</button></div>
                        </div>
                     </form>
                  </div>
                  <div class="col-md-6 col-xl-4">
                     <div class="row"><h3>Měny</h3></div>

                     <form method="post">
                        <div class="row my-2 p-2">
                           {foreach $meny as $mena}
                              <div class="row my-1">
                                 <div class="d-inline-block">
                                    {$mena->getMena()->getTitle()}
                                    <small n:if="$mena->is_primary === 1" class="text-danger"> ({_'Primární'})</small>
                                    <button n:if="$mena->is_primary !== 1"
                                            class="btn btn-sm btn-outline-danger" name="btnNastavitPrimarniMenu" value="{$mena->getMena()->value}"
                                            data-confirm="Opravdu chcete nastavit {$mena->getMena()->getTitle()} jako primární měnu?">Nastavit primární</button>
                                 </div>
                              </div>
                           {/foreach}
                        </div>
                     </form>

                     <form method="post">
                        <div class="row mt-4">
                           <div class="col-8">
                              <select class="form-select" name="selected_mena">
                                 {foreach $allMeny as $mena}
                                    <option n:if="!in_array($mena->value, array_column($meny, 'id_mena'))" value="{$mena->value}">{$mena->getTitle()}</option>
                                 {/foreach}
                              </select>
                           </div>
                           <div class="col-4"><button type="submit" class="btn btn-outline-success" name="btnPridatMenu">Přidat</button></div>
                        </div>
                     </form>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>