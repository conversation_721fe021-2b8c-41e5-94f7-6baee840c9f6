<?php namespace app\admin\mista\panelySeznamu\import;

use app\front\FrontApplication;
use app\front\organizace\model\organizace\event\import\ImportPolozkyXlsxEvent;
use app\front\organizace\model\organizace\Organizace;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\modul\panels\Panel;
use Exception;
use Nette\Http\FileUpload;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 01.07.2024 */
class OrganizaceImportPanel extends Panel
{

   function getMenuName() :string {
      return 'Importy';
   }

   public function preparePanel(Templater $templater) :void {

   }

   protected function preparePostListeners() :void {
      $this->isset('btnUploadImportPolozky', function($post) {
         $file = new FileUpload($_FILES['importFile']);

         if(!$file->isOk())
            throw new Exception('Poškozený soubor', FrontApplication::EXCEPTION_ERROR_INPUT);

         if(!in_array($file->getSuggestedExtension(), ['xls', 'xlsx']))
            throw new Exception('Špatný typ souboru', FrontApplication::EXCEPTION_ERROR_INPUT);

         $event = (new ImportPolozkyXlsxEvent(
            Organizace::getMisto(intval($this->getParameter('id_organizace'))),
            $file
         ))->call();

         FlashMessages::setSuccess(sprintf(
            'Import proběhl úspěšně, importováno %d položek a %d kategorii',
            $event->importedPolozky,
            $event->importedKategorie,
         ))->setNoTranslate();
      });
   }

}