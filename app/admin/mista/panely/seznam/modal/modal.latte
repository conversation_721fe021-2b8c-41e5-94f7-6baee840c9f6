{varType app\system\model\translator\Jazyky[] $jazyky}
{varType app\system\model\uzivatele\UzivatelRow[] $uzivatele}

<div class="container">
   <div class="card p-3">
      <form method="post">
         <div class="row pb-4">
            <div class="form-floating col-md-12">
               <input type="text" id="name" name="name" class="form-control col-sm-9"
                      minlength="1" maxlength="60" required>
               <label for="name" class="ms-2">Název organizace<span class="text-danger">&nbsp;*</span></label>
            </div>
         </div>

         <div class="row pb-4">
            <div class="form-floating col-md-12">
               <select class="form-select col-sm-9" name="primarni_jazyk" id="primarni_jazyk" required>
                  {foreach $jazyky as $jazyk}
                     <option value="{$jazyk->value}">{$jazyk->getTitle()}</option>
                  {/foreach}
               </select>
               <label for="primarni_jazyk" class="ms-2">Primární jazyk<span class="text-danger">&nbsp;*</span></label>
            </div>
         </div>

         <div class="row pb-4">
            <div class="row">
               <label>Typ</label>
            </div>
            <div class="form-check-inline mb-3 user-reg-radio">
               <div class="form-check-inline">
                  <input class="btn-check" type="radio" name="typ_organizace" id="inlineRadio1" value=1 checked>
                  <label class="btn btn-outline-primary form-control" for="inlineRadio1">VENUE - místo</label>
               </div>
               <div class="form-check form-check-inline">
                  <input class="btn-check" type="radio" name="typ_organizace" id="inlineRadio2" value=2>
                  <label class="btn btn-outline-primary form-control" for="inlineRadio2">VENDOR - dodavatel</label>
               </div>
            </div>
         </div>

         <div class="row pb-4">
            <div class="row">
               <label>Typ licence</label>
            </div>
            <div class="form-check-inline mb-3 user-reg-radio">
               {foreach app\system\model\organizace\subscription\OrganizaceLicenceTyp::cases() as $licenceTyp}
                  <div class="form-check-inline">
                     <input class="btn-check" type="radio" name="licenceTyp" id="inlineRadioLicence{$licenceTyp->value}" value="{$licenceTyp->value}" n:attr="checked: $iterator->isFirst()">
                     <label class="btn btn-outline-primary form-control" for="inlineRadioLicence{$licenceTyp->value}">{$licenceTyp->name}</label>
                  </div>
               {/foreach}
            </div>
         </div>

         <div class="row pb-4">
            <div class="form-floating col-md-12">
               <select class="form-select col-sm-9" name="id_uzivatel" id="id_uzivatel" required>
                  <option value="">Vyberte existujicího uživatele</option>
                  {foreach $uzivatele as $uzivatel}
                     <option value="{$uzivatel->id_uzivatele}">{$uzivatel->getFullName()}&nbsp;-&nbsp;{$uzivatel->email}</option>
                  {/foreach}
               </select>
               <label for="id_uzivatel" class="ms-2">Zaregistrovaný majitel místa<span class="text-danger">&nbsp;*</span></label>
            </div>
         </div>

         <div class="row pb-4">
            <div class="form-check-inline mb-3 user-reg-radio">
               <div class="form-check-inline">
                  <input class="btn-check" type="checkbox" name="sendWelcomeMail" id="sendWelcomeMail" checked>
                  <label class="btn btn-outline-primary form-control" for="sendWelcomeMail">Poslat úvodní mail</label>
               </div>
            </div>
         </div>

         <div class="row d-flex justify-content-center m-4">
            <div class="col-md-3 col-sm-4 col">
               <button type="submit" name="btnVytvoritOrganizaci" class="form-control btn btn-primary">Vytvořit organizaci</button>
            </div>
         </div>
      </form>
   </div>
</div>