<?php namespace app\admin\mista\panely\seznam;

use app\admin\mista\panely\seznam\modal\NovaOrganizaceAdminModal;
use app\system\component\Templater;
use app\system\modul\panels\Panel;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 10.11.2022 */
class SeznamMistPanel extends Panel
{

   function getMenuName() :string {
      return 'Seznam';
   }

   public function preparePanel(Templater $templater) :void {
      $templater->addData([
         'tableMista' => (string)(new SeznamMistTable()),
         'novaOrgAttr' => NovaOrganizaceAdminModal::init()->btnToggleAttributes(),
      ]);
   }
}