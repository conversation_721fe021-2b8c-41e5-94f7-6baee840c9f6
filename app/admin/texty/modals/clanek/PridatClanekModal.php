<?php namespace app\admin\texty\modals\clanek;

use app\admin\controllers\clanek\EditClanekController;
use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\model\clanky\event\SaveClanekEvent;
use app\system\model\translator\Jazyky;
use app\system\modul\modal\Modal;
use app\system\Redirect;
use Nette\Utils\Random;

class PridatClanekModal extends Modal
{
   public function prepareModal(Templater $templater) :void {
      $templater->addData([
         'randomID' => Random::generate(),
         'languages' => Jazyky::getPairs(),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnAddClanek', function ($post) {
         if(!isset($post['nazev']) || trim($post['nazev']) === '' || $post['id_jazyk'] === '')
            throw FlashException::create('<PERSON><PERSON><PERSON> k chybě')
               ->setNoTranslate();

         Redirect::to(
            EditClanekController::getUrl(SaveClanekEvent::create($post)->id)
         );
      });
   }

   public function getTitleName() :string {
      return 'Přidat článek';
   }
}