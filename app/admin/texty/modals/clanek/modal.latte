{varType int $randomID}
{varType array $languages}

<div class="container">
   <form method="post" enctype="multipart/form-data" id="clanek-form-{$randomID}">
      <div class="mb-3">
         <label for="nazev" class="form-label">{_'Název'}</label>
         <input type="text" class="form-control" name="nazev" id="nazev" required>
      </div>

      <div class="mb-3">
         <label for="id_jazyk" class="form-label">{_'Jazyk'}</label>
         <select name="id_jazyk" id="id_jazyk" class="form-select" required>
            {foreach $languages as $id_language => $name}
               <option value="{$id_language}">
                  {$name}
               </option>
            {/foreach}
         </select>
      </div>

      <div class="row mt-3">
         <div class="col-auto">
            <button type="submit" class="btn btn-primary" name="btnAddClanek">{_'Vytvo<PERSON>it článek'}</button>
         </div>
      </div>
   </form>
</div>

<script>
   $(function() {
      const form = $('#clanek-form-'+{$randomID});
      form.validate();
   });
</script>