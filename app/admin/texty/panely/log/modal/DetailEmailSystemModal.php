<?php namespace app\admin\texty\panely\log\modal;

use app\admin\controllers\sablonyTexty\SablonyTextuActionController;
use app\system\component\Templater;
use app\system\mailer\data\MailItemRow;
use app\system\mailer\data\MailsModel;
use app\system\modul\modal\AjaxModal;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 24.09.2024 */
class DetailEmailSystemModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Detail odeslaného mailu';
   }

   public function prepareAjaxData() :void {
      $this->mail = MailsModel::get(intval($_POST['slug']));
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'mail' => $this->mail,
         'deleteAttachmentUrl' => SablonyTextuActionController::getUrl(SablonyTextuActionController::ACTION_DELETE_ATTACHMENT),
      ]);
   }

   private MailItemRow $mail;
}