{varType app\system\mailer\data\MailItemRow $mail}
{varType string $deleteAttachmentUrl}

<div class="container">
   <form method="post">
      <div class="row mt-3">
         <div class="col-md-auto form-group border radius-card border-light-gray p-2 ps-4">
            <label><i class="bi bi-envelope-plus me-1"></i>{_'Vytvořen'}</label>
            <p>{$mail->created|date: 'j.n.Y H:i'}</p>
         </div>
         <div class="col-md-auto text-center align-content-center mx-4">
            <i class="bi bi-arrow-right lead text-primary"></i>
         </div>
         <div class="col-md-auto form-group border radius-card border-light-gray p-2 ps-4">
            <label><i class="bi bi-envelope-check me-1"></i>{_'Odeslán'}</label>
            <p>
               {if $mail->sended}
                  {$mail->sended|date: 'j.n.Y H:i'}
               {else}
                  {if $mail->is_error === 0}
                     {_'Ještě nebyl odeslán'}
                  {else}
                     {_'Chyba při odesílání'}
                  {/if}
               {/if}
            </p>
         </div>
      </div>
      <div class="row mt-3">
         <label>{_'Předmět emailu'}</label>
         <p class="lead">{$mail->subject}</p>
      </div>
      <div class="row mt-3">
         <h3 class="lead">{_'Příjemci'}</h3>
         {foreach $mail->getRecipients() as $recipient}
            <div class="col-auto form-group">
               <label for="recipientEmail[{$recipient->id}]">{$recipient->name}</label>
               <input type="text" id="recipientEmail[{$recipient->id}]"
                      name="recipientEmail[{$recipient->id}]" value="{$recipient->email}"
                      class="form-control" maxlength="255" required>
            </div>
         {/foreach}
      </div>
      <div n:if="!empty($mail->getAttachments())" class="row mt-3 js-attachment-container">
         <h3 class="lead">{_'Přílohy'}</h3>
         {foreach $mail->getAttachments() as $attachment}
            <div class="col-auto d-flex gap-1 js-attachment-item">
               <div class="border radius-card border-light-gray py-1 px-2">
                  {$attachment->getFileName()}
               </div>
               <button type="button" class="btn btn-sm btn-outline-secondary" data-delete-attachment="{$attachment->id}">
                  <i class="bi bi-x-circle mx-1"></i>
               </button>
            </div>
         {/foreach}
      </div>
      <div n:if="!empty($mail->getErrors())" class="row mt-3">
         <h3 class="lead">{_'Chyby'}</h3>
         {foreach $mail->getErrors() as $error}
            <div class="col-auto">
               <small><i class="bi bi-envelope-exclamation me-2"></i>{$error->message} -> {$error->created|date: 'j.n.Y H:i'}
               </small>
            </div>
         {/foreach}
      </div>
      <div class="row mt-3">
         <h3 class="lead">{_'Náhled'}</h3>
         <div class="border radius-card border-light-gray p-3">
            <iframe id="iframeMailPreview" style="width: 100%; min-height: 30vh"></iframe>
         </div>
      </div>
      <div class="row mt-2 d-flex justify-content-center gap-2">
         <button class="btn btn-primary w-auto shaddow-hover" type="submit" name="btnResendEmail"
                 value="{$mail->id}">{_'Uložit a odeslat znovu'}</button>
         <button class="btn btn-outline-secondary w-auto shaddow-hover" type="submit"
                 data-confirm="{_'Opravdu chcete email smazat?'}" name="btnDeleteEmail"
                 value="{$mail->id}">{_'Smazat'}</button>
      </div>
   </form>
</div>


<script>
   $(function() {
      const iframe = document.getElementById('iframeMailPreview');

      iframe.contentWindow.document.open();
      iframe.contentWindow.document.write(`{$mail->content|noescape}`);
      iframe.contentWindow.document.close();

      $('body').on('click', 'button[data-delete-attachment]', function(e) {
         e.preventDefault();
         const btn = $(this);
         const col = btn.closest('div.js-attachment-item');
         const attachmentID = btn.attr('data-delete-attachment');
         const container = col.closest('div.js-attachment-container');

         btn.prop('disabled', true);

         promiseHandler.post(
                 {$deleteAttachmentUrl},
            {
               mailID: {$mail->id},
               attachmentID
            },
         ).then(
            (r) => {
               if(!r.success){
                  btn.prop('disabled', null);
                  return;
               }

               col.remove();

               if(container.find('div.js-attachment-item').length === 0)
                  container.remove();
            },
         );

         e.stopPropagation();
      });
   });
</script>