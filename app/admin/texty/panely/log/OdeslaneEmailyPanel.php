<?php namespace app\admin\texty\panely\log;

use app\system\flash\FlashMessages;
use app\system\mailer\data\MailsModel;
use app\system\mailer\EmailSender;
use app\system\modul\panels\Panel;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 24.09.2024 */
class OdeslaneEmailyPanel extends Panel
{

   function getMenuName() :string {
      return 'Odeslané emaily';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnResendEmail', function($post) {
         if(
            !($mailID = intval($post['btnResendEmail']))
            || !($mail = MailsModel::get($mailID))
            || $mail->id_organizace !== 0
         ) {
            FlashMessages::setError('Email nelze znovu odeslat');
            return;
         }

         $recipients = $post['recipientEmail'];
         foreach($mail->getRecipients() as $recipient){
            if(!($newEmail = trim($recipients[$recipient->id] ?? ''))) {
               FlashMessages::setError('Email je povinný');
               return;
            }

            $recipient->email = $newEmail;
            $recipient->save();
         }

         (new EmailSender($mail))->send();
      });

      $this->isset('btnDeleteEmail', function($post) {
         if(
            !($mailID = intval($post['btnDeleteEmail']))
            || !($mail = MailsModel::get($mailID))
            || $mail->id_organizace !== 0
         ) {
            FlashMessages::setError('Tento email nelze smazat');
            return;
         }

         MailsModel::delete($mail);
      });
   }

   public function returnString() :?string {
      return (new SystemOdeslaneEmailyTable());
   }
}