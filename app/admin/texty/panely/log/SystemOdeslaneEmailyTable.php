<?php namespace app\admin\texty\panely\log;

use app\admin\texty\panely\log\modal\DetailEmailSystemModal;
use app\system\helpers\HtmlBuilder;
use app\system\mailer\data\MailsModel;
use app\system\table\DynamicTable;
use dibi;
use Dibi\DateTime;
use Dibi\Fluent;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 24.09.2024 */
class SystemOdeslaneEmailyTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addText('id', 'ID');
      $this->addText('subject', 'Předmět');
      $this->addText('sended', 'Odesláno')
         ->setOrderable(false)
         ->setSearchable(false)
         ->setFormatter(function(?DateTime $value, $row) {
            if(!$value)
               return 'Neodesláno';

            return $value->format('j.n.Y H:i');
         });

      $this->addText('id2.id', ' ')
         ->setSearchable(false)
         ->setOrderable(false)
         ->setFormatter(function(int $id) {
            return HtmlBuilder::buildElement('a', array_merge([
               'href' => '#'
            ], DetailEmailSystemModal::getShowAttributes($id)))->setHtml('Detail')->get();
         });
   }

   function getQuery() :Fluent {
      return dibi::select('*')
         ->from(MailsModel::TABLE)
         ->where('%n = 0', MailsModel::FK_ORGANIZACE);
   }
}