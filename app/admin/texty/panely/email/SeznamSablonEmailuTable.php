<?php namespace app\admin\texty\panely\email;

use app\admin\controllers\sablonyTexty\DetailSablonyEmailuController;
use app\System;
use app\system\helpers\HtmlBuilder;
use app\system\mailer\templates\Templates;
use app\system\table\DynamicTable;
use dibi;
use Dibi\Fluent;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 25.09.2022 */
class SeznamSablonEmailuTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addText('id_template', 'ID');
      $this->addText('kod', 'Kod');
      $this->addText('title', 'Title');
      $this->addText('id.id_template', ' ')->setFormatter(function($value) {
         return HtmlBuilder::buildElement('a', [
            'href' => DetailSablonyEmailuController::getUrl($value),
         ])->setHtml(System::getTranslator()->translate('Detail'))->get();
      })->setOrderable(false)->setSearchable(false);
   }

   function getQuery() :Fluent {
      return dibi::select('*')
         ->from('mail_template')
         ->where('kod IN %in', Templates::getValues(Templates::cases()));
   }
}