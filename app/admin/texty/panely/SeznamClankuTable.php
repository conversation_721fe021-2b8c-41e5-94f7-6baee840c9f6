<?php namespace app\admin\texty\panely;

use app\admin\controllers\clanek\EditClanekController;
use app\System;
use app\system\helpers\HtmlBuilder;
use app\system\model\clanky\SystemClanek;
use app\system\table\DynamicTable;
use Dibi\Fluent;

class SeznamClankuTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addText('id', 'ID');
      $this->addText('nazev', 'Název');
      $this->addText('priority', 'Priority');
      $this->addSelect('published', 'Zveřejněno', $stavZverejneni = [
         1  => System::getTranslator()->translate('Zveřejněno'),
         0  => System::getTranslator()->translate('Nezveřejněno'),
      ])
         ->setFormatter(function ($value) use ($stavZverejneni) {
            return $value !== null ? $stavZverejneni[1] : $stavZ<PERSON>jneni[0];
         })
         ->setSearchCallback(function (Fluent $query, $search) {
            if ($search === '1') {
               $query->where('published IS NOT NULL');
            } elseif ($search === '0') {
               $query->where('published IS NULL');
            }
         });
      $this->addText('edit.id', ' ')
         ->setFormatter(function ($value) {
            return HtmlBuilder::buildElement('a', array_merge([
               'href' => EditClanekController::getUrl($value),
            ]))->setHtml('Upravit')->get();
         });
   }

   function getQuery() :Fluent {
      return SystemClanek::find();
   }
}