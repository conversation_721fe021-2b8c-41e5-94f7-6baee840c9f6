{varType app\system\model\texty\sablony\SablonaTextBlokRow $blok}

<div class="row plainText">
    <form method="post" enctype="multipart/form-data">
        <div class="col-12">
            <div class="row">
                <input type="hidden" name="id_blok" value="{$blok->id_blok}">
                <label for="obsah">Obsah</label>
                <textarea name="obsah"
                    {if $blok->id_typ == app\system\model\texty\ciselniky\TypyBloku::FORMATED_TEXT}
                          id="editace"{/if}>{$blok->obsah}
                </textarea>
            </div>
        </div>
        <div class="row mt-2 mb-2">
            <div class="col-2">
                <input type="submit" class="form-control btn btn-primary" name="btnEditBlok" value="Upravit blok">
            </div>
        </div>
    </form>
</div>

<script>
    $(function (){
        tinymce.init({
            selector: 'textarea#editace'
        });
    });
</script>