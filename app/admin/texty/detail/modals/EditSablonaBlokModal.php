<?php namespace app\admin\texty\detail\modals;

use app\system\component\Templater;
use app\system\model\texty\sablony\SablonyTextBloky;
use app\system\modul\modal\AjaxModal;

class EditSablonaBlokModal extends AjaxModal
{

   public function getTitleName(): string {
      return 'Editace bloku';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnEditBlok', function() {
         $_POST['id_text'] = (int)$_GET['id'];
         SablonyTextBloky::editBlok($_POST);
      });
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'blok' => SablonyTextBloky::get((int)($_POST['slug'])),
      ]);
   }
}