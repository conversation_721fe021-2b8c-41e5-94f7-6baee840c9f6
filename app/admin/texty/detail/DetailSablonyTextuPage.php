<?php namespace app\admin\texty\detail;

use app\admin\texty\detail\modals\EditSablonaBlokModal;
use app\admin\texty\detail\panely\DetailSablonyTextuPanel;
use app\admin\texty\detail\panely\NahledSablonyTextuPanel;
use app\system\helpers\text\blok\TextBlokyComponent;
use app\system\lay\panels\PanelsLayout;
use app\system\model\texty\sablony\SablonaTextRow;
use app\system\model\texty\sablony\SablonyTexty;
use app\system\router\NewRouter;
use Dibi\Exception;

class DetailSablonyTextuPage extends PanelsLayout
{

   function getPageName(): string {
      return 'Detail šablony';
   }

   function preparePanely() :void {
      $id = NewRouter::get()->getRoute()->getParametrs()['slug'];

      if(!$this->text = SablonyTexty::get($id))
         throw new Exception('Nejsou šablony, ošetřit vyhozením 404');

      $this->appendDetailPanel();
      $this->appendNahledPanel();
   }

   protected function appendDetailPanel() :void {
      $panel = DetailSablonyTextuPanel::init()
         ->addParameter('id_text', $this->text->id_text)
         ->addModal(EditSablonaBlokModal::init());

      $this->subscribePanel($panel);
   }

   protected function appendNahledPanel() :void {
      $panel = NahledSablonyTextuPanel::init()
         ->addParameter('id_text', $this->text->id_text)
         ->addParameter('id_type', TextBlokyComponent::SABLONA);

      $this->subscribePanel($panel);
   }

   public SablonaTextRow $text;
}