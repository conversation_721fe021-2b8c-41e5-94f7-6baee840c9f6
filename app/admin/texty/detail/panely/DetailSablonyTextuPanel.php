<?php namespace app\admin\texty\detail\panely;

use app\system\component\Templater;
use app\system\helpers\text\blok\TextBlokyComponent;
use app\system\model\texty\ciselniky\TypyTextu;
use app\system\model\texty\sablony\SablonyPodtypuTextu;
use app\system\model\texty\sablony\SablonyTextBloky;
use app\system\model\texty\sablony\SablonyTexty;
use app\system\model\texty\Texty;
use app\system\model\translator\Jazyky;
use app\system\modul\panels\Panel;

class DetailSablonyTextuPanel extends Panel
{

   function getMenuName(): string {
      return 'Detail textu';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnEditStranka', function($post) {
         Texty::editText($post);
      });

      $this->isset('btnNewBlok', function($post) {
         $blok = SablonyTextBloky::createBlok($post);
         SablonyTexty::addBlok($blok, (int)$post['id_text']);
      });
   }

   public function preparePanel(Templater $templater) :void {
      $id = $this->getParameter('id_text');
      $text = SablonyTexty::get($id);
      $podtyp = SablonyPodtypuTextu::get($text->id_podtyp);

      $templater->addData([
         'text' => $text,
         'podtyp' => $podtyp,
         'bloky' => (string)TextBlokyComponent::getComponent()->setIdText($text->id_text)
            ->setType(TextBlokyComponent::SABLONA),
         'typ' => TypyTextu::$TypyTextuArray[$podtyp->id_typ],
         'jazyky' => Jazyky::getOthers($text->id_jazyk),
         'zvolenyJazyk' => Jazyky::from($text->id_jazyk),
      ]);
   }
}