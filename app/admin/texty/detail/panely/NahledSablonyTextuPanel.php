<?php namespace app\admin\texty\detail\panely;

use app\system\component\Templater;
use app\system\model\texty\sablony\SablonyTexty;
use app\system\modul\panels\Panel;

class NahledSablonyTextuPanel extends Panel
{

   function getMenuName(): string {
      return 'náhled textu';
   }

   public function preparePanel(Templater $templater) :void {
      $templater->addData([
         'link' => '/nahled-text-' . 's' . SablonyTexty::get((int)$_POST['id_text'])->id_text,
      ]);
   }
}