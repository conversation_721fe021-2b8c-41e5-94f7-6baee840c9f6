{varType app\system\model\texty\sablony\SablonaTextRow $text}
{varType app\system\model\texty\sablony\SablonaPodtypuTextuRow $podtyp}
{varType app\system\model\translator\Jazyky[] $jazyky}
{varType app\system\model\translator\Jazyky $zvolenyJazyk}

<div class="container">
    <form method="post" enctype="multipart/form-data" class="pe-2">
        <div class="row">
            <div class="col-6">
                <div class="row">
                    <label for="nazev">Název</label>
                    <input type="text" class="form-control" name="nazev" value="{$text->nazev}">
                    <input type="hidden" class="form-control" name="id_text" value="{$text->id_text}">
                </div>
            </div>
        </div>
        <div class="row justify-content-between">
            <div class="col-2">
                <div class="row">
                    <label for="typ">Typ</label>
                    <input type="text" class="form-control" name="typ" value="{$typ}" disabled>
                </div>
            </div>
            <div class="col-2">
                <div class="row">
                    <label for="podtyp">Podtyp</label>
                    <input type="text" class="form-control" name="podtyp" value="{$podtyp->nazev}" disabled>
                </div>
            </div>
            <div class="col-2">
                <div class="row">
                    <label for="kod">Kód</label>
                    <input type="text" class="form-control" name="kod" {if $text->kod} value="{$text->kod}" {/if} >
                </div>
            </div>
            <div class="col-2">
                <div class="row">
                    <label for="kod">Jazyk</label>
                    <select name="id_jazyk" class="form-select">
                        <option value="{$text->id_jazyk}">{$zvolenyJazyk->getTitle()}</option>
                        {foreach $jazyky as $jazyk}
                            <option value="{$jazyk->value}">{$jazyk->getTitle()}</option>
                        {/foreach}
                    </select>
                    {*                    <input type="text" class="form-control" name="id_jazyk" value="{$jazyky[$text->id_jazyk]}">*}
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="row">
                    <label for="popis">Popis</label>
                    <textarea name="popis">{$text->popis}</textarea>
                </div>
            </div>
        </div>

        <hr class="my-4">

        <div class="row mt-2 mb-2">
            <div class="col-2 col-md-2 col-sm-3">
                <input type="submit" class="form-control btn btn-primary" name="btnEditStranka" value="Ulož změny">
            </div>
            <div class="col-2 col-md-2 col-sm-3">
                <input type="button" class="form-control btn btn-primary textBtn" id="showPlainText" data-text="Přidat HTML" data-type="plainText" value="Přidat HTML">
            </div>
            <div class="col-2 col-md-2 col-sm-3">
                <input type="button" class="form-control btn btn-primary textBtn" id="showFormatedText" data-text="Přidat text" data-type="formatedText" value="Přidat text">
            </div>
        </div>
    </form>


    <hr class="my-4">

    <div class="row formatedText hiddenBlock block" id="formatedText">
        <form method="post" enctype="multipart/form-data">
            <div class="col-12">
                <input type="hidden" value="{app\system\model\texty\ciselniky\TypyBloku::FORMATED_TEXT}" name="id_typ">
                <input type="hidden" value="{$text->id_text}" name="id_text">
                <div class="row">
                    <label for="obsah">Obsah</label>
                    <textarea name="obsah" class="js-tinyeditor"></textarea>
                </div>
            </div>
            <div class="row mt-2 mb-2">
                <div class="col-2">
                    <input type="submit" class="form-control btn btn-primary" name="btnNewBlok" value="Přidat blok">
                </div>
            </div>
        </form>
        <hr class="my-4">
    </div>

    <div class="row plainText hiddenBlock block" id="plainText">
        <form method="post" enctype="multipart/form-data">
            <div class="col-12">
                <input type="hidden" value="{app\system\model\texty\ciselniky\TypyBloku::PLAIN_TEXT}" name="id_typ">
                <input type="hidden" value="{$text->id_text}" name="id_text">
                <div class="row">
                    <label for="obsah">Obsah</label>
                    <textarea name="obsah"></textarea>
                </div>
            </div>
            <div class="row mt-2 mb-2">
                <div class="col-2">
                    <input type="submit" class="form-control btn btn-primary" name="btnNewBlok" value="Přidat blok">
                </div>
            </div>
        </form>
        <hr class="my-4">
    </div>

    <script>
        $(function (){
            tinymce.init({
                selector: 'textarea.js-tinyeditor',
            });
        });
    </script>
    <div id="bloky" class="pe-2">
        {$bloky|noescape}
    </div>

</div>


<script>
    let closeText = 'Zavřít';

    function closeBlocks(){
        $('.block').removeClass('displayedBlock').addClass('hiddenBlock');
    }

    function showBlock(id, text, button){
        let blok = $('.' + id);

        if(blok.hasClass("displayedBlock")){
            $(button).val(text);
            closeBlocks();
        }else {

            $( ".textBtn" ).each(function() {
                $( this ).val($(this).data('text'));
            });

            closeBlocks();
            blok.addClass("displayedBlock").removeClass('hiddenBlock');
            $(button).val(closeText);
        }
    }

    $(function() {
        $('.textBtn').on('click', function() {
            let type = $(this).data('type');
            let text = $(this).data('text');
            let id = '#' + $(this).attr('id');
            showBlock(type, text, id);
        });
    })
</script>