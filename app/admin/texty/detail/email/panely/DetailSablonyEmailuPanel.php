<?php namespace app\admin\texty\detail\email\panely;

use app\front\controllers\api\ApiTextController;
use app\system\application\AdminApplicationEnvironment;
use app\system\component\Templater;
use app\system\mailer\templates\data\MailTemplate;
use app\system\modul\panels\Panel;
use app\system\tiny\images\AdminEditorImages;
use app\system\tiny\TinyEditor;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.09.2022 */
class DetailSablonyEmailuPanel extends Panel
{

   function getMenuName() :string {
      return $this->menuName;
   }

   public function setMenuName(string $menuName) :DetailSablonyEmailuPanel {
      $this->menuName = $menuName;
      return $this;
   }

   protected function preparePostListeners() :void {
      $this->isset('btnSaveEmailSablona', function($post) {
         $idTemplate = $this->getParameter('id_template');
         MailTemplate::saveContent($idTemplate, $post['id_jazyk'], $post['text']);
      });
   }

   public function preparePanel(Templater $templater) :void {
      $idTemplate = $this->getParameter('id_template');
      $idJazyk = $this->getParameter('id_jazyk');

      $this->template = MailTemplate::getById($idTemplate, $idJazyk);
      $this->appendEditor();
      $this->appendNahled();
      $this->data['id_jazyk'] = $idJazyk;

      $templater->addData($this->data);
   }

   protected function appendEditor() {
      $this->data['tinyEditor'] = TinyEditor::init()
         ->setInputName('text')
         ->setImagesRules(new AdminEditorImages(AdminApplicationEnvironment::get()->user->userID))
         ->prepareForEmailTemplate($this->template)
         ->render();
   }
   protected function appendNahled() {
      $this->data['nahledLink'] = ApiTextController::getUrl('email') .
         sprintf('?template=%s&lang=%d', $this->template->kod, $this->template->id_jazyk);
   }

   protected string $menuName;
   protected array $data = [];
   protected ?MailTemplate $template;
}