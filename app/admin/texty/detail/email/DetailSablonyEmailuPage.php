<?php namespace app\admin\texty\detail\email;

use app\admin\texty\detail\email\panely\DetailSablonyEmailuPanel;
use app\System;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\lay\panels\PanelsLayout;
use app\system\mailer\templates\data\MailTemplate;
use app\system\model\translator\Jazyky;
use app\system\model\translator\TextVariablesFactory;
use app\system\traits\PostListener;
use dibi;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 29.09.2022 */
class DetailSablonyEmailuPage extends PanelsLayout
{

   use PostListener;

   function getPageName() :string {
      return '';
   }

   protected function getTranslatedPageName() :?TextVariablesFactory {
      return System::getTranslator()
         ->layoutTranslate('Edit emailu: $1', $this->title ?? '');
   }

   function getPageLabelAddon() :?string {
      return $this->dropdown;
   }

   function preparePanely() :void {
//      Vyřešit
      $this->title = MailTemplate::getTitleById($this->id_template);

      $contents = MailTemplate::getJazyky($this->id_template);

      foreach($contents as $id_content => $id_jazyk)
         $this->subscribePanel((new DetailSablonyEmailuPanel($id_content))
            ->setMenuName(Jazyky::from($id_jazyk)->getTitle())
            ->addParameter('id_template', $this->id_template)
            ->addParameter('id_jazyk', $id_jazyk));


      if(count($contents) < count(Jazyky::cases())){
         $unused = [];

         foreach(Jazyky::cases() as $jazyk)
            if(!in_array($jazyk->value, $contents))
               $unused[$jazyk->value] = $jazyk->getTitle();

         if(!empty($unused))
            $this->dropdown = Templater::prepare(__DIR__ . '/dropdown-preklad.latte', [
               'jazyky' => $unused,
               'title' => $this->title,
               'id_template' => $this->id_template,
            ])->render();
      }
   }

   public function setIdTemplate(int $id_template) :static {
      $this->id_template = $id_template;
      return $this;
   }

   protected function preparePostListeners() :void {
      $this->isset('btnPridatPreklad', function($post) {
         dibi::query('INSERT INTO %sql %v', MailTemplate::TABLE_CONTENT, [
            'id_template%i' => $post['id_template'],
            'id_jazyk%i' => $post['id_jazyk'],
            'title%s' => $post['title'],
            'content%s' => dibi::fetchSingle('SELECT content FROM %sql WHERE id_template = %i ORDER BY id_jazyk',
               MailTemplate::TABLE_CONTENT, $post['id_template'])
         ]);
         FlashMessages::setSuccess('Překlad vytvořen');
      });
   }

   protected int $id_template;
   protected ?string $dropdown = null;
   private string $title;
}