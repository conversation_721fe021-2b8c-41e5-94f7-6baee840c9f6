<?php namespace app\admin\texty;

use app\admin\texty\modals\clanek\PridatClanekModal;
use app\admin\texty\panely\email\SeznamSablonEmailuPanel;
use app\admin\texty\panely\log\modal\DetailEmailSystemModal;
use app\admin\texty\panely\log\OdeslaneEmailyPanel;
use app\admin\texty\panely\SeznamClankuPanel;
use app\system\lay\panels\PanelsLayout;
use app\system\model\texty\sablony\SablonaTextRow;

class SablonyTextyPage extends PanelsLayout
{

   function getPageName() :string {
      return 'Texty';
   }

   function preparePanely() :void {
      $this->appendDetailPanel();
   }

   protected function appendDetailPanel() :void {
      $panel = SeznamClankuPanel::init()
         ->addModal(PridatClanekModal::init());

      $this->subscribePanel($panel);
      $this->subscribePanel(SeznamSablonEmailuPanel::init());

      $this->subscribePanel(
         OdeslaneEmailyPanel::init()
            ->addModal(DetailEmailSystemModal::init())
      );
   }

   protected SablonaTextRow $text;
}