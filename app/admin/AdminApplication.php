<?php namespace app\admin;

use app\admin\controllers\AdminControllerList;
use app\front\uzivatel\LoginController;
use app\System;
use app\system\Application;
use app\system\application\AdminApplicationEnvironment;
use app\system\application\IEnvironment;
use app\system\Environment;
use app\system\flash\FlashMessages;
use app\system\lay\error\Error404Page;
use app\system\layout\head\HeadComponent;
use app\system\model\uzivatele\admin\UzivatelAdminLoginEvent;
use app\system\recaptcha\Recaptcha;
use app\system\Redirect;
use app\system\router;
use app\system\traits\JsonAjax;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.11.2023 */
class AdminApplication extends Application
{

   use JsonAjax;

   public static HeadComponent $head;

   protected function onPrepare() :void {
      $this->setAfterStart(function() {
         if(isset($_POST['btnPrihlasit'])){
            if(Recaptcha::isEnabled() && !Recaptcha::checkToken($_POST[Recaptcha::POST_PARAM_NAME], 'LOGIN')){
               FlashMessages::setError('Chyba přihlášení, zkuste to znovu');
               Redirect::self();
            }

            if(UzivatelAdminLoginEvent::handleLogin($_POST['login'], $_POST['pass']))
               Redirect::homepage();
            else
               Redirect::self();
         }
      });

      if(!Environment::isAjax())
         $this->prepareHead();
   }

   public function notFoundCallback() :?callable {
      return function() {
         if($this->environment->isLogged())
            Error404Page::show();

         if(Environment::isAjax()){
            FlashMessages::setError('Vaše přihlášení vypršelo, prosím přihlašte se znovu')->setNoTranslate();
            $this->sendAjaxResponse([
               'unloggedUser' => LoginController::getUrl()
            ], true);
         }

         Redirect::to(LoginController::getUrl());
      };
   }

   public function prepareRoutes(router\RoutesContainer $container) :void {
      if($this->environment instanceof AdminApplicationEnvironment)
         AdminControllerList::prepareControllers($container, $this->environment);
   }

   protected function prepareApplicationEnvironment(System $system) :IEnvironment {
      return AdminApplicationEnvironment::init($system);
   }

   protected function prepareHead() {
      self::$head = HeadComponent::getComponent();
   }
}