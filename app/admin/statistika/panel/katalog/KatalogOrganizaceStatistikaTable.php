<?php namespace app\admin\statistika\panel\katalog;

use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\system\table\DynamicTable;
use app\system\table\DynamicTableColumn;
use app\system\tracker\data\OrganizaceKatalogTrackerDailyRow;
use app\system\tracker\data\OrganizaceKatalogTrackerModel;
use app\system\tracker\TrackerPageType;
use Dibi\Fluent;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 28.08.2025 */
class KatalogOrganizaceStatistikaTable extends DynamicTable
{

   function prepareTable() :void {
      $this->appendOrganizace();
      $this->addSelect('page_key', 'Typ stránky', TrackerPageType::getPairs());
      $this->addDate('day', 'Datum')->setDefaultOrder();
      $this->addText('views', 'Počet zobrazení')->setSearchable(false);
      $this->addText('unique', 'Unikátní zobrazení')->setSearchable(false);
   }

   function getQuery() :Fluent {
      return OrganizaceKatalogTrackerModel::find();
   }

   private function appendOrganizace() :DynamicTableColumn {
      $this->addDataPreparator(
      /** @param OrganizaceKatalogTrackerDailyRow[] $rows */
         function(array $rows) :void {
            $organizaceID = [];
            foreach($rows as $row)
               $organizaceID[$row->id_organizace] ??= $row->id_organizace;

            if (empty($organizaceID))
               return;

            $this->organizace = Organizace::getByIds($organizaceID);
         }
      );

      return $this
         ->addText('id_organizace', 'Organizace ID')
         ->setFormatter(
            function (int $value) {
               return $this->organizace[$value]->nazev;
            }
         )->setSearchCallback(
            function(Fluent $query, string $search) {
               $query
                  ->join(Organizace::TABLE, 'o')
                  ->on('oktd.id_organizace = o.id_organizace')
                  ->where('o.nazev LIKE %~like~', $search);
            }
         )->setOrderable(false);
   }

   /** @var OrganizaceRow[] */
   protected array $organizace = [];
}