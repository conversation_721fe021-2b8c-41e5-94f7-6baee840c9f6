<?php namespace app\admin\uzivatele\prehled;

use app\admin\uzivatele\prehled\modals\edit\EditUzivateleModal;
use app\admin\uzivatele\prehled\modals\odkaz\OdkazUzivateleModal;
use app\system\helpers\HtmlBuilder;
use app\system\model\uzivatele\Uzivatel;
use app\system\model\uzivatele\UzivatelRow;
use app\system\table\DynamicTable;
use Dibi\Fluent;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 09.02.2022 */
class SeznamUzivateluTable extends DynamicTable
{

   function prepareTable() :void {
      $this->addText(Uzivatel::COLUMN_ID, 'ID');
      $this->addText('prijmeni')->setFormatter(function($value, $row) {
         /** @var UzivatelRow $row */
         return $row->getFullName();
      });
      $this->addText('email', 'Email');
      $this->addText('id.' . Uzivatel::COLUMN_ID, '-')->setFormatter(function($value, $row) {
         $links = [];
         if(!$row->heslo)
            $links[] = HtmlBuilder::buildElement('a', array_merge([
               'href' => '#'
            ], OdkazUzivateleModal::getShowAttributes($value)))->setHtml('registrace')->get();

         $links[] = HtmlBuilder::buildElement('a', array_merge([
            'href' => '#'
         ], EditUzivateleModal::getShowAttributes($value)))->setHtml('upravit')->get();
         return implode(', ', $links);
      });
   }

   function getQuery() :Fluent {
      return Uzivatel::find();
   }
}