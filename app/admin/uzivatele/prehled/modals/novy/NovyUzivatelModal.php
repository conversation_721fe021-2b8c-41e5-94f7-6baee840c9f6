<?php namespace app\admin\uzivatele\prehled\modals\novy;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\uzivatele\Uzivatel;
use app\system\modul\modal\Modal;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 09.02.2022 */
class NovyUzivatelModal extends Modal
{

   public function getTitleName() :string {
      return 'Nový uživatel';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnZalozitUzivatele', function($post) {
         Uzivatel::createUser($post);
         FlashMessages::setSuccess('Uživatel byl vytvořen');
      });
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         //'isAdmin' => Uzivatel::getAccess('beatapp'),
      ]);
   }

   protected bool $topMenuLink = true;
}