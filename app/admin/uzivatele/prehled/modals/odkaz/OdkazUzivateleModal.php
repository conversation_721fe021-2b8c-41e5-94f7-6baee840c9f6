<?php namespace app\admin\uzivatele\prehled\modals\odkaz;

use app\system\component\Templater;
use app\system\model\uzivatele\Uzivatel;
use app\system\model\uzivatele\UzivatelRow;
use app\system\modul\modal\AjaxModal;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 14.02.2022 */
class OdkazUzivateleModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Odkaz pro dokončení registrace';
   }

   public function prepareAjaxData() :void {
      $this->uzivatel = Uzivatel::get($_POST['slug']);
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'href' => $this->uzivatel->getPasswordChangeLink()
      ]);
   }

   protected UzivatelRow $uzivatel;
}