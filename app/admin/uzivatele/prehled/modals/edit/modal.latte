{varType app\system\model\uzivatele\UzivatelRow $uzivatel}

<div class="row">
   <form method="post">
      <div class="row">
         <div class="col-sm-6">
            <div class="form-group row pb-4">
               <label for="jmeno" class="col-sm-3 col-form-label">Jméno</label>
               <div class="col-sm-9">
                  <input type="text" name="jmeno" id="jmeno" class="form-control" value="{$uzivatel->jmeno}">
               </div>
            </div>
         </div>
         <div class="col-sm-6">
            <div class="form-group row pb-4">
               <label for="prijmeni" class="col-sm-3 col-form-label">Přijmení</label>
               <div class="col-sm-9">
                  <input type="text" name="prijmeni" id="prijmeni" class="form-control" value="{$uzivatel->prijmeni}">
               </div>
            </div>
         </div>
      </div>
      <div class="form-group row pb-4">
         <label for="email" class="col-sm-2 col-form-label">Email zaměstnance</label>
         <div class="col-sm-10">
            <input type="text" id="email" class="form-control" value="{$uzivatel->email}" disabled>
         </div>
      </div>
      <div class="row">
         <div class="col-sm-8">
{*            <div class="form-group row pb-4">*}
{*               <div class="col-sm" n:if="$isAdmin?? false">*}
{*                  <div class="form-check">*}
{*                     <input class="form-check-input" type="checkbox" name="beatappAccess" id="beatappAccess" n:attr="checked=>!!$uzivatel->hasAccess('beatapp')">*}
{*                     <label class="form-check-label" for="beatappAccess">*}
{*                        Administrátor*}
{*                     </label>*}
{*                  </div>*}
{*               </div>*}
{*               <div class="col-sm">*}
{*                  <div class="form-check">*}
{*                     <input class="form-check-input" type="checkbox" name="adminAccess" id="adminAccess" n:attr="checked=>!!$uzivatel->hasAccess('admin')">*}
{*                     <label class="form-check-label" for="adminAccess">*}
{*                        Správce systému*}
{*                     </label>*}
{*                  </div>*}
{*               </div>*}
{*            </div>*}
         </div>
      </div>
      <div class="row">
         <div class="col-sm-3">
            <input type="hidden" name="id_uzivatel" value="{$uzivatel->id_uzivatel}">
            <input type="submit" name="btnUlozUzivatele" class="form-control btn btn-primary" value="Uložit uživatele"
                   data-confirm="Opravdu chcete upravit uživatele {$uzivatel->getFullName()}?">
         </div>
      </div>
   </form>
</div>