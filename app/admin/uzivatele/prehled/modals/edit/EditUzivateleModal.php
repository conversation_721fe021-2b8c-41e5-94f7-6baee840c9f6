<?php namespace app\admin\uzivatele\prehled\modals\edit;

use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\model\uzivatele\Uzivatel;
use app\system\model\uzivatele\UzivatelRow;
use app\system\modul\modal\AjaxModal;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 14.02.2022 */
class EditUzivateleModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Upravit uživatele';
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUlozUzivatele', function($post) {
         $user = Uzivatel::get($post[Uzivatel::COLUMN_ID]);
         $user->createFromPost($post);
         Uzivatel::save($user);
         FlashMessages::setSuccess('Uživatel uložen');
      });
   }

   public function prepareAjaxData() :void {
      $this->uzivatel = Uzivatel::get($_POST['slug']);
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'uzivatel' => $this->uzivatel,
//         'isAdmin' => Uzivatel::getAccess('beatapp'),
      ]);
   }

   protected UzivatelRow $uzivatel;
}