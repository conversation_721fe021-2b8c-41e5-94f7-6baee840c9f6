<?php namespace app\admin\uzivatele;

use app\admin\uzivatele\prehled\modals\edit\EditUzivateleModal;
use app\admin\uzivatele\prehled\modals\novy\NovyUzivatelModal;
use app\admin\uzivatele\prehled\modals\odkaz\OdkazUzivateleModal;
use app\admin\uzivatele\prehled\SeznamUzivateluPanel;
use app\system\lay\panels\PanelsLayout;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 18.04.2022 */
class UzivatelePage extends PanelsLayout
{

   function getPageName() :string {
      return 'Seznam všech uživatelů aplikace';
   }

   function preparePanely() :void {
      $this->subscribePanel(SeznamUzivateluPanel::init()
         ->addModal(NovyUzivatelModal::init())
         ->addModal(OdkazUzivateleModal::init())
         ->addModal(EditUzivateleModal::init()));
   }
}