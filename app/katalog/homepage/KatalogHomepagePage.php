<?php namespace app\katalog\homepage;

use app\front\organizace\model\organizace\Organizace;
use app\katalog\clanky\KatalogClanekController;
use app\katalog\clanky\KatalogSeznamClankuController;
use app\katalog\filtrace\components\Search;
use app\katalog\filtrace\FiltraceProstoryController;
use app\katalog\filtrace\FiltraceSluzbyController;
use app\katalog\kodex\KatalogKodexController;
use app\katalog\kontakt\KatalogKontaktController;
use app\katalog\layout\BaseKatalogContentLayout;
use app\katalog\organizace\KatalogRegistraceOrganizaceController;
use app\katalog\organizace\nahled\OrganizaceNahledComponent;
use app\katalog\uzivatel\KatalogUzivatelController;
use app\system\component\Templater;
use app\system\Environment;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 14.10.2024 */
class KatalogHomepagePage extends BaseKatalogContentLayout
{

   function getPageName() :string {
      return 'Booking portal pro eventy';
   }

   protected function prepareTemplate(Templater $templater) {
      $componentsProstoryHtmls = [];
      $componentsSluzbyHtmls = [];

      if(Environment::isProduction())
         foreach([72, 23, 83] as $id) {
            $componentsProstoryHtmls[] = OrganizaceNahledComponent::getComponent()
               ->setOrganizace(Organizace::getMisto($id))
               ->renderHtml(cache: false);
         }

      if(Environment::isProduction())
         foreach([22,25, 92] as $id) {
            $componentsSluzbyHtmls[] = OrganizaceNahledComponent::getComponent()
               ->setOrganizace(Organizace::getMisto($id))
               ->renderHtml(cache: false);
         }

      $templater->addData([
         'katalogLokace' => FiltraceProstoryController::getUrl('cz'),
         'katalogSluzby' => FiltraceSluzbyController::getUrl('cz'),

         'kontakt' => KatalogKontaktController::getUrl('cz'),
         'onas' => KatalogKodexController::getUrl(),
         'clanek' => KatalogClanekController::getUrl('cs', 'testovaci-clanek', 123),
         'seznamClanku' => KatalogSeznamClankuController::getUrl('cs'),

         'nastaveniUzivatele' => KatalogUzivatelController::getUrl(),
         'mojeAkce' => KatalogUzivatelController::getUrl(KatalogUzivatelController::ACTION_EVENTS),
         'mojeOrganizace' => KatalogUzivatelController::getUrl(KatalogUzivatelController::ACTION_ORGANIZATIONS),

         'registraceOrganizace' => KatalogRegistraceOrganizaceController::getUrl(),

         'searchBar' => Search::getComponent()->renderHtml(),
         'topProstory' => $componentsProstoryHtmls,
         'topDodavatele' =>$componentsSluzbyHtmls,
      ]);
   }
}