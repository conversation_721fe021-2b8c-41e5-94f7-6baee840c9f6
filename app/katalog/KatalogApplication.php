<?php namespace app\katalog;

use app\client\controllers\ClientActionController;
use app\client\controllers\HandleController;
use app\client\controllers\organization\OrganizationDetailController;
use app\client\controllers\verification\VerificationCodeController;
use app\client\controllers\verification\VerificationController;
use app\front\controllers\chat\ChatActionController;
use app\front\controllers\DynamicController;
use app\front\controllers\EnvironmentController;
use app\front\controllers\stazene\StazeneSouboryController;
use app\front\controllers\TinyEditorController;
use app\katalog\about\KatalogAboutController;
use app\katalog\clanky\KatalogClanekController;
use app\katalog\clanky\KatalogSeznamClankuController;
use app\katalog\cookies\KatalogCookiesController;
use app\katalog\faq\KatalogFaqController;
use app\katalog\filtrace\FiltraceActionController;
use app\katalog\filtrace\FiltraceProstoryController;
use app\katalog\filtrace\FiltraceSluzbyController;
use app\katalog\homepage\HomepageController;
use app\katalog\katalog\KatalogController;
use app\katalog\kategorie\KatalogCityController;
use app\katalog\kodex\KatalogKodexController;
use app\katalog\kontakt\KatalogKontaktController;
use app\katalog\oblibene\KatalogOblibeneActionController;
use app\katalog\organizace\DetailOrganizaceController;
use app\katalog\organizace\DetailOrganizaceEntitaController;
use app\katalog\organizace\KatalogRegistraceOrganizaceController;
use app\katalog\organizace\OrganizaceEntitaRecenzeController;
use app\katalog\organizace\OrganizationPoptavkaController;
use app\katalog\uzivatel\ApiOAuthController;
use app\katalog\uzivatel\KatalogLoginController;
use app\katalog\uzivatel\KatalogUzivatelController;
use app\katalog\uzivatel\ZakaznikDokonceniRegistraceController;
use app\katalog\uzivatel\ZakaznikLogoutController;
use app\katalog\uzivatel\ZakaznikOauthPrihlaseniController;
use app\System;
use app\system\Application;
use app\system\application\ClientApplicationEnvironment;
use app\system\application\IEnvironment;
use app\system\controller\InternApiHandshakeController;
use app\system\Environment;
use app\system\layout\client\modals\souhlasy\cookie\CookieNastaveniModal;
use app\system\layout\client\modals\souhlasy\zakaznik\SouhlasyZakaznikModal;
use app\system\router\RoutesContainer;
use app\system\users\zakaznici\event\HashLoginEvent;

/** Created by Kryštof Czyź. Date: 30.09.2024 */
class KatalogApplication extends Application
{

   protected function onPrepare() :void {
      if(!($this->environment instanceof ClientApplicationEnvironment))
         throw new \Exception('Environment není instance ClientApplicationEnvironment');

      $this->setBeforeStart(function() {
         if(Environment::isHttpAuthorization())
            $this->checkAuthentication('qvamp', 'qvamp');
      });

      $this->setAfterStart(function() {
         SouhlasyZakaznikModal::init()->checkPost();
         CookieNastaveniModal::init()->checkPost();

         HashLoginEvent::listenGet($this->realUrl, $this->environment->zakaznik);
      });
   }

   public function prepareRoutes(RoutesContainer $container) :void {
      $container->addControllers(
         HomepageController::class,
         KatalogCityController::class,
         KatalogController::class,
         KatalogRegistraceOrganizaceController::class,
         KatalogAboutController::class,
         KatalogCookiesController::class,
         KatalogKodexController::class,
         KatalogFaqController::class,
         KatalogKontaktController::class,
         KatalogClanekController::class,
         KatalogSeznamClankuController::class,
         KatalogOblibeneActionController::class,
         FiltraceActionController::class,
         FiltraceSluzbyController::class,
         FiltraceProstoryController::class,
      );

      $container->addControllers(
         DetailOrganizaceController::class,
         DetailOrganizaceEntitaController::class,
      );

      $container->addControllers(
         InternApiHandshakeController::class,
         VerificationController::class,
         VerificationCodeController::class,
         ChatActionController::class,
//         @TODO HandleController dočasný pro testování přihlašování
         HandleController::class,
         EnvironmentController::class,
         OrganizationPoptavkaController::class,
         OrganizationDetailController::class,
         DynamicController::class,
//         Musí být nakonec
         ZakaznikLogoutController::class,
         ZakaznikOauthPrihlaseniController::class,
         ApiOAuthController::class,
      );

      $container->addControllers(
         OrganizaceEntitaRecenzeController::class,
      );

      if(ClientApplicationEnvironment::get()->zakaznik !== null){
         $container->addControllers(
            StazeneSouboryController::class,
            ClientActionController::class,
            TinyEditorController::class,
            KatalogUzivatelController::class,
         );

         if(!ClientApplicationEnvironment::get()->zakaznik->isRegistered)
            $container->addControllers(
               ZakaznikDokonceniRegistraceController::class,
            );
      }

      $container->addControllers(
         KatalogLoginController::class,
      );
   }

   public function prepareSystem(System $system) :void {
      $this->realUrl = $system->realPath;
   }

   protected string $realUrl;

   protected function prepareApplicationEnvironment(System $system) :IEnvironment {
      return ClientApplicationEnvironment::init($system);
   }
}