{varType app\system\model\mesta\SystemMestoNazevRow[] $mesta}
{varType app\system\model\sluzby\SystemSluzbyNazevRow[] $sluzby}

<section class="container pb-5 position-relative">
   <div class="row align-items-center g-4">
      <div class="col-12 col-lg-6">
      <span n:if="false" class="badge rounded-pill green-bg-card text-success px-3 py-2 mb-3 d-inline-flex gap-2 align-items-center shadow-sm">
        <i class="bi bi-shield-check"></i> {_'Za poslední týden'} 126 {_'poptávek'} {*@TODO dodělat počet poptávek za posledních 7 dní*}
      </span>

         <h1 class="h-portal-home fw-bolder text-secondary">{_'Najděte prostor, dodavatele a spravujte své eventy online'}</h1>
         <p class="text-muted mb-3">{_'1. <PERSON>tejte → 2. <PERSON>rovnej<PERSON> nabídky → 3. <PERSON><PERSON><PERSON><PERSON> → 4. Připravujte event'}</p>
      </div>

      <div class="col-12 col-lg-6">
         <div class="hero-img-wrapper position-relative d-none d-lg-block">
            <img id="heroImage"
                 class="radius-card shadow-sm object-fit-cover w-100"
                 src="/files/layout/img/not_found_product_photo.png"
                 alt=""
                  style="height: 430px;">
            <p id="heroCaption" class="hero-caption badge bg-white"></p>
         </div>
      </div>
   </div>

   <div class="search-panel card border shaddow-hover border border-primary bg-white radius-card ">
      <div class="row g-0 align-items-stretch">
         <!-- Event type -->
         <div class="col-12 col-md border-end">
            <div class="p-3 d-flex align-items-center gap-3 flex-grow-1 position-relative">
               <i class="bi bi-search fs-5 text-muted"></i>
               <div class="w-100">
                  <div class="text-uppercase text-muted-emphasis fw-semibold small mb-1">{_'Hledám'}</div>
                  <input
                          type="text"
                          id="whatInput"
                          class="form-control form-control-lg border-0 p-0 shadow-none bg-transparent"
                          placeholder={_'Vyberte'}
                          aria-label= {_'Co hledám'}
                          autocomplete="off"
                  >
               </div>
               <div id="suggestionDropdownWhat"
                    class="position-absolute card suggestion-card p-3 d-none border border-primary"
                    style="margin-top:60px; top:100%; left:0; width:100%; z-index:9999 !important;">

                  <h6 class="mb-2">{_'Co hledám'}</h6>
                  <ul class="list-unstyled mb-0 suggestion-list">
                     <li>
                        <a href="#" class="text-decoration-none suggestion-item" data-target="whatInput" data-id="10000">
                           <i class="bi bi-building me-1"></i>{_'Prostor'}
                        </a>
                     </li>
                     <li>
                        <a href="#" class="text-decoration-none suggestion-item" data-target="whatInput" data-id="10001">
                           <i class="bi bi-building me-1"></i>{_'Dodavatele'}
                        </a>
                     </li>

                     {foreach $sluzby as $sluzba}
                        <li>
                           <a href="#" class="text-decoration-none suggestion-item" data-target="whatInput" data-id="{$sluzba->id_sluzba}">
                              <i class="{$sluzba->icon} me-1"></i>{$sluzba->nazev}
                           </a>
                        </li>
                     {/foreach}
                  </ul>
               </div>
            </div>
         </div>

         <!-- Location -->
         <div class="col-12 col-md">
            <div class="flex-grow-1 position-relative p-3 d-flex align-items-center gap-3">
               <i class="bi bi-geo-alt fs-5 text-muted"></i>
               <div class="w-100">
                  <div class="text-uppercase text-muted-emphasis fw-semibold small mb-1">{_'Kde'}</div>
                  <input
                          type="text"
                          id="whereInput"
                          class="form-control form-control-lg border-0 p-0 shadow-none bg-transparent"
                          placeholder=  {_'Vyberte nebo napište'}
                          aria-label= {_'Kde'}
                          autocomplete="off"
                  >
               </div>
               <div
                       id="suggestionDropdownWhere"
                       class="position-absolute card suggestion-card radius-card p-3 d-none border border-primary "
                       style="margin-top: 60px; top: 100%; left: 0; width: 100%; z-index: 9999 !important;"
               >
                  <h6 class="mb-2">{_'Kde'}</h6>
                  <ul class="list-unstyled mb-0 suggestion-list">
                     {foreach $mesta as $mesto}
                        <li><a href="#" class="text-decoration-none suggestion-item" data-target="whereInput" data-id="{$mesto->id_mesto}">{$mesto->nazev}</a></li>
                     {/foreach}
                  </ul>
               </div>
            </div>
         </div>

         <!-- Search button -->
         <div class="col-12 col-md">
            <div class="p-3 d-flex align-items-center justify-content-center h-100">
               <button type="button" id="searchButton" class="btn btn-lg btn-primary w-100 btn-search btnHledat shaddow-hover">{_'Najít'}</button>
            </div>
         </div>
      </div>
   </div>
</section>

<style>
    .input-group {
        position: relative;
        z-index: 1;
    }

    .suggestion-card {
        position: absolute;    /* nebo sticky podle potřeby */
        z-index: 1200;         /* vyšší než ostatní prvky uvnitř panelu */
    }

    /* výška jedné položky (můžeš upravit) */
    :root { --suggestion-item-h: 44px; }

    .suggestion-list {
        /* max. 3 položky + drobné mezery */
        max-height: calc(var(--suggestion-item-h) * 3 + 8px);
        overflow-y: auto;
        overscroll-behavior: contain; /* nepropaguje skrolování do body */
        scrollbar-gutter: stable;     /* stabilní šířka scrollbaru */
    }

    /* aby měly položky konzistentní výšku a dobře se klikaly */
    .suggestion-list .suggestion-item {
        display: block;
        padding: .5rem .75rem;
        line-height: calc(var(--suggestion-item-h) - 16px); /* 16px = vert. padding */
        border-radius: .5rem;
    }

    /* hover/active pro lepší UX */
    .suggestion-list .suggestion-item:hover,
    .suggestion-list .suggestion-item:focus {
        background: rgba(0,0,0,.04);
        text-decoration: none;
    }

    /* volitelné: sticky nadpis v dropdownu */
    #suggestionDropdown > h6 {
        position: sticky;
        top: 0;
        background: inherit;
        padding-bottom: .25rem;
        margin-bottom: .25rem;
        z-index: 1;
    }

    /* volitelné: decentní scrollbar (webkit) */
    .suggestion-list::-webkit-scrollbar { width: 8px; }
    .suggestion-list::-webkit-scrollbar-thumb { background: rgba(0,0,0,.15); border-radius: 8px; }
    .suggestion-list::-webkit-scrollbar-track { background: transparent; }



    /* Layout helpers */
    /* Layout helpers */
    .hero-img-wrapper { position: relative; }
    .object-cover { object-fit: cover; width: 100%; height: 100%; max-height: 520px; }
    .search-panel {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 0px;
        width: min(980px, 95%);
        overflow: visible;
    }

    @media (min-width: 991.98px) and (max-width: 1199px) {
        .search-panel{
            bottom: -60px;
        }
    }

    @media (min-width: 1200px) {
        .search-panel{
            bottom: 0px;
        }
    }

    /* Panel look */
    .search-panel .form-control::placeholder { color: var(--bs-secondary-color); }
    .search-panel .form-control:focus { box-shadow: none; }
    .search-panel .border-end { border-color: var(--bs-border-color)!important; }

    /* Button style (Tagvenue-like coral) */
    .btn-search {
        font-weight: 600;
        border-radius: .75rem;
        border: none;
        padding-block: .9rem;
    }
    .btn-search:hover { filter: brightness(.95); }

    /* Responsive adjustments */
    @media (max-width: 991.98px){
        .search-panel { position: static; transform:none; width:100%; margin-top: 1rem; }
        .object-cover { max-height: 360px; }
    }

    /* Panel look */
    .search-panel .form-control::placeholder { color: var(--bs-secondary-color); }
    .search-panel .form-control:focus { box-shadow: none; }
    .search-panel .border-end { border-color: var(--bs-border-color)!important; }

    /* Button style (Tagvenue-like coral) */
    .btn-search {
        font-weight: 600;
        border-radius: .75rem;
        border: none;
        padding-block: .9rem;
    }
    .btn-search:hover { filter: brightness(.95); }

    /* Responsive adjustments */
    @media (max-width: 991.98px){
        .search-panel { position: static; transform:none; width:100%; margin-top: 1rem; }
        .object-cover { max-height: 360px; }
    }

    .hero-img-wrapper {
        position: relative; // pro absolutní pozicování captionu
    }
    .hero-img-wrapper .hero-caption {
        position: absolute;
        top: 1rem;
        right: 1rem;
        margin: 0;
        padding: .25rem .75rem;
        font-size: .9rem;
        border-radius: .5rem;
        max-width: 70%;          // ať text nezabere celou šířku
    }
</style>

<script>
   const whatInput = document.getElementById('whatInput');
   const whereInput = document.getElementById('whereInput');
   const suggestionDropdownWhat = document.getElementById('suggestionDropdownWhat');
   const suggestionDropdownWhere = document.getElementById('suggestionDropdownWhere');

   let selectedWhatId = null;
   let selectedWhereId = null;

   document.querySelector('#searchButton').addEventListener('click', function() {
      // if (!selectedWhatId || !selectedWhereId) {
      //    // alert('Prosím vyberte hodnoty ze seznamu návrhů.');
      //    return;
      // }

      window.searchbar.search(selectedWhereId,selectedWhatId);
   });

   function showDropdown(dropdown, inputElement) {
      dropdown.classList.remove('d-none');
      dropdown.style.zIndex = "9999";
      dropdown.style.top = (inputElement.offsetHeight + 4) + "px";
      dropdown.style.left = "0";
   }

   function hideDropdown(dropdown) {
      dropdown.classList.add('d-none');
   }

   whatInput.addEventListener('input', () => {
      selectedWhatId = null;
   });

   whereInput.addEventListener('input', () => {
      selectedWhereId = null;
   });

   whatInput.addEventListener('focus', () => {
      showDropdown(suggestionDropdownWhat, whatInput);
   });

   whereInput.addEventListener('focus', () => {
      showDropdown(suggestionDropdownWhere, whereInput);
   });

   whatInput.addEventListener('blur', () => {
      setTimeout(() => hideDropdown(suggestionDropdownWhat), 200);
   });

   whereInput.addEventListener('blur', () => {
      setTimeout(() => hideDropdown(suggestionDropdownWhere), 200);
   });

   document.querySelectorAll('.suggestion-item').forEach(function(item) {
      item.addEventListener('click', function(e) {
         e.preventDefault();
         const targetId = this.getAttribute('data-target');
         const targetInput = document.getElementById(targetId);
         const selectedId = this.getAttribute('data-id');

         targetInput.value = this.textContent.trim();

         if (targetId === 'whatInput') {
            selectedWhatId = selectedId;
            hideDropdown(suggestionDropdownWhat);
            setTimeout(() => {
               whereInput.focus();
               showDropdown(suggestionDropdownWhere, whereInput);
            }, 100);
         } else if (targetId === 'whereInput') {
            selectedWhereId = selectedId;
            hideDropdown(suggestionDropdownWhere);
         }
      });
   });

   //hlavní obrázky -> mění se při každém refreshi
   document.addEventListener("DOMContentLoaded", function() {
       const images = [
           {
               src: "/files/layout/img/main/1.webp",
               alt: "Choryňský mlýn (Choryně)",
               caption: "Choryňský mlýn (Choryně)"
           },
           {
               src: "/files/layout/img/main/2.webp",
               alt: "Město Moře (Praha)",
               caption: "Město Moře (Praha)"
           },
           {
               src: "/files/layout/img/main/3.webp",
               alt: "Radlická sportovní (Praha)",
               caption: "Radlická sportovní (Praha)"
           }
       ];

       const idx = Math.floor(Math.random() * images.length);
       const chosen = images[idx];

       const heroImg = document.getElementById("heroImage");
       const heroCap = document.getElementById("heroCaption");

       if (heroImg) {
           heroImg.src = chosen.src;
           heroImg.alt = chosen.alt;
       }
       if (heroCap) {
           heroCap.textContent = chosen.caption;
       }
   });
</script>