<?php namespace app\katalog\cookies;

use app\system\controller\Controller;
use app\system\router\Route;

/** Created by Petr Knap. Date: 27.08.2025 */
#[Route([
   'cs' => '^/(?<region>cz|pl|hu)/cookies/?$',
   'en' => '^/(?<region>cz|pl|hu)/cookies/?$',
   'pl' => '^/(?<region>cz|pl|hu)/cookies/?$',
   'hu' => '^/(?<region>cz|pl|hu)/cookies/?$',
   'de' => '^/(?<region>cz|pl|hu)/cookies/?$',
])]
class KatalogCookiesController
{

   use Controller;

   public function call() :void {
      KatalogCookiesPage::echoThis();
   }
}