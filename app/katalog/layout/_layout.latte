{varType ?string $title}
{varType bool $isProduction}
{varType ?app\system\layout\client\head\meta\BaseMetaTagy $metaTagy}
{varType bool $isAnalyticsConsent}

<!DOCTYPE html>
<html class="no-js" dir="ltr" xml:lang="cs" lang="cs">
<head>

   <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta http-equiv="X-UA-Compatible" content="IE=edge">

{*   <link rel="canonical" href="https://qvamp.com"/>*}
{*   <link rel="alternate" href="https://qvamp.com/cs" hreflang="cs"/>*}
{*   <link rel="alternate" href="https://qvamp.com/pl" hreflang="pl"/>*}
{*   <link rel="alternate" href="https://qvamp.com/hu" hreflang="hu"/>*}
{*   <link rel="alternate" href="https://qvamp.com" hreflang="en"/>*}
{*   <link rel="alternate" href="https://qvamp.com" hreflang="x-default"/>*}

   <link rel="apple-touch-icon" sizes="180x180" href="/files/system/img/favicon/apple-touch-icon.png">
   <link rel="icon" type="image/png" sizes="32x32" href="/files/system/img/favicon/favicon-32x32.png">
   <link rel="icon" type="image/png" sizes="16x16" href="/files/system/img/favicon/favicon-16x16.png">
   <link rel="manifest" href="/files/system/img/favicon/site.webmanifest?2">
   <link rel="mask-icon" href="/files/system/img/favicon/safari-pinned-tab.svg" color="#a30657">
   <meta name="msapplication-TileColor" content="#ffffff">
   <meta name="theme-color" content="#ffffff">

   <!-- Primary Meta Tags -->
   <title>{if $title}{$title} | {/if}{_'Qvamp Booking Portal'}</title>
   <meta name="title" content="{if $title}{$title} | {/if}{_'Qvamp Booking Portal'}"/>

   <meta name="description"
         content="Qvamp Booking Portal – eventy bez chaosu. Rezervujte prostory a služby, komunikujte online a mějte vše přehledně na jednom místě."/>
   <meta name="keywords"
         content="prostory pro akce, dodavatelé pro akce, pronájem prostor, catering, dekorace, eventy, Qvamp, akce, event služby, hledání prostor, najít dodavatele, najít DJ, najít fotografa, booking portal"/>
   <!-- Open Graph / Facebook -->
{*   <meta property="og:locale" content="cs">*}
{*   <meta property="og:site_name" content="QVAMP">*}
{*   <meta property="og:type" content="website"/>*}
{*   <meta property="og:url" content="https://qvamp.com"/>*}
{*   <meta property="og:title" content="Najděte prostory a dodavatele pro své akce | Qvamp"/>*}
{*   <meta property="og:description"*}
{*         content="Najděte ideální prostory a dodavatele pro své akce s Qvamp. Bez provizí a poplatků. Snadné a rychlé hledání prostor, cateringu, dekorací a dalších služeb."/>*}
{*   <meta property="og:image" content="/images/banners/cz-qvamp-prev.webp"/>*}

{*   <!-- Twitter -->*}
{*   <meta name="twitter:site" content="@QVAMP">*}
{*   <meta property="twitter:card" content="summary_large_image"/>*}
{*   <meta property="twitter:url" content="https://qvamp.com"/>*}
{*   <meta property="twitter:title" content="Najděte prostory a dodavatele pro své akce | Qvamp"/>*}
{*   <meta property="twitter:description"*}
{*         content="Najděte ideální prostory a dodavatele pro své akce s Qvamp. Bez provizí a poplatků. Snadné a rychlé hledání prostor, cateringu, dekorací a dalších služeb."/>*}
{*   <meta property="twitter:image" content="/images/banners/cz-qvamp-prev.webp"/>*}

{*   <meta name="apple-mobile-web-app-title" content="QVAMP">*}
{*   <meta name="application-name" content="QVAMP">*}

{*   <meta name="author" content="Qvamp">*}
{*   <meta name="robots" content="index, follow">*}
{*   <meta name="mobile-web-app-capable" content="yes">*}
{*   <meta name="apple-mobile-web-app-title" content="QVAMP">*}
{*   <meta name="application-name" content="QVAMP">*}

   {if $isProduction}
      <script>
         var _paq = window._paq = window._paq || [];
         _paq.push(['requireConsent']);        // bez souhlasu se neměří
         _paq.push(['requireCookieConsent']);  // a nepoužijí se cookies
         _paq.push(['disableCookies']);        // pojistka
         (function() {
            var u="//matomo.qvamp.eu/";
            _paq.push(['setTrackerUrl', u+'matomo.php']);
            _paq.push(['setSiteId', '1']);
            var d=document,g=d.createElement('script'),s=d.getElementsByTagName('script')[0];
            g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
         })();
      </script>

      <noscript><p><img referrerpolicy="no-referrer-when-downgrade" src="//matomo.qvamp.eu/matomo.php?idsite=1&amp;rec=1" style="border:0;" alt="" /></p></noscript>

      <script n:if="$isAnalyticsConsent" type="text/javascript">
         (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){ (c[a].q=c[a].q||[]).push(arguments) };
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
         })(window, document, "clarity", "script", "p4pwbjamzn");
      </script>

      <script n:if="$isAnalyticsConsent">
         _paq.push(['rememberConsentGiven']);
         _paq.push(['trackPageView']);
         _paq.push(['enableLinkTracking']);
      </script>
   {/if}

   {if $metaTagy}
      {$metaTagy->renderTags() ?: ''}
{*   {else}*}
{*      <meta name="robots" content="noindex, nofollow">*}
   {/if}

   {cssFile 'layout/css/qvamp.css'}
   {cssFile 'layout/css/main.css'}
   {cssFile 'layout/css/nove.css'}
</head>

{varType Latte\Runtime\Html $headerComponent}
{varType Latte\Runtime\Html $footerComponent}
{varType Latte\Runtime\Html $content}
{varType Latte\Runtime\Html[] $modals}
{varType app\system\flash\FlashItem[] $messages}

{varType Latte\Runtime\Html $validatorComponent}
{varType Latte\Runtime\Html $souhlasModal}
{varType Latte\Runtime\Html $cookieModal}

{varType app\system\model\translator\Jazyky $jazyk}

<body class="bg-white">
   <style>
      .grecaptcha-badge { visibility: hidden; }
   </style>
   {jsFile 'layout/js/app.js'}
   {jsFile 'layout/js/tiny/tinymce.min.js'}
   {if $jazyk->getTinyTranslateFile()}{jsFile ltrim($jazyk->getTinyTranslateFile(), '/')}{/if}
   {jsFile 'layout/js/qvamp.min.js'}
   {jsFile 'layout/js/katalog/index.min.js'}
   {jsFile 'layout/js/katalog/filtrace/index.min.js'}

{block navigation}{$headerComponent}{/block}

{block content}
   <div class="container mt-2 mt-md-5 min-h-100">
      {$content}
   </div>
{/block}

{block}
   {$footerComponent}
{/block}


{foreach $modals as $modal}
      {$modal}
{/foreach}

<script>
   {if !empty($messages)}
   $(function() {
      {foreach $messages as $message}
      {$message->createFunction()|noescape}
      {/foreach}
   });
   {/if}
</script>

{$souhlasModal ?? ''}
{$cookieModal ?? ''}

{$validatorComponent}

</body>