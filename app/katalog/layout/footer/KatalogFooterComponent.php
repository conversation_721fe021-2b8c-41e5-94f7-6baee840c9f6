<?php namespace app\katalog\layout\footer;

use app\katalog\about\KatalogAboutController;
use app\katalog\clanky\KatalogSeznamClankuController;
use app\katalog\cookies\KatalogCookiesController;
use app\katalog\faq\KatalogFaqController;
use app\katalog\homepage\HomepageController;
use app\katalog\kodex\KatalogKodexController;
use app\katalog\kontakt\KatalogKontaktController;
use app\katalog\organizace\KatalogRegistraceOrganizaceController;
use app\katalog\uzivatel\KatalogUzivatelController;
use app\system\application\ClientApplicationEnvironment;
use app\system\component\Component;

/** Created by <PERSON><PERSON> K<PERSON>. Date: 18.08.2025 */
class KatalogFooterComponent extends Component
{

   function setData() :array {
      return [
//         @TODO add to environment region
         'homepageUrl' => HomepageController::getUrl('cz'),
         'kontakt' => KatalogKontaktController::getUrl('cz'),
         'faq' => KatalogFaqController::getUrl('cz'),
         'kodex' => KatalogKodexController::getUrl('cz'),
         'onas' => KatalogAboutController::getUrl(),
         'seznamClanku' => KatalogSeznamClankuController::getUrl('cs'),
         'cookies' => KatalogCookiesController::getUrl('cz'),
         'nastaveniUzivatele' => KatalogUzivatelController::getUrl(),
         'mojeAkce' => KatalogUzivatelController::getUrl(KatalogUzivatelController::ACTION_EVENTS),
         'mojeOrganizace' => KatalogUzivatelController::getUrl(KatalogUzivatelController::ACTION_ORGANIZATIONS),
         'isLogged' => ClientApplicationEnvironment::get()->isLogged(),
         'registraceOrganizace' => KatalogRegistraceOrganizaceController::getUrl(),
      ];
   }

   public function setEnvironment(ClientApplicationEnvironment $environment) :KatalogFooterComponent {
      $this->environment = $environment;
      return $this;
   }

   private ClientApplicationEnvironment $environment;
}