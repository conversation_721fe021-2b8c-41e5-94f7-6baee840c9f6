<?php namespace app\katalog\layout;

use app\front\organizace\model\organizace\OrganizaceRow;
use app\katalog\layout\header\KatalogHeaderComponent;
use app\katalog\layout\footer\KatalogFooterComponent;
use app\katalog\uzivatel\sprava\modal\LoginZakaznikKatalogModal;
use app\katalog\uzivatel\sprava\registrace\dokonceni\modal\KatalogDokonceniRegistraceModal;
use app\System;
use app\system\application\ClientApplicationEnvironment;
use app\system\component\Component;
use app\system\component\Templater;
use app\system\cookies\CookieSouhlasy;
use app\system\cookies\CookieTypy;
use app\system\Environment;
use app\system\flash\FlashMessages;
use app\system\layout\client\head\meta\BaseMetaTagy;
use app\system\layout\client\modals\souhlasy\cookie\CookieNastaveniModal;
use app\system\layout\client\modals\souhlasy\zakaznik\SouhlasyZakaznikModal;
use app\system\layout\validator\ValidatorDefaultsComponent;
use app\system\model\translator\TextVariablesFactory;
use app\system\modul\modal\Modal;
use app\system\modul\modal\ModalContainer;
use app\system\traits\PostListener;
use Exception;
use Latte\Runtime\Html;
use ReflectionClass;

/** Created by Kryštof Czyź. Date: 14.10.2024 */
abstract class BaseKatalogContentLayout extends Component
{

   use PostListener;

   function getPageName() :?string { return 'Najděte prostory a dodavatele pro své akce'; }

   protected function prepareTemplate(Templater $templater) { }
   protected function prepareFromString() :?string { return null; }

   private function pageName() :string {
      return $this->getPageName()
         ? System::getTranslator()->layoutTranslate($this->getPageName())
         : '';
   }

   public function setTitle(string|TextVariablesFactory $title) :static {
      if($title instanceof TextVariablesFactory){
         $this->title = $title;
         return $this;
      }

      $this->title = $title
         ? System::getTranslator()->layoutTranslate($title)
         : '';

      return $this;
   }

   final protected function addMetaTags(BaseMetaTagy $metaTagy) :void {
      $this->metaTags = $metaTagy;
   }

   public function getOrganization() :?OrganizaceRow { return null; }

   public static function echoThis() :void {
      static::getComponent()->echo();
   }

   public function setData() :array {
      $this->prepare();
      return $this->data;
   }

   protected function getTemplatePath(string $templateName) :string {
      return __DIR__ . '/_layout.latte';
   }

   protected function getHeaderComponent() :?Component {
      return KatalogHeaderComponent::getComponent()
         ->setEnvironment(ClientApplicationEnvironment::get());
   }

   protected function getFooterComponent() :?Component {
      return KatalogFooterComponent::getComponent()
         ->setEnvironment(ClientApplicationEnvironment::get());
   }

   private function prepare() :void {
      $env = ClientApplicationEnvironment::get();
      $this->prepareKatalogModals();

      $this->addData([
         'headerComponent' => $this->getHeaderComponent()?->renderHtml(),
         'footerComponent' => $this->getFooterComponent()?->renderHtml(),
         'modals' => $this->prepareModals(),
         'messages' => FlashMessages::getMessageAndClean(),
         'jazyk' => System::get()->getApplication()->environment->getJazyk(),
      ]);

      $this->data['validatorComponent'] = new Html(ValidatorDefaultsComponent::getComponent());

      if(
         $env->isLogged() && $this->getOrganization()
         && $env->zakaznik->getOrganizaceZakaznik($this->getOrganization()->id_organizace)?->hasFilledSouhlasy() === false
      ){
         $this->data['souhlasModal'] = new Html(
            SouhlasyZakaznikModal::init()
               ->setOrganization($this->getOrganization())
               ->render()
         );
      }elseif(!CookieSouhlasy::hasFilledSettings()){
         $this->data['cookieModal'] = new Html(CookieNastaveniModal::init()->render());
      }

      $this->setContentData();

      $this->addData([
         'title' => $this->title ?? $this->pageName(),
         'metaTagy' => $this->metaTags ?? null,
         'isProduction' => Environment::isProduction(),
         'isAnalyticsConsent' => CookieSouhlasy::hasTypyAccepted(CookieTypy::STATISTICKE)
      ]);
   }

   /** @return array<int, Html> */
   private function prepareModals() :array {
      $modals = [];

      if(!empty($this->modals)){
         foreach($this->modals as $modal)
            $modals[] = new Html($modal->render());
      }

      return $modals;
   }

   private function setContentData() :void {
      if($this->prepareFromString()){
         $this->data['content'] = new Html($this->prepareFromString());
         return;
      }

      if($template = $this->getTemplateFile(static::class)){
         $templater = Templater::prepare($template);
         $this->prepareTemplate($templater);
         $this->data['content'] = new Html($templater->render());
      }
   }

   private function getTemplateFile(string $className) :string {
      $refl = new ReflectionClass($className);

      if(is_file($file = dirname($refl->getFileName()) . '/' . $refl->getShortName() . '.latte'))
         $templateFile = $file;
      else
         throw new Exception('Template layoutu nebyl nalezen');

      return $templateFile;
   }

   final public function addModal(Modal $modal) :static {
      $mc = new ModalContainer($modal);

      //@TODO Ošetření existujících
      $this->modals += [$mc->getIdModal() => $mc];

      return $this;
   }

   private function prepareKatalogModals() {
      $env = ClientApplicationEnvironment::get();

      if(!$env->isLogged()){
         $this->addModal(LoginZakaznikKatalogModal::init());
      } elseif($env->zakaznik->isRegistered === false) {
         $this->addModal(KatalogDokonceniRegistraceModal::init());
      }
   }

   /** @var ModalContainer[]  */
   protected array $modals = array();
   protected string|TextVariablesFactory $title;
   protected BaseMetaTagy $metaTags;
}