<?php namespace app\katalog\layout\header;

use app\katalog\filtrace\FiltraceProstoryController;
use app\katalog\homepage\HomepageController;
use app\katalog\organizace\KatalogRegistraceOrganizaceController;
use app\katalog\uzivatel\KatalogUzivatelController;
use app\katalog\uzivatel\sprava\modal\LoginZakaznikKatalogModal;
use app\katalog\uzivatel\ZakaznikDokonceniRegistraceController;
use app\katalog\uzivatel\ZakaznikLogoutController;
use app\system\application\ClientApplicationEnvironment;
use app\system\component\Component;
use app\system\model\translator\Jazyky;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 14.10.2024 */
class KatalogHeaderComponent extends Component
{

   function setData() :array {
      return [
         'isLogged' => $this->environment->isLogged(),
         'loggedZakaznik' => $this->environment->zakaznik,
         'selected_lang' => $this->environment->getJazyk(),
         'jazyky' => Jazyky::getAllowed(),
         'modalAttrs' => LoginZakaznikKatalogModal::init()->btnToggleAttributes(),
//         @TODO add to environment region
         'homepageUrl' => HomepageController::getUrl('cz'),
         'logoutUrl' => ZakaznikLogoutController::getUrl(),
         'nastaveniUzivateleUrl' => KatalogUzivatelController::getUrl(),
         'mojeAkceUrl' => KatalogUzivatelController::getUrl(KatalogUzivatelController::ACTION_EVENTS),
         'mojeOrganizaceUrl' => KatalogUzivatelController::getUrl(KatalogUzivatelController::ACTION_ORGANIZATIONS),
         'registraceOrganizaceUrl' => KatalogRegistraceOrganizaceController::getUrl(),
         'dokonceniRegistraceUrl' => ZakaznikDokonceniRegistraceController::getUrl(),
         'filtraceProstorUrl' => FiltraceProstoryController::getUrl('cz')
      ];
   }

   public function setEnvironment(ClientApplicationEnvironment $environment) :KatalogHeaderComponent {
      $this->environment = $environment;
      return $this;
   }

   private ClientApplicationEnvironment $environment;
}