{varType app\system\model\translator\Jazyky[] $jazyky}
{varType app\system\model\translator\Jazyky $selected_lang}
{varType app\system\users\zakaznici\LoggedZakaznik $loggedZakaznik}
{varType array $modalAttrs}
{varType string $logoutUrl}
{varType string $homepageUrl}
{varType bool $isLogged}

{varType string $mojeAkceUrl}
{varType string $mojeOrganizaceUrl}
{varType string $registraceOrganizaceUrl}
{varType string $nastaveniUzivateleUrl}
{varType string $dokonceniRegistraceUrl}

{varType string $filtraceProstorUrl}

<nav class="navbar navbar-expand-lg bg-transparent mt-2 d-none d-md-block">
   <div class="container-fluid align-self-md-stretch mt-1">
      <div class="col-auto ps-3 pe-1 py-1">
         <a class="navbar-brand" href="{$homepageUrl}">
            <img src="/files/system/img/icons/Qvamp_logo-500-pink.webp" alt="Qvamp - Event Booking Portal">
         </a>
      </div>

      <div class="row">
         <div n:if="!$isLogged" class="col-auto px-2 py-1 mx-2">
            <a href="{$registraceOrganizaceUrl}" class="btn btn-sm btn-outline-secondary form-control shaddow-hover">{_'Registrovat organizaci'}</a>
         </div>

         <div class="col-auto px-2 py-1 mx-2">
            <div class="btn-group btn-group-sm">
{*         @TODO pouze pokud bude registrovaný do qvamp *}
               {if $isLogged}

                  {if $loggedZakaznik->isRegistered}
                     <a class="btn btn-outline-secondary position-relative" href="{$mojeOrganizaceUrl}">
                        {_'Uložené organizace'}
                     </a>
                  {else}
                     <a class="btn btn-secondary position-relative" href="{$dokonceniRegistraceUrl}">
                        {_'Dokončit registraci'}
                     </a>
                  {/if}

               {/if}
               <a n:if="$isLogged"
                       class="btn btn-outline-secondary position-relative"
                       href="{$mojeAkceUrl}"
               >
                  {_'Moje akce'}
{*                  <span class="badge text-bg-secondary ms-1">1</span>*}
               </a>
               <a n:if="!$isLogged" n:attr="$modalAttrs" class="btn btn-outline-secondary shaddow-hover">{_'Přihlásit se'}</a>
               <a n:if="!$isLogged" n:attr="$modalAttrs" class="btn btn-outline-secondary shaddow-hover">{_'Registrovat'}</a>
            </div>
         </div>

         <!-- @TODO Toogle s volbou jazyka a nastavením a notifikace-->
         <div class="col-auto d-flex">
            {if $isLogged}
{*               <div class="nav-item mx-3">*}
{*                  <a class="nav-link dropdown-toggle position-relative" href="#" id="notifyDropdown" data-bs-toggle="dropdown" data-bs-auto-close="outside">*}
{*                     <i class="align-middle bi bi-bell-fill"></i>*}
{*                  </a>*}
{*               </div>*}
               <div class="nav-item dropdown">
                  <a class="nav-link dropdown-toggle position-relative" href="#" id="userDropdown" data-bs-toggle="dropdown">
                     <div class="nav-flex border-corner">
                        {$loggedZakaznik->full_name}
                     </div>
                  </a>
                  <div class="dropdown-menu dropdown-menu-end js-dropdown-keep" aria-labelledby="userDropdown">
                     <div class="px-2">
                        <select class="js-select-language">
                           {foreach $jazyky as $jazyk}
                              <option value="{$jazyk->value}" {if $jazyk === $selected_lang}selected{/if} data-flag-icon="{$jazyk->getIcon()}">{$jazyk->getTitle()}{if $jazyk->isNotVisible()} - DEV{/if}</option>
                           {/foreach}
                        </select>
                     </div>
                     <div class="dropdown-divider"></div>
                     <a class="dropdown-item" href="{$nastaveniUzivateleUrl}">
                        <i class="align-middle me-1 bi bi-person-gear"></i> Nastavení profilu
                     </a>
                     <div class="dropdown-divider"></div>
                     <a class="dropdown-item" href="{$logoutUrl}"><i class="align-middle me-1 bi bi-box-arrow-right"></i> Odhlásit se</a>
                  </div>
               </div>
            {else}
               <div class="px-2">
                  <select class="js-select-language">
                     {foreach $jazyky as $jazyk}
                        <option value="{$jazyk->value}" {if $jazyk === $selected_lang}selected{/if} data-flag-icon="{$jazyk->getIcon()}">{$jazyk->getTitle()}{if $jazyk->isNotVisible()} - DEV{/if}</option>
                     {/foreach}
                  </select>
               </div>
            {/if}
         </div>
      </div>
   </div>
</nav>

<!-- Mobilní spodní panel -->
<nav class="mobile-bottom-nav d-block d-md-none">
   <div class="container">
      <ul class="nav nav-justified text-center">
         <li class="nav-item">
            <a class="nav-link active" href="{$homepageUrl}" aria-current="page">
               <i class="bi bi-house"></i>
               <span>{_'Domů'}</span>
            </a>
         </li>
         <li class="nav-item">
            <a class="nav-link active" href="{$filtraceProstorUrl}" aria-current="page">
               <i class="bi bi-search"></i>
               <span>{_'Hledat'}</span>
            </a>
         </li>
{*         @TODO pouze pokud bude registrovaný do qvamp *}
         <li n:if="$isLogged" class="nav-item">
            <a class="nav-link" href="{$mojeOrganizaceUrl}">
               <i class="bi bi-heart"></i>
               <span>{_'Uložené'}</span>
            </a>
         </li>
         <li n:if="$isLogged" class="nav-item">
            <a class="nav-link" href="{$mojeAkceUrl}">
               <i class="bi bi-calendar2"></i>
               <span>{_'Eventy'}</span>
            </a>
         </li>
{*         <li class="nav-item">*}
{*            <a class="nav-link" href="/blog">*}
{*               <i class="bi bi-text-center"></i>*}
{*               <span>{_'Inspirace'}</span>*}
{*            </a>*}
{*         </li>*}
         <li n:if="$isLogged" class="nav-item">
            <a class="nav-link" href="{$nastaveniUzivateleUrl}">
               <i class="bi bi-person"></i>
               <span>{_'Profil'}</span>
            </a>
         </li>
         <li n:if="!$isLogged" n:attr="$modalAttrs" class="nav-item">
            <a class="nav-link">
               <i class="bi bi-box-arrow-in-right"></i>
               <span>{_'Přihlásit se'}</span>
            </a>
         </li>
         <li n:if="!$isLogged" n:attr="$modalAttrs" class="nav-item">
            <a class="nav-link">
               <i class="bi bi-person-plus"></i>
               <span>{_'Registrovat'}</span>
            </a>
         </li>
      </ul>
   </div>
</nav>