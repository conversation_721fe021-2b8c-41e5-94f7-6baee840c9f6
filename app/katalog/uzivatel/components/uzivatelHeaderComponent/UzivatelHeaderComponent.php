<?php namespace app\katalog\uzivatel\components\uzivatelHeaderComponent;

use app\katalog\filtrace\FiltraceProstoryController;
use app\katalog\uzivatel\KatalogUzivatelController;
use app\katalog\uzivatel\ZakaznikDokonceniRegistraceController;
use app\system\application\ClientApplicationEnvironment;
use app\system\component\Component;

class UzivatelHeaderComponent extends Component
{
   function setData() :array {
      $env = ClientApplicationEnvironment::get()->zakaznik;

      if(!$env->isRegistered){
         $this->setTemplateName('uncomplete');

         return [
            'jmeno' => $env->full_name,
            'budouciEventy' => $env->getBudouciEventyCount(),
            'mojeEventy' => KatalogUzivatelController::getUrl('events'),
            'dokoncitRegistraciUrl' => ZakaznikDokonceniRegistraceController::getUrl(),
         ];
      }

      return [
         'jmeno' => $env->full_name,
         'budouciEventy' => $env->getBudouciEventyCount(),
         'ulozeneOrganizace' => $env->getUlouzeneOrganizaceCount(),
         'profilFotoUrl' => '/files/layout/img/not_found_product_photo.png',
         'mojeAkceUrl' => KatalogUzivatelController::getUrl('organizations'),
         'mojeEventy' => KatalogUzivatelController::getUrl('events'),
         'mojeOrganizace' => KatalogUzivatelController::getUrl(),
         'vyhledavani' => FiltraceProstoryController::getUrl('cz'),
      ];
   }

}