<?php namespace app\katalog\uzivatel;

use app\katalog\uzivatel\sprava\registrace\dokonceni\KatalogDokonceniRegistracePage;
use app\system\controller\Controller;
use app\system\router\Route;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 05.09.2025 */
#[Route([
   'cs' => '^/dokoncit-registraci',
   'en' => '^/complete-registration$',
])]
class ZakaznikDokonceniRegistraceController
{

   use Controller;

   public function call() :void {
      KatalogDokonceniRegistracePage::echoThis();
   }
}