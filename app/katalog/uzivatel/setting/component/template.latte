{varType app\front\organizace\model\organizace\OrganizaceRow[] $organizace}
{varType Latte\Runtime\Html[] $udaje}
{varType app\system\users\zakaznici\LoggedZakaznik $zakaznik}
{varType Latte\Runtime\Html $telefonInput}


<section class="mt-3">
   <div class="row">
      <div class="col-lg-4 col-xl-3">

         <div>
            <div class="list-group" role="tablist">
               <a n:if="$zakaznik->isRegistered"
                  class="list-group-item list-group-item-action list-group-item-secondary text-truncate active"
                  data-bs-toggle="list"
                  data-bs-target="#userSettingTab"
                  role="tab"
                  aria-selected="true"
                  tabindex="-1"
               >
                  {_'Nastavení účtu'}
               </a>

               {foreach $organizace as $org}
                  <a class="list-group-item list-group-item-action list-group-item-secondary text-truncate{if !$zakaznik->isRegistered && $iterator->first} active{/if}" data-bs-toggle="list" data-bs-target="#org{$org->id_organizace}" role="tab" aria-selected="{if !$zakaznik->isRegistered && $iterator->first}true{else}false{/if}" tabindex="-1">
                     {$org->nazev}
                  </a>
               {/foreach}
            </div>
         </div>
      </div>
      <div class="col">
         <div class="tab-content" id="v-pills-tabContent">
            <div n:if="$zakaznik->isRegistered" class="tab-pane fade show active" id="userSettingTab" role="tabpanel" aria-labelledby="userSettingTab" tabindex="0">
               <form id="katalogZakaznikUdajeForm" method="post">
                  <div class="row">
                     <div class="col col-lg-10">
                        <div class="row">
                           <label for="full_name">{_'Vaše jméno'}</label>
                           <input type="text" id="full_name" name="full_name" class="form-control"
                                  value="{$zakaznik?->full_name}" placeholder="{_'Napište jméno zákazníka'}" minlength="1" maxlength="160" required>
                        </div>

                        <div class="row mt-2">
                           {$telefonInput}
                        </div>

                        <div class="row justify-content-center mt-4">
                           <div class="col-auto">
                              <div class="text-center mt-3">
                                 <button type="submit" class="btn btn-primary shaddow-hover" name="btnUlozitUdajeRegistrovany">
                                    {_'Uložit změny'}
                                 </button>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </form>
            </div>

            {foreach $organizace as $org}
               <div class="tab-pane fade{if !$zakaznik->isRegistered && $iterator->first} show active{/if}" id="org{$org->id_organizace}" role="tabpanel" aria-labelledby="org{$org->id_organizace}-tab" tabindex="0">
                  {if isset($udaje[$org->id_organizace])}
                     {$udaje[$org->id_organizace]}
                  {else}
                     <h3>{$org->nazev}</h3>
                     <p>{_'Údaje pro tuto organizaci nejsou k dispozici.'}</p>
                  {/if}
               </div>
            {/foreach}
         </div>
      </div>
   </div>
</section>

<script n:if="$zakaznik->isRegistered">
   $(function() {
      const form = $('form#katalogZakaznikUdajeForm');

      form.validate();
   })
</script>