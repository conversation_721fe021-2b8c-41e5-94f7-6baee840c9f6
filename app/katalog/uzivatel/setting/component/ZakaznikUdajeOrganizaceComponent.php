<?php namespace app\katalog\uzivatel\setting\component;

use app\front\organizace\model\organizace\Organizace;
use app\front\zakaznici\component\form\EditZakaznikaFormComponent;
use app\system\application\ClientApplicationEnvironment;
use app\system\component\Component;
use app\system\helpers\telefon\input\TelefonInputComponent;
use app\system\model\organizace\zakaznici\OrganizaceZakaznici;
use app\system\model\organizace\zakaznici\OrganizaceZakaznikRow;

class ZakaznikUdajeOrganizaceComponent extends Component
{

   function setData() :array {
      $zakaznik = ClientApplicationEnvironment::get()->zakaznik;
      $loggedOrganizaction = $zakaznik->getOrganizations();
      $organizace = Organizace::getByIds(array_keys($loggedOrganizaction));
      $udaje = [];

         foreach ($loggedOrganizaction as $userOrg) {

            $zkz = OrganizaceZakaznici::get($userOrg->id_zakaznik_organizace);
            if (!$zkz instanceof OrganizaceZakaznikRow)
               continue;

            $udaje[$userOrg->id_organizace] = EditZakaznikaFormComponent::getComponent()
               ->setTelephoneColumnClass('col-lg-12')
               ->setZakaznik($zkz)
               ->prepareTemplater('client',false)
               ->renderHtml();
         }

      return [
         'organizace' => $organizace,
         'udaje' => $udaje,
         'zakaznik' => $zakaznik,
         'telefonInput' => $zakaznik->getKatalogZakaznik()
            ? TelefonInputComponent::getInput(
               'telefon',
               $zakaznik->getKatalogZakaznik()->getPrimarniTelefon()?->prefix,
               $zakaznik->getKatalogZakaznik()->getPrimarniTelefon()?->telefon,
            ) : null
      ];
   }

   protected function preparePostListeners() :void {
     EditZakaznikaFormComponent::preparePost();
   }
}