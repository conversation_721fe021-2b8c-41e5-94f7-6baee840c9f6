<?php namespace app\katalog\uzivatel\setting;

use app\katalog\layout\BaseKatalogContentLayout;
use app\katalog\uzivatel\components\uzivatelHeaderComponent\UzivatelHeaderComponent;
use app\katalog\uzivatel\setting\component\ZakaznikUdajeOrganizaceComponent;
use app\system\application\ClientApplicationEnvironment;
use app\system\component\Templater;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\model\katalog\zakaznici\telefon\KatalogZakazniciTelefonRow;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 11.04.2025 */
class UzivatelNastaveniPage extends BaseKatalogContentLayout
{

   function getPageName() :string {
      return 'Nastavení uživatele';
   }

   protected function prepareTemplate(Templater $templater) :void {
      $templater->addData([
         'header' => new Html(UzivatelHeaderComponent::getComponent()->renderHtml()),
         'body' => new Html(ZakaznikUdajeOrganizaceComponent::getComponent()->renderHtml()),
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnUlozitUdajeRegistrovany', function(array $post) {
         if(!($name = trim($post['full_name'])))
            throw FlashException::create('Jméno musí být vyplněné');

         if(!($telefonString = trim($post['telefon'])))
            throw FlashException::create('Telefon musí být vyplněný');

         if(!($katalogZakaznik = ClientApplicationEnvironment::get()->zakaznik->getKatalogZakaznik()))
            throw FlashException::create('Nelze upravit údaje');

         $katalogZakaznik->full_name = $name;
         $katalogZakaznik->save();

         if(!($telefon = $katalogZakaznik->getPrimarniTelefon())){
            $telefon = new KatalogZakazniciTelefonRow();
            $telefon->id_zakaznik = $katalogZakaznik->id_zakaznik;
            $telefon->is_primarni = 1;
         }

         $telefon->telefon = $telefonString;
         $telefon->prefix = $post['prefixTelefon'];
         $telefon->save();

         FlashMessages::setSuccess('Údaje byly uloženy');
      });
   }
}