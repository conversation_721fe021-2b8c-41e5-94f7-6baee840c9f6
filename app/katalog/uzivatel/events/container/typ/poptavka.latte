{extends '_default.latte'}

{varType app\system\model\poptavky\BasePoptavkaRow $poptavka}
{varType app\katalog\uzivatel\events\container\EntityContainer $container}
{varType app\front\organizace\model\organizace\OrganizaceRow $organizace}

{block content}
   <div class="row d-flex align-content-center mt-2">
      <h1 class="h2 col-lg col-12">{_'Poptávka'}: {$poptavka->getTypAkceString()}</h1>
      <div class="col-lg-auto d-flex align-items-center justify-content-md-end justify-content-start mb-2 mb-md-2">
         <small>{_'ID'}: {$poptavka->getID()}</small><span class="ms-2 badge bg-secondary rounded-pill text-white">{$poptavka->getStav()->getTranslatedTitle()}</span>
      </div>
   </div>
   <hr>
   <div class="row">
      <div class="col-md-auto d-md-inline gap-2 form-group">
         <label>{_'Datum'}</label>
         <h2 class="lead">
            {if $poptavka->date_start}
               {if $poptavka->date_end}
                  {$poptavka->date_start|date: 'j.n.'}&nbsp;-&nbsp;{$poptavka->date_end|date: 'j.n.Y'}
               {else}
                  {$poptavka->date_start|date: 'j.n.Y'}
               {/if}
            {else}
               {_'Není zadáno'}
            {/if}
         </h2>
      </div>
      {if $poptavka->time_start}
         <div class="vr p-0 ms-3 me-3 d-none d-md-inline-block"></div>
         <div class="col-md-auto col d-md-inline gap-2 form-group">
            <label>{_'Začátek'}</label>
            <h2 class="lead">{$poptavka->time_start->format('%h:%I')}</h2>
         </div>
      {/if}
      {if $poptavka->time_end}
         <div class="vr p-0 mx-2 d-none d-sm-inline-block"></div>
         <div class="col-md-auto col d-md-inline gap-2 form-group">
            <label>{_'Konec'}</label>
            <h2 class="lead">{$poptavka->time_end->format('%h:%I')}</h2>
         </div>
      {/if}

{*       Dočasně vypnuto, u poptávky nelze nastavit místnosti *}
{*      {var $location = $container->getEntityLocationName($poptavka)}*}
{*      {if $location}*}
{*         <div class="vr p-0 md-3 me-3 d-none d-md-inline-block"></div>*}
{*         <div class="col-sm-auto d-sm-inline gap-2 form-group">*}
{*            <label>{_'Místnost/Prostor'}</label>*}
{*            <h2 class="lead">{$container->getEntityLocationName($poptavka)}</h2>*}
{*         </div>*}
{*      {/if}*}

      <div class="col-lg col-12 my-md-3 d-flex justify-content-end">
         <div class="btn-group">
            <a n:if="!$poptavka->getStav()->isNovy()" class="btn btn-primary shaddow-hover" href="{$poptavka->getClientDetailUrl()}"><i class="bi bi-calendar-date me-2"></i>{_'Detail poptávky'}</a>
            <a class="btn btn-outline-primary shaddow-hover" href="{$organizace->getClientDetailUrl()}"><i class="bi bi-building-check me-2"></i>{$organizace->nazev}</a>
         </div>
      </div>
   </div>
{/block}