{extends '_default.latte'}

{varType app\system\model\nabidka\BaseNabidkaRow $nabidka}
{varType app\katalog\uzivatel\events\container\EntityContainer $container}
{varType app\front\organizace\model\organizace\OrganizaceRow $organizace}

{block content}
   <div class="row d-flex align-content-center mt-2">
      <h1 class="h2 col-lg col-12">{_'Kalkulace'}:
         {if $nabidka->nazev}
            {$nabidka->nazev}
         {else}
            {if $nabidka->date_end}
               {$nabidka->date_start|date: 'j.n.'}&nbsp;-&nbsp;{$nabidka->date_end|date: 'j.n.Y'}
            {else}
               {$nabidka->date_start|date: 'j.n.Y'}
            {/if}
         {/if}
      </h1>
      <div class="col-lg-auto d-flex align-items-center justify-content-md-end justify-content-start mb-2 mb-md-2">
         <small>{_'ID'}: {$nabidka->getID()}</small><span class="ms-2 badge bg-secondary rounded-pill text-white">{$nabidka->getStatus()->getTranslatedTitle()}</span>
      </div>
   </div>
   <hr>
   <div class="row">
      <div class="col-md-auto d-sm-inline gap-2 form-group">
         <label>{_'Datum'}</label>
         <h2 class="lead">
            {if $nabidka->date_end}
               {$nabidka->date_start|date: 'j.n.'}&nbsp;-&nbsp;{$nabidka->date_end|date: 'j.n.Y'}
            {else}
               {$nabidka->date_start|date: 'j.n.Y'}
            {/if}
         </h2>
      </div>
      {if $nabidka->time_start}
         <div class="vr p-0 ms-3 me-3 d-none d-md-inline-block"></div>
         <div class="col-md-auto col d-sm-inline gap-2 form-group">
            <label>{_'Začátek'}</label>
            <h2 class="lead">{$nabidka->time_start->format('%h:%I')}</h2>
         </div>
      {/if}

      {if $nabidka->time_end}
         <div class="vr p-0 mx-2 d-none d-sm-inline-block"></div>
         <div class="col-md-auto col d-sm-inline gap-2 form-group">
            <label>{_'Konec'}</label>
            <h2 class="lead">{$nabidka->time_end->format('%h:%I')}</h2>
         </div>
      {/if}


      {var $location = $container->getEntityLocationName($nabidka)}
      {if $location}
         <div class="vr p-0 ms-3 me-3 d-none d-md-inline-block"></div>
         <div class="col-sm-auto d-sm-inline gap-2 form-group">
            {if $nabidka instanceof app\system\model\nabidka\VenueNabidkaRow}
               <label>{_'Místnost/Prostor'}</label>
            {elseif $nabidka instanceof app\system\model\nabidka\VendorNabidkaRow}
               <label>{_'Místo pořádání'}</label>
            {/if}
            <h2 class="lead">{$container->getEntityLocationName($nabidka)}</h2>{*@todo - dopojit místo pořádání u dodavatele*}
         </div>
      {/if}
      <div class="col-lg col-12 my-md-3 d-flex justify-content-end">
         <div class="btn-group">
            <a class="btn btn-primary shaddow-hover" href="{$nabidka->getClientDetailUrl()}"><i class="bi bi-calendar-range me-2"></i>{_'Detail nabídky'}</a>
            <a class="btn btn-outline-primary shaddow-hover" href="{$organizace->getClientDetailUrl()}"><i class="bi bi-building-check me-2"></i>{$organizace->nazev}</a>
         </div>
      </div>
   </div>
{/block}