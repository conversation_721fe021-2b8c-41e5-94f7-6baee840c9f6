<?php namespace app\katalog\uzivatel;

use app\katalog\uzivatel\sprava\auth\KatalogAuthPage;
use app\system\model\katalog\zakaznici\event\AuthorizeFromGoogleEvent;
use app\libs\oauth\GoogleAuthApiClient;
use app\system\controller\ActionController;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\Redirect;
use app\system\router\Route;
use app\system\traits\JsonAjax;
use Google\Service\Oauth2;

/** Created by <PERSON><PERSON>. Date: 09.04.2025 */
//@TODO rename ReturnOAuthResponseController
#[Route('^/oauth(/(?<action>\w*))?$')]
class ZakaznikOauthPrihlaseniController
{

   use ActionController;
   use JsonAjax;

   const string GOOGLE = 'google';
   public function action_google() :void {
      if(!isset($_GET['code']))
         $this->getErrorMsg();

      $client = GoogleAuthApiClient::getClient();
      $token = $client->fetchAccessTokenWithAuthCode($_GET['code']);

      if(!isset($token['access_token']))
         $this->getErrorMsg();

      $client->setAccessToken($token['access_token']);
      $oauth = new Oauth2($client);
      $userInfo = $oauth->userinfo->get();

      try{
         (new AuthorizeFromGoogleEvent($userInfo))
            ->call();
      }catch(FlashException $e){
         $e->setFlashMessage();
         Redirect::to(KatalogLoginController::getUrl());
      }

      FlashMessages::setSuccess('Byli jste úspěšně přihlášen');
      Redirect::homepage();
   }

   private function getErrorMsg() :never {
      FlashMessages::setError('Nastala chyba během přihlašování, zkuste to prosím později, případně kontaktuje podporu');
      Redirect::to(KatalogLoginController::getUrl());
   }
}