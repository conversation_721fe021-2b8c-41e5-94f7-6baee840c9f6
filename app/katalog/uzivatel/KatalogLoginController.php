<?php namespace app\katalog\uzivatel;

use app\katalog\homepage\HomepageController;
use app\katalog\uzivatel\sprava\auth\forms\login\LoginFormComponent;
use app\katalog\uzivatel\sprava\auth\forms\register\RegisterFormComponent;
use app\katalog\uzivatel\sprava\auth\forms\verify\VerifyFormComponent;
use app\katalog\uzivatel\sprava\auth\KatalogAuthPage;
use app\system\controller\ActionController;
use app\system\Environment;
use app\system\event\FlashException;
use app\system\flash\FlashMessages;
use app\system\mailer\MailRecipient;
use app\system\model\katalog\zakaznici\event\RegisterByEmailPostEvent;
use app\system\model\katalog\zakaznici\KatalogZakaznici;
use app\system\model\katalog\zakaznici\verify\KatalogExistZakaznikCode;
use app\system\model\organizace\zakaznici\email\VerifikaceExistZakaznikEmail;
use app\system\model\organizace\zakaznici\OrganizaceZakaznici;
use app\system\recaptcha\Recaptcha;
use app\system\Redirect;
use app\system\router\Route;
use app\system\traits\JsonAjax;
use app\system\users\zakaznici\event\LoginZakaznikEvent;
use app\system\users\zakaznici\LoggedZakaznik;

/** Created by Kryštof Czyź. Date: 02.09.2025 */
#[Route('^/auth(-(?<action>\w*))?$')]
class KatalogLoginController
{

   use ActionController;
   use JsonAjax;

   public function action_default() :void {
      if(LoggedZakaznik::getLogged() !== null)
         Redirect::homepage();

      KatalogAuthPage::echoThis();
   }

   public function action_email() {
      $this->checkTokenAndLoggedZakaznik('EMAIL_CHECK');
      $email = trim($_POST['email']);

      if(KatalogZakaznici::getByEmail($email))
         $this->sendAjaxResponse([
            'success' => true,
            'nextForm' => (string)LoginFormComponent::getComponent()->setEmail($email)->renderHtml()
         ]);

      if($zakaznikID = OrganizaceZakaznici::checkExistEmail($email)){
         $codeRow = KatalogExistZakaznikCode::create($zakaznikID);

         if(Environment::isProduction()){
            (new VerifikaceExistZakaznikEmail())
               ->setCodeRow($codeRow)
//               @TODO jazyk systému
               ->send(new MailRecipient($email));

         } else {
            FlashMessages::setInfo($codeRow->code)
               ->setTitle('LOCALHOST: Verifikační kód')
               ->setTimeout(120000)
               ->setNoTranslate();
         }

         $this->sendAjaxResponse([
            'success' => true,
            'nextForm' => (string)VerifyFormComponent::getComponent()->setEmail($email)->renderHtml()
         ]);
      }

      $this->sendAjaxResponse([
         'success' => true,
         'nextForm' => (string)RegisterFormComponent::getComponent()->setEmail($email)->renderHtml()
      ]);
   }

   public function action_submit() {
      $this->checkTokenAndLoggedZakaznik('AUTH_SUBMIT');

      if(isset($_POST['btnPrihlasit'])){
         $email = trim($_POST['email']);
         $heslo = $_POST['heslo'];

         if(
            !$email
            || !($zakaznik = KatalogZakaznici::getByEmail($email))
            || !(password_verify($heslo, $zakaznik->password ?? ''))
         ) {
            FlashMessages::setError('Neplatný email nebo špatné heslo');
            $this->sendAjaxResponse([
               'success' => false,
            ]);
         }

         LoginZakaznikEvent::loginRegistered(null, $zakaznik)->call();
         FlashMessages::setSuccess('Byl jste úspěšně přihlášen');
         $this->sendAjaxResponse([
            'success' => true,
            'redirectUrl' => HomepageController::getUrl('cz'),
         ], true);
      }elseif(isset($_POST['btnVerifyCode'])){
         $email = trim($_POST['email']);
         $code = trim($_POST['code']);

         if(!($codeRow = KatalogExistZakaznikCode::getByCode($code, $email))){
            FlashMessages::setError('Ověřovací kód není správný');
            $this->sendAjaxResponse([
               'success' => false,
            ]);
         }

         $codeRow->delete();
         $this->sendAjaxResponse([
            'success' => true,
            'nextForm' => (string)RegisterFormComponent::getComponent()->setEmail($email)->renderHtml()
         ]);
      }elseif(isset($_POST['btnRegistrovat'])){
         try{
            (new RegisterByEmailPostEvent($_POST))
               ->call();

            $this->sendAjaxResponse([
               'success' => true,
               'redirectUrl' => '/cz',
            ]);
         } catch(FlashException $e){
            $e->setFlashMessage();
            $this->sendAjaxResponse([
               'success' => false,
            ]);
         }
      }

      $this->sendAjaxResponse([
         'success' => false,
      ]);
   }

   private function checkTokenAndLoggedZakaznik(string $recaptchaAction) :void {
      if(!isset($_POST['token']) || !Recaptcha::checkToken($_POST['token'], $recaptchaAction)){
         FlashMessages::setError('Chyba při zpracovávání formuláře');
         $this->sendAjaxResponse([
            'success' => false
         ]);
      }

      if(LoggedZakaznik::getLogged() !== null)
         $this->sendAjaxResponse([
            'success' => true,
            'redirectUrl' => '/cz',
         ]);
   }
}