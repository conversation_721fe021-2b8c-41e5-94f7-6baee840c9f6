<?php namespace app\katalog\uzivatel\sprava\modal;

use app\katalog\uzivatel\KatalogLoginController;
use app\system\component\Templater;
use app\system\modul\modal\Modal;
use app\system\modul\modal\styles\ModalScaleEnum;

/** Created by <PERSON><PERSON>. Date: 10.04.2025 */
class LoginZakaznikKatalogModal extends Modal
{

   public function getTitleName() :string {
      return 'Přihlašte se nebo se zaregistrujte';
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'loginUrl' => KatalogLoginController::getUrl(),
      ]);
   }

   protected function getStaticID() :?string {
      return 'loginZakaznikKatalogModal';
   }

   protected ModalScaleEnum $scale = ModalScaleEnum::SMALL;
}