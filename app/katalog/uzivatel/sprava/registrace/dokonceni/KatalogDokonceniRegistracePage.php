<?php namespace app\katalog\uzivatel\sprava\registrace\dokonceni;

use app\katalog\homepage\HomepageController;
use app\katalog\layout\BaseKatalogContentLayout;
use app\katalog\uzivatel\sprava\auth\forms\register\RegisterFormComponent;
use app\system\application\ClientApplicationEnvironment;
use app\system\component\Templater;
use app\system\model\katalog\zakaznici\event\RegisterByEmailPostEvent;
use app\system\Redirect;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 13.08.2025 */
class KatalogDokonceniRegistracePage extends BaseKatalogContentLayout
{

   function getPageName() :string {
      return 'Dokončení registrace';
   }

   protected function prepareTemplate(Templater $templater) {
      $templater->addData([
         'backUrl' => $_SERVER['HTTP_REFERER'] ?? null,
         'homeUrl' => HomepageController::getUrl('cz'),
         'formRegister' => RegisterFormComponent::getComponent()
            ->setEmail(ClientApplicationEnvironment::get()->zakaznik->getEmail())
            ->setFullName(ClientApplicationEnvironment::get()->zakaznik->full_name)
      ]);
   }

   protected function preparePostListeners() :void {
      $this->isset('btnRegistrovat', function(array $post) {
         (new RegisterByEmailPostEvent($post))
            ->call();
         Redirect::homepage();
      });

   }
}