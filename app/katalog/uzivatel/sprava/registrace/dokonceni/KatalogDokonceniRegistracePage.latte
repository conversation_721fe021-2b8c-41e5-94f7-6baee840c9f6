{varType ?string $backUrl}
{varType string $homeUrl}
{varType app\katalog\uzivatel\sprava\auth\forms\register\RegisterFormComponent $formRegister}


<div class="mt-5 d-flex justify-content-center">
   <section class="container mt-5 col-md-7 col-lg-6 col-xl-5">
      <div class="row text-center my-4">
         <a class="navbar-brand d-block d-md-none" href="{$homeUrl}">
            <img src="/files/system/img/icons/Qvamp_logo-500-pink.webp" alt="Qvamp - Event Management App">
         </a>
      </div>
      <h3>{_'Dokončete svou registraci'}</h3>

      <div class="mb-3 mt-3">
         {$formRegister->renderHtml()}
      </div>

      <div class="js-button-group">
         <hr>
         <div class="text-center my-3">
            <small class="text-muted">{_'nebo'}</small>
         </div>
         <!-- Přihlašovací tlačítka přes sociální sítě -->
         <div class="d-grid gap-2">
            <button type="button" class="btn btn-outline-secondary" data-google-login>
               <i class="bi bi-google me-2"></i>{_'Pokračovat pomocí Google'}
            </button>
            <div n:if="$backUrl" class="row d-flex justify-content-center">
               <div class="col-auto">
                  <a href="{$backUrl}" class="primary">{_'Zpět'}</a>
               </div>
            </div>
            <div n:else class="row d-flex justify-content-center">
               <div class="col-auto">
                  <a href="{$homeUrl}" class="primary">{_'Zpět na domovskou stránku'}</a>
               </div>
            </div>
         </div>
      </div>
   </section>
</div>

<script>
   $(function() {
      const form = document.querySelector('form#registerForm');

      form.addEventListener('submit', (e) => {
         if (e.defaultPrevented) return;

         const targetForm = e.target;

         const $form = $(targetForm);
         const hasValidator = !!$form.data('validator');
         if (hasValidator && !$form.valid()) {
            return;
         }

         const submitter = (e.submitter instanceof HTMLButtonElement) ? e.submitter : null;
         if (submitter) submitter.disabled = true;
      });
   })
</script>


