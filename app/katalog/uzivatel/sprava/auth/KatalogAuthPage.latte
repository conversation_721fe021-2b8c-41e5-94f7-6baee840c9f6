{varType ?string $backUrl}
{varType string $homeUrl}
{varType app\katalog\uzivatel\sprava\auth\forms\init\InitialFormComponent $initForm}

<div class="js-step-one">
   <div class="mt-5 d-flex justify-content-center">
      <section class="container mt-5 col-md-7 col-lg-6 col-xl-5">
         <div class="row text-center my-4">
            <a class="navbar-brand d-block d-md-none" href="{$homeUrl}">
               <img src="/files/system/img/icons/Qvamp_logo-500-pink.webp" alt="Qvamp - Event Management App">
            </a>
         </div>
         <h3>{_'Přihlaste se nebo se zaregistrujte'}</h3>

         <div class="mb-3 mt-3"
              data-katalog-auth="{app\system\recaptcha\Recaptcha::getPublicKey()}"
              id="katalogAuthformContainer"
         >
            {$initForm->renderHtml()}
         </div>

         <div class="js-button-group">
            <hr>
            <div class="text-center my-3">
               <small class="text-muted">{_'nebo'}</small>
            </div>
            <!-- Přihlašovací tlačítka přes sociální sítě -->
            <div class="d-grid gap-2">
               <button type="button" class="btn btn-outline-secondary" data-google-login>
                  <i class="bi bi-google me-2"></i>{_'Pokračovat pomocí Google'}
               </button>
               <div n:if="$backUrl" class="row d-flex justify-content-center">
                  <div class="col-auto">
                     <a href="{$backUrl}" class="primary">{_'Zpět'}</a>
                  </div>
               </div>
               <div n:else class="row d-flex justify-content-center">
                  <div class="col-auto">
                     <a href="{$homeUrl}" class="primary">{_'Zpět na domovskou stránku'}</a>
                  </div>
               </div>
            </div>
         </div>
      </section>
   </div>
</div>

<script n:if="app\system\recaptcha\Recaptcha::isEnabled()"
        src="https://www.google.com/recaptcha/enterprise.js?render={app\system\recaptcha\Recaptcha::getPublicKey()}"
        async defer
></script>