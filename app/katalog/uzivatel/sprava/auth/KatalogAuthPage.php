<?php namespace app\katalog\uzivatel\sprava\auth;

use app\katalog\homepage\HomepageController;
use app\katalog\layout\BaseKatalogContentLayout;
use app\katalog\uzivatel\sprava\auth\forms\init\InitialFormComponent;
use app\system\component\Templater;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 02.09.2025 */
class KatalogAuthPage extends BaseKatalogContentLayout
{

   function getPageName() :?string {
      return 'Přihlašte se nebo zaregistrujte';
   }

   protected function prepareTemplate(Templater $templater) {
      $templater->addData([
         'backUrl' => $_SERVER['HTTP_REFERER'] ?? null,
         'homeUrl' => HomepageController::getUrl('cz'),
         'initForm' => InitialFormComponent::getComponent(),
      ]);
   }

   protected function preparePostListeners() :void {

   }
}