{varType string $email}

<form id="verifyExistForm">
   <p>{_'<PERSON><PERSON><PERSON><PERSON> to že jste u nás už někdy byl, na email $1 jsme Vám zaslali kód pro ověření', $email}</p>

   <div class="row mt-2">
      <label for="codeEmail">{_'Email'}</label>
      <input type="text" name="email" class="form-control pb-2" id="codeEmail" value="{$email}" readonly>
   </div>

   <div class="row mt-2">
      <label for="codeInput" class="form-label">{_'Verifikační kód'}</label>
      <input type="text" class="form-control pb-2" id="codeInput" name="code" placeholder="{_'Zadejte verifikační kód'}" required>
   </div>

   <div class="row justify-content-center mt-4">
      <div class="col-auto">
         <button type="submit" name="btnVerifyCode" class="btn btn-primary js-verify-client-code mt-3">{_'Ověřit'}</button>
      </div>
   </div>
</form>

<script>
   $(function() {
      const form = $('form#verifyExistForm');

      form.validate({
         rules: {
            code: {
               required: true,
               digits:   true,
               maxlength: 6,
            }
         },
      })
   });
</script>