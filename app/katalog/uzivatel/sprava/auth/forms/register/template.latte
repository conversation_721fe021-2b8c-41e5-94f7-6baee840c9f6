{varType ?string $email}
{varType ?string $jmeno}
{varType ?string $prijmeni}
{varType Latte\Runtime\Html $telefonInput}

<form id="registerForm" method="post">
   <p>{_'Dokončete svou registraci a pusťte se do světa Qvampu'}</p>

   <div class="row mt-4">
      <div class="col-6">
         <label for="clientName" class="form-label ms-2">{_'Jméno'}<span
                    class="text-primary me-1">&nbsp;*</span></label>
         <input n:attr="value: $jmeno"
                 type="text"
                 class="form-control"
                 id="clientName"
                 name="jmeno"
                 placeholder="{_'Zadejte své jméno'}"
                 required>
      </div>

      <div class="col-6">
         <label for="clientSurname" class="form-label ms-2">{_'Přijmení'}<span class="text-primary me-1">&nbsp;*</span></label>
         <input n:attr="value: $prijmeni"
                 type="text" class="form-control"
                 id="clientSurname"
                 name="prijmeni"
                 placeholder="{_'Zadejte své přijmení'}" required>
      </div>
   </div>

   <div class="row mt-2">
      <div class="col-12">
         <label for="emailRegistrace" class="form-label">{_'E-mail'}</label>
         <input type="text" class="form-control"
                id="emailRegistrace"
                name="email"
                placeholder="{_'Zadejte svou e-mailovou adresu'}"
                 n:attr="value: $email, readonly: !!$email"
                required>
      </div>
   </div>

   <div class="row mt-2">
      <div class="col-12">
         <label for="clientPassword" class="form-label ms-2">{_'Heslo'}<span class="text-primary me-1">&nbsp;*</span></label>
         <input type="password" class="form-control" id="clientPassword" name="heslo" placeholder="{_'Zadejte nové heslo'}" required>
      </div>
   </div>

   <div class="row mt-2">
      <div class="col-12">
         <label for="clientPassword2" class="form-label ms-2">{_'Heslo pro kontrolu'}<span class="text-primary me-1">&nbsp;*</span></label>
         <input type="password" class="form-control" id="clientPassword2" name="heslo2" placeholder="{_'Zadejte heslo pro kontrolu'}" required>
      </div>
   </div>

   <div class="mt-2">
      {$telefonInput}
   </div>

   <div class="row justify-content-center mt-4">
      <div class="col-auto">
         <div class="text-center mt-3">
            <div class="row js-changing-text d-none">
               <small>{_'Probíhá přihlašování'}...</small>
            </div>
            <button type="submit" class="btn btn-primary shaddow-hover" name="btnRegistrovat">
               <div class="spinner-border spinner-border-sm text-secondary me-2 js-login-loader d-none" role="status">
                  <span class="visually-hidden">{_'Načítání'}...</span>
               </div>
               {_'Registrovat'}
            </button>
         </div>
      </div>
   </div>
</form>

{var $pattern = "/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/"}

<script>
   jQuery.validator.addMethod('pwcheck', function(value) {
      return new RegExp({$pattern|noescape}).test(value);
   }, "{_'Vaše heslo můsí mít alespoň 8 znaků a obsahovat alespoň 1 velké písmeno a 1 číslo'}");

   $(function() {
      const form = $('form#registerForm');

      form.validate({
         rules: {
            heslo: {
               minlength: 8,
               pwcheck: true,
            },
            heslo2: {
               equalTo: '#clientPassword',
            },
         },
      });

      $(window).trigger('qvamp::component.loaded', [form[0]]);
   });
</script>