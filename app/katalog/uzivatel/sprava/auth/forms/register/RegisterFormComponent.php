<?php namespace app\katalog\uzivatel\sprava\auth\forms\register;

use app\system\component\Component;
use app\system\helpers\telefon\input\TelefonInputComponent;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 02.09.2025 */
class RegisterFormComponent extends Component
{

   function setData() :array {
      return [
         'email' =>$this->email,
         'jmeno' => $this->jmeno,
         'prijmeni' => $this->prijmeni,
         'telefonInput' => TelefonInputComponent::getInput('telefon'),
      ];
   }

   public function setEmail(?string $email) :RegisterFormComponent {
      $this->email = $email;
      return $this;
   }

   public function setFullName(string $jmeno) :RegisterFormComponent {
      $parts = explode(' ', $jmeno, 2);
      $this->jmeno = $parts[0];
      $this->prijmeni = $parts[1];
      return $this;
   }

   protected ?string $email = null;
   protected ?string $jmeno = null;
   protected ?string $prijmeni = null;
}