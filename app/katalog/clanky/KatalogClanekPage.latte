{varType app\system\model\clanky\SystemClanekRow $clanek}
{varType Latte\Runtime\Html $autor}
{varType string $seznamClankuUrl}
{varType string $homepageUrl}

<!-- <PERSON><PERSON><PERSON><PERSON> obsah <PERSON> -->
<main class="container my-5">
   <!-- Navigace -->
   <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
         <li class="breadcrumb-item"><a class="text-black" href="{$homepageUrl}"><i class="align-middle me-2 bi bi-houses"></i></a></li>
         <li class="breadcrumb-item"><a class="text-black" href="{$seznamClankuUrl}">{_'Články'}</a></li>
         <li class="breadcrumb-item active text-primary" aria-current="{$clanek->getDetailUrl()}">{$clanek->nazev}</li>
      </ol>
   </nav>

   <div class="row">
      <!-- <PERSON><PERSON><PERSON><PERSON> -->
      <div class="col-lg-8">
         <header>
            <h1 class="fw-bold">{$clanek->nazev}</h1>
            <div class="mb-3">
               <span>{_'Publikováno'}: {$clanek->published|date:'j. n. Y'}</span>
{*               <span>{_'Autor'}: {$autor}</span>*}
            </div>
            <img n:if="$clanek->getThumbnail()" src="/{$clanek->getThumbnailSrc()}"
                 alt="{$clanek->nazev}"
                 loading="lazy"
                 itemprop="image"
                 class="radius-card img-fluid my-3 w-100">
         </header>
         <section class="p-2 clanek" itemprop="articleBody">
            {$clanek->text|noescape}
         </section>
         <!-- Sdílení článku -->
         <div class="share-icons my-4"
              data-share-url="{$clanek->getDetailUrl()}"
              data-share-text="{$clanek->title}">
            <a href="#" data-network="whatsapp" title="Sdílet na WhatsApp"><i class="bi bi-whatsapp text-primary"></i></a>
            <a href="#" data-network="twitter"  title="Sdílet na X"><i class="bi bi-twitter-x text-primary"></i></a>
            <a href="#" data-network="linkedin" title="Sdílet na LinkedIn"><i class="bi bi-linkedin text-primary"></i></a>
         </div>
{*       <hr>
              <section class="my-3">
                  <header class="mb-3">
                     <h2>Další inspirace</h2>
                  </header>
                  <div class="row row-cols-1 row-cols-md-3 g-3">
                     <!-- Inspirace 1 -->
                     <div class="col">
                        <a href="#" class="card p-0 shadow-hover h-100 text-decoration-none shaddow-hover">
                           <img src="https://qvamp.cz/wp-content/uploads/2025/02/qvamp.crm-pro-eventy.jpg"
                                alt="Jak uspořádat úspěšnou konferenci? Inspirace 1"
                                class="card-img-top img-fluid" loading="lazy">
                           <div class="card-body p-3">
                              <article>
                                 <h3 class="card-title">Jak uspořádat úspěšnou konferenci?</h3>
                              </article>
                           </div>
                        </a>
                     </div>
                     <!-- Inspirace 2 -->
                     <div class="col">
                        <a href="#" class="card p-0 shadow-hover h-100 text-decoration-none shaddow-hover">
                           <img src="https://qvamp.cz/wp-content/uploads/2025/02/qvamp.crm-pro-eventy.jpg"
                                alt="Jak uspořádat úspěšnou konferenci? Inspirace 2"
                                class="card-img-top img-fluid" loading="lazy">
                           <div class="card-body p-3">
                              <article>
                                 <h3 class="card-title">Jak uspořádat úspěšnou konferenci?</h3>
                              </article>
                           </div>
                        </a>
                     </div>
                     <!-- Inspirace 3 -->
                     <div class="col">
                        <a href="#" class="card p-0 shadow-hover h-100 text-decoration-none shaddow-hover">
                           <img src="https://qvamp.cz/wp-content/uploads/2025/02/qvamp.crm-pro-eventy.jpg"
                                alt="Jak uspořádat úspěšnou konferenci? Inspirace 3"
                                class="card-img-top img-fluid" loading="lazy">
                           <div class="card-body p-3">
                              <article>
                                 <h3 class="card-title">Jak uspořádat úspěšnou konferenci?</h3>
                              </article>
                           </div>
                        </a>
                     </div>
                  </div>
               </section>*}
      </div>

      <!-- Sidebar (volitelný) -->
      <div class="col-lg-4">
         <aside class="sidebar sticky-top pt-3">
{*            <h3 class="sidebar-title">{_'Najít prostor nebo dodavatele'}</h3>
            Tady bude vyhledávací komponenta*}
            <a href="https://qvamp.com/cz/sluzby" class="btn btn-secondary w-auto shaddow-hover">
               {_'Najít prostor nebo dodavatele'}
            </a>
         </aside>
      </div>
   </div>
</main>

<script n:syntax=off>
    (function () {
        const box = document.querySelector('.share-icons');
        if (!box) return;

        const url  = box.dataset.shareUrl || location.href;
        const text = box.dataset.shareText || document.title;

        // Jednoduchá mobilní detekce (iOS/Android/tablety)
        function isMobile() {
            return /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent);
        }

        const links = {
            facebook: (u, t) => `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(u)}`,
            twitter:  (u, t) => `https://twitter.com/intent/tweet?url=${encodeURIComponent(u)}&text=${encodeURIComponent(t)}`,
            linkedin: (u, t) => `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(u)}`,
            whatsapp: (u, t) => `https://api.whatsapp.com/send?text=${encodeURIComponent(t)}%20${encodeURIComponent(u)}`,
            email:    (u, t) => `mailto:?subject=${encodeURIComponent(t)}&body=${encodeURIComponent(u)}`
        };

        function popup(href) {
            const w = 600, h = 500;
            const y = window.top.outerHeight / 2 + window.top.screenY - (h / 2);
            const x = window.top.outerWidth  / 2 + window.top.screenX - (w / 2);
            window.open(href, '_blank', `width=${w},height=${h},left=${x},top=${y},noopener,noreferrer`);
        }

        async function handleShare(network) {
            // Mobil: použij nativní dialog (pokud je k dispozici)
            if (isMobile() && navigator.share) {
                try {
                    await navigator.share({ title: text, text, url });
                    return;
                } catch (e) {
                    // Uživatel zrušil → spadneme na klasický odkaz níže
                }
            }
            // Desktop (a fallback): otevři cílové okno dané sítě
            const builder = links[network] || links.facebook;
            popup(builder(url, text));
        }

        box.addEventListener('click', (e) => {
            const a = e.target.closest('a[data-network]');
            if (!a) return;
            e.preventDefault();
            handleShare(a.dataset.network);
        });
    })();
</script>



