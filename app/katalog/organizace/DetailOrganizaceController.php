<?php namespace app\katalog\organizace;

use app\client\login\verification\ClientVerificationPage;
use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\katalog\organizace\detail\DetailOrganizacePage;
use app\system\controller\Controller;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\translator\Jazyky;
use app\system\router\NewRouter;
use app\system\router\Route;
use app\system\tracker\OrganizaceKatalogTracker;
use app\system\tracker\TrackerPageType;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 14.10.2024 */
#[Route('^/(?<region>cz|pl|hu)/(?<orgSlug>[a-z0-9-]+)/(?<langCode>cs|pl|hu|sk|de|en)$')]
class DetailOrganizaceController
{

   use Controller;

   public static function getUrlOrganizace(OrganizaceRow $organizace, ?Jazyky $lang = null) :string {
      $orgJazyky = OrganizaceJazyky::getAllJazyky($organizace->id_organizace);

      if(!$lang || !in_array($lang, $orgJazyky))
         foreach(OrganizaceJazyky::getAll($organizace->id_organizace) as $langRow){
            if($langRow->is_primary === 1){
               $lang = $langRow->getJazyk();
               break;
            }
         }

      return self::getUrl(
         $organizace->getKatalogRegion()->value,
         $organizace->portal_odkaz,
         $lang?->getISO6391() ?: Jazyky::CZ->getISO6391()
      );
   }

   public function call() :void {
      $params = NewRouter::get()->getRoute()->getParametrs();

      $nazev = $params['orgSlug'];

//      @TODO getPrimary jazyk org if langCode is empty and lang of system dont accept in org
      $jazyk = isset($params['langCode']) ? Jazyky::tryFromISO($params['langCode']) : Jazyky::CZ;

      if(
         !$jazyk
         || !($this->organizace = Organizace::getByPortalOdkaz($nazev))
//         @TODO kontrola existujícího regionu (cz|pl|...) u organizace
      )
         $this->exit404();

//      @TODO kontrola existujícího jazyku organizace (cs|en|...) - else redirect na dostupnou mutaci

      if(
         !$this->environment->isLogged()
         || !$this->environment->zakaznik->getOrganizaceZakaznik($this->organizace->id_organizace)
      ){
         if(isset($_GET['verify'])){
            ClientVerificationPage::getComponent()
               ->setIdOrganizace($this->organizace->id_organizace)
               ->echo();
            exit;
         }
      }

      OrganizaceKatalogTracker::trackPage(
         $this->organizace->id_organizace,
         TrackerPageType::DETAIL,
      );

      DetailOrganizacePage::getComponent()->setOrganizace($this->organizace)->echo();
   }

   private ?OrganizaceRow $organizace;
}