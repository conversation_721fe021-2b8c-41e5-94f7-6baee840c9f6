<?php namespace app\katalog\organizace;

use app\client\ClientApplication;
use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\katalog\organizace\entita\poptavka\nova\ClientNovaPoptavkaPage;
use app\System;
use app\system\application\ClientApplicationEnvironment;
use app\system\controller\Controller;
use app\system\controller\exceptions\page404\Exception404;
use app\system\Redirect;
use app\system\router\NewRouter;
use app\system\router\Route;
use app\system\SystemVersion;
use app\system\tracker\OrganizaceKatalogTracker;
use app\system\tracker\TrackerPageType;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 12.10.2023 */
#[Route([
   'cs' => '^/(?<venue_code>[a-z-0-9]*)/poptavka$',
   'en' => '^/(?<venue_code>[a-z-0-9]*)/request$',
   'pl' => '^/(?<venue_code>[a-z-0-9]*)/zapytania$',
   'hu' => '^/(?<venue_code>[a-z-0-9]*)/reszletei$',
])]
class OrganizationPoptavkaController
{

   use Controller;

   public function call() :void {
      $params = NewRouter::get()->getRoute()->getParametrs();

      $nazev = $params['venue_code'];

      if(!($this->misto = Organizace::getByPortalOdkaz($nazev)))
         throw new Exception404();

      if(System::get()->getApplication() instanceof ClientApplication)
         Redirect::to(self::getUrl($this->misto->portal_odkaz), SystemVersion::KATALOG);

      OrganizaceKatalogTracker::trackPage(
         $this->misto->id_organizace,
         TrackerPageType::POPTAVKA,
      );

      ClientNovaPoptavkaPage::getComponent()
         ->setMisto($this->misto)
         ->setZakaznik(ClientApplicationEnvironment::get()->zakaznik)
         ->echo();
   }

   private ?OrganizaceRow $misto;
}