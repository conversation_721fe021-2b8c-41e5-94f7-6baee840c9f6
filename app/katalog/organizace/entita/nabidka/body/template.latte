{varType app\system\model\nabidka\BaseNabidkaRow $nabidka}
{varType app\katalog\organizace\entita\nabidka\body\ClientKalkulaceBodyComponent $_component}

{varType Latte\Runtime\Html $introduction}
{varType app\system\model\organizace\prilohy\kalkulace\data\OrganizacePrilohaRow[] $prilohy}
{varType bool $isPreview}

{varType ?Latte\Runtime\Html $scenarOsa}
{varType app\system\model\organizace\portal\galerie\OrganizacePortalGalerieRow[] $galerieFotky}
{varType Latte\Runtime\Html $productsComponent}

<div class="row mt-4">
   <div class="col">
      <h2 class="display-5 text-center">{_'Kalkulace'} - {$nabidka->nazev}</h2>
   </div>
</div>

<div class="row mt-4 gap-1">
   <div class="col-md col-12 form-group">
      <small class="text-strong">{_'Typ eventu'}:</small>
      <small>{$nabidka->getPoptavka()->getTypAkceString()}</small>
   </div>
   <div n:if="$nabidka->platnost" class="col-md col-12 form-group">
      <small for="platnost_nabidky" class="text-strong">{_'Platnost kalkulace do'}:</small>
      <small>{$nabidka->platnost|date:'j.n.Y'}</small>
   </div>
   <div class="col-md col-12 form-group">
      <small class="text-strong">{_'ID poptávky / kalkulace'}:</small>
      <small><a n:tag-if="!$isPreview" href="{$nabidka->getPoptavka()->getClientDetailUrl()}">{$nabidka->getPoptavkaID()}</a>/{$nabidka->getID()}</small>
   </div>
   <div class="col-md col-12 form-group" n:if="$nabidka->getEvent()">
      <small class="text-strong">{_'Event'}:</small>
      <small><a n:tag-if="!$isPreview" href="{$nabidka->getEvent()->getClientDetailUrl()}">{$nabidka->getEvent()->getID()}</a></small>
   </div>

   <div class="col-auto mt-2 mt-md-0">
      {if $isPreview}
         <button type="button"
                 class="btn btn-sm btn-outline-secondary"
                 data-bs-toggle="tooltip"
                 data-bs-title="{_'V tomto náhledu tlačítko není aktivní. Ve vašem zákaznickém portále však bude fungovat pro stažení kalkulace do PDF.'}"
         >
            <i class="bi bi-filetype-pdf"></i>&nbsp;Export
         </button>
      {else}
         <form method="post"><button class="btn btn-sm btn-outline-secondary" name="btnDownloadPdfCalculation"><i class="bi bi-filetype-pdf"></i>&nbsp;Export</button></form>
      {/if}
   </div>
</div>
<hr class="mt-2">

<div n:if="$nabidka->getHlavicka()" class="row">
   {$nabidka->getHlavicka()->getHtml()}
</div>

<div class="row gap-3 my-5 d-flex justify-content-center">
   <div class="col-lg-auto col-12 px-0">
      <div class="mt-2 p-3 border border-light-gray radius-card">
         <div class="row gap-3">
            {if $nabidka->date_end}
               <div class="col-md-auto col form-group">
                  <label>{_'Začátek'}</label>
                  <p class="lead">
                     {$nabidka->date_start|date: 'j.n.Y'}&nbsp;{$nabidka->time_start?->format('%h:%I')}
                  </p>
               </div>
               <div class="vr p-0 mx-1 d-none d-md-inline-block"></div>
               <div class="col-md-auto col form-group">
                  <label>{_'Konec'}</label>
                  <p class="lead">
                     {$nabidka->date_end|date: 'j.n.Y'} {$nabidka->time_end?->format('%h:%I')}
                  </p>
               </div>
            {else}
               <div class="col-md-auto col form-group">
                  <label>{_'Datum'}</label>
                  <p class="lead">
                     {$nabidka->date_start|date: 'j.n.Y'}
                  </p>
               </div>
               {if $nabidka->time_start}
                  <div class="vr p-0 mx-1 d-none d-md-inline-block"></div>
                  <div class="col-md-auto col form-group">
                     <label>{_'Začátek'}</label>
                     <p class="lead">{$nabidka->time_start->format('%h:%I')}</p>
                  </div>
               {/if}

               {if $nabidka->time_end}
                  <div class="vr p-0 mx-1 d-none d-md-inline-block"></div>
                  <div class="col-md-auto col form-group">
                     <label>{_'Konec'}</label>
                     <p class="lead">{$nabidka->time_end->format('%h:%I')}</p>
                  </div>
               {/if}
            {/if}

            <div class="vr p-0 mx-1 d-none d-md-inline-block"></div>
            <div class="col-auto d-sm-inline gap-2 form-group">
               <label>{_'Hosté'}</label>
               <p class="lead">{$nabidka->getPocetHostu()} <a href="#" class="ms-1" data-bs-toggle="modal" data-bs-target="#hosteInfoKalkulaceModal">
                     <i class="bi bi-info-circle"></i>
                  </a></p>
            </div>
         </div>
      </div>

      {*Modal - detail hosté*}
      <div class="modal modal-sm fade" id="hosteInfoKalkulaceModal" tabindex="-1" aria-labelledby="hosteInfoKalkulaceModalLabel" aria-hidden="true">
         <div class="modal-dialog">
            <div class="modal-content">
               <div class="modal-header">
                  <h1 class="modal-title fs-5" id="hosteInfoKalkulaceModalLabel">{_'Informace o hostech'}</h1>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
               </div>
               <div class="modal-body">
                  <div class="row">
                     {foreach $nabidka->getHosteContainer()->getVisiableSkupinyHostu() as $skupina}
                        <div n:if="$skupina->getPocet() > 0" class="col my-1 d-inline-flex">
                           <div class="d-flex mx-1 form-group col">
                              <div class="col-auto mx-1 text-end">
                                 <label n:attr="title: strlen($skupina->nazev_skup) > 18? $skupina->nazev_skup" class="ps-0 col-form-label-sm">
                                    {$skupina->nazev_skup|truncate: 18}:
                                 </label>
                              </div>
                              <div class="col-7 p-0">
                                 <p class="js-pocet-skupiny" name="pocet_hostu[{$skupina->id_skupiny}]">{$skupina->getPocet()}</p>
                              </div>
                           </div>
                        </div>
                     {/foreach}
                  </div>
               </div>
            </div>
         </div>
      </div>

   </div>
</div>

{if isset($introduction) || !empty($prilohy)}
   <div class="mt-5">
      {if isset($introduction)}
         <div class="col-12 card p-lg-3 p-2">
            <p>{$introduction}</p>
         </div>
      {/if}

      {if !empty($prilohy)}
         <div class="col-12 d-flex justify-content-center gap-3">
            {foreach $prilohy as $priloha}
               <a href="{$priloha->getHrefLink()}" class="form-control btn btn-secondary w-auto" target="_blank">
                  <i class="bi bi-paperclip me-2"></i>{$priloha->nazev}
               </a>
            {/foreach}
         </div>
      {/if}
   </div>
{/if}

<div n:if="!empty($galerieFotky)" id="profilGalerie" class="row my-3 d-flex justify-content-center">
   <div class="col-md-10 col-lg-8 col-12">
      {if count($galerieFotky) === 1}
         {foreach $galerieFotky as $fotka}
            <img src="/{$fotka->getPhotoFullSrc()}" class="w-100">
         {/foreach}
      {else}
         <div id="carouselOrganizationGallery" class="carousel slide">
            <div class="carousel-indicators">
               {foreach $galerieFotky as $fotka}
                  <button type="button" data-bs-target="#carouselOrganizationGallery" data-bs-slide-to="{$iterator->counter0}" {if $iterator->first}class="active" aria-current="true"{/if}></button>
               {/foreach}
            </div>
            <div class="carousel-inner">
               {foreach $galerieFotky as $fotka}
                  <div n:class="carousel-item, gallery-item, $iterator->first ? active">
                     <img src="/{$fotka->getPhotoFullSrc()}" alt="{$fotka->name}" class="radius-card w-100">
                     {*                     <div class="carousel-caption d-none d-md-block">*}
{*                        <h5>First slide label</h5>*}
{*                        <p>Some representative placeholder content for the first slide.</p>*}
{*                     </div>*}
                  </div>
               {/foreach}
            </div>
            <button class="carousel-control-prev" type="button" data-bs-target="#carouselOrganizationGallery" data-bs-slide="prev">
               <span class="carousel-control-prev-icon bg-secondary rounded-circle" aria-hidden="true"></span>
               <span class="visually-hidden">{_'Předchozí'}</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#carouselOrganizationGallery" data-bs-slide="next">
               <span class="carousel-control-next-icon bg-secondary rounded-circle" aria-hidden="true"></span>
               <span class="visually-hidden">{_'Další'}</span>
            </button>
         </div>
      {/if}
   </div>
</div>

{if $nabidka instanceof app\system\model\nabidka\VenueNabidkaRow}
   {include venueProstory}
{elseif $nabidka instanceof app\system\model\nabidka\VendorNabidkaRow}
   {include vendorMista}
{/if}

<div class="mt-5">
   {$productsComponent}

   <div class="container my-5 border border-radius border-light-gray p-0" n:if="isset($scenarOsa)">
      <div class="d-flex justify-content-between align-items-center mb-3">
         <small class="label-card lb-left px-2 py-1">{_'Přehled plateb'}</small>
      </div>
      <div class="d-flex flex-wrap my-3 align-content-center px-lg-5 px-2">
         <div class="js-scenar-container">
            {$scenarOsa}
         </div>
      </div>
   </div>
</div>

{define venueProstory}
   {varType app\system\model\nabidka\VenueNabidkaRow $nabidka}
   <div class="bg-white border-radius my-5">
      <small class="label-card lb-left">{_'Prostory eventu'}</small>
      <div class="container d-flex flex-wrap gap-3 p-3">
         {if !empty($nabidka->getMistnosti())}
            {foreach $nabidka->getMistnosti() as $mistnost}
               <a href="#" n:attr="$_component->getDetailMistnostiModal()::getShowAttributes($mistnost->id_mistnost)" class="col-md-3 col-12 p-0 bg-white border border-light-gray border-radius shaddow-hover">
                  <img n:if="$mistnost->getMainPhoto()" src="/{$mistnost->getMainPhoto()->getPhotoFullSrc()}" class="img-fluid img-lb-top" style="object-fit: cover;min-height: 175px;" alt="{$mistnost->nazev} {_'fotka'}">
                  <div class="row mt-3 px-3">
                     <div class="col">
                        <p class="lead">{$mistnost->nazev}</p>
                     </div>
                     <div class="col-auto d-lg-none">
                        <p class="lead text-primary"><i class="bi bi-arrow-up-right-circle"></i></p>
                     </div>
                  </div>
               </a>
            {/foreach}
         {else}
            <div class="row gap-3 mt-3 d-flex justify-content-center">
               <div class="col-md-3 col-12 p-0 bg-white border border-light-gray border-radius">
                  <div class="row mt-3 px-3">
                     <div class="col">
                        <p class="lead">
                           {if $nabidka->is_vsechny_mistnosti === 1}
                              {_'Všechny prostory'}
                           {else}
                              {_'Zatím nebyl přiřazen prostor'}
                           {/if}
                        </p>
                     </div>
                  </div>
               </div>
            </div>
         {/if}
      </div>
   </div>

   {if !empty($nabidka->getMistnosti())}
      {$_component->getDetailMistnostiModal()->render()|noescape}
   {/if}
{/define}

{define vendorMista}
   {varType app\system\model\nabidka\VendorNabidkaRow $nabidka}

   <div class="bg-white border-radius my-5">
      <small class="label-card lb-left">{_'Prostory eventu'}</small>
      <div class="container d-flex flex-wrap gap-3 p-3">
         <div class="col-md-3 col-12 p-0 bg-white border border-light-gray border-radius">
            <div class="row mt-3 px-3">
               <div class="col">
                  <p class="lead">{$nabidka->getLocationName()}</p>
               </div>
            </div>
         </div>
      </div>
   </div>
{/define}