<?php namespace app\katalog\organizace\entita\poptavka\nova;

use app\client\controllers\HomepageController;
use app\front\organizace\model\organizace\OrganizaceNastaveni;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\katalog\layout\BaseKatalogContentLayout;
use app\katalog\organizace\DetailOrganizaceController;
use app\katalog\organizace\entita\poptavka\nova\component\kontaktni\PoptavkaKontaktniFormComponent;
use app\System;
use app\system\component\Templater;
use app\system\flash\FlashMessages;
use app\system\layout\client\head\meta\poptavka\PoptavkaPageMetaTags;
use app\system\model\leady\lead\NovaPoptavkaEvent;
use app\system\model\organizace\jazyk\OrganizaceJazyky;
use app\system\model\organizace\personal\Personal;
use app\system\model\organizace\personal\prirazeni\event\ChangePersonalPrirazeniEvent;
use app\system\model\organizace\portal\OrganizacePortal;
use app\system\model\organizace\zakaznici\souhlasy\SouhlasyTypy;
use app\system\model\translator\Jazyky;
use app\system\recaptcha\Recaptcha;
use app\system\users\zakaznici\LoggedZakaznik;

/** Created by Kryštof Czyź. Date: 12.10.2023 */
class ClientNovaPoptavkaPage extends BaseKatalogContentLayout
{

   public function setMisto(OrganizaceRow $misto) :static {
      $this->misto = $misto;
      return $this;
   }

   public function setZakaznik(?LoggedZakaznik $zakaznik) :static {
      $this->zakaznik = $zakaznik;
      return $this;
   }

   public function getOrganization() :?OrganizaceRow {
      return $this->misto;
   }

   protected function preparePostListeners() :void {
      $this->isset('btnCreateNovaPoptavka', function($post) {
         if(Recaptcha::isEnabled() && !Recaptcha::checkToken($post[Recaptcha::POST_PARAM_NAME], 'NEW_LEAD')){
            FlashMessages::setError('Poptávka nebyla zpracována');
            return;
         }

         $souhlasy = [];

         foreach(($post['uzivatelske_souhlasy'] ?? []) as $id_souhlas => $on)
            if($souhlas = SouhlasyTypy::tryFrom($id_souhlas))
               $souhlasy[] = $souhlas;

         if(!(in_array(SouhlasyTypy::GDPR, $souhlasy) || $this->zakaznik?->getOrganizaceZakaznik($this->misto->id_organizace)?->getZakaznikRow()->hasSouhlas(SouhlasyTypy::GDPR))){
            FlashMessages::setError('Musíte souhlasit s podmínkami spracování osobních údajů');
            return;
         }

//         @TODO zapojení existujícího zakaznika, může chybět email protože může být disabled
         $orgJazyky = OrganizaceJazyky::getAll($this->misto->id_organizace);
         $envJazyk = System::get()->getApplication()->environment->getJazyk();

         $zkzJazyk = null;

         foreach($orgJazyky as $jazykRow)
            if($jazykRow->getJazyk() === $envJazyk){
               $zkzJazyk = $envJazyk;
               break;
            }

         if(!$zkzJazyk)
            $zkzJazyk = Jazyky::from(OrganizaceJazyky::getPrimary($this->misto->id_organizace));

          $poptavka = (new NovaPoptavkaEvent())
            ->setLoggedZakaznik($this->zakaznik?->getOrganizaceZakaznik($this->misto->id_organizace))
            ->setOrganizaceRow($this->misto)
            ->setSouhlasy($souhlasy)
            ->preparePoptavka(array_merge($post, ['id_jazyk_zakaznik' => $zkzJazyk->value]))
            ->setZdrojPoptavky(OrganizaceNastaveni::getByOrganizace($this->misto->id_organizace)?->id_poptavkovy_zdroj)
            ->setTypAkce($post['id_event_type'] ?? null)
            ->setSystemTypAkce($post['id_system_event_type'] ?? null)
            ->setNotifikaceZakaznik()
            ->call();

         if(isset(OrganizaceNastaveni::getByOrganizace($this->misto->id_organizace)->id_prirazovany_personal)
            && ($personal = Personal::get(OrganizaceNastaveni::getByOrganizace($this->misto->id_organizace)->id_prirazovany_personal))
         ) {
            (new ChangePersonalPrirazeniEvent(
               $poptavka->getPoptavka(),
               [$personal->id_personal => $personal->id_personal])
            )->call();
         }

         FlashMessages::setSuccess('Poptávka byla úspěšně odeslána');
      }, DetailOrganizaceController::getUrlOrganizace($this->misto));
   }

   protected function prepareTemplate(Templater $templater) {
      $this->pageData['isMobil'] = System::get()->detect->isMobile();
      $this->pageData['isLogged'] = !!$this->zakaznik;
      $this->pageData['missingGdpr'] = !($this->zakaznik?->getOrganizaceZakaznik($this->misto->id_organizace)?->getZakaznikRow()->hasSouhlas(SouhlasyTypy::GDPR) ?: false);
      $this->pageData['organizace'] = $this->misto;
      $this->pageData['isVendor'] = $this->misto->isDodavatel();

      $this->appendKontaktniForm();
      $this->appendOdkazy();
      $this->appendOrganizacePortal();

      $this->setTitle(System::getTranslator()->layoutTranslate('Nová poptávka pro organizaci $1', $this->misto->nazev));
      $templater->addData($this->pageData);

      $this->addMetaTags(new PoptavkaPageMetaTags($this->misto));
   }

   private function appendOdkazy() {
      $this->pageData['urlOrganizace'] = DetailOrganizaceController::getUrlOrganizace($this->misto);
      $this->pageData['clientHpUrl'] = HomepageController::getUrl();
   }

   private function appendKontaktniForm() {
      $this->pageData['kontaktniForm'] = PoptavkaKontaktniFormComponent::getComponent()
         ->setLoggedZakaznik($this->zakaznik)
         ->setOrganizace($this->misto);
   }

   private function appendOrganizacePortal() :void {
      $this->pageData['portalInfo'] = OrganizacePortal::get($this->misto->id_organizace);
   }

   private OrganizaceRow $misto;
   private ?LoggedZakaznik $zakaznik = null;
   private array $pageData;
}