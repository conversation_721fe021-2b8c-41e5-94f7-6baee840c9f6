<?php namespace app\katalog\organizace\entita\poptavka\nova\component\kontaktni;

use app\front\organizace\model\organizace\OrganizaceRow;
use app\system\component\Component;
use app\system\helpers\telefon\input\TelefonInputComponent;
use app\system\users\zakaznici\LoggedZakaznik;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 03.09.2025 */
class PoptavkaKontaktniFormComponent extends Component
{

   function setData() :array {
      return [
         'jmeno' => $this->getJmeno(),
         'prijmeni' => $this->getPrijmeni(),
         'email' => $this->getEmail(),
         'telefonInputComponent' => $this->getTelefonInput(),
      ];
   }

   public function setOrganizace(OrganizaceRow $organizace) :static {
      $this->organizace = $organizace;
      return $this;
   }

   public function setLoggedZakaznik(?LoggedZakaznik $zakaznik) :static {
      $this->zakaznik = $zakaznik;
      return $this;
   }

   private function getTelefonInput() :?Html {
      if(!$this->zakaznik)
         return TelefonInputComponent::getInput('telefon');

      if($zkz = $this->zakaznik->getOrganizaceZakaznik($this->organizace->id_organizace)?->getZakaznikRow()){
         return TelefonInputComponent::getInput('telefon', $zkz->getPrimarniTelefon()->prefix, $zkz->getPrimarniTelefon()->telefon);
      }

      if($kZkz = $this->zakaznik->getKatalogZakaznik()?->getPrimarniTelefon()){
         return TelefonInputComponent::getInput(
            'telefon',
            $kZkz->prefix,
            $kZkz->telefon
         );
      }

      return TelefonInputComponent::getInput('telefon');
   }

   private function getJmeno() :?string {
      if(!$this->zakaznik)
         return null;

      if($zkz = $this->zakaznik->getOrganizaceZakaznik($this->organizace->id_organizace)?->getZakaznikRow())
         return $zkz->getFirstName();

      if($kZkz = $this->zakaznik->getKatalogZakaznik())
         return $kZkz->getFirstName();

      return null;
   }

   private function getPrijmeni() :?string {
      if(!$this->zakaznik)
         return null;

      if($zkz = $this->zakaznik->getOrganizaceZakaznik($this->organizace->id_organizace)?->getZakaznikRow())
         return $zkz->getLastName();

      if($kZkz = $this->zakaznik->getKatalogZakaznik())
         return $kZkz->getLastName();

      return null;
   }

   private function getEmail() :?string {
      if(!$this->zakaznik)
         return null;

      if($zkz = $this->zakaznik->getOrganizaceZakaznik($this->organizace->id_organizace)?->getZakaznikRow())
         return $zkz->email;

      if($kZkz = $this->zakaznik->getKatalogZakaznik())
         return $kZkz->email;

      return null;
   }

   protected ?LoggedZakaznik $zakaznik;
   protected OrganizaceRow $organizace;
}