{varType ?string $jmeno}
{varType ?string $prijmeni}
{varType ?string $email}
{varType Latte\Runtime\Html $telefonInputComponent}

<div class="border radius-card border-light-gray p-3 mt-4">
   <p><i class="bi bi-person-lines-fill me-2 text-primary"></i>{_'Kontaktní informace'}</p>
   <div class="row gap-2">
      <div class="col-md col-12">
         <div class="form-group">
            <label>{_'Jméno'}<span class="text-primary ms-1">*</span></label>
            <input type="text" n:attr="value: $jmeno"
                   class="form-control" id="jmeno_modal" name="jmeno" required>
         </div>
      </div>
      <div class="col-md col-12">
         <div class="form-group">
            <label>{_'Příjmení'}<span class="text-primary ms-1">*</span></label>
            <input type="text" n:attr="value: $prijmeni"
                   class="form-control" id="prijmeni_modal" name="prijmeni" required>
         </div>
      </div>
   </div>
   <div class="row mt-2 gap-2">
      <div class="col-md col-12">
         <div class="form-group">
            <label for="email">{_'Email'}<span class="text-primary ms-1">*</span></label>
            {* @TODO upravit pokud bude zkz přihlášený ale není veden u místa tak předvyplnit email přihlášeného *}
            <input type="text" n:attr="value: $email, readonly: !!$email"
                   class="form-control" id="email" name="email" required>
         </div>
      </div>
      <div class="col-md col-12">
         {$telefonInputComponent}
      </div>
   </div>
</div>