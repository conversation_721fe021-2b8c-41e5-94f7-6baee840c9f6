{varType bool $isMobil}
{varType app\front\organizace\model\organizace\OrganizaceRow $organizace}
{varType null $registrovany}
{varType app\katalog\organizace\entita\poptavka\nova\component\kontaktni\PoptavkaKontaktniFormComponent $kontaktniForm}
{varType app\system\model\organizace\portal\OrganizacePortalRow $portalInfo}

{varType bool $isVendor}
{varType bool $isLogged}
{varType bool $missingGdpr}
{varType string $urlOrganizace}
{varType string $clientHpUrl}

<div class="container mt-4">
   <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
         <li n:if="$isLogged" class="breadcrumb-item">
            <a class="text-black" href="{$clientHpUrl}"><i class="align-middle me-2 bi bi-houses"></i></a>
         </li>
         <li class="breadcrumb-item"><a class="text-black" href="{$urlOrganizace}">{$organizace->nazev}</a></li>
         <li class="breadcrumb-item active text-primary" aria-current="page">{_'Nová poptávka'}</li>
      </ol>
   </nav>

   <section class="mt-md-0 mt-3 p-md-3 p-2">
      <div class="row d-flex justify-content-center">
         <div n:if="$portalInfo->getProfilPhotoFullSrc() !== null" class="col-lg-5 col-12">
            <img src="/{$portalInfo->getProfilPhotoFullSrc()}" class="w-100 radius-card shaddow-light">
         </div>

         <div n:class="'col-12 col-lg mx-auto d-table h-100', $portalInfo->getProfilPhotoFullSrc() === null ? 'col-lg-8'">
            <div class="d-table-cell align-middle">
               <div class="mt-4 mt-lg-0">
                  <small class="text-uppercase text-primary"><i class="bi bi-check-circle me-1"></i>{_'Nová poptávka'}</small>
                  <h2>
                     {$organizace->nazev}
                  </h2>
               </div>

               <div class="m-md-1">
                     <form class="form" id="newLeadForm" method="post" enctype="multipart/form-data" autocomplete="off">
                        <div>
                           <div class="border border-light-gray radius-card px-3 py-3 mt-4">
                              <div class="row">
                                 <div class="col-md col-12 align-content-center pb-1 pb-xl-0">
                                    <span><i class="bi bi-people me-2 text-primary"></i>{_'Typ akce a počet hostů'}</span>
                                 </div>
                                 <div class="col-md-8 col-lg-7 col-12">
                                    <div class="input-group" role="group">
                                       {if !empty($organizace->getAkceTypySelectData())}
                                          <select name="id_event_type" id="id_event_type" class="form-select w-25">
                                             {foreach $organizace->getAkceTypySelectData() as $id_akce => $nazev}
                                                <option value="{$id_akce}">{$nazev}</option>
                                             {/foreach}
                                             <option value="0">{_'Event'}</option>
                                          </select>
                                       {else}
                                          <select name="id_system_event_type" id="id_event_type" class="form-select w-25">
                                             {foreach $organizace->getSystemAkceTypySelectData() as $id_akce => $nazev}
                                                <option value="{$id_akce}">{$nazev}</option>
                                             {/foreach}
                                             <option value="0">{_'Event'}</option>
                                          </select>
                                       {/if}

                                       <input type="number" class="form-control" id="pocet_hostu" name="pocet_hostu" min="1" data-mask="0#" pattern="[0-9]*" inputmode="numeric">
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="mt-4">
                              <div class="border border-light-gray radius-card px-3 py-2">
                                 <div class="row mt-3">
                                    <div class="col-md col-12 align-content-center">
                                       <span><label><i class="bi bi-circle-fill me-2 text-primary"></i></label>{_'Začátek'}</span>
                                    </div>
                                    <div class="col-md-8 col-lg-6 col-12">
                                       <div class="input-group">
                                          {if !$isMobil}
                                             <input id="datum_zacatek" type="text" class="form-control datetimepicker-input js-datepicker date" data-target="#datum_zacatek"
                                                    name="datumStart" placeholder="DD.MM.YYYY" data-toggle="datetimepicker"
                                                    data-datepicker-min="{date('m/d/Y')}" data-target-input="nearest">
                                             <input id="time_start" type="text" class="form-control js-timepicker text-end" data-target="#time_start"
                                                    name="timeStart" pattern="{app\system\helpers\RegexPatterns::TIME}" placeholder="HH:MM" data-toggle="datetimepicker">
                                          {else}
                                             <input id="datum_zacatek" type="date" class="form-control datetimepicker-input" name="datumStart" autocomplete="off" min="{date('Y-m-d')}">
                                             <input id="time_start" type="time" class="form-control text-end" autocomplete="off" name="timeStart">
                                          {/if}
                                       </div>
                                    </div>
                                 </div>
                                 <div class="row mt-1">
                                    <div class="col-md col-12 align-content-center">
                                       <span><label><i class="bi bi-circle me-2 text-primary"></i></label>{_'Konec'}</span>
                                    </div>
                                    <div class="col-md-8 col-lg-6 col-12">
                                       <div class="input-group">
                                          {if !$isMobil}
                                             <input id="datum_konec" type="text" class="form-control datetimepicker-input date js-datepicker" data-target="#datum_konec"
                                                    name="datumEnd" placeholder="DD.MM.YYYY" data-toggle="datetimepicker"
                                                    data-datepicker-min="{date('m/d/Y')}" data-target-input="nearest">
                                             <input id="time_konec" type="text" class="form-control js-timepicker text-end" data-target="#time_konec"
                                                    name="timeEnd" pattern="{app\system\helpers\RegexPatterns::TIME}"
                                                    placeholder="HH:MM" data-toggle="datetimepicker">
                                          {else}
                                             <input id="datum_konec" type="date" class="form-control datetimepicker-input" name="datumEnd" autocomplete="off" min="{date('Y-m-d')}">
                                             <input id="time_konec" type="time" class="form-control text-end" autocomplete="off" name="timeEnd">
                                          {/if}
                                       </div>
                                    </div>
                                 </div>
                                 <div class="text-end mt-2">
                                    <div class="input-group justify-content-end">
                                       <label class="align-content-center mb-0 me-3">{_'Je datum pevné?'}</label>
                                       <div class="form-check form-check-inline">
                                          <input class="form-check-input" type="radio" name="pevneDatumMoznost" id="pevneDatumMoznost1" value="1">
                                          <label class="form-check-label" for="pevneDatumMoznost1">{_'Ano'}</label>
                                       </div>
                                       <div class="form-check form-check-inline">
                                          <input class="form-check-input" type="radio" name="pevneDatumMoznost" id="pevneDatumMoznost0" value="0">
                                          <label class="form-check-label" for="pevneDatumMoznost0">{_'Ne'}</label>
                                       </div>
                                    </div>
                                 </div>
                              </div>
                           </div>

                           {$kontaktniForm->renderHtml()}

                           <div class="row mt-2" n:if="$isVendor">
                              <div class="col-12">
                                 <div class="form-group">
                                    <label>{_'Místo konání'}</label>
                                    <input type="text" class="form-control" name="required-venue" maxlength="140">
                                 </div>
                              </div>
                           </div>
                           <div class="row mt-3">
                              <div class="col-12">
                                 <div class="form-group">
                                    <p><i class="bi bi-pen me-2 text-primary"></i>{_'Další informace'}</p>
                                    <textarea type="text" class="form-control" id="poznamka" name="poznamka"
                                              placeholder="{_'Napište nám prosím hrubou představu o průběhu akce'}"
                                              rows="5"></textarea>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <div class="row d-flex justify-content-center mt-3">
                              <div n:if="!$isLogged || $missingGdpr" class="row text-center mb-2">
                                 <div class="d-flex justify-content-center gap-1 form-check form-switch">
                                    <input class="form-check-input" type="checkbox" role="switch" name="uzivatelske_souhlasy[{app\system\model\organizace\zakaznici\souhlasy\SouhlasyTypy::GDPR->value}]"
                                           id="acceptGdpr" n:attr="checked: true" required>
                                    <label class="align-content-end form-check-label text-secondary" for="acceptGdpr">
                                       {_'Souhlasím se zpracováním osobních údaju.'}
                                    </label>
                                 </div>
                              </div>
                              <div n:if="!$isLogged" class="row text-center mb-2">
                                 <div class="d-flex justify-content-center gap-1 form-check form-switch">
                                    <input class="form-check-input" type="checkbox" role="switch" name="uzivatelske_souhlasy[{app\system\model\organizace\zakaznici\souhlasy\SouhlasyTypy::MARKETING->value}]"
                                           id="acceptMarketing" n:attr="checked: false">

                                    <label class="align-content-end form-check-label text-secondary" for="acceptMarketing">
                                       {_'Souhlasím se zpracováním údajů pro marketingové účely.'}
                                    </label>
                                 </div>
                              </div>
                              <div class="row js-changing-text text-center" style="display: none">
                                 <small>{_'Probíhá odesílání poptávky'}...</small>
                              </div>
                              <input n:if="app\system\recaptcha\Recaptcha::isEnabled()" type="hidden" name="{app\system\recaptcha\Recaptcha::POST_PARAM_NAME}">
                              <div class="row d-flex justify-content-center">
                                 <button type="submit" name="btnCreateNovaPoptavka" class="btn btn-primary btn-lg w-100 shaddow-hover px-md-5">
                                    <div class="spinner-border spinner-border-sm text-secondary me-2 js-loader" role="status" style="display: none">
                                       <span class="visually-hidden">{_'Načítání'}...</span>
                                    </div>
                                    {_'Odeslat poptávku'}
                                 </button>
                              </div>
                           </div>
                     </form>
               </div>
            </div>
         </div>
      </div>
   </section>

   <div class="row text-center mt-2">
      <small>{_'Pole označené $1 jsou povinná', '<span class="text-primary ms-1">*</span>'}</small>
   </div>
   <div class="row text-center mt-2">
      <small>{_'Nedaří se vám odeslat poptávku?'} <a href="mailto:<EMAIL>?subject=Problem->lead">{_'dejte nám vědět'}</a></small>
   </div>
   <div class="row text-center my-2">
      <small><a href="https://qvamp.cz/podminky-pouzivani-klientskeho-portalu" target="_blank">{_'Podmínky používání aplikace'}</a></small>
   </div>
</div>

<script n:if="app\system\recaptcha\Recaptcha::isEnabled()" src="https://www.google.com/recaptcha/enterprise.js?render={app\system\recaptcha\Recaptcha::getPublicKey()}" async defer></script>

<script n:if="app\system\recaptcha\Recaptcha::isEnabled()">
   $(function() {
      waitingTelefonFull = false;
      const body = $('body');
      const logginText = body.find('div.js-changing-text');
      const loader = body.find('div.js-loader');
      const telefonInput = $('input[name=telefon]');
      let cancelled = false;

      const form = $('form#newLeadForm');
      const inputDateStart = form.find('input[name=datumStart]');
      const inputDateEnd = form.find('input[name=datumEnd]');

      const validator = form.validate({
         rules: {
            'telefon': {
               telefonFull: true,
            },
            'email': {
               email: true,
            },
            'datumStart': {
               date: false,
               required: function(element) {
                  return inputDateEnd.val().trim() !== "";
               },
               dateBefore: inputDateEnd[0],
            },
            'datumEnd': {
               date: false,
            },
            'pevneDatumMoznost': {
               required: function(element) {
                  return inputDateEnd.val().trim() !== '' || inputDateStart.val().trim() !== '';
               },
            }
         },
         onkeyup: function( element, event ) {
            // Zamezení kontroly při psaní čísla
            if(element.name === 'telefon'){
               return;
            }

            // Avoid revalidate the field when pressing one of the following keys
            // Shift       => 16
            // Ctrl        => 17
            // Alt         => 18
            // Caps lock   => 20
            // End         => 35
            // Home        => 36
            // Left arrow  => 37
            // Up arrow    => 38
            // Right arrow => 39
            // Down arrow  => 40
            // Insert      => 45
            // Num lock    => 144
            // AltGr key   => 225
            var excludedKeys = [
               16, 17, 18, 20, 35, 36, 37,
               38, 39, 40, 45, 144, 225
            ];

            if ( event.which === 9 && this.elementValue( element ) === "" || $.inArray( event.keyCode, excludedKeys ) !== -1 ) {
               return;
            }

            if ( element.name in this.submitted || element.name in this.invalid ) {
               this.element( element );
            }
         },
      })

       if(telefonInput.val())
           telefonInput.valid();

      body.on('click', 'button[name=btnCreateNovaPoptavka]', function(e) {
         if(e.isDefaultPrevented())
            return;

         e.preventDefault();
         cancelled = false;

         const rawBtn = this;
         const btn = $(this);

         const form = btn.closest('form');
         const rawForm = form[0];

         if(!form.valid() || !validator.valid())
            return;

         if(waitingTelefonFull){
            form.find('input[name=telefon]').focus();
            return;
         }

         const input = form.find('input[name=' + {app\system\recaptcha\Recaptcha::POST_PARAM_NAME} + ']');

         btn.prop('disabled', true);

         logginText.show();
         loader.show();

         window.addEventListener('beforeunload', beforeUnloadHandler);

         grecaptcha.enterprise.ready(async () => {
            grecaptcha.enterprise.execute({app\system\recaptcha\Recaptcha::getPublicKey()}, { action: 'NEW_LEAD' }).then(function(token) {
               window.removeEventListener('beforeunload', beforeUnloadHandler);

               if(!document.contains(form[0])){
                  alert('Login error, form not found, please try it again');
                  return;
               }

               if(cancelled){
                  logginText.hide();
                  loader.hide();

                  btn.prop('disabled', null);
                  return;
               }

               input.val(token);
               btn.prop('disabled', null);
               rawForm.requestSubmit(rawBtn);
               btn.prop('disabled', true);
            })
         });
      }).on('change', 'input[name=telefon]', function(e) {
         waitingTelefonFull = false;
         $(this).valid();
      });

      const beforeUnloadHandler = (event) => {
         // Recommended
         event.preventDefault();
         cancelled = true;
         // Included for legacy support, e.g. Chrome/Edge < 119
         event.returnValue = true;
      };
   })
</script>