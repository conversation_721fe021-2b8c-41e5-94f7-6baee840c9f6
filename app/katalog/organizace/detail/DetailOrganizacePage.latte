{varType app\katalog\organizace\detail\DetailOrganizacePage $_page}
{varType app\front\organizace\model\organizace\OrganizaceRow $organizace}

{block breadcrumbs}
   {varType string $homepageUrl}

   <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
         <li class="breadcrumb-item"><a class="text-black" href="{$homepageUrl}"><i
                       class="align-middle me-2 bi bi-houses"></i></a></li>
         <li class="breadcrumb-item active text-primary" aria-current="page">{$organizace->nazev}</li>
      </ol>
   </nav>
{/block}

{block topNavigation}
   {varType app\katalog\organizace\detail\component\top\OrganizaceTopNavigationComponent $topNavigation}

   {$topNavigation->renderHtml()}
{/block}

{block galerie}
   {varType app\katalog\organizace\detail\component\galerie\OrganizaceGalerieComponent $galerie}

   {$galerie->renderHtml()}
{/block}

{block informace}
   {varType string $informaceElementID}
   {varType app\katalog\organizace\detail\component\info\OrganizaceInformaceComponent $informace}

   <div id="{$informaceElementID}" class="mt-4">
      {$informace->renderHtml(sprintf('template-%s', strtolower($organizace->getVersion()->name)))}
   </div>
{/block}

<hr class="my-5">

{*{block promoakce}*}
{*   {varType string $promoakceElementID}*}
{*   {varType app\katalog\organizace\detail\component\promo\OrganizacePromoakceComponent $promoakce}*}

{*   {ifset $promoakce}*}
{*      <div id="{$promoakceElementID}" class="mt-5">*}
{*         {$promoakce->renderHtml()}*}
{*      </div>*}
{*   {/ifset}*}
{*{/block}*}

{*<hr class="my-5">*}

{if $organizace->getVersion()->isVenue()}
   {block prostory}
      {varType string $prostoryElementID}
      {varType app\katalog\organizace\detail\component\prostory\OrganizaceProstoryComponent $prostory}

      <div id="{$prostoryElementID}" class="mt-5">
         {$prostory->renderHtml()}
      </div>
   {/block}

   {block vybaveni}
      {varType string $vybaveniElementID}
      {varType app\katalog\organizace\detail\component\vybaveni\OrganizaceVybaveniComponent $vybaveni}

      <div n:ifset="$vybaveni" id="{$vybaveniElementID}" class="mt-5 mx-3 mx-lg-5">
         {$vybaveni->renderHtml()}
      </div>
   {/block}

   <hr class="my-5">
{/if}

{block sluzby}
   {varType string $sluzbyElementID}
   {varType app\katalog\organizace\detail\component\sluzby\OrganizaceSluzbyComponent $sluzby}

   <div n:ifset="$sluzby" id="{$sluzbyElementID}" class="mt-5">
      {$sluzby->renderHtml()}
   </div>

   <hr n:ifset="$sluzby" class="my-5">
{/block}

{block zakaznickeRecenze}
   {varType string $zakaznickeRecenzeElementID}
   {varType app\katalog\organizace\detail\component\recenze\zakaznik\OrganizaceRecenzeComponent $zakaznickeRecenze}

   <div n:ifset="$zakaznickeRecenze" id="{$zakaznickeRecenzeElementID}" class="mt-5">
      {$zakaznickeRecenze->renderHtml()}
   </div>
   <hr n:ifset="$zakaznickeRecenze" class="my-5">
{/block}

{*{block qvampRecenze}
   {varType string $qvampRecenzeElementID}
   {varType app\katalog\organizace\detail\component\recenze\qvamp\OrganizaceQvampRecenzeComponent $qvampRecenze}

   <div n:ifset="$qvampRecenze" id="{$qvampRecenzeElementID}" class="mt-4">
      {$qvampRecenze->renderHtml()}
   </div>
{/block}

<hr class="my-5">*}

{if $organizace->getVersion()->isVenue()}
   {block mapa}
      {varType string $mapaElementID}
      {varType app\katalog\organizace\detail\component\mapa\OrganizaceMapaComponent $mapa}

      <div n:ifset="$mapa" id="{$mapaElementID}" class="mt-4">
         {$mapa->renderHtml()}
      </div>
   {/block}
{/if}

{block dalsiInformace}
   {varType string $dalsiInformaceElementID}
   {varType app\katalog\organizace\detail\component\info\more\OrganizaceDalsiInformaceComponent $dalsiInformace}

   <div id="{$dalsiInformaceElementID}" class="mt-4 mb-7">
      {$dalsiInformace->renderHtml()}
   </div>
{/block}

