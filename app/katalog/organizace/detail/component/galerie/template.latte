{varType app\system\model\organizace\portal\galerie\OrganizacePortalGalerieRow[] $galerieFotky}

<div n:if="!empty($galerieFotky)" class="container px-0">
   <div class="row row-cols-2 row-cols-sm-2 row-cols-md-4 row-cols-lg-4 g-3 gallery">
      {foreach $galerieFotky as $fotka}
         <div n:tag-if="$iterator->counter > 4" class="d-none">
            <div class="col">
               <a href="/{$fotka->getPhotoFullSrc()}"
                  class="h-100 border-0 shadow-sm overflow-hidden"
                  data-fancybox="org-gallery">

                  <!-- čtvercový náhled s cover ořezem -->
                  <div class="ratio ratio-1x1">
                     <img
                             src="/{$fotka->getPhotoFullSrc()}"
                             class="w-100 h-100 object-fit-cover"
                             alt="{$fotka->name}"
                             loading="lazy"
                     >
                  </div>
               </a>
            </div>
         </div>
      {/foreach}
   </div>
</div>

<div n:if="empty($galerieFotky)" class="container px-0">
   <div class="row row-cols-2 row-cols-sm-3 row-cols-md-3 row-cols-lg-3 g-3 gallery">
      {for $i = 1; $i <= 4; $i++}
         <div class="col">
            <a href="/files/layout/img/not_found_product_photo.png"
               class="card h-100 border-0 shadow-sm overflow-hidden"
               data-fancybox="org-gallery"
               data-caption="{_'Fotka bude doplněna'}">

               <!-- čtvercový náhled s cover ořezem -->
               <div class="ratio ratio-1x1">
                  <img
                          src="/files/layout/img/not_found_product_photo.png"
                          class="w-100 h-100 object-fit-cover"
                          alt="{_'Fotka bude doplněna'}"
                          loading="lazy"
                  >
               </div>
            </a>
         </div>
      {/for}
   </div>
</div>

{*@TODO přesunout do main.scss a module.js *}
<!-- Fancybox -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css" rel="stylesheet">
<script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script>

<script>
   document.addEventListener('DOMContentLoaded', function () {
      const fancyBoxElement = $('[data-fancybox="gallery"]');
      // Inicializace Fancyboxu pro existující obrázky
      fancyBoxElement.fancybox({
         buttons: [
            "zoom",
            "slideShow",
            "thumbs",
            "close"
         ],
         youtube: {
            controls: 1,
            showinfo: 0
         }
      });

      // Přidání události na tlačítko pro zobrazení lightboxu s kompletní galerií
      const showMoreImagesButton = document.getElementById('showMoreImages');
      if (showMoreImagesButton) {
         showMoreImagesButton.addEventListener('click', function () {
            // Otevřít Fancybox s celou galerií
            $.fancybox.open(fancyBoxElement, {
               loop: true
            });
         });
      }
   });
</script>