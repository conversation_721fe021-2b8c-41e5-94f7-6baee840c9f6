{varType app\front\organizace\model\organizace\OrganizaceRow $organizace}
{varType app\system\model\recenze\RecenzeRow[] $recenze}
{varType float $hodnoceniPrumer}
{varType app\katalog\organizace\detail\component\recenze\zakaznik\modal\PrehledRecenzeOrganizaceModal $recenzeModal}
{varType int $pocetRecenzi}

<h2>
   <i class="align-middle px-2 py-1 me-2 bi bi-chat-square-quote bg-primary radius-icon icon-shadow text-white"></i>{_'Hodnocení zákazníků'}
</h2>

<div n:if="!empty($recenze)" class="row mt-5">
   <div class="col-md-4">
      <div class="col-auto">
               <span class="h1 text-primary me-2 px-2 py-1 border border-primary radius-card"><i
                          class="bi bi-star me-1"></i>{$hodnoceniPrumer}</span>
         <small>{$pocetRecenzi} {_'hodnocení'}</small>
         <button class="btn btn-outline-secondary shaddow-hover mt-5 mb-2 d-block" type="button" n:attr="$recenzeModal::getShowAttributes($organizace->id_organizace)">
            {_'Zobrazit všechna hodnocení'}
            <i class="bi bi-arrow-up-right-circle ms-1"></i></button>
         <div class="d-block">
            <small class="text-black-50" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-custom-class="custom-tooltip" data-bs-title="{_'Hodnocení může přidat pouze uživatel, který uskutečnil akci přes Qvamp Booking Portal. Každé hodnocení je ověřeno a slouží jako férová zpětná vazba pro ostatní uživatele.'}"><i class="bi bi-info-circle me-1"></i>{_'Jak funguje hodnocení?'}</small>
         </div>
      </div>
   </div>
   <div class="col-md-8">
      <div id="scrollspyReviews" class="scrollspy-reviews" style="min-height: 150px;max-height: 400px; overflow-y: scroll;">
         {foreach $recenze as $r}
            <div class="card border border-light-gray radius-card p-3 mt-2 shaddow-hover">
               <div class="row d-flex justify-content-between">
                  <div class="col">
                     <span class="lead">{$r->getZakaznik()->full_name}</span>
                     <small>• {_'Datum hodnocení'}: {$r->created|date: 'j.n.Y'} </small>
                     <small class="text-muted small"><i class="bi bi-patch-check-fill text-primary ms-2 me-1"></i>{_'Ověřené hodnocení'}</small>
                  </div>
                  <div class="col-auto">
                     <span class="me-2 text-primary lead"><i class="bi bi-star me-1"></i>{$r->pocet_bodu}</span>
                  </div>
{*                  @TODO dočasně schováno připravit modal pro detail jednotlivé recenze*}
{*                  <div class="col-auto">*}
{*                     <a href="#" class="text-secondary display-6" data-bs-toggle="modal"*}
{*                        data-bs-target="#modalReview" aria-controls="modalReview">*}
{*                        <i class="bi bi-arrow-up-right-circle hover-icon-arrow"></i>*}
{*                     </a>*}
{*                  </div>*}
               </div>
               <div class="row mt-3">
                  <p class="text-truncate">
                     <i class="bi bi-plus-square-fill text-primary me-2"></i>{$r->popisek_pozitivni}
                  </p>
               </div>
            </div>
         {/foreach}
      </div>
   </div>
</div>

<div n:else class="row mt-5">
   <div class="col">
      {_'Zatím žádná recenze'}
   </div>
</div>

{if !empty($recenze)}
   {$recenzeModal->render()|noescape}
{/if}