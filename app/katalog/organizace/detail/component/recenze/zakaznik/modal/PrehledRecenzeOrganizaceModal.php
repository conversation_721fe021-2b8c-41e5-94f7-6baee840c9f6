<?php namespace app\katalog\organizace\detail\component\recenze\zakaznik\modal;

use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\system\component\Templater;
use app\system\model\recenze\factory\OrganizaceRecenzeFactory;
use app\system\model\recenze\Recenze;
use app\system\model\recenze\RecenzeOtazkyEnum;
use app\system\modul\modal\AjaxModal;

class PrehledRecenzeOrganizaceModal extends AjaxModal
{

   public function getTitleName(): string {
      return 'Všechna hodnocení';
   }

   public function prepareAjaxData() :void {
      $this->organizace = Organizace::getMisto($_POST['slug']);
   }

   public function prepareModal(Templater $templater) :void {
      $templater->addData([
         'recenze' => OrganizaceRecenzeFactory::getForOrganizace($this->organizace->id_organizace),
         'recenzeOtazky' => RecenzeOtazkyEnum::getForTyp($this->organizace->getVersion()),
         'hodnoceniPrumer' => Recenze::getRecenzeStatistika($this->organizace->id_organizace)->prumer,
         'pocetRecenzi' => Recenze::getRecenzeStatistika($this->organizace->id_organizace)->pocet,
      ]);
   }

   private OrganizaceRow $organizace;
}