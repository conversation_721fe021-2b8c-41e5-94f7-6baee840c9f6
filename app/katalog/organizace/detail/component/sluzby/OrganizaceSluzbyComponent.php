<?php namespace app\katalog\organizace\detail\component\sluzby;

use app\front\organizace\model\organizace\OrganizaceRow;
use app\katalog\organizace\detail\component\sluzby\modal\DetailSluzbyModal;
use app\System;
use app\system\component\Component;
use app\system\model\organizace\sluzby\OrganizaceSluzby;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 12.08.2025 */
class OrganizaceSluzbyComponent extends Component
{

   function setData() :array {
      $envJazyk = System::get()->getApplication()->environment->getJazyk();

      return [
         'clientLang' => $envJazyk,
         'organizace' => $this->organizace,
         'sluzby' => OrganizaceSluzby::getAllInLanguage(
            $envJazyk,
            $this->organizace->id_organizace,
         ),
         'sluzbaDetailModal' => DetailSluzbyModal::init(),
      ];
   }

   public function setOrganizace(OrganizaceRow $organizace) :OrganizaceSluzbyComponent {
      $this->organizace = $organizace;
      return $this;
   }

   private OrganizaceRow $organizace;
}