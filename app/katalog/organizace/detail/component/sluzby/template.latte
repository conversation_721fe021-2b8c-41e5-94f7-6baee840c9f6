{varType app\system\model\translator\Jazyky $clientLang}
{varType app\front\organizace\model\organizace\OrganizaceRow $organizace}
{varType app\system\model\organizace\sluzby\OrganizaceSluzbyRow[] $sluzby}
{varType app\katalog\organizace\detail\component\sluzby\modal\DetailSluzbyModal $sluzbaDetailModal}


<h2>
   <i class="align-middle px-2 py-1 me-2 bi bi-boxes bg-primary radius-icon icon-shadow text-white"></i>{_'Služby'}
</h2>
{*<div class="row my-3">*}
{*   <div class="col-auto">*}
{*      <label>členové týmu</label>*}
{*      <p>12</p>*}
{*   </div>*}
{*   <div class="col-auto">*}
{*      <label>Vzdálenost poskytování služeb</label>*}
{*      <p>100 km</p>*}
{*   </div>*}
{*</div>*}

<div class="mt-5">

   {foreach $sluzby as $sluzba}
      {var $thumb = $sluzba->getThumbnail()}
      {var $texty = $sluzba->getTexty($clientLang)}
      {var $fallback = $sluzba->getTexty($organizace->getPrimaryJazyk())}
      {var $altText = $texty?->nazev ?? $fallback?->nazev ?? 'Neznámý název'}

      <a href="#" n:attr="$sluzbaDetailModal::getShowAttributes($sluzba->id)" class="card border border-light-gray radius-card shaddow-hover px-0 mb-3">
         <div class="row gap-2 p-0">
            <div class="col-md-3 col-lg-2 col-12 d-flex justify-content-center align-items-center">
               {if $thumb !== null}
                  <img
                          src="/{$thumb->getFullSrc($sluzba->id_organizace)}"
                          alt="{$altText}"
                          class="w-100 img-fit radius-card-catalog d-block d-sm-none h-100 object-fit-cover">
                  <img
                          src="/{$thumb->getFullSrc($sluzba->id_organizace)}"
                          alt="{$altText}"
                          class="w-100 img-fit radius-card-catalog d-none d-sm-block object-fit-cover service-card">
               {else}
                  <div class="service-card align-content-center">
                     <i class="bi bi-camera display-3"></i>
                  </div>
               {/if}
            </div>
            <div class="col-12 col-md-7 align-content-center px-5 px-lg-2">
               <h5 class="lead text-truncate">
                  {if !empty($texty?->nazev)}
                     {$texty->nazev}
                  {elseif !empty($fallback?->nazev)}
                     {$fallback->nazev}
                  {else}
                     {_ 'Neznámý název'}
                  {/if}
               </h5>
{*               <div n:ifset="$sluzba->getSystemSluzba()->nazev">*}
{*                  <i class="bi bi-music-note-beamed me-1"></i>*}
{*                  {_'Typ služby'}: {$sluzba->getSystemSluzba()->nazev}*}
{*               </div>*}
               <div n:if="$texty?->popis">
                  <small class="d-block text-truncate" style="height: 18px">
                     {$texty->getPopisTextTruncate()|noescape}
                  </small>
               </div>
            </div>
            <div class="col-md col-12 text-end align-content-center py-3">
               <span class="text-secondary display-6 m-3">
                  <i class="bi bi-arrow-up-right-circle hover-icon-arrow"></i>
               </span>
            </div>
         </div>
      </a>
   {/foreach}
</div>

{$sluzbaDetailModal->render()|noescape}