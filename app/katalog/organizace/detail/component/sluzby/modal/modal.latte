{varType app\system\model\translator\Jazyky $lang}
{varType app\system\model\translator\Jazyky $primaryLang}
{varType app\system\model\organizace\sluzby\OrganizaceSluzbyRow $sluzba}

{var $texty = $sluzba->getTexty($lang) ?: $sluzba->getTexty($primaryLang)}

<div class="container">
   <div class="row gap-2 d-flex justify-content-between">
      <h4>
         <i class="align-middle px-2 py-1 me-2 bi bi-box bg-primary radius-icon icon-shadow text-white"></i>{$texty->nazev}
      </h4>
   </div>
   <div class="row d-flex">
      {foreach $sluzba->getPhotos() as $fotka}
         <div class="align-items-center col-lg-4 col-md-4 col-6 d-flex">
            <img src="/{$fotka->getFullSrc($sluzba->id_organizace)}"
                 alt="{$texty->nazev}-{$iterator->counter}"
                 class="radius-card w-100 mt-3"
                 style="height: 300px; object-fit: cover;">
         </div>
      {/foreach}
   </div>
   {if $texty->popis}
   <div class="row mt-4">
      {$texty->popis|noescape}
   </div>
   {/if}
   <div class="row mt-2">
      <small class="d-block text-truncate">
         <i class="bi bi-compass me-1"></i>
         {foreach $sluzba->getAkceTypy() as $typ}
            {$typ->nazev}{sep}, {/sep}
         {/foreach}
      </small>
   </div>
</div>