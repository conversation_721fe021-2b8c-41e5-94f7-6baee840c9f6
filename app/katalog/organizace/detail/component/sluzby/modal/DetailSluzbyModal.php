<?php namespace app\katalog\organizace\detail\component\sluzby\modal;

use app\front\organizace\model\organizace\Organizace;
use app\front\organizace\model\organizace\OrganizaceRow;
use app\System;
use app\system\component\Templater;
use app\system\model\organizace\sluzby\OrganizaceSluzby;
use app\system\model\organizace\sluzby\OrganizaceSluzbyRow;
use app\system\model\translator\Jazyky;
use app\system\modul\modal\AjaxModal;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 27.08.2025 */
class DetailSluzbyModal extends AjaxModal
{

   public function getTitleName() :string {
      return 'Detail služby';
   }

   public function prepareAjaxData() :void {
      $slug = $_POST['slug'];
      $this->lang = System::get()->getApplication()->environment->getJazyk();
      $this->sluzba = OrganizaceSluzby::getAllInLanguageById($this->lang, $slug);
      $this->organizace = Organizace::getMisto($this->sluzba->id_organizace);
   }

   public function prepareModal(Templater $templater) {
      $templater->addData([
         'lang' => $this->lang,
         'primaryLang' => $this->organizace->getPrimaryJazyk(),
         'sluzba' => $this->sluzba,
      ]);
   }


   protected OrganizaceSluzbyRow $sluzba;
   protected OrganizaceRow $organizace;
   protected Jazyky $lang;
}