{varType app\front\organizace\model\organizace\OrganizaceRow $organizace}
{varType app\system\model\recenze\RecenzeStatistikaRow $recenzeStatistika}
{varType string $recenzeBlockID}
{varType bool $isOblibena}
{varType bool $isLogged}
{varType string $organizaceLoginUrl}
{varType string $novaPoptavkaUrl}

<div class="row sticky-top pt-3 bg-glass radius-card pb-3 pb-md-0">
   <div class="col">
      <div class="row trigger-element">
         <h1 class="fw-bold">{$organizace->nazev}, {$organizace->getKontakt()->mesto}, {$organizace->getKontakt()->getStat()->getTranslatedName()}</h1>
      </div>
      <div class="row mb-3">
         <a n:tag-if="$recenzeStatistika->prumer > 0"
                 class="lead cursor-pointer"
                 href="#{$recenzeBlockID}"
         >
            <span n:tag-if="$recenzeStatistika->prumer === 0.0" class="lead">
               <i class="bi bi-star-fill me-1 text-primary"></i>
               {if $recenzeStatistika->prumer > 0}
                  {$recenzeStatistika->prumer} <small class="text-black-500 text-sm">/ {_'$1 hodnocení', $recenzeStatistika->pocet}</small>
               {else}
                  {_'Zatím bez hodnocení'}
               {/if}
            </span>
         </a>
      </div>
   </div>
   <div class="col-md-auto">
      <div class="justify-content-center">
         <div class="btn-group shaddow p-0">

            <a href="{$novaPoptavkaUrl}" class="btn btn-primary fw-bolder w-auto shaddow-hover">
               <i class="bi bi-calendar4-range me-2"></i>{_'Cena a dostupnost'}
            </a>

            <a n:if="!$isLogged"
                    href="{$organizaceLoginUrl}"
                    class="btn btn-outline-primary w-auto shaddow-hover"
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    data-bs-custom-class="custom-tooltip"
                    data-bs-title="{_'Přihlásit se do organizace'}"
            >
               <i class="bi bi-person-lock me-1"></i>
            </a>

            <button n:class="'btn w-auto shaddow-hover', $isOblibena ? 'btn-primary' : 'btn-outline-primary'"
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    data-bs-custom-class="custom-tooltip"
                    data-bs-title="{_'Uložit do oblíbených'}"
                    data-katalog-favourite-place="{$organizace->id_organizace}"
            >
               <i class="bi bi-heart me-1"></i>
            </button>
         </div>
      </div>
   </div>
</div>