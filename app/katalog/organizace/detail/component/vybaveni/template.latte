{varType app\front\organizace\model\organizace\OrganizaceRow $organizace}
{varType app\system\model\vlastnosti\VlastnostiNazevRow[] $vlastnosti}

{if count($vlastnosti) !== 0}
<div class="row my-2">
  {foreach $vlastnosti as $vlastnost}
      {if $iterator->counter <= 6}
         <div class="col-md-4">
            <i class="{$vlastnost->icon} me-1 "></i>{$vlastnost->nazev}
         </div>
      {/if}
   {/foreach}
</div>
<div class="row">
   <div class="mt-2">
      <button class="btn btn-sm btn-outline-secondary shaddow-hover w-auto"
              type="button"
              data-bs-toggle="modal"
              data-bs-target="#kompletVybaveniOrganizace"
      >
         {_'Zobrazit všechno vybavení'}
         <i class="bi bi-arrow-up-right-circle ms-1"></i>
      </button>
   </div>
   <!-- Vybavení modal -->
   <div class="modal modal-lg fade" id="kompletVybaveniOrganizace" tabindex="-1"
        aria-labelledby="kompletVybaveniOrganizaceLabel" aria-hidden="true">
      <div class="modal-dialog">
         <div class="modal-content">
            <div class="modal-header">
               <h5 class="modal-title">{_'Obecné vybavení'} - {$organizace->nazev}</h5>
               <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{_'Zavřít'}"></button>
            </div>
            <div class="modal-body">
               <div class="row">
                  {foreach $vlastnosti as $vlastnost}
                     <div class="col-md-4 mb-2">
                        <i class="{$vlastnost->icon} me-1 "></i>{$vlastnost->nazev}
                     </div>
                  {/foreach}
               </div>
            </div>
         </div>
      </div>
   </div>
</div>
{/if}