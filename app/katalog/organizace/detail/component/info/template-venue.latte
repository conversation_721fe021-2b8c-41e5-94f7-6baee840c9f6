{varType app\front\organizace\model\organizace\OrganizaceRow $organizace}
{varType app\system\model\tagy\SystemTagyNazvyRow[] $vybraneTagy}
{varType app\system\model\organizace\portal\OrganizacePortalRow $portalInfo}
{varType app\system\model\highlighty\OrganizaceHighlightRow[] $highlights}



<div id="highlightsVenue" class="container">
   <div class="row my-5">
      {foreach $highlights as $highlight}
         <div class="col-lg-4 my-2">
            <div class="row">
               <div class="col-auto">
                  <i class="bi {$highlight->icon} text-primary display-6"></i>
               </div>
               <div class="col">
                  <h2 class="lead">
                     {$highlight->title}
                  </h2>
                  <p>
                     {$highlight->text}
                  </p>
               </div>
            </div>
         </div>
      {/foreach}
   </div>
</div>

<div n:if="$portalInfo->getPopis() || !empty($vybraneTagy)"
        class="border border-light-gray radius-card p-3 shaddow-hover">
   {if $portalInfo->getPopis()}
      <div>{$portalInfo->getPopisKratky()}</div>
      <div class="mt-2">
         <a href="#" data-bs-toggle="modal" data-bs-target="#popisOrganizaceModal"><strong
                    class="text-secondary">{_'Zobrazit více'}<i
                       class="bi bi-arrow-up-right-circle ms-2"></i></strong></a>
      </div>

      <div class="modal modal-lg fade" id="popisOrganizaceModal" tabindex="-1"
           aria-labelledby="popisOrganizaceModalLabel" aria-hidden="true">
         <div class="modal-dialog">
            <div class="modal-content">
               <div class="modal-header">
                  <h5 class="modal-title">{_'Obecné informace'} - {$organizace->nazev}</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{_'Zavřít'}"></button>
               </div>
               <div class="modal-body">
                  {$portalInfo->getPopis()}
               </div>
            </div>
         </div>
      </div>
   {/if}

   <div n:if="!empty($vybraneTagy)" class="mt-3">
      {foreach $vybraneTagy as $tag}
         <small class="badge bg-secondary mx-1">{$tag->nazev}</small>
      {/foreach}
   </div>
</div>