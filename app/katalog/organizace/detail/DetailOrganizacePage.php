<?php namespace app\katalog\organizace\detail;

use app\front\organizace\model\organizace\OrganizaceRow;
use app\katalog\homepage\HomepageController;
use app\katalog\layout\BaseKatalogContentLayout;
use app\katalog\organizace\detail\component\galerie\OrganizaceGalerieComponent;
use app\katalog\organizace\detail\component\info\more\OrganizaceDalsiInformaceComponent;
use app\katalog\organizace\detail\component\info\OrganizaceInformaceComponent;
use app\katalog\organizace\detail\component\mapa\OrganizaceMapaComponent;
use app\katalog\organizace\detail\component\promo\OrganizacePromoakceComponent;
use app\katalog\organizace\detail\component\prostory\OrganizaceProstoryComponent;
use app\katalog\organizace\detail\component\recenze\qvamp\OrganizaceQvampRecenzeComponent;
use app\katalog\organizace\detail\component\recenze\zakaznik\OrganizaceRecenzeComponent;
use app\katalog\organizace\detail\component\sluzby\OrganizaceSluzbyComponent;
use app\katalog\organizace\detail\component\top\OrganizaceTopNavigationComponent;
use app\katalog\organizace\detail\component\vybaveni\OrganizaceVybaveniComponent;
use app\System;
use app\system\component\Templater;
use app\system\layout\client\head\meta\organizace\DetailOrganizaceMetaTags;
use app\system\model\katalog\KatalogOrganizaceRegion;
use app\system\model\organizace\sluzby\OrganizaceSluzby;

/** Created by Kryštof Czyź. Date: 11.08.2025 */
class DetailOrganizacePage extends BaseKatalogContentLayout
{

   public const string INFORMACE_BLOCK_ID = 'info';
   public const string PROMOAKCE_BLOCK_ID = 'promo';

   public const string PROSTORY_BLOCK_ID = 'spaces';
   public const string VYBAVENI_BLOCK_ID = 'vybaveni';

   public const string SLUZBY_BLOCK_ID = 'sluzby';

   public const string RECENZE_BLOCK_ID = 'reviews';
   public const string RECENZE_QVAMP_BLOCK_ID = 'q-reviews';

   public const string MAPA_BLOCK_ID = 'map';
   public const string DALSI_INFORMACE_BLOCK_ID = 'more';

   public function setOrganizace(OrganizaceRow $organizace) :static {
      $this->organizace = $organizace;
      return $this;
   }

   protected function prepareTemplate(Templater $templater) :void {
      $this->appendBreadcrumbs();
      $this->appendTopNavigation();
      $this->appendGalerie();
      $this->appendInformace();
      $this->appendPromoakce();

      $this->appendProstory();
      $this->appendVybaveni();

      $this->appendSluzby();

      $this->appendZakaznickeRecenze();
      $this->appendQvampRecenze();

      $this->appendMapa();

      $this->appendDalsiInformace();

      $templater->addData(
         [
            '_page' => $this,
            'organizace' => $this->organizace,
         ] + $this->pageData
      );

      $this->setTitle(System::getTranslator()->layoutTranslate('$1 - detail organizace', $this->organizace->nazev));

      $this->addMetaTags(new DetailOrganizaceMetaTags($this->organizace));
   }

   private function appendBreadcrumbs() :void {
      $this->pageData['homepageUrl'] = HomepageController::getUrl(KatalogOrganizaceRegion::CZ->value);
   }

   private function appendTopNavigation() :void {
      $this->pageData['topNavigation'] = OrganizaceTopNavigationComponent::getComponent()
         ->setOrganizace($this->organizace);
   }

   private function appendGalerie() :void {
      $this->pageData['galerie'] = OrganizaceGalerieComponent::getComponent()
         ->setOrganizace($this->organizace);
   }

   private function appendInformace() :void {
      $this->pageData['informaceElementID'] = self::INFORMACE_BLOCK_ID;
      $this->pageData['informace'] = OrganizaceInformaceComponent::getComponent()
         ->setOrganizace($this->organizace);
   }

   private function appendPromoakce() :void {
//      @TODO pouze pokud budou promoakce přidá se componenta
//      if(true)
//         return;

      $this->pageData['promoakceElementID'] = self::PROMOAKCE_BLOCK_ID;
      $this->pageData['promoakce'] = OrganizacePromoakceComponent::getComponent()
         ->setOrganizace($this->organizace);
   }

   private function appendProstory() :void {
      if(!$this->organizace->getVersion()->isVenue())
         return;

      $this->pageData['prostoryElementID'] = self::PROSTORY_BLOCK_ID;
      $this->pageData['prostory'] = OrganizaceProstoryComponent::getComponent()
         ->setOrganizace($this->organizace);
   }

   private function appendVybaveni() :void {
      if(!$this->organizace->getVersion()->isVenue())
         return;

      $this->pageData['vybaveniElementID'] = self::VYBAVENI_BLOCK_ID;
      $this->pageData['vybaveni'] = OrganizaceVybaveniComponent::getComponent()
         ->setOrganizace($this->organizace);
   }

   private function appendSluzby() :void {
      if(empty(OrganizaceSluzby::getForOrganizace($this->organizace->id_organizace)))
         return;

      $this->pageData['sluzbyElementID'] = self::SLUZBY_BLOCK_ID;
      $this->pageData['sluzby'] = OrganizaceSluzbyComponent::getComponent()
         ->setOrganizace($this->organizace);
   }

   private function appendZakaznickeRecenze() :void {
      $this->pageData['zakaznickeRecenzeElementID'] = self::RECENZE_BLOCK_ID;
      $this->pageData['zakaznickeRecenze'] = OrganizaceRecenzeComponent::getComponent()
         ->setOrganizace($this->organizace);
   }

   private function appendQvampRecenze() :void {
      $this->pageData['qvampRecenzeElementID'] = self::RECENZE_QVAMP_BLOCK_ID;
      $this->pageData['qvampRecenze'] = OrganizaceQvampRecenzeComponent::getComponent()
         ->setOrganizace($this->organizace);
   }

   private function appendMapa() :void {
      if(!$this->organizace->getVersion()->isVendor())
         return;

      $this->pageData['mapaElementID'] = self::MAPA_BLOCK_ID;
      $this->pageData['mapa'] = OrganizaceMapaComponent::getComponent()
         ->setOrganizace($this->organizace);
   }

   private function appendDalsiInformace() :void {
      $this->pageData['dalsiInformaceElementID'] = self::DALSI_INFORMACE_BLOCK_ID;
      $this->pageData['dalsiInformace'] = OrganizaceDalsiInformaceComponent::getComponent()
         ->setOrganizace($this->organizace);
   }

   private OrganizaceRow $organizace;
   private array $pageData = [];
}