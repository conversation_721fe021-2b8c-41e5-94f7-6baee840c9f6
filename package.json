{"name": "qvamp", "version": "4.2.0", "description": "QVAMP - Venue & Event managment system", "scripts": {"build": "cross-env NODE_ENV=production webpack --config webpack.config.js --progress"}, "author": "<beat>app s.r.o", "private": true, "browserslist": ["> 0.5%", "last 2 major versions", "not dead", "Chrome >= 60", "Firefox >= 60", "not Edge < 79", "Firefox ESR", "iOS >= 10", "Safari >= 10", "Android >= 6", "not Explorer <= 11"], "devDependencies": {"@babel/core": "7.17.0", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-transform-runtime": "^7.26.10", "@babel/preset-env": "7.16.11", "@babel/preset-typescript": "^7.27.0", "@babel/register": "7.17.0", "@types/bootstrap": "^5.2.10", "@types/grecaptcha": "^3.0.9", "@types/jquery": "^3.5.32", "@types/js-cookie": "^3.0.6", "@types/node": "^18.19.86", "@types/qs": "^6.14.0", "@types/sortablejs": "^1.15.8", "@types/toastr": "^2.1.43", "@types/vue-select": "^2.5.0", "@vee-validate/zod": "^4.15.0", "@vue/runtime-core": "^3.5.13", "autoprefixer": "10.4.2", "babel-eslint": "10.1.0", "babel-loader": "8.2.3", "babel-preset-es2015": "^6.24.1", "babel-preset-minify": "^0.5.2", "babelify": "^10.0.0", "browser-sync": "^3.0.4", "browserify": "^17.0.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "9.1.0", "cross-env": "7.0.3", "css-loader": "6.6.0", "css-minimizer-webpack-plugin": "3.4.1", "eslint": "8.28", "eslint-plugin-unicorn": "^46.0.1", "expose-loader": "3.1.0", "file-loader": "6.2.0", "filemanager-webpack-plugin": "^8.0.0", "fork-ts-checker-webpack-plugin": "^7.3.0", "graceful-fs": "^4.2.2", "gulp": "^5.0.0", "gulp-if": "^3.0.0", "gulp-load-plugins": "^2.0.7", "gulp-minify": "^3.0.0", "gulp-postcss": "^9.0.1", "gulp-rename": "^2.0.0", "gulp-sass": "^5.1.0", "gulp-sourcemaps": "^2.6.5", "gulp-terser": "^2.1.0", "gulp-uglify": "^3.0.2", "gulp-uglify-es": "^3.0.0", "gulp-util": "^1.0.0", "gulp-watch-sass": "^1.3.2", "gulp-webpack": "^0.0.1", "hard-source-webpack-plugin": "0.13.1", "imask": "^7.6.1", "mini-css-extract-plugin": "2.5.3", "postcss": "^8.5.3", "postcss-loader": "6.2.1", "postcss-reporter": "^7.1.0", "resolve-url-loader": "^5.0.0", "sass": "1.49.7", "sass-loader": "12.4.0", "script-loader": "0.7.2", "sortablejs": "^1.15.6", "style-loader": "3.3.1", "terser-webpack-plugin": "5.3.1", "ts-loader": "^9.5.2", "tsconfig-paths": "^4.2.0", "tsconfig-paths-webpack-plugin": "^4.2.0", "tsify": "^5.0.4", "typescript": "^4.9.5", "url-loader": "4.1.1", "utils-merge": "^1.0.1", "vinyl-buffer": "^1.0.1", "vinyl-source-stream": "^2.0.0", "vue-loader": "^17.4.2", "vue-style-loader": "^4.1.3", "vuedraggable": "^4.1.0", "watchify": "^4.0.0", "webpack": "^5.99.6", "webpack-cli": "4.9.2", "webpack-dev-server": "^4.15.2", "webpack-hot-middleware": "^2.26.1", "webpack-remove-empty-scripts": "0.7.3"}, "dependencies": {"@babel/polyfill": "7.12.1", "@eonasdan/tempus-dominus": "^6.9.9", "@fortawesome/fontawesome-free": "^6.2.1", "@popperjs/core": "2.11.6", "@vueform/vueform": "^1.12.11", "apexcharts": "^3.33.0", "bootstrap": "5.2.3", "bootstrap-daterangepicker": "3.1.0", "bootstrap-icons": "^1.11.2", "chart.js": "2.9.4", "datatables.net": "1.11.4", "datatables.net-bs5": "1.11.4", "datatables.net-buttons": "2.2.2", "datatables.net-buttons-bs5": "2.2.2", "datatables.net-fixedheader": "3.2.1", "datatables.net-fixedheader-bs5": "3.2.1", "datatables.net-responsive": "2.2.9", "datatables.net-responsive-bs5": "2.2.9", "datatables.net-select": "1.3.4", "decimal.js": "^10.5.0", "dragula": "3.7.3", "feather-icons": "4.28.0", "fullcalendar": "5.10.1", "funnel-graph-js": "^1.4.2", "i18next": "^22.5.1", "ionicons": "4.6.3", "jquery": "3.6.0", "jquery-mask-plugin": "1.14.16", "jquery-ui": "^1.13.2", "jquery-validation": "^1.21.0", "js-cookie": "^3.0.5", "maska": "^3.1.1", "moment": "^2.30.1", "nouislider": "^15.8.1", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "qs": "^6.14.0", "regenerator-runtime": "^0.14.1", "select2": "4.0.13", "smartwizard": "5.1.1", "toastr": "2.1.4", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-i18n": "^11.1.11", "wampy": "^7.1.1", "zod": "^3.25.76"}}